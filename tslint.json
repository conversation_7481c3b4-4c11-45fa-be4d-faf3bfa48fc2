{
  "extends": [
    "tslint:latest",
    "tslint-react"
  ],
  "rules": {
    "no-constant-condition": true,
    // override tslint-react rules here
    "jsx-wrap-multiline": false,
    "object-literal-sort-keys": false,
    "no-submodule-imports": false,
    "no-console": false,
    "max-classes-per-file": [
      false
    ],
    "max-line-length": false,
    "no-eval": false,
    "jsx-no-lambda": false,
    "jsx-no-multiline-js": false,
    "no-var-requires": false,
    "ordered-imports": false,
    "no-implicit-dependencies": false
  },
  "linterOptions": {
    "exclude": [
      "./packages/@core/webpack/index.ts"
    ]
  }
}
