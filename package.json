{"name": "kingkong-supplychainms-f2e", "version": "3.1.43", "description": "", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "start": "cross-env PROXY=https://scmtest.haoduoke.cn webpack-dev-server --hot --progress --config ./packages/src/webpack.config.js", "clean": "rm -rf ./__out__", "dll": "webpack -p --profile --optimize-minimize --config ./packages/src/webpack-dll.config.js", "otherdll": "webpack -p --profile --optimize-minimize --config ./packages/src/webpack-dll-other.config.js", "bundle": "npm run clean && NODE_ENV=production webpack -p --profile --config ./packages/src/webpack-bundle.config.js", "beta": "npm run bundle && gulp copy:file && gulp ftp:saas:beta", "qa": "npm run bundle && gulp copy:file && gulp ftp:saas:qa", "remote": "gulp copy:file", "publish:dev": "gulp html:dev && gulp js:dev", "publish:uat": "gulp html:uat && gulp js:uat", "publish:ppe": "gulp html:ppe && gulp js:ppe", "publish:prod": "gulp html:prod && gulp js:prod", "publish:zcjprod": "gulp html:zcjprod && gulp js:zcjprod", "publish:pressure": "gulp html:pressure && gulp js:pressure", "publish:sales": "gulp html:sales && gulp js:sales", "publish:yblprod": "gulp html:yblprod && gulp js:yblprod", "publish:hmsprod": "gulp html:hmsprod && gulp js:hmsprod", "publish:updev": "gulp html:updev && gulp js:updev", "publish:upuat": "gulp html:upuat && gulp js:upuat"}, "keywords": [], "author": "pekon", "dependencies": {"antd": "^3.4.3", "antd-mobile": "^2.3.1", "axios": "^0.19.1", "fastclick": "^1.0.6", "gregorian-calendar": "^4.1.6", "gregorian-calendar-format": "^4.1.3", "history": "^4.10.1", "inversify": "^4.13.0", "inversify-binding-decorators": "^3.2.0", "inversify-inject-decorators": "^3.0.2", "lodash": "^4.17.15", "mobx": "^4.2.0", "mobx-react": "^5.0.0", "numeral": "^2.0.6", "qrcode.react": "^1.0.0", "qs": "^6.9.1", "react": "^16.12.0", "react-content-loader": "^4.3.4", "react-dom": "^16.12.0", "react-iscroll": "^2.0.3", "react-router": "^5.1.2", "react-router-dom": "^5.1.2", "react-slick": "^0.25.2", "react-virtualized": "^9.21.2", "reflect-metadata": "^0.1.13", "slick-carousel": "^1.8.1", "styled-components": "^4.4.1", "uuid": "^3.3.3", "vconsole": "^3.3.4"}, "devDependencies": {"@babel/core": "^7.7.7", "@babel/plugin-proposal-object-rest-spread": "^7.7.7", "@types/lodash": "^4.14.149", "@types/node": "^13.1.5", "@types/react": "^16.9.17", "@types/react-dom": "^16.9.4", "babel-loader": "^8.0.6", "babel-plugin-import": "^1.13.0", "babel-plugin-syntax-dynamic-import": "^6.18.0", "babel-plugin-transform-object-rest-spread": "^6.26.0", "browser-sync": "^2.26.7", "browser-sync-webpack-plugin": "^2.2.2", "bundle-loader": "^0.5.6", "compression-webpack-plugin": "^3.0.1", "cross-env": "^6.0.3", "css-loader": "^3.4.1", "file-loader": "^5.0.2", "gulp": "^3.9.1", "gulp-concat": "^2.6.1", "gulp-rename": "^2.0.0", "gulp-replace": "^1.0.0", "gulp-sftp": "^0.1.5", "gulp-util": "^3.0.8", "less": "^3.10.3", "less-loader": "^5.0.0", "postcss-loader": "^3.0.0", "react-dom-factories": "^1.0.2", "react-hot-loader": "^4.12.18", "style-loader": "^1.1.2", "svg-react-loader": "^0.4.6", "ts-loader": "^6.2.1", "ts-node": "^8.5.4", "tslint": "^5.20.1", "tslint-eslint-rules": "^5.4.0", "tslint-react": "^4.1.0", "typescript": "^3.7.4", "uglifyjs-webpack-plugin": "^2.2.0", "url-loader": "^3.0.0", "vinyl-ftp": "^0.6.1", "webpack": "^4.41.5", "webpack-bundle-analyzer": "^3.6.0", "webpack-cli": "^3.3.10", "webpack-dev-server": "^3.10.1", "webpack-merge": "^4.2.2"}}