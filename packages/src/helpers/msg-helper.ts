import { Toast } from "antd-mobile";
import { isEmpty } from "lodash";

const RESPONSE_ERROR = "fail";

export function msgSuccess(msg) {
  Toast.success(msg);
}

export function msgError(msg) {
  Toast.fail(msg);
}

export function msgTimeOut() {
  Toast.fail("您的网络抛锚了,请刷新重试");
}

export function resultWrapper(res, failCb?) {
  const { status, message, data } = res;
  if (status === RESPONSE_ERROR) {
    if (!isEmpty(message)) {
      msgError(message);
    }
    if (failCb) {
      failCb(data);
    }
  }
  return null;
}
