// 系统
export enum SystemOSEnum {
  IPHONE = "iPhone",
  ANDROID = "Android",
  WP = "WP",
  OTHERS = "OTHERS",
}

// 系统环境
export const getOS = () => {
  // 获取当前操作系统
  let os;
  if (navigator.userAgent.indexOf("Android") > -1 || navigator.userAgent.indexOf("Linux") > -1) {
    os = SystemOSEnum.ANDROID;
  } else if (/iP(ad|hone|od)/.test(navigator.userAgent)) {
    os = SystemOSEnum.IPHONE;
  } else if (navigator.userAgent.indexOf("Windows Phone") > -1) {
    os = SystemOSEnum.WP;
  } else {
    os = SystemOSEnum.OTHERS;
  }
  return os;
};

export const isIPhone = () => {
  return getOS() === SystemOSEnum.IPHONE;
};
