import axios from "axios";
import { SITE_PATH } from "../modules/app";
import { msgError, msgSuccess } from "./msg-helper";
import { Modal } from "antd-mobile";
import packageJson from "../../../package.json";

const alert = Modal.alert;
declare const require: any;
declare const window: any;
const wxConfig = require("./wxconfig.json");
const ua = window.navigator.userAgent.toLowerCase();
const SCANNINGLOGIN = "Y";

// export const REQUEST_SERVER = "http://localhost:3000";
// export const REQUEST_SERVER = "http://kkwxtest.haoduoke.cn"; // 测试 产品uat
// export const REQUEST_SERVER = "http://uatorder.fwh1988.cn:8088"; // 客户uat
// 客户uat : scm 13918421696  111
// export const REQUEST_SERVER = "https://order.fwh1988.cn"; // fawa生产环境
// export const REQUEST_SERVER = "http://************:8080";
// export const REQUEST_SERVER = "http://*************:4444";
export const REQUEST_SERVER = "https://scmtest.haoduoke.cn";
// export const REQUEST_SERVER = "http://*************:8080";
// export const REQUEST_SERVER = REMOTE_SERVER;
// export const REQUEST_SERVER = window.ENV.server;

console.log(document.domain);
console.log(wxConfig);
const domainCustomer = document.domain;
let wxUrl;
if (domainCustomer.indexOf("uatorder.fwh") > -1) {
  wxUrl = wxConfig.fwhuat.wxUrl;
} else if (domainCustomer.indexOf("order.fwh") > -1) {
  wxUrl = wxConfig.fwh.wxUrl;
} else if (domainCustomer.indexOf("ybl.haoduoke") > -1) {
  wxUrl = wxConfig.ybl.wxUrl;
} else if (domainCustomer.indexOf("zcj") > -1) {
  wxUrl = wxConfig.zcj.wxUrl;
} else if (domainCustomer.indexOf("static-show") > -1) {
  wxUrl = wxConfig.sales.wxUrl;
}
// 比较大小
function compareSize(frontVerison, afterVerison) {
  if (frontVerison && afterVerison) {
    const arrA = String(frontVerison).split(".");
    const arrB = String(afterVerison).split(".");
    let blen = false;
    for (let i = 0; i < arrB.length; i ++) {
      console.log(Number(arrB[i]), Number(arrA[i]), i);
      if ((Number(arrB[i]) || 0) > (Number(arrA[i]) || 0)) {
        blen = true
        break;
      } else if ((Number(arrB[i]) || 0) < (Number(arrA[i]) || 0)) {
        blen = false
        break;
      }
    }
    return blen;
  } else {
    return false;
  }
}
const recordLogUrl = `/integration/scm/frontlog/print`;
// 记录日志
function logRecord(params) {
  if (params && params.path === recordLogUrl) {
    return;
  }
  const serverVersion = localStorage.getItem("server_version");
  params.js_version = packageJson.version;
  params.server_version = serverVersion ? serverVersion : "没有获取到服务端版本号";
  params.mobileInfo = window.ENV.mobileInfo;
  post(recordLogUrl, { logInfo: JSON.stringify(params)});
}
// 捕捉异常
function catchError(res, url) {
  if (res) {
    const { type, reason } = res;
    console.log("result", res);
    if (type === "success") {
      return res.data;
    } else if (type === "pageSkip") {
      console.log("不做处理");
    } else if (type === "codeExecutionError") {
      const obj = {
        type: "recordErrorInfo",
        path: url,
        pagePath: window.location.href,
        reason: `codeExecutionError${JSON.stringify(reason ? reason : "")}`,
      };
      logRecord(obj);
    } else {
      if (url !== recordLogUrl) {
        // msgError("请求异常，请稍后重试");
      }
      const obj = {
        type: "recordErrorInfo",
        path: url,
        pagePath: window.location.href,
        reason: `接口返回异常${JSON.stringify(res)}`,
      };
      logRecord(obj);
    }
  } else {
    if (url !== recordLogUrl) {
      // msgError("请求异常，请稍后重试");
    }
    const obj = {
      type: "recordErrorInfo",
      path: url,
      pagePath: window.location.href,
      reason: `接口没有返回结果`,
    };
    logRecord(obj);
  }
}
export function loginPost<TErr, TRes>(url: string, params?: any) {
  return axios({
    url: `${REQUEST_SERVER}/k${url}`,
    // url: `/k${url}`,
    method: "post",
    headers: {
      "Accept": "application/json",
      "Content-Type": "application/json",
      "ssoSessionId": localStorage.getItem("token") || "",
    },
    data: params,
  }).then((response) => {
    try {
      if (response) {
        if (response.status === 302) {
          window.location.reload();
        } else {
          return { type: "success", data: response.data };
        }
      } else {
        return { type: "noResponse" };
      }
    } catch (e) {
      return{ type: "codeExecutionError", reason: e };
    }
  })
    .then((res) => {
    console.log("最后执行的内容", res);
    return catchError(res, url);
  });
}
console.log("----------", window.location.href, `/${SITE_PATH}/session-expired`);
export function post<TErr, TRes>(url: string, params?: any, interfaceType?: any) {
  console.log(packageJson.version);
  return axios({
    url: `${REQUEST_SERVER}/k${url}`,
    // url: `/k${url}`,
    method: "post",
    headers: {
      "Accept": "application/json",
      "Content-Type": "application/json",
      "ssoSessionId": localStorage.getItem("token") || "",
    },
    data: params,
  }).then((response) => {
    try {
      if (response) {
        const { js_version } = response.headers;
        if (js_version !== localStorage.getItem("server_version")) {
          localStorage.setItem("server_version", js_version);
        }
        if (js_version && compareSize(packageJson.version, js_version) && interfaceType === "routeSkip") {
          // 页面地址跳转检测到版本变化自动帮用户更新js版本
          let queryJsTimer = Number(sessionStorage.getItem("queryJsTimer"));
          if (!queryJsTimer) {
            queryJsTimer = 1;
            sessionStorage.setItem("queryJsTimer", String(queryJsTimer));
          }
          if (queryJsTimer <= 3 && queryJsTimer > 0) {
            console.log("queryJsTimer1", queryJsTimer);
            sessionStorage.setItem("queryJsTimer", String(queryJsTimer + 1));
            setTimeout(() => {
              window.location.reload();
            }, 500);
            return;
          } else if (queryJsTimer > 3) {
            sessionStorage.removeItem("queryJsTimer");
            alert("系统已为你升级到新版本，请退出后重新登陆", "", [
              {
                text: "我知道了", onPress: () => {
                  console.log("queryJsTimer2", queryJsTimer);
                  return;
                },
              },
            ]);
          }
          return ;
        } else if (js_version && compareSize(packageJson.version, js_version) && interfaceType !== "routeSkip") {
          // 用户手动触法接口操作检测到版本变化让用户有感知的去刷新新的js版本
          alert("系统已为你升级到新版本，快去体验吧", "", [
            {
              text: "我知道了", onPress: () => {
                window.location.reload();
                return;
              },
            },
          ]);
        }
        if (response.status === 302) {
          window.location.reload();
        } else if (response.status === 200) {
          console.log(response);
          if (response.data.code === 401) {
            if (ua.match(/MicroMessenger/i) == "micromessenger") {
              localStorage.setItem("token", "");
              // window.location.href = `/${SITE_PATH}/session-expired`;
              window.location.href = wxUrl;
            } else {
              localStorage.setItem("token", "");
              if (localStorage.getItem("scanningLogin") === SCANNINGLOGIN) { // 扫码登录
                msgError("会话已过期，请重新扫码登录");
                localStorage.setItem("scanningLogin", "");
                window.location.href = `/${SITE_PATH}/scanning-login`;
                return;
              } else {
                msgError("会话已过期，请重新访问商城");
                setTimeout(() => {
                  window.location.href = `/${SITE_PATH}/login`;
                }, 2);
                return { type: "pageSkip" };
              }
            }
          }
          if (response.data.message) {
            msgSuccess(response.data.message);
          }
          return { type: "success", data: response.data.data };
        } else if (response.status === 500) {
          if (response.data.message) {
            if (response.data.message.indexOf("project") > -1) {
              msgError("系统异常");
              console.log(response.data.message);
            } else if (response.data.message.indexOf("user auth error") > -1) {
              // window.location.href = wxUrl;
              if (window.location.href.indexOf(`/${SITE_PATH}/session-expired`) === -1) {
                window.location.href = `/${SITE_PATH}/session-expired`;
              }
            } else {
              msgError(response.data.message);
            }
          }
        } else if (response.status === 401) {
          if (ua.match(/MicroMessenger/i) == "micromessenger") {
            localStorage.setItem("token", "");
            // window.location.href = `/${SITE_PATH}/session-expired`;
            window.location.href = wxUrl;
          } else {
            localStorage.setItem("token", "");
            if (localStorage.getItem("scanningLogin") === SCANNINGLOGIN) { // 扫码登录
              msgError("会话已过期，请重新扫码登录");
              localStorage.setItem("scanningLogin", "");
              window.location.href = `/${SITE_PATH}/scanning-login`;
              return;
            } else {
              msgError("会话已过期，请重新访问商城");
              setTimeout(() => {
                window.location.href = `/${SITE_PATH}/login`;
              }, 2);
              return { type: "pageSkip" };
            }
          }
        }
        return Promise.reject(response.data);
      } else {
        return { type: "noResponse" };
      }
    } catch (e) {
      return{ type: "codeExecutionError", reason: e };
    }
  })
    .catch((ex) => {
      try {
        const { response } = ex;
        if (response) {
          if (response.status === 500) {
            console.log(response);
            if (response.data.message) {
              if (response.data.message.indexOf("project") > -1) {
                msgError("系统异常");
                console.log(response.data.message);
              } else if (response.data.message.indexOf("user auth error") > -1) {
                // window.location.href = wxUrl;
                if (window.location.href.indexOf(`/${SITE_PATH}/session-expired`) === -1) {
                  window.location.href = `/${SITE_PATH}/session-expired`;
                }
              } else {
                msgError(response.data.message);
              }
            }
          } else if (response.status === 401) {
            if (ua.match(/MicroMessenger/i) == "micromessenger") {
              localStorage.setItem("token", "");
              // window.location.href = `/${SITE_PATH}/session-expired`;
              window.location.href = wxUrl;
            } else {
              localStorage.setItem("token", "");
              if (localStorage.getItem("scanningLogin") === SCANNINGLOGIN) { // 扫码登录
                msgError("会话已过期，请重新扫码登录");
                localStorage.setItem("scanningLogin", "");
                window.location.href = `/${SITE_PATH}/scanning-login`;
                return;
              } else {
                msgError("会话已过期，请重新访问商城");
                setTimeout(() => {
                  window.location.href = `/${SITE_PATH}/login`;
                }, 2);
                return { type: "pageSkip" };
              }
            }
          }
        } else {
          return { type: "noResponse" };
        }
      } catch (e) {
        return{ type: "codeExecutionError", reason: e };
      }
    })
    .then((res) => {
      console.log("最后执行的内容", res);
      return catchError(res, url);
    });
}

export function wxpost<TErr, TRes>(url: string, params?: any, interfaceType?: any) {
  // const token = localStorage.getItem("ssoSessionId");
  // if (!token) {
  //   window.location.href = `/${SITE_PATH}/login`;
  //   return;
  // }
  return axios({
    url: `${REQUEST_SERVER}/k${url}`,
    method: "post",
    headers: {
      "Accept": "application/json",
      "Content-Type": "application/json",
      "ssoSessionId": localStorage.getItem("token") || "",
    },
    data: params,
  })
    .then((response) => {
      try {
        if (response) {
          console.log(response.headers);
          const { js_version } = response.headers;
          if (js_version && compareSize(packageJson.version, js_version) && interfaceType === "routeSkip") {
            let queryJsTimer = Number(sessionStorage.getItem("queryJsTimer"));
            if (!queryJsTimer) {
              queryJsTimer = 1;
              sessionStorage.setItem("queryJsTimer", String(queryJsTimer));
            }
            if (queryJsTimer <= 3 && queryJsTimer > 0) {
              console.log("queryJsTimer1", queryJsTimer);
              sessionStorage.setItem("queryJsTimer", String(queryJsTimer + 1));
              setTimeout(() => {
                window.location.reload();
              }, 500);
              return;
            } else if (queryJsTimer > 3) {
              sessionStorage.removeItem("queryJsTimer");
              alert("系统已为你升级到新版本，请退出后重新登陆", "", [
                {
                  text: "我知道了", onPress: () => {
                    console.log("queryJsTimer2", queryJsTimer);
                    return;
                  },
                },
              ]);
            }
            return ;
          } else if (js_version && compareSize(packageJson.version, js_version) && interfaceType !== "routeSkip") {
            alert("系统已为你升级到新版本，快去体验吧", "", [
              {
                text: "我知道了", onPress: () => {
                  window.location.reload();
                  return;
                },
              },
            ]);
          }
          if (response.status === 302) {
            window.location.reload();
          } else if (response.status === 200) {
            console.log(response);
            if (response.data.code === 401) {
              if (ua.match(/MicroMessenger/i) == "micromessenger") {
                localStorage.setItem("token", "");
                // window.location.href = `/${SITE_PATH}/session-expired`;
                window.location.href = wxUrl;
              } else {
                localStorage.setItem("token", "");
                if (localStorage.getItem("scanningLogin") === SCANNINGLOGIN) { // 扫码登录
                  msgError("会话已过期，请重新扫码登录");
                  localStorage.setItem("scanningLogin", "");
                  window.location.href = `/${SITE_PATH}/scanning-login`;
                  return;
                } else {
                  msgError("会话已过期，请重新访问商城");
                  setTimeout(() => {
                    window.location.href = `/${SITE_PATH}/login`;
                  }, 2);
                  return { type: "pageSkip" };
                }
              }
            }
            if (response.data.message) {
              msgSuccess(response.data.message);
            }
            return { type: "success", data: response.data.data };
          } else if (response.status === 500) {
            if (response.data.message) {
              if (response.data.message.indexOf("project") > -1) {
                msgError("系统异常");
                console.log(response.data.message);
              } else {
                msgError(response.data.message);
              }
            }
          } else if (response.status === 401) {
            if (ua.match(/MicroMessenger/i) == "micromessenger") {
              localStorage.setItem("token", "");
              // window.location.href = `/${SITE_PATH}/session-expired`;
              window.location.href = wxUrl;
            } else {
              localStorage.setItem("token", "");
              if (localStorage.getItem("scanningLogin") === SCANNINGLOGIN) { // 扫码登录
                msgError("会话已过期，请重新扫码登录");
                localStorage.setItem("scanningLogin", "");
                window.location.href = `/${SITE_PATH}/scanning-login`;
                return;
              } else {
                msgError("会话已过期，请重新访问商城");
                setTimeout(() => {
                  window.location.href = `/${SITE_PATH}/login`;
                }, 2);
                return { type: "pageSkip" };
              }
            }
          }
          return Promise.reject(response.data);
        } else {
          return { type: "noResponse" };
        }
      } catch (e) {
        return{ type: "codeExecutionError", reason: e };
      }
  })
    .then((res) => {
    console.log("最后执行的内容", res);
    return catchError(res, url);
  });
}

export function post2<TErr, TRes>(url: string, params?: any, ssosessionId?: any) {
  // const token = localStorage.getItem("ssoSessionId");
  // if (!token) {
  //   window.location.href = `/${SITE_PATH}/login`;
  //   return;
  // }

  return axios({
    url: `${REQUEST_SERVER}/k${url}`,
    // url: `/k${url}`,
    method: "post",
    headers: {
      "Accept": "application/json",
      "Content-Type": "application/json",
      "ssoSessionId": localStorage.getItem("token") || "",
    },
    data: params,
  })
    .then((response) => {
      try {
        if (response) {
          console.log(response.headers);
          const { js_version } = response.headers;
          if (js_version && compareSize(packageJson.version, js_version)) {
            alert("系统已为你升级到新版本，快去体验吧", "", [
              {
                text: "我知道了", onPress: () => {
                  window.location.reload();
                  return;
                },
              },
            ]);
          }
          if (response.status === 302) {
            window.location.reload();
          } else if (response.status === 200) {
            if (response.data.code === 401) {
              if (ua.match(/MicroMessenger/i) == "micromessenger") {
                localStorage.setItem("token", "");
                // window.location.href = `/${SITE_PATH}/session-expired`;
                window.location.href = wxUrl;
              } else {
                localStorage.setItem("token", "");
                if (localStorage.getItem("scanningLogin") === SCANNINGLOGIN) { // 扫码登录
                  msgError("会话已过期，请重新扫码登录");
                  localStorage.setItem("scanningLogin", "");
                  window.location.href = `/${SITE_PATH}/scanning-login`;
                  return;
                } else {
                  msgError("会话已过期，请重新访问商城");
                  setTimeout(() => {
                    window.location.href = `/${SITE_PATH}/login`;
                  }, 2);
                  return { type: "pageSkip" };
                }
              }
            }
            if (response.data.message) {
              msgSuccess(response.data.message);
            }
            return { type: "success", data: response.data.data };
          } else if (response.status === 500) {
            if (response.data.message) {
              if (response.data.message.indexOf("project") > -1) {
                msgError("系统异常");
                console.log(response.data.message);
              } else if (response.data.message.indexOf("user auth error") > -1) {
                // window.location.href = wxUrl;
                if (window.location.href.indexOf(`/${SITE_PATH}/session-expired`) === -1) {
                  window.location.href = `/${SITE_PATH}/session-expired`;
                }
              } else {
                msgError(response.data.message);
              }
            }
          } else if (response.status === 401) {
            if (ua.match(/MicroMessenger/i) == "micromessenger") {
              localStorage.setItem("token", "");
              // window.location.href = `/${SITE_PATH}/session-expired`;
              window.location.href = wxUrl;
            } else {
              localStorage.setItem("token", "");
              if (localStorage.getItem("scanningLogin") === SCANNINGLOGIN) { // 扫码登录
                msgError("会话已过期，请重新扫码登录");
                localStorage.setItem("scanningLogin", "");
                window.location.href = `/${SITE_PATH}/scanning-login`;
                return;
              } else {
                msgError("会话已过期，请重新访问商城");
                setTimeout(() => {
                  window.location.href = `/${SITE_PATH}/login`;
                }, 2);
                return { type: "pageSkip" };
              }
            }
          }
          return Promise.reject(response.data);
        } else {
          return { type: "noResponse" };
        }
      } catch (e) {
        return{ type: "codeExecutionError", reason: e };
      }
    })
    .catch((ex) => {
      /*if (ex.timeout) { // 超时
        msgTimeOut();
      }*/
      const { response } = ex;
      try {
        if (response) {
          if (response.status === 500) {
            if (response.data.message) {
              if (response.data.message.indexOf("project") > -1) {
                msgError("系统异常");
                console.log(response.data.message);
              } else if (response.data.message.indexOf("user auth error") > -1) {
                // window.location.href = wxUrl;
                if (window.location.href.indexOf(`/${SITE_PATH}/session-expired`) === -1) {
                  window.location.href = `/${SITE_PATH}/session-expired`;
                }
              } else {
                msgError(response.data.message);
              }
            }
          } else if (response.status === 401) {
            if (ua.match(/MicroMessenger/i) == "micromessenger") {
              localStorage.setItem("token", "");
              // window.location.href = `/${SITE_PATH}/session-expired`;
              window.location.href = wxUrl;
            } else {
              localStorage.setItem("token", "");
              if (localStorage.getItem("scanningLogin") === SCANNINGLOGIN) { // 扫码登录
                msgError("会话已过期，请重新扫码登录");
                localStorage.setItem("scanningLogin", "");
                window.location.href = `/${SITE_PATH}/scanning-login`;
                return;
              } else {
                msgError("会话已过期，请重新访问商城");
                setTimeout(() => {
                  window.location.href = `/${SITE_PATH}/login`;
                }, 2);
                return { type: "pageSkip" };
              }
            }
          }
        } else {
          return { type: "noResponse" };
        }
      } catch (e) {
        return{ type: "codeExecutionError", reason: e };
      }
    })
    .then((res) => {
      console.log("最后执行的内容", res);
      return catchError(res, url);
    });
}
export function postNew<TErr, TRes>(url: string, params?: any, interfaceType?: any) {
  return axios({
    url: `${REQUEST_SERVER}${url}`,
    // url: `/k${url}`,
    method: "post",
    headers: {
      "Accept": "application/json",
      "Content-Type": "application/json",
      "ssoSessionId": localStorage.getItem("token") || "",
      "tenant": localStorage.getItem("tenant") || "",
    },
    data: params,
  }).then((response) => {
    try {
      if (response) {
        const { js_version } = response.headers;
        if (js_version !== localStorage.getItem("server_version")) {
          localStorage.setItem("server_version", js_version);
        }
        if (js_version && compareSize(packageJson.version, js_version) && interfaceType === "routeSkip") {
          // 页面地址跳转检测到版本变化自动帮用户更新js版本
          let queryJsTimer = Number(sessionStorage.getItem("queryJsTimer"));
          if (!queryJsTimer) {
            queryJsTimer = 1;
            sessionStorage.setItem("queryJsTimer", String(queryJsTimer));
          }
          if (queryJsTimer <= 3 && queryJsTimer > 0) {
            // console.log("queryJsTimer1", queryJsTimer);
            sessionStorage.setItem("queryJsTimer", String(queryJsTimer + 1));
            setTimeout(() => {
              window.location.reload();
            }, 500);
            return;
          } else if (queryJsTimer > 3) {
            sessionStorage.removeItem("queryJsTimer");
            alert("系统已为你升级到新版本，请退出后重新登陆", "", [
              {
                text: "我知道了", onPress: () => {
                  // console.log("queryJsTimer2", queryJsTimer);
                  return;
                },
              },
            ]);
          }
          return ;
        } else if (js_version && compareSize(packageJson.version, js_version) && interfaceType !== "routeSkip") {
          // 用户手动触法接口操作检测到版本变化让用户有感知的去刷新新的js版本
          alert("系统已为你升级到新版本，快去体验吧", "", [
            {
              text: "我知道了", onPress: () => {
                window.location.reload();
                return;
              },
            },
          ]);
        }
        if (response.status === 302) {
          window.location.reload();
        } else if (response.status === 200) {
          const responesCode = Number(response.data.code);
          if (responesCode === 401) {
            if (ua.match(/MicroMessenger/i) == "micromessenger") {
              localStorage.setItem("token", "");
              window.location.href = wxUrl;
            } else {
              localStorage.setItem("token", "");
              if (localStorage.getItem("scanningLogin") === SCANNINGLOGIN) { // 扫码登录
                msgError("会话已过期，请重新扫码登录");
                localStorage.setItem("scanningLogin", "");
                window.location.href = `/${SITE_PATH}/scanning-login`;
                return;
              } else {
                msgError("会话已过期，请重新访问商城");
                setTimeout(() => {
                  window.location.href = `/${SITE_PATH}/login`;
                }, 2);
                return { type: "pageSkip" };
              }
            }
          }
          if (responesCode === 10000) {
            return { type: "success", data: response.data.data || {} };
          }
          if (response.data.message) {
            msgError(response.data.message);
          }
          // console.log("responesCode", response.data.message, responesCode);
          return null;
        } else if (response.status === 500) {
          if (response.data.message) {
            if (response.data.message.indexOf("project") > -1) {
              msgError("系统异常");
              // console.log(response.data.message);
            } else if (response.data.message.indexOf("user auth error") > -1) {
              // window.location.href = wxUrl;
              if (window.location.href.indexOf(`/${SITE_PATH}/session-expired`) === -1) {
                window.location.href = `/${SITE_PATH}/session-expired`;
              }
            } else {
              msgError(response.data.message);
            }
          }
        } else if (response.status === 401) {
          if (ua.match(/MicroMessenger/i) == "micromessenger") {
            localStorage.setItem("token", "");
            // window.location.href = `/${SITE_PATH}/session-expired`;
            window.location.href = wxUrl;
          } else {
            localStorage.setItem("token", "");
            if (localStorage.getItem("scanningLogin") === SCANNINGLOGIN) { // 扫码登录
              msgError("会话已过期，请重新扫码登录");
              localStorage.setItem("scanningLogin", "");
              window.location.href = `/${SITE_PATH}/scanning-login`;
              return;
            } else {
              msgError("会话已过期，请重新访问商城");
              setTimeout(() => {
                window.location.href = `/${SITE_PATH}/login`;
              }, 2);
              return { type: "pageSkip" };
            }
          }
        }
        return Promise.reject(response.data);
      } else {
        return { type: "noResponse" };
      }
    } catch (e) {
      return{ type: "codeExecutionError", reason: e };
    }
  })
    .catch((ex) => {
      // 2020.11.20+
      if (ex.message.indexOf("777") >= 0) {
        window.location.href = `${window.ENV.sitePath}/organization-select`;
      }
      try {
        const { response } = ex;
        if (response) {
          if (response.status === 500) {
            console.log(response);
            if (response.data.message) {
              if (response.data.message.indexOf("project") > -1) {
                msgError("系统异常");
                console.log(response.data.message);
              } else if (response.data.message.indexOf("user auth error") > -1) {
                // window.location.href = wxUrl;
                if (window.location.href.indexOf(`/${SITE_PATH}/session-expired`) === -1) {
                  window.location.href = `/${SITE_PATH}/session-expired`;
                }
              } else {
                msgError(response.data.message);
              }
            }
          } else if (response.status === 401) {
            if (ua.match(/MicroMessenger/i) == "micromessenger") {
              localStorage.setItem("token", "");
              // window.location.href = `/${SITE_PATH}/session-expired`;
              window.location.href = wxUrl;
            } else {
              localStorage.setItem("token", "");
              if (localStorage.getItem("scanningLogin") === SCANNINGLOGIN) { // 扫码登录
                msgError("会话已过期，请重新扫码登录");
                localStorage.setItem("scanningLogin", "");
                window.location.href = `/${SITE_PATH}/scanning-login`;
                return;
              } else {
                msgError("会话已过期，请重新访问商城");
                setTimeout(() => {
                  window.location.href = `/${SITE_PATH}/login`;
                }, 2);
                return { type: "pageSkip" };
              }
            }
          }
        } else {
          return { type: "noResponse" };
        }
      } catch (e) {
        return{ type: "codeExecutionError", reason: e };
      }
    })
    .then((res) => {
      // console.log("最后执行的内容", res);
      return catchError(res, url);
    });
}
