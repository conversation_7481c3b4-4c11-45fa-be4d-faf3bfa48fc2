import * as React from "react";
import { observer } from "mobx-react";
import styled from "styled-components";
import {
  $OfflineTransferRecordOrderStatus,
} from "../../classes/const/$offline-transfer-record";
@observer
export class SingleOfflineTransferRecordPictureInfo extends React.Component<any, any> {
  public componentDidMount(): void {
  }

  public getReceiptStatus = (status) => {
    const { DRAFT, DEALWITH, CONFIRMEDRECEIPT, PAYFORFAILURE, CANCELLATION, WITHDRAW } = $OfflineTransferRecordOrderStatus;
    let receiptStatus = {
      color: "",
      text: "",
    };
    switch (status) {
      case DRAFT:
        receiptStatus = {
          color: "#999999",
          text: "草稿",
        };
        break;
      case PAYFORFAILURE:
        receiptStatus = {
          color: "#999999",
          text: "支付失败",
        };
        break;
      case DEALWITH:
        receiptStatus = {
          color: "#FF3030",
          text: "待审核",
        };
        break;
      case CONFIRMEDRECEIPT:
        receiptStatus = {
          color: "#52C41A",
          text: "已确认收款",
        };
        break;
      case CANCELLATION:
        receiptStatus = {
          color: "#999999",
          text: "已作废",
        };
        break;
      case WITHDRAW:
        receiptStatus = {
          color: "#999999",
          text: "已撤回",
        };
        break;
      default:
        break;
    }
    return receiptStatus;
  }

  public render() {
    const { record } = this.props;
    const { docStatus, sellerBankName, sellerAccountName, sellerAccountCode, amount, submitTime, imageUrl } = record || {};
    const receiptStatus = docStatus && this.getReceiptStatus(docStatus);
    const picture = imageUrl && imageUrl.length > 0 && imageUrl.slice(0, 1);
    return (
      <Record onClick={() => {
        // goToOrderDetail && goToOrderDetail(oid, docType);
      }}>
        <div className="record-picture">
          {
            picture ?
              <img src={picture} alt=""/>
              :
              <img src="https://order.fwh1988.cn:14501/static-img/scm/ico-nopic.png" alt=""/>
          }
        </div>
        <div className="record-info-left">
          <span>{`${sellerAccountName} (${sellerAccountCode})`}</span>
          <span>{sellerBankName}</span>
          <span>提交时间：{submitTime}</span>
        </div>
        <div className="record-info-right">
          <span>¥ {amount}</span>
          <span>{receiptStatus.text}</span>
        </div>
      </Record>
    );
  }
}

const Record = styled.div`// styled
  & {
    background:rgba(242,242,242,1);
    border-radius:3px;
    box-sizing: border-box;
    padding: 10px;
    margin-bottom: 16px;
    overflow: hidden;
    width: 100%;
    .record-picture{
      display: inline-block;
      float: left;
      width: 72px;
      height: 72px;
      background-color: #333333;
      margin-right: 8px;
      position: relative;
      >img{
        display: inline-block;
        width: 100%;
        height: auto;
        position: absolute;
        top: 50%;
        transform: translateY(-50%);
      }
    }
    .record-info-left{
      display: inline-block;
      float: left;
      width: calc(${document.documentElement.clientWidth - 207}px);
      height: 72px;
      >span{
        display: block;
        width: 100%;
        font-size: 12px;
        line-height: 12px;
      }
      >span:first-of-type{
        color: #2F2F2F;
        height: 28px;
        font-size: 12px;
        line-height: 14px;
        font-weight: 500;
        -webkit-line-clamp: 2;
        display: -webkit-box;
        -webkit-box-orient: vertical;
        overflow: hidden;
        text-overflow: ellipsis;
        margin-bottom: 14px;
      }
      >span:nth-of-type(2){
        white-space: nowrap;
        text-overflow:ellipsis;
        overflow: hidden;
      }
      >span:nth-of-type(3){
        margin-top:4px;
      }
    }
    .record-info-right{
      display: inline-block;
      float: right;
      width: 75px;
      height: 72px;
      position: relative;
      >span{
        color: #FF3030;
        text-align: right;
        display: block;
        width: 100%;
        font-size: 12px;
        line-height: 14px;
        white-space: nowrap;
        text-overflow:ellipsis;
        overflow: hidden;
      }
      >span:last-of-type{
        color: #2F2F2F;
        position: absolute;
        right: 0;
        bottom: 0;
        font-size: 12px;
        line-height: 12px;
      }
    }
  }
`;
