import * as React from "react";
import { observer } from "mobx-react";
import styled from "styled-components";
import { Button, Modal } from "antd-mobile";
import { findIndex } from "lodash";
import {
  offlineTransferRecordOrderType,
  offlineTransferRecordOrderStatus,
  $OfflineTransferRecordOrderStatus,
} from "../../classes/const/$offline-transfer-record";
const alert = Modal.alert;

@observer
export class SingleOfflineTransferRecordInfo extends React.Component<any, any> {
  public searchTargeObj = (searchTarge, searchRange) => {
    const index = findIndex(searchRange, { key: searchTarge });
    console.log(searchTarge, searchRange, index);
    return index >= 0 ? searchRange[index] : { text: "", color: "", key: "" };
  }
  public withdraw = (e, oid, docType) => {
    e.preventDefault();
    e.stopPropagation();
    alert("确认要撤回这个单据？", "", [
      {
        text: "取消", onPress: () => {
          console.log("取消");
        },
      },
      {
        text: "确认撤回", onPress: () => {
          const { withdraw } = this.props;
          withdraw && withdraw(oid, docType);
        },
      },
    ]);
  }
  public render() {
    const { record, goToOrderDetail } = this.props;
    console.log(record);
    const { docStatus, docType, docNo, sellerBankName, sellerAccountName, sellerAccountCode, amount, paymentTime, submitTime, memo, oid,paymentCode,paymentType,paymentMode } = record;
    const {  DEALWITH, CANCELLATION } = $OfflineTransferRecordOrderStatus;
    const offlineTransferStatus = this.searchTargeObj(docStatus, offlineTransferRecordOrderStatus);
    const orderType = this.searchTargeObj(docType, offlineTransferRecordOrderType);
    return (
      <Record onClick={() => {
        goToOrderDetail && goToOrderDetail(oid, docType);
      }}>
         <div className="record-title">
          <div className="receipt-type">{`${orderType.text}：`}</div>
          <div className="receipt-number">{docNo}</div>
          <div className="receipt-status" style={{ color: offlineTransferStatus.color }}>{offlineTransferStatus.text}</div>
        </div>
        <div className="record-content">
        {paymentCode != "allinpay" &&<div className="bank-info">
            <span>{sellerBankName}</span>
            <span>{sellerAccountName}</span>
            {
              sellerAccountCode &&
							<span>({sellerAccountCode})</span>
            }
            <span>{amount}</span>
          </div>}
          <ul className="order-info">
            <li><span>付款方式:</span><span>{paymentType}</span></li>
            {paymentCode == "allinpay" &&  <li><span>支付方式:</span><span>{paymentMode ? paymentMode : "未查询到支付方式" }</span></li>}
            <li><span>付款时间:</span><span>{paymentTime}</span></li>
            <li><span>提交时间:</span><span>{submitTime}</span></li>
            {
              docStatus === CANCELLATION && memo && <li><span>备注:</span><span>{memo}</span></li>
            }
          </ul>
        </div>
        {
          docStatus === DEALWITH && paymentCode != "allinpay" &&
					<div className="record-foot">
						<Button className="withdraw-button" onClick={(e) => {
						  this.withdraw(e, oid, docType);
						}}>撤回</Button>
					</div>
        }
      </Record>
    );
  }
}

const Record = styled.div`// styled
  & {
    width: 100%;
    background-color: #fff;
    box-sizing: border-box;
    .record-title{
      width: 100%;
      height: 40px;
      padding: 0 16px;
      .receipt-type, .receipt-number, .receipt-status{
        display: inline-block;
        float: left;
        line-height: 40px;
        font-size: 13px;
        color: #307DCD;
      }
      .receipt-number{
        color: #2F2F2F;
      }
      .receipt-status{
        float: right;
        color: #FF3030;
        font-size: 12px;
      }
    }
    .record-content{
      width: 100%;
      padding: 9px 16px 12px;
      border-top: 1px solid rgba(216,216,216,1);
      height: auto;
      font-size: 12px;
      color: #595959;
      .bank-info{
        height: auto;
        font-size: 13px;
        line-height: 20px;
        color: #2F2F2F;
        font-weight:500;
        padding-right: 112px;
        position: relative;
        >span{
          margin-right: 8px;
        }
        >span:last-of-type{
          position: absolute;
          top: 0;
          right: 0;
        }
      }
      .order-info{
        list-style: none;
        margin: 5px 0 0;
        >li{
          line-height: 20px;
          position: relative;
          padding-left: 56px;
          height: auto;
          >span:first-of-type{
            position: absolute;
            left: 0;
            top: 0;
          }
        }
      }
    }
    .record-foot{
      height: 50px;
      padding: 10px 16px;
      border-top: 1px solid rgba(216,216,216,1);
      .withdraw-button{
        height: 30px;
        width: 72px;
        color: #307DCD;
        text-align: center;
        line-height: 30px;
        float: right;
        font-size: 12px;
        border: none !important;
        border: 1px solid #307DCD !important;
        border-radius:5px;
      }
    }
  }
`;
