import { observer } from "mobx-react";
import moment from "moment";
import * as React from "react";
import styled from "styled-components";
import Bar from "./bar";
import Tooltip from "./tooltip/tooltip";
import { ReportSegmentEnum } from "../product-order-proposals/report-segment";

interface IHistoryReportProps {
  xAxis: string[];
  series: any[];
  data: any[];
  isHomeProduct?: boolean;
  unit?: string;
  reportType: ReportSegmentEnum;
  onClickBar?: (params: any) => void;
}

@observer
export class HistoryReport extends React.Component<IHistoryReportProps, any> {
  private barBoxRef: any = null;
  constructor(props: any) {
    super(props);
    this.barBoxRef = React.createRef();
    this.state = {
      openTooltip: false,
      boxWidth: 0,
      arrowRect: {},
      tooltipRect: {},
      curData: {},
    }
  }

  public componentDidMount(): void {
    const { width } = this.barBoxRef.current.getBoundingClientRect();
    this.setState({
      boxWidth: width,
    });
  }

  public componentWillUnmount(): void {
    this.removeListenClick();
  }

  public removeListenClick = () => {
    window.removeEventListener("click", this.listenClick);
  }

  public listenClick = (event) => {
    if (!event.target.closest("#reportBox")) {
      this.removeListenClick();
      this.setState({
        openTooltip: false,
      });
    }
  }

  public clickBar = (params, index) => {
    const { data } = this.props;
    window.addEventListener("click", this.listenClick);
    this.setState({
      openTooltip: true,
      curData: data[index],
      ...params,
    });
  }

  public render() {
    const {
      xAxis,
      series,
      reportType,
      unit,
    } = this.props;
    const maxSeries = Math.max(...series);
    const {
      openTooltip,
      arrowRect,
      tooltipRect,
      boxWidth,
      curData,
    } = this.state;
    return (<Box>
      <BarBox ref={this.barBoxRef}>
        {series.map((s, index) => <BarCol
          key={`${reportType}_${index}`}
        >
          <Bar
            height={Math.round(s / maxSeries * 100)}
            value={s}
            onClick={(rect) => this.clickBar(rect, index)}
            boxWidth={boxWidth}
          />
        </BarCol>)}
        {openTooltip && <Tooltip
          arrowRect={arrowRect}
          tooltipRect={tooltipRect}
          data={curData}
          reportType={reportType}
          unit={unit}
        />}
      </BarBox>
      <BaseLine />
      <BarNameBox>
        {xAxis.map((x) => <BarName key={x}>
          {moment(x).format("M")}月
        </BarName>)}
      </BarNameBox>
    </Box>);
  }
}

const Box = styled.div`// styled
  & {
    position: relative;

  }
`;
const BarBox = styled.div`// styled
  & {
    display: flex;
    margin-top: 20px;
  }
`;
const BaseLine = styled.div`// styled
  & {
    height: 1px;
    background-color: #E8E8E8;
    width: 100%;
    margin-top: .2px;
  }
`;
const BarCol = styled.div`// styled
  & {
    height: 120px;
    width: calc(100% / 14);
    position: relative;
  }
`;
const BarNameBox = styled.div`// styled
  & {
    margin-top: 8px;
    display: flex;
  }
`;
const BarName = styled.div`// styled
  & {
    width: calc(100% / 14);
    text-align: center;
    font-size: 11px;
    font-weight: normal;
    line-height: 11px;
    color: #999999;
  }
`;
