import { observer } from "mobx-react";
import * as React from "react";
import styled from "styled-components";

const TOOLTIP_BOTTOM_OFFSET = 8 + 5;
const BAR_TOP_OFFSET = 30;
const LEFT_PADDING = 12;
const TOOLTIP_WIDTH = 140;
const ARROW_LEFT_OFFST = 16;

@observer
export default class Bar extends React.Component<any, any> {
    private barRef: any = null;
    constructor(props: any) {
        super(props);
        this.barRef = React.createRef();
    }

    public click = () => {
        const { height, left } = this.barRef.current.getBoundingClientRect();
        const { boxWidth } = this.props;
        const x = left - ARROW_LEFT_OFFST - LEFT_PADDING;
        const y = height + TOOLTIP_BOTTOM_OFFSET + BAR_TOP_OFFSET;
        const totalX = x + TOOLTIP_WIDTH;
        if (totalX > boxWidth) {
            this.props.onClick({
                tooltipRect: {
                    bottom: y,
                    left: boxWidth - TOOLTIP_WIDTH,
                },
                arrowRect: {
                    left: totalX - boxWidth + ARROW_LEFT_OFFST,
                },
            });
        } else {
            this.props.onClick({
                tooltipRect: {
                    bottom: y,
                    left: x,
                },
                arrowRect: {
                    left: ARROW_LEFT_OFFST,
                },
            });
        }
    }

    public render() {
        const { height, value } = this.props;
        return (<BarItem
            style={{ height: `${height}%` }}
            ref={this.barRef}
        >
            <div onClick={this.click} className="bar-inner">
                <BarValue>{value}</BarValue>
            </div>
        </BarItem>);
    }
}

const BarItem = styled.div`// styled
  & {
    width: 48%;
    background: #437DF0;
    border-radius: 2px;
    margin: 0 auto;
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
    bottom: 0;
    .bar-inner {
      position: relative;
      height: 100%;
    }
  }
`;
const BarValue = styled.div`// styled
  & {
    color: #666666;
    font-size: 12px;
    line-height: 12px;
    position: absolute;
    top: -20px;
    left: 50%;
    transform: translateX(-50%);
  }
`;