import { observer } from "mobx-react";
import * as React from "react";
import "./tooltip.less";
import moment from "moment";
import { ReportSegmentEnum } from "../../product-order-proposals/report-segment";

@observer
export default class Tooltip extends React.Component<any, any> {

    public render() {
        const {
            arrowRect,
            tooltipRect,
            data,
            reportType,
            unit,
        } = this.props;
        const isConsume = ReportSegmentEnum.CONSUME === reportType;
        const {
            consumeYearMonth,
            consumeQuantity,
            nursingMemberQuantity,
            perNursingTimes,
            saleYearMonth,
            saleQuantity,
            saleMemberQuantity,
            perSaleQuantity,
        } = data;
        return (<div style={tooltipRect} className="tooltip">
            <div className="tooltip-content">
                <div style={arrowRect} className="tooltip-arrow" />
                <div className="tooltip-inner">
                    {isConsume ? <>
                        <div className="title">{moment(consumeYearMonth).format("YYYY年MM月")}</div>
                        <div className="line">
                            <div>消耗数量</div>
                            <div>{consumeQuantity}<span>{unit}</span></div>
                        </div>
                        <div className="line">
                            <div>护理会员量</div>
                            <div>{nursingMemberQuantity}</div>
                        </div>
                        <div className="line">
                            <div>人均护理次数</div>
                            <div>{perNursingTimes}<span>次</span></div>
                        </div>
                    </> : <>
                        <div className="title">{moment(saleYearMonth).format("YYYY年MM月")}</div>
                        <div className="line">
                            <div>销售数量</div>
                            <div>{saleQuantity}</div>
                        </div>
                        <div className="line">
                            <div>销售会员量</div>
                            <div>{saleMemberQuantity}</div>
                        </div>
                        <div className="line">
                            <div>人均购买数量</div>
                            <div>{perSaleQuantity}</div>
                        </div>
                    </>}
                </div>
            </div>
        </div>);
    }
}
