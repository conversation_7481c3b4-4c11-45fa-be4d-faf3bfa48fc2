.tooltip {

    font-size: 14px;
    line-height: 1.5;
    color: rgba(0, 0, 0, 0.65);
    box-sizing: border-box;
    margin: 0;
    padding: 0;
    list-style: none;
    position: absolute;
    z-index: 9;
    display: block;
    visibility: visible;
    padding-bottom: 8px;

    .tooltip-content {}

    .tooltip-arrow {
        position: absolute;
        width: 0;
        height: 0;
        border-color: transparent;
        border-style: solid;
        bottom: 3px;
        border-width: 5px 5px 0;
        border-top-color: #FFF;
        left: 16px;
    }

    .tooltip-inner {
        width: 140px;
        padding: 8px;
        color: #333;
        text-align: left;
        text-decoration: none;
        background-color: #FFF;
        border-radius: 4px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
        min-height: 32px;

        >.title {
            font-size: 12px;
            line-height: 18px;
            color: #666666;
            margin-bottom: 2px;
        }

        >.line {
            height: 22px;
            display: flex;
            align-items: center;
            margin-bottom: 2px;
            >div:nth-child(1) {
                font-size: 12px;
                color: #333;
            }

            >div:nth-child(2) {
                margin-left: auto;
                font-size: 14px;
                font-weight: bold;
                > span {
                    font-weight: normal;
                    color: #666;
                    font-size: 12px;
                }
            }
        }
    }
}