import React from "react";
import styled from "styled-components";

export const ANCHOR_HEIGHT = 47;

export enum ProductAnchorEnum {
  ORDER = "order",
  RICH = "rich",
}

interface IProductSegmentProps {
  value: ProductAnchorEnum;
  onClick: (e: ProductAnchorEnum) => void;
}

const SEGMENT_LIST = [
  { label: "订货建议", value: ProductAnchorEnum.ORDER },
  { label: "产品图文", value: ProductAnchorEnum.RICH },
];

export function ProductSegment(props: IProductSegmentProps) {
  const {
    value,
    onClick,
  } = props;
  return (<Wrapper>
    {SEGMENT_LIST.map((s) => <Item
      key={s.value}
      data-selected={s.value === value}
      onClick={(e) => {
        e.stopPropagation();
        e.preventDefault();
        onClick(s.value);
      }}
    >
      {s.label}
    </Item>)}
  </Wrapper>);
}

const Wrapper = styled.div`// styled
  & {
    padding: 8px 4px 16px;
    display: flex;
    height: ${ANCHOR_HEIGHT}px;
    box-sizing: border-box;
    justify-content: space-evenly;
    position: sticky;
    top: 0;
    z-index: 99;
    background: #F2F2F2;
  }
`;

const Item = styled.div`// styled
  & {
    font-size: 14px;
    font-weight: normal;
    line-height: 23px;
    color: #333333;
    font-weight: normal;
    position: relative;

    &[data-selected="true"] {
      color: #437DF0;

      &::after {
        position: absolute;
        content: " ";
        height: 2px;
        width: 70%;
        left: 50%;
        transform: translateX(-50%);
        bottom: -7px;
        background: #437DF0;
      }
    }
  }
`;
