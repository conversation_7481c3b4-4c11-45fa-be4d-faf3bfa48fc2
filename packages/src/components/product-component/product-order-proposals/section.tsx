import { observer } from "mobx-react";
import * as React from "react";
import styled from "styled-components";

interface ISectionProps {
  title: React.ReactNode;
  subTitle?: React.ReactNode;
  rightChildren?: React.ReactNode;
  hideTag?: boolean;
  style?: React.CSSProperties;
}

@observer
export default class Section extends React.Component<ISectionProps, any> {

  public render() {
    const {
      title,
      subTitle,
      rightChildren,
      hideTag,
      style,
    } = this.props;
    return (<Wrapper
      style={style}
    >
      <Title data-tag={hideTag}>{title}</Title>
      {subTitle && <SubTitle>{subTitle}</SubTitle>}
      {rightChildren}
    </Wrapper>);
  }
}

const Wrapper = styled.div`// styled
  & {
    padding: 12px 0;
    margin: 0 12px;
    position: relative;
    border-bottom: 0.5px solid #E8E8E8;
    background-color: #FFF;
  }
`;
const Title = styled.div`// styled
  & {
    margin-left: 12px;
    position: relative;
    line-height: 24px;
    font-size: 16px;
    font-weight: 500;
    color: #333;

    &::before {
      position: absolute;
      content: "";
      width: 4px;
      height: 16px;
      border-radius: 20px;
      top: 50%;
      transform: translateY(-50%);
      left: -12px;
      background: #437DF0;
    }

    &[data-tag="true"] {
      &::before {
        display: none;
      }
    }
  }
`;
const SubTitle = styled.div`// styled
  & {
    margin: 1px 0 0 12px;
    line-height: 18px;
    color: #666;
    font-size: 12px;
  }
`;
