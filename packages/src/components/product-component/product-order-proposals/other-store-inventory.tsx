import { observer } from "mobx-react";
import * as React from "react";
import { useImage, useProductCateByData, useProductIndexValue, useShowModal } from "../../../hooks";
import styled from "styled-components";
import StoreInventoryModal from "../../order/store-inventory-modal";
import { autowired } from "@classes/ioc/ioc";
import { $CommodityDetailsMv } from "../../../modules/commodity-details/commodity-details-mv";
import { Toast } from "antd-mobile";

@observer
export default class OtherStoreInventory extends React.Component<any, any> {
    @autowired($CommodityDetailsMv)
    public $commodityDetailsMv: $CommodityDetailsMv;

    constructor(props) {
        super(props);
        this.state = {
            otherStoreInventoryInfo: {},
        };
    }

    public loadData = () => {
        const { productSkuId } = this.props;
        Toast.loading("加载中...", 0);
        return new Promise((resolve, reject) => {
            this.$commodityDetailsMv.queryOtherStoreInventory({ productSkuId })
                .then((res) => {
                    if (res?.hasOwnProperty("otherStoreInventoryList")) {
                        Toast.hide();
                        this.setState({ otherStoreInventoryInfo: res || {} }, () => resolve(1));
                    } else {
                        return reject({});
                    }
                });
        });
    }

    public clickStoreInventory = () => {
        this.loadData().then(() => this.showInventoryModal());
    }

    public showInventoryModal = () => {
        const { otherStoreInventoryInfo } = this.state;
        const { isHomeProduct, unit } = this.props;
        useShowModal({
            children: <StoreInventoryModal
                unit={unit}
                data={otherStoreInventoryInfo}
                isHomeProduct={isHomeProduct}
            />,
        });
    }
    public render() {
        const { consumeData, saleData, isHomeProduct, unit } = this.props;
        const { domRc } = useProductCateByData(isHomeProduct, {
            domRc: (<Box>
                <TCol>
                    <div className="title">门店实物总库存</div>
                    <div className="value">{saleData?.realInventoryQuantityTotal}<span>{unit}</span></div>
                    <div className="sub-title">店均<span>{saleData?.averageInventoryQuantity}</span>{unit}</div>
                </TCol>
                <TCol>
                    <div className="title">月均销售合计</div>
                    <div className="value">{saleData?.monthSaleQuantity}<span>{unit}</span></div>
                    <div className="sub-title">店均<span>{saleData?.averageMonthSaleQuantity}</span>{unit}</div>
                </TCol>
                <TCol>
                    <div className="title">库存可销售月数</div>
                    <div style={{ color: "#FF3030" }} className="value">{saleData?.inventoryMonths}<span>个月</span></div>
                </TCol>
            </Box>),
        }, {
            domRc: (<Box>
                <TCol>
                    <div className="title">法人实物库存合计</div>
                    <div className="value">{consumeData?.realInventoryQuantityTotal}<span>{unit}</span></div>
                    <div className="sub-title">店均<span>{consumeData?.averageInventoryQuantity}</span>{unit}</div>
                </TCol>
                <TCol>
                    <div className="title">月均消耗合计</div>
                    <div className="value">{consumeData?.monthConsumeQuantity}<span>{unit}</span></div>
                    <div className="sub-title">店均<span>{consumeData?.averageMonthConsumeQuantity}</span>{unit}</div>
                </TCol>
                <TCol>
                    <div className="title">预计可消耗</div>
                    <div className="value">{useProductIndexValue(consumeData?.inventoryMonths)}<span>个月</span></div>
                </TCol>
            </Box>),
        });
        return (<Wrapper>
            {domRc}
            <More>
                <div onClick={this.clickStoreInventory}>
                    查看各门店库存
                    <img src={useImage("icon-arrow")} />
                </div>
            </More>
        </Wrapper>);
    }
}

const Wrapper = styled.div`// styled
  & {
    padding: 12px;
  }
`;
const Box = styled.div`// styled
  & {
   display: flex;
   justify-content: space-between;

   > div:last-child {
       min-width: 96px;
   }
  }
`;
const TCol = styled.div`// styled
  & {
    > .title {
        font-size: 12px;
        line-height: 18px;
        color: #666666;
    }

    > .value {
        font-size: 18px;
        font-weight: 500;
        line-height: 27px;
        color: #333333;
        > span {
            line-height: 27px;
            margin-left: 2px;
            color: #666;
            font-weight: normal;
            font-size: 14px;
            vertical-align: bottom;
        }
    }

    > .sub-title {
        color: #666;
        font-size: 12px;
        line-height: 18px;

        > span {
            vertical-align: bottom;
            margin: 0 4px;
            color: #333;
        }
    }
  }
`;
const More = styled.div`// styled
  & {
    display: flex;
    > div {
        display: flex;
        align-items: center;
        font-size: 12px;
        line-height: 18px;
        color: #666666;
        margin: 8px 0 0 auto;
        > img {
            width: 14px;
            height: 14px;
        }
    }
  }
`;
