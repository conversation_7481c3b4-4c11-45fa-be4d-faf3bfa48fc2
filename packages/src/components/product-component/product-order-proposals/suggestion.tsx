import { observer } from "mobx-react";
import * as React from "react";
import styled from "styled-components";
import { useImage, useProductCateByData, useShowModal } from "../../../hooks";
import MemberPredictModal from "../../order/member-predict-modal";

@observer
export default class Suggestion extends React.Component<any, any> {
  public showMemberModal = () => {
    const { data, isMainSaleProduct, unit } = this.props;
    useShowModal({
      children: <MemberPredictModal
        isMainSaleProduct={isMainSaleProduct}
        data={data}
        unit={unit}
      />,
    });
  }

  public render() {
    const {
      data,
      isHomeProduct,
      isMainSaleProduct,
      unit,
    } = this.props;
    const {
      suggestionOrderQuantity,
      demandQuantity,
      inventoryQuantity,
      minInventory,
      yearSaleTargetAmount,
      months,
      salesProportion,
      yearSaleTargetProductAmount,
      productSaleDiscount,
      yearSaleTargetQuantity,
      monthSaleTargetQuantity,
      predictedConsumingMembers,
      consumeDemandQuantity,
      perMonthlyConsume,
      satisfyDemandQuantity,
      startMonth,
      endMonth,
    } = data || {};
    const salesPercent = salesProportion * 100;
    const productSaleDiscountPercent = productSaleDiscount * 100;
    const {
      dataRc,
    } = useProductCateByData(isHomeProduct, {
      dataRc: (<>
        <Section>
          <SectionLabel>
            <div>建议补货量</div>
            <div style={{ color: "#FF3030" }}>{suggestionOrderQuantity}<span>{unit}</span></div>
          </SectionLabel>
          <img src={useImage("icon-equal-circular")}/>
          <SectionLabel>
            <div>需求量</div>
            <div>{demandQuantity}<span>{unit}</span></div>
          </SectionLabel>
          <img src={useImage("icon-minus-circular")}/>
          <SectionLabel>
            <div>库存量(含在途)</div>
            <div>{inventoryQuantity}<span>{unit}</span></div>
          </SectionLabel>
        </Section>
        <Tip>
          <div>需求量为什么是<span>{demandQuantity}</span>{unit}</div>
          <div>补货应同时满足以下{isMainSaleProduct ? 2 : 1}个方面</div>
        </Tip>
        <Box>
          <Title style={{ marginBottom: 0 }}>
            <img className="img" src={useImage("icon-circular-success")}/>
            <div className="name">满足安全库存要求</div>
            <div className="suffix">{minInventory}<span>{unit}</span></div>
          </Title>
        </Box>
        {isMainSaleProduct && <Box>
          <Title>
            <img className="img" src={useImage("icon-circular-success")}/>
            <div className="name">满足{months}个月的销量目标</div>
            <div className="suffix">{satisfyDemandQuantity}<span>{unit}</span></div>
          </Title>
          <MultiLabel>
            {`门店年销售额目标${yearSaleTargetAmount}万，产品销售占比${salesPercent}%，年销售额目标${yearSaleTargetProductAmount}万。按${productSaleDiscountPercent}%折扣计算，年销售量目标${yearSaleTargetQuantity}${unit}，按进度${startMonth}-${endMonth}月应销售${satisfyDemandQuantity}${unit}`}
          </MultiLabel>
        </Box>}
      </>),
    }, {
      dataRc: (<>
        <Section>
          <SectionLabel>
            <div>建议补货量</div>
            <div style={{ color: "#FF3030" }}>{suggestionOrderQuantity}<span>{unit}</span></div>
          </SectionLabel>
          <img src={useImage("icon-equal-circular")}/>
          <SectionLabel>
            <div>需求量</div>
            <div>{demandQuantity}<span>{unit}</span></div>
          </SectionLabel>
          <img src={useImage("icon-minus-circular")}/>
          <SectionLabel>
            <div>库存量(含待发货)</div>
            <div>{inventoryQuantity}<span>{unit}</span></div>
          </SectionLabel>
        </Section>
        <Tip>
          <div>需求量为什么是<span>{demandQuantity}</span>{unit}</div>
          <div>补货应同时满足以下{isMainSaleProduct ? 3 : 2}个方面</div>
        </Tip>
        <Box>
          <Title style={{ marginBottom: 0 }}>
            <img className="img" src={useImage("icon-circular-success")}/>
            <div className="name">满足最低陈列量、安全库存要求</div>
            <div className="suffix">{minInventory}<span>{unit}</span></div>
          </Title>
        </Box>
        {isMainSaleProduct && <Box>
          <Title>
            <img className="img" src={useImage("icon-circular-success")}/>
            <div className="name">满足至少2个月的销量目标</div>
            <div className="suffix">{satisfyDemandQuantity}<span>{unit}</span></div>
          </Title>
          <MultiLabel>
            {`门店年销售额目标${yearSaleTargetAmount}万，产品销售占比${salesPercent}%\n年销售额目标${yearSaleTargetProductAmount}万，按${productSaleDiscountPercent}%折扣计算，年销售量目标${yearSaleTargetQuantity}${unit}，平均${monthSaleTargetQuantity}${unit}/月，2个月则是${satisfyDemandQuantity}${unit}。`}
          </MultiLabel>
        </Box>}
        <Box>
          <Title>
            <img className="img" src={useImage("icon-circular-success")}/>
            <div className="name">满足会员消耗需求</div>
            <div className="suffix">{consumeDemandQuantity}<span>{unit}</span></div>
          </Title>
          <div className="label">
            预计有{predictedConsumingMembers}个会员消耗
            <div onClick={this.showMemberModal} className="more">
              如何预估？
              <img src={useImage("icon-arrow")}/>
            </div>
          </div>
          <div
            className="label"
          >
            {consumeDemandQuantity}{unit}={predictedConsumingMembers}人×{perMonthlyConsume}{unit}/月×{months}个月
          </div>
        </Box>
      </>),
    });
    return (<Wrapper>
      {dataRc}
    </Wrapper>);
  }
}

const Wrapper = styled.div`// styled
  & {
    padding: 0 12px;

    > div:last-child {
      border-bottom: none;
    }
  }
`;
const Section = styled.div`// styled
  & {
    padding: 12px 0;
    border-bottom: .5px solid #E8E8E8;
    display: flex;
    justify-content: space-between;
    align-items: center;

    > img {
      width: 16px;
      height: 16px;
    }
  }
`;
const SectionLabel = styled.div`// styled
  & {
    > div:first-child {
      color: #666;
      font-size: 14px;
      line-height: 22px;
    }

    > div:last-child {
      font-weight: 500;
      line-height: 27px;
      font-size: 18px;
      color: #437DF0;
      text-align: left;

      > span {
        margin-left: 2px;
        color: #666;
        font-size: 14px;
        vertical-align: bottom;
      }
    }
  }
`;
const Tip = styled.div`// styled
  & {
    background: #F0F4FC;
    padding: 8px;
    border-radius: 4px;

    > div:first-child {
      color: #333;
      line-height: 22px;
      font-weight: 400;
      font-size: 14px;

      > span {
        font-weight: 500;
        margin: 0 4px;
      }
    }

    > div:last-child {
      margin-top: 4px;
      color: #666;
      line-height: 18px;
      font-size: 12px;
    }
  }
`;
const Box = styled.div`// styled
  & {
    padding: 12px 0;
    border-bottom: .5px solid #E8E8E8;

    > .label {
      color: #666;
      line-height: 18px;
      font-size: 12px;
      font-weight: normal;
      margin-top: 2px;
      position: relative;
      margin-left: 24px;

      > .more {
        position: absolute;
        top: 50%;
        transform: translateY(-50%);
        right: 0;
        color: #666;
        font-size: 12px;
        display: flex;
        align-items: center;

        > img {
          width: 14px;
          height: 14px;
        }
      }
    }
  }
`;
const Title = styled.div`// styled
  & {
    height: 27px;
    display: flex;
    align-items: center;
    margin-bottom: 4px;

    > .img {
      width: 20px;
      height: 20px;
      margin-right: 4px;
    }

    > .name {
      color: #333;
      font-size: 14px;
    }

    > .suffix {
      color: #333;
      font-size: 18px;
      font-weight: 500;
      margin-left: auto;
      display: flex;
      align-items: center;

      > span {
        margin-left: 2px;
        color: #666;
        font-size: 14px;
      }
    }
  }
`;
const MultiLabel = styled.div`
  & {
    color: #666;
    line-height: 18px;
    font-size: 12px;
    font-weight: normal;
    white-space: pre-wrap;
    margin-left: 24px;
  }
`;
