import React from "react";
import styled from "styled-components";

export enum ReportSegmentEnum {
  CONSUME = "CONSUME",
  SALE = "SALE",
}

const REPORT_SEGMENT_LIST = [
  { label: "消耗", value: ReportSegmentEnum.CONSUME },
  { label: "销售", value: ReportSegmentEnum.SALE },
];

export function ReportSegment(props: any) {
  const { value, onClick } = props;

  return (<Wrapper>
    {REPORT_SEGMENT_LIST.map((item) => (<Item
      key={item.value}
      data-selected={value === item.value}
      onClick={() => onClick(item.value)}
    >
      {item.label}
    </Item>))}
  </Wrapper>);
}

const Wrapper = styled.div`// styled
  & {
    width: 108px;
    height: 32px;
    background: #F8F8F8;
    position: absolute;
    right: 0;
    top: 50%;
    transform: translateY(-50%);
    padding: 2px;
    box-sizing: border-box;
  }
`;

const Item = styled.div`// styled
  & {
    display: inline-block;
    width: 52px;
    height: 28px;
    line-height: 28px;
    text-align: center;
    border-radius: 4px;
    font-size: 14px;
    font-weight: normal;
    color: #666;

    &[data-selected="true"] {
      background: #437DF0;
      color: #FFF;
    }
  }
`;
