import { toJS } from "mobx";
import { observer } from "mobx-react";
import moment from "moment";
import * as React from "react";
import styled from "styled-components";
import { HistoryReport } from "../history-report";
import OtherStoreInventory from "./other-store-inventory";
import { ReportSegment, ReportSegmentEnum } from "./report-segment";
import SectionBox from "./section-box";
import StoreInventory from "./store-inventory";
import Suggestion from "./suggestion";

enum ProductCategoryEnum {
  HOME = "家居",
  STORE = "店护",
}

@observer
export class ProductOrderProposals extends React.Component<any, any> {
  constructor(props) {
    super(props);
    const isHomeProduct = ProductCategoryEnum.HOME === props?.data?.productInfo?.productCategory;
    const isMainSaleProduct = "是" === props?.data?.productInfo?.mainSaleFlag;
    this.state = {
      isHomeProduct,
      isMainSaleProduct,
      reportType: isHomeProduct ? ReportSegmentEnum.SALE : ReportSegmentEnum.CONSUME,
    };
  }

  public getReportData() {
    const { reportType } = this.state;
    const { saleQuantityHistory, consumeQuantityHistory } = this.props.data || {};
    if (reportType === ReportSegmentEnum.SALE) {
      return ({
        xAxis: saleQuantityHistory.map((s) => s.saleYearMonth),
        series: saleQuantityHistory.map((s) => s.saleQuantity),
        reportData: saleQuantityHistory,
      });
    } else {
      return ({
        xAxis: consumeQuantityHistory.map((s) => s.consumeYearMonth),
        series: consumeQuantityHistory.map((s) => s.consumeQuantity),
        reportData: consumeQuantityHistory,
      });
    }
  }

  public changeReportType = (e) => {
    this.setState({ reportType: e });
  }

  public getMonthsRange = (months) => {
    if (!!months) {
      const start = moment(new Date()).add(1, "months").format("M");
      const end = moment(new Date()).add(months, "months").format("M");
      return [start, end];
    }
    return ["0", "0"];
  }

  public render() {
    const {
      wrapperRef,
      data,
      productSkuId,
      unit,
    } = this.props;
    const {
      suggestionContent,
      storeInventory,
      otherStoreConsumeInventory,
      otherStoreSaleInventory,
    } = data || {};
    const {
      isHomeProduct,
      isMainSaleProduct,
      reportType,
    } = this.state;

    const { xAxis, series, reportData } = this.getReportData();
    const [startMonth, endMonth] = this.getMonthsRange(suggestionContent?.months);
    return (<Wrapper ref={wrapperRef}>
      <SectionBox
        title={"建议补货量"}
        subTitle={`补货满足：${startMonth}～${endMonth}月（${suggestionContent?.months}个月）`}
      >
        <Suggestion
          isHomeProduct={isHomeProduct}
          isMainSaleProduct={isMainSaleProduct}
          unit={unit}
          data={{ ...suggestionContent, startMonth, endMonth }}
        />
      </SectionBox>
      <SectionBox
        title={"门店库存"}
      >
        <StoreInventory
          isHomeProduct={isHomeProduct}
          data={storeInventory}
          unit={unit}
        />
      </SectionBox>
      <SectionBox
        title={"历史数据"}
        subTitle={"历史14个月"}
        titleRightChildren={!isHomeProduct && <ReportSegment
          value={reportType}
          onClick={this.changeReportType}
        />}
      >
        <ReportBox id="reportBox">
          <HistoryReport
            isHomeProduct={isHomeProduct}
            unit={unit}
            xAxis={xAxis}
            series={series}
            data={toJS(reportData)}
            reportType={reportType}
          />
        </ReportBox>
      </SectionBox>
      <SectionBox
        title={"其他门店库存"}
        subTitle={"同法人+同区域"}
      >
        <OtherStoreInventory
          productSkuId={productSkuId}
          isHomeProduct={isHomeProduct}
          unit={unit}
          consumeData={otherStoreConsumeInventory}
          saleData={otherStoreSaleInventory}
        />
      </SectionBox>
    </Wrapper>);
  }
}

const Wrapper = styled.div`// styled
  & {
    font-family: PingFang SC;
  }
`;
const ReportBox = styled.div`// styled
  & {
    padding: 32px 12px 12px;
    position: relative;
  }
`;
