import { observer } from "mobx-react";
import * as React from "react";
import styled from "styled-components";
import { useProductCateByData, useProductIndexColor } from "../../../hooks";

@observer
export default class StoreInventory extends React.Component<any, any> {
  public render() {
    const { data, isHomeProduct, unit } = this.props;
    const {
      realInventoryQuantity,
      waitDeliverQuantity,
      dailyConsumeQuantity,
      monthConsumeQuantity,
      monthSaleQuantity,
      inventoryDays,
      inventoryMonths,
      inventoryTip,
      memStorageQuantity,
      memQuantity,
      perMemQuantity,
    } = data || {};
    const tipColor = useProductIndexColor(inventoryTip);
    const {
      dataRc,
      showMem,
    } = useProductCateByData(isHomeProduct, {
      showMem: false,
      dataRc: (<Box>
        <TCol>
          <div className="title">门店实物总库存量</div>
          <div className="value">{realInventoryQuantity}<span>{unit}</span></div>
          <div className="sub-title">含待发货{waitDeliverQuantity}{unit}</div>
        </TCol>
        <TCol>
          <div className="title">近12个月月均销量</div>
          <div className="value">{monthSaleQuantity}<span>{unit}</span></div>
        </TCol>
        <TCol>
          <div className="title">库存可销售天数</div>
          <div className="value">{inventoryDays}<span>天</span></div>
          <div style={{ color: tipColor }} className="sub-title">{inventoryTip}</div>
        </TCol>
      </Box>),
    }, {
      showMem: true,
      dataRc: (<Box>
        <TCol>
          <div className="title">门店实物总库存量</div>
          <div className="value">{realInventoryQuantity}<span>{unit}</span></div>
          <div className="sub-title">含待发货{waitDeliverQuantity}{unit}</div>
        </TCol>
        <TCol>
          <div className="title">近12个月月均消耗量</div>
          <div className="value">{monthConsumeQuantity}<span>{unit}</span></div>
        </TCol>
        <TCol>
          <div className="title">库存可消耗天数</div>
          <div className="value">{inventoryDays}<span>天</span></div>
          <div style={{ color: tipColor }} className="sub-title">{inventoryTip}</div>
        </TCol>
      </Box>),
    });
    return (<Wrapper>
      {dataRc}
      {showMem && <Box>
        <TCol>
          <div className="title">会员寄存数量</div>
          <div className="value">{memStorageQuantity}<span>{unit}</span></div>
          <div className="sub-title">寄存会员数{memQuantity}人，人均{perMemQuantity}{unit}</div>
        </TCol>
      </Box>}
    </Wrapper>);
  }
}

const Wrapper = styled.div`// styled
  & {
    > div:nth-child(2) {
      border-bottom: none;
    }
  }
`;
const Box = styled.div`// styled
  & {
    padding: 12px 0;
    margin: 0 12px;
    display: flex;
    justify-content: space-between;
    border-bottom: .5px solid #E8E8E8;

    > div:last-child {
      min-width: 84px;
    }
  }
`;
const TCol = styled.div`// styled
  & {
    > .title {
      font-size: 12px;
      line-height: 18px;
      color: #666666;
    }

    > .value {
      font-size: 18px;
      font-weight: 500;
      line-height: 27px;
      color: #333333;

      > span {
        line-height: 27px;
        margin-left: 2px;
        color: #666;
        font-weight: normal;
        font-size: 14px;
        vertical-align: bottom;
      }
    }

    > .sub-title {
      color: #666;
      font-size: 12px;
      line-height: 18px;
    }
  }
`;
