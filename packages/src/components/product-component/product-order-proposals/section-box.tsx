import { observer } from "mobx-react";
import * as React from "react";
import styled from "styled-components";
import Section from "./section";

interface ISectionBoxProps {
  title: React.ReactNode;
  subTitle?: React.ReactNode;
  titleRightChildren?: React.ReactNode;
  children?: React.ReactNode;
}

@observer
export default class SectionBox extends React.Component<ISectionBoxProps, any> {

  public render() {
    const {
      title,
      subTitle,
      titleRightChildren,
      children,
    } = this.props;
    return (<Wrapper>
      <Section
        title={title}
        subTitle={subTitle}
        rightChildren={titleRightChildren}
      />
      {children}
    </Wrapper>);
  }
}

const Wrapper = styled.div`// styled
  & {
    margin-bottom: 12px;
    background-color: #FFF;
  }
`;
