import { observer } from "mobx-react";
import React from "react";
import { withRouter } from "react-router";
import styled from "styled-components";
import { $ActiveType } from "../../classes/const/$active-type";
import { SITE_PATH } from "../../modules/app";
import { $AppStore, AppStoreKey } from "../../classes/stores/app-store-mv";
import { autowired } from "@classes/ioc/ioc";
declare let window: any;
declare let require: any;
const IconHome = require("../svg/ico_home.svg");
const IconClassify = require("../svg/ico_classify.svg");
const IconCart = require("../svg/ico_cart.svg");
const IconList = require("../svg/ico_list.svg");
const IconMy = require("../svg/ico_my.svg");

@withRouter
@observer
export class Footer extends React.Component<any, any> {
  @autowired($AppStore)
  public $AppStore: $AppStore;
  private SkipAddress = {
    HOMEPAGE: `/${SITE_PATH}/home`,
    SHOPCART: `/${SITE_PATH}/shop/cart`,
    SHOPLIST: `/${SITE_PATH}/shop/list`,
    SHOPMY:  `/${SITE_PATH}/my`,
    SHOPORDERLIST: `/${SITE_PATH}/shop/order-list`,
  };
  public beforeSkipPageOprate = (type) => {
    const { HOMEPAGE, SHOPLIST, SHOPORDERLIST, SHOPCART, SHOPMY } = this.SkipAddress;
    const { leaveCurrentPage } = this.props;
    switch (type) {
      case HOMEPAGE:
        leaveCurrentPage && leaveCurrentPage();
        this.$AppStore.clearPageMv(AppStoreKey.HOMEPRODUCTS);
        break;
      case SHOPLIST:
        this.$AppStore.clearPageMv(AppStoreKey.SHOPLIST);
        leaveCurrentPage && leaveCurrentPage();
        break;
      case SHOPORDERLIST:
        leaveCurrentPage && leaveCurrentPage();
        this.$AppStore.clearPageMv(AppStoreKey.SINGLESHOPORDERLISTSTORE);
        break;
      case SHOPCART:
        leaveCurrentPage && leaveCurrentPage();
        this.$AppStore.clearPageMv(AppStoreKey.SHOPCART);
        break;
      case SHOPMY:
        leaveCurrentPage && leaveCurrentPage();
        break;
      default:
        break;
    }
  }
  public historyPushSkipPage = (address, status?) => {
    console.log("status", status);
    this.beforeSkipPageOprate(address);
    setTimeout(() => {
      this.props.history.push({
        pathname: address,
        state: { status: status || "" },
      });
    }, 50);
  }
  public windowSkip = (address, params = "") => {
    this.beforeSkipPageOprate(address);
    setTimeout(() => {
      window.location.href = address + params;
    }, 50);
  }
  public render() {
    const { activeKey, count, isAgency } = this.props;
    const { HOMEPAGE, SHOPLIST, SHOPORDERLIST, SHOPCART, SHOPMY } = this.SkipAddress;
    console.log(count);
    return (
      <Ul>
        <Li
          className={activeKey === $ActiveType.MAIN_KEY ? "active" : ""}
          onClick={() => this.historyPushSkipPage(HOMEPAGE)}
        >
          <IconHome/><span>首页</span>
        </Li>
        <Li
          className={activeKey === $ActiveType.CATEGORY_KEY ? "active" : ""}
          onClick={() => this.historyPushSkipPage(SHOPLIST)}
        >
          <IconClassify/><span>分类</span>
        </Li>
        <Li
          className={activeKey === $ActiveType.CAR_KEY ? "active" : ""}
          onClick={() => this.windowSkip(SHOPCART)}
        >
          <IconCart/>
          <span>购物车</span>
          {count > 0 && <Notice>{count}</Notice>}
        </Li>
        {
          isAgency ?
            <Li
              className={activeKey === $ActiveType.ORDER_KEY ? "active" : ""}
              onClick={() => this.historyPushSkipPage(SHOPORDERLIST, "ALL")}
            >
              <IconList/><span>订单</span>
            </Li>
            :
            <Li
              className={activeKey === $ActiveType.ORDER_KEY ? "active" : ""}
              onClick={() => this.historyPushSkipPage(SHOPORDERLIST)}
            >
              <IconList/><span>订单</span>
            </Li>
        }
        {
          isAgency ?
            <Li
              className={activeKey === $ActiveType.MY_KEY ? "active" : ""}
              onClick={() => this.windowSkip(SHOPMY, "?agencyParams=agency")}
            >
              <IconMy/><span>我的</span>
            </Li>
            :
            <Li
              className={activeKey === $ActiveType.MY_KEY ? "active" : ""}
              onClick={() => this.windowSkip(SHOPMY)}
            >
              <IconMy/><span>我的</span>
            </Li>
        }
      </Ul>
    );
  }
}

const Ul = styled.ul`// styled
  & {
    position: fixed;
    width: 100%;
    bottom: 0;
    height: 49px;
    list-style: none;
    display: flex;
    justify-content: space-around;
    align-items: center;
    background: #FFFFFF;
    border-top: 1px solid #D8D8D8;
    z-index: 99;
    margin-bottom: 0;
  }
`;
const Li = styled.li`// styled
  & {
    position: relative;
    display: flex;
    flex-direction: column;
    align-items: center;
    font-size: 12px;
    color: #8A8A8A;
    > span {
      margin-top: 4px;
    }

    &.active {
      color: #307DCD;
      path {
        fill: #307DCD;
      }
      ​
    }
  }
`;
const Notice = styled.span`// styled
  & {
    position: absolute;
    height: 14px;
    line-height: 16px;
    padding: 0 2px;
    top: -7px;
    right: -10px;
    min-width: 20px;
    background: #FF3030;
    border-radius: 100px;
    color: #fff;
    text-align: center;
  }
`;
