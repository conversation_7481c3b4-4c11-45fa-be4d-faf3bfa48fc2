/*
* confirmAmount： 字符串（必填）
* paymentList：数组（必填）每个对象必有三个参数isShow：是否展示，name： 付款方式字段名，paymentDefaultAmount： 付款金额。
* */

import { observer } from "mobx-react";
import * as React from "react";
import "./paymentModleDetailShow.less";

@observer
export default class PaymentModleDetailShow extends React.Component<any, any> {
  constructor(props) {
    super(props);
  }

  public componentDidMount() {
    console.log(this.props);
  }

  public render() {
    const {
      confirmAmount,
      unPayableAmount,
      paymentList,
    } = this.props
    console.log(this.props);
    const residualAmount = unPayableAmount - confirmAmount > 0 ? unPayableAmount - confirmAmount : 0;
    const paymentModelDetailListHtml = paymentList.map((item, index) => {
      const DefaultAmount = item.paymentDefaultAmount ? item.paymentDefaultAmount === "0" ? 0 : Number(item.paymentDefaultAmount).toFixed(2) : 0;
      if (item.isShow) {
        return (<div>
          {item.name}
          <div className="payment-model-amount">¥ {DefaultAmount}</div>
        </div>);
      }
    })
    return (
      <div className="payment-detail-show-warp">
        <div className="payment-model-detail">
          {
            paymentModelDetailListHtml
          }
        </div>
        <div className="payment-total">
          <div>
            合计支付：<span className="payment-total-amount">¥ {confirmAmount ? confirmAmount === "0" ? 0 : Number(confirmAmount).toFixed(2) : 0}</span>
          </div>
          <div>
            剩余支付：<span className="payment-total-amount-red">¥ {Number(residualAmount).toFixed(2)}</span>
          </div>
        </div>
      </div>
    );
  }
}
