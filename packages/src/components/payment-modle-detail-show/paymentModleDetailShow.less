.payment-detail-show-warp {
  width: 100%;
  height: 107px;
  padding: 12px 8px;
  font-size: 11px;
  font-weight: 400;
  color: rgba(102, 102, 102, 1);
  line-height: 11px;
  background: #fff;
  .payment-model-detail {
    width: 100%;
    height: 56px;
    padding: 12px 0;
    display: flex;
    border-radius: 4px;
    // border: 1px solid rgba(216, 216, 216, 1);
    justify-content: space-around;
    position: relative;
  }
  .payment-model-detail:after {
    content: "  ";
    position: absolute;
    left: 0;
    top: 0;
    z-index: 1;
    width: 200%;
    height: 200%;
    border: 1px solid #D8D8D8;
    border-radius: 4px;
    padding: 3px 6px;
    -webkit-transform-origin: 0 0;
    transform-origin: 0 0;
    -webkit-transform: scale(.5, .5);
    transform: scale(.5, .5);
  }
  .payment-total {
    float: right;
    overflow: hidden;
    margin-top: 12px;
    width: 100%;
    > div {
      font-size: 12px;
      display: inline-block;
      width: 50%;
      >span{
        font-size: 14px;
        line-height: 14px;
      }
    }
    > div:last-child {
      text-align: right;
      > .payment-total-amount-red {
        color: #FF3030;
      }
    }
  }
}

.payment-model-amount, .payment-total-amount {
  margin-top: 8px;
  color: rgba(51, 51, 51, 1);
}
