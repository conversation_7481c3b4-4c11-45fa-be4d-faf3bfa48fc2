import * as React from "react";
import { observer } from "mobx-react";
import { $CartType } from "../../classes/const/$cart-type";
import { $OrderType } from "../../classes/const/$order-type";
import DateUtils from "../../classes/utils/DateUtils";
import styled from "styled-components";
import { Checkbox, Modal } from "antd-mobile";
import { $PayType } from "../../classes/const/$pay-type";
import { autowired } from "@classes/ioc/ioc";
import { $OrderService } from "../../classes/service/$order-service";
import disabledOprate from "../../components/noWX-disabled-operate";
import { $AppStore } from "@classes/stores/app-store-mv";
import { CustomModal } from "../custom-modal/custom-modal";

const CheckboxItem = Checkbox.CheckboxItem;
const alert = Modal.alert;
declare let $: any;

@observer
export class SingleOrderInfo extends React.Component<any, any> {

  @autowired($AppStore)
  public $AppStore: $AppStore;

  @autowired($OrderService)
  public $orderService: $OrderService;

  public constructor(props) {
    super(props);
    this.state = {
      scanningLogin: localStorage.getItem("scanningLogin") === "Y",
      showFreight: false,
      showPayInfo: false,
      isCheckOrder: true,
      payInfo: null,
      sumAmount: 0,
      freightInfo: null,
    };
  }

  public getPriceShowHtml = (amountList, isBySkuType, totalAmount, orderId, orderSchemeName, length) => {
    return isBySkuType ? amountList.map((item) => {
        const name = item.name.substr(0, item.name.length - 2);
        return <p onClick={() => this.props.goToOrderDetail(orderId, orderSchemeName, length)}>
          <span>{name + "零售价总额"}：</span><span>{`￥${item.amountBySkuType}`}</span></p>
      })
      :
      <p onClick={() => this.props.goToOrderDetail(orderId, orderSchemeName, length)}>
        <span>零售价总额：</span><span>{"￥" + totalAmount}</span></p>
  }

  public paymentFinish = (orderId, isCreateFreightFeeDocument) => {
    const { isCheckOrder } = this.state;
    let payRange = null;
    if (isCreateFreightFeeDocument === $CartType.ISCREATEFREIGHTFEEDOCUMENT) {
      payRange = isCheckOrder ? "All" : "Freight";
    } else {
      payRange = null;
    }
    this.$orderService.checkBatchPaymentMode({ salesOrderIds: [orderId] }).then((data) => {
      const { errorCode, errorMessage } = data;
      if (errorCode === "0") {
        this.props.orderButton("submitFinance", orderId, null, null, payRange);
      } else if (errorCode === $OrderType.INVALIDPAYMENT) {
        alert(`${errorMessage}`, "", [
          {
            text: "确认", onPress: () => {
              this.props.componentAfreshDidMount();
            },
          },
        ]);
      }
    });
  }

  public auditPass = (orderId, isEditing) => {
    if (isEditing === $OrderType.ISEDITING) { // 订单在编辑中
      alert("订单正在编辑中，需编辑后重新提交订单，是否继续？", "", [
        { text: "取消", onPress: () => console.log("cancel") },
        { text: "继续", onPress: () => this.props.orderButton("edit", orderId) },
      ]);
    } else {
      this.props.orderButton("auditPass", orderId);
    }
  }

  public showFreightModal = (orderId) => {
    $("html").css("overflow", "hidden");
    $("body").css("overflow", "hidden");
    if ($(".scroll-ability-wrap")) {
      $(".scroll-ability-wrap").css("overflow", "hidden");
      $(".scroll-ability-wrap").scrollTop(0);
    }
    this.setState({
      showFreight: true,
    }, () => {
      this.$orderService.feedocumentDetail({ salesOrderId: orderId, docType: "Freight" }).then((data) => {
        console.log("运费信息", data);
        this.setState({
          freightInfo: data,
        });
      });
    });
  }

  public close = () => {
    this.setState({
      showFreight: false,
    });
    $("html").css("overflow", "scroll");
    $("body").css("overflow", "scroll");
    if ($(".scroll-ability-wrap")) {
      $(".scroll-ability-wrap").css("overflow", "scroll");
    }
  }

  public closePay = () => {
    this.setState({
      showPayInfo: false,
    });
    $("html").css("overflow", "scroll");
    $("body").css("overflow", "scroll");
    if ($(".scroll-ability-wrap")) {
      $(".scroll-ability-wrap").css("overflow", "scroll");
    }
  }

  public showPayInfo = (orderId) => {
    this.$orderService.salesorderPayinfo({ salesOrderIdList: [orderId] }).then((data) => {
      console.log("支付信息", data);
      this.setState({
        payInfo: data,
        sumAmount: data.totalPayInfo && data.totalPayInfo.totalPayAmount,
      });
      if (data.totalPayInfo && data.totalPayInfo.freightTotalAmount === 0) {
        const payRange = "All";
        this.props.orderButton("submitFinance", orderId, null, null, payRange);
      } else {
        this.setState({
          showPayInfo: true,
          isCheckOrder: true,
        }, () => {
          if ($(".scroll-ability-wrap")) {
            $("html").css("overflow", "hidden");
            $("body").css("overflow", "hidden");
            $(".scroll-ability-wrap").css("overflow", "hidden");
            $(".scroll-ability-wrap").scrollTop(0);
          }
          const { order } = this.props;
          const { isLimitPaymentMode } = order.paymentModeInfo;
          if (isLimitPaymentMode) {
            this.changeCheck({ target: { checked: false } });
          }
        });
      }
    });
  }

  public renderContent = () => {
    const { freightInfo } = this.state;
    return (
      <FreightContent>
        <p>
          <span>
            <i className={"scmIconfont scm-bianma"}/>
            编码
          </span>
          <span className={"value name"}>{freightInfo && freightInfo.code}</span>
        </p>
        <p>
          <span>
            <i className={"scmIconfont scm-mendian"}/>
            门店
          </span>
          <span className={"value name"}>{freightInfo && freightInfo.orderOrgName}</span>
        </p>
        <p>
          <span>
            <i className={"scmIconfont scm-kuaidi"}/>
            运费
          </span>
          <span className={"value"}>¥{freightInfo && freightInfo.amount}</span>
        </p>
        <p>
          <span>
            <i className={"scmIconfont scm-dingdanjilu"}/>
            关联订单号
          </span>
          <p style={{ display: "inline-block" }}>
            {
              freightInfo && freightInfo.itemList && freightInfo.itemList.length > 0 && freightInfo.itemList.map((item) => {
                return (
                  <p className={"value smallName"} key={item.id}>{item.relatedDocNo}</p>
                );
              })
            }
          </p>
        </p>
      </FreightContent>
    );
  }

  public changeCheck = (e) => {
    const { payInfo } = this.state;
    console.log(e.target.checked, "isCheckOrder");
    this.setState({
      isCheckOrder: e.target.checked,
    }, () => {
      if (e.target.checked) {
        this.setState({
          sumAmount: payInfo.totalPayInfo && (payInfo.totalPayInfo.salesOrderTotalAmount + payInfo.totalPayInfo.freightTotalAmount),
        }, () => {
          console.log(this.state.sumAmount, "true");
        });
      } else {
        this.setState({
          sumAmount: payInfo.totalPayInfo && payInfo.totalPayInfo.freightTotalAmount,
        }, () => {
          console.log(this.state.sumAmount, "false");
        });
      }
    });
  }

  public renderPayContent = (isLimitPaymentMode) => {
    const { isCheckOrder, payInfo, sumAmount } = this.state;
    return (
      <PayContent>
        {
          isLimitPaymentMode && <div className={"title-notice"}>由于订货方案限制了货款的支付方式，请先单独支付运费！</div>
        }
        <div>
          <p>
            <CheckboxItem onChange={(val) => this.changeCheck(val)} checked={isCheckOrder} disabled={isLimitPaymentMode} >
              <i className={"scmIconfont scm-dingdanjilu"}/>
              <span className={"title"}>订单</span>
              <span>
              <span className={"toPay"}>待付金额：</span>
              <span
                className={"toPay amount"}>￥{payInfo && payInfo.totalPayInfo && payInfo.totalPayInfo.salesOrderTotalAmount}</span>
            </span>
            </CheckboxItem>
          </p>
          <p>
            <CheckboxItem disabled={true} checked={true}>
              <i className={"scmIconfont scm-kuaidi"}/>
              <span className={"title"}>运费</span>
              <span>
              <span className={"toPay"}>待付金额：</span>
              <span
                className={"toPay amount"}>￥{payInfo && payInfo.totalPayInfo && payInfo.totalPayInfo.freightTotalAmount}</span>
            </span>
            </CheckboxItem>
          </p>
        </div>
        <div className={"total"}>
          <CheckboxItem checked={isCheckOrder} onChange={(val) => this.changeCheck(val)} disabled={true} >
            <span className={"sum"}>合计</span>
            <span>
              <span className={"info"}>总计:</span>
              <span className={"Symbol"}>￥</span>
              <span className={"Symbol red"}>{Number(sumAmount).toFixed(2)}</span>
            </span>
            <span>
              <span className={"info"}>商品总数:</span>
              <span
                className={"black"}> {payInfo && payInfo.totalPayInfo && payInfo.totalPayInfo.totalProductSkuCount} </span>
              <span className={"info"}>件</span>
            </span>
          </CheckboxItem>
        </div>
      </PayContent>
    );
  }

  public render() {
    const { scanningLogin, showFreight, showPayInfo } = this.state;
    const { order, isBySkuType, showCheckboxItem } = this.props;
    const { totalAmount, amountList } = order.retailAmount;
    const { isLimitPaymentMode, paymentModeList } = order.paymentModeInfo;
    return (
      <div>
        <Order>
          <div>
            <div>
              {
                showCheckboxItem && <CheckboxItem
                  onChange={(e) => this.props.chooseOne(e.target.checked, order.orderId, this.props.index)}
                  checked={order.checked}
                  disabled={order.disabled}
                />
              }
              {
                order.disabled ? <DisabledSpan>
                  <span>不满足合并</span>
                  <span>无相同支付方式</span>
                </DisabledSpan> : <span
                  onClick={() => this.props.goToOrderDetail(order.orderId, order.orderSchemeName, paymentModeList.length)}>订单号：{order.docNo}</span>
              }
              {order.originType === $OrderType.ORIGINTYPE && order.disabled !== true &&
              <span className="originType"> 铺货单</span>}
              <span
                onClick={() => this.props.goToOrderDetail(order.orderId, order.orderSchemeName, paymentModeList.length)}>{order.docStatus}</span>
            </div>
            <div>
              <p
                onClick={() => this.props.goToOrderDetail(order.orderId, order.orderSchemeName, paymentModeList.length)}>
                <span>下单门店：</span><span>{order.orderOrgName}</span></p>
              <p
                onClick={() => this.props.goToOrderDetail(order.orderId, order.orderSchemeName, paymentModeList.length)}>
                <span>商品数量：</span><span>{order.totalQuantity}件</span></p>
              <p
                onClick={() => this.props.goToOrderDetail(order.orderId, order.orderSchemeName, paymentModeList.length)}>
                <span>订货方案：</span><span>{order.orderSchemeName}</span></p>
              <p
                onClick={() => this.props.goToOrderDetail(order.orderId, order.orderSchemeName, paymentModeList.length)}>
                <span>下单时间：</span><span>{DateUtils.toStringFormat(order.docDate, "yyyy-MM-dd HH:mm:ss")}</span>
              </p>
              {
                order.retailPriceViewPermission === $CartType.RETAILPRICEVIEWPERMISSION &&
                this.getPriceShowHtml(amountList, isBySkuType, totalAmount, order.orderId, order.orderSchemeName, paymentModeList.length)
              }
              {
                order.orderPriceViewPermission === $CartType.ORDERPRICEVIEWPERMISSION &&
                <p
                  onClick={() => this.props.goToOrderDetail(order.orderId, order.orderSchemeName, paymentModeList.length)}>
                  <span>货款：</span><span>{"￥" + Number(order.totalAmount - order.freightAmount).toFixed(2)}</span></p>
              }
              {
                ((order.isCreateFreightFeeDocument === $CartType.ISCREATEFREIGHTFEEDOCUMENT) && (order.freightFeeDocumentPaymentStatusCode !== "" && order.freightFeeDocumentPaymentStatusCode !== null)) &&
                <div className={"freightFeeDocument"}>
                  <span onClick={() => this.showFreightModal(order.orderId)}>查看关联运费单</span>
                  <span>{order.freightFeeDocumentPaymentStatusName}</span>
                </div>
              }
              {
                (order.freightViewPermission === $CartType.ORDERFREIGHTVIEWPERMISSION && order.isUseFreight === $PayType.ISUSEFREIGHT && order.freightAmount > 0) && (order.isCreateFreightFeeDocument !== $CartType.ISCREATEFREIGHTFEEDOCUMENT || order.freightFeeDocumentPaymentStatusCode === "") &&
                <p
                  onClick={() => this.props.goToOrderDetail(order.orderId, order.orderSchemeName, paymentModeList.length)}>
                  <span>运费：</span><span>{"￥" + order.freightAmount}</span></p>
              }
            </div>
            {
              JSON.stringify(order.paymentInfo) !== "{}" && order.orderPriceViewPermission === $CartType.ORDERPRICEVIEWPERMISSION &&
              <div
                onClick={() => this.props.goToOrderDetail(order.orderId, order.orderSchemeName, paymentModeList.length)}>
                <p>
                  <span className="name">分期规则：</span>
                  {/*<span className="payment">分期</span>*/}
                </p>
                {
                  order.paymentInfo &&
                  order.paymentInfo.paymentList &&
                  order.paymentInfo.paymentList.map((payment, index) => {
                    return (
                      <p key={index}>
                      <span className="name">
                        {payment.name}：
                        <span className="amount">￥{payment.amount}</span>
                      </span>
                        <span className="name" style={{ float: "right" }}>
                        支付开始时间：{payment.startTime}
                      </span>
                      </p>
                    );
                  })
                }
              </div>}
            {
              order.orderPriceViewPermission === $CartType.ORDERPRICEVIEWPERMISSION &&
              <div
                onClick={() => this.props.goToOrderDetail(order.orderId, order.orderSchemeName, paymentModeList.length)}>
                <p><span className="name">订单合计：</span><span
                  className="amount">￥{order.totalAmount}</span><span
                  className="status">{order.paymentStatus}</span></p>
                <p>
                              <span className="name">待确认金额：<span
                                className="amount">￥{order.unAuditedAmount}</span></span>
                  <span className="name">  已确认金额：<span
                    className="amount">￥{order.auditedAmount}</span></span>
                  <span className="name">  未支付金额：<span
                    className="amount">￥{order.payableAmount}</span></span>
                </p>
                {
                  isLimitPaymentMode &&
                  <p className="payment-type">
                    <span>特定支付方式</span>
                    {
                      paymentModeList.map((item) => {
                        return <span key={item.code}>{item.name}</span>;
                        // switch (item.code) {
                        //   case $PaymentModeType.REBATEDEDUCTION:
                        //     return <span>返利抵扣</span>
                        //   // break;
                        //   case $PaymentModeType.STOREDVALUE:
                        //     return <span>余额支付</span>
                        //   // break;
                        //   case $PaymentModeType.CREDIT:
                        //     return <span>信用支付</span>
                        //   // break;
                        //   case $PaymentModeType.BANKTRANSFER:
                        //     return <span>线下转账支付</span>
                        //   // break;
                        //   default:
                        //     break;
                        // }
                      })
                    }
                  </p>
                }
              </div>
            }
          </div>
          {
            (order.isShowPaymentBtn || order.isShowAuditBtn || order.isShowEditBtn || order.isShowCancelBtn || order.isShowRejectBtn || order.isShowSendFinancialBtn || order.isShowDeliveryBtn || order.isShowCopySalesOrderBtn) &&
            <div>
              {
                order.isShowSendFinancialBtn &&
                <span onClick={() => {
                  if (scanningLogin) {
                    disabledOprate();
                  } else {
                    if ((order.isCreateFreightFeeDocument !== $CartType.ISCREATEFREIGHTFEEDOCUMENT) || (order.freightFeeDocumentPaymentStatusCode === $CartType.PAYEND)) {
                      this.paymentFinish(order.orderId, order.isCreateFreightFeeDocument);
                    } else {
                      this.showPayInfo(order.orderId);
                    }
                  }
                }}>付款</span>
              }
              {
                order.isShowRejectBtn &&
                <span
                  onClick={() =>
                    alert(`确认拒绝这笔订单？`, "", [
                      { text: "取消", onPress: () => console.log("cancel") },
                      { text: "继续", onPress: () => this.props.orderButton("reject", order.orderId) },
                    ])
                  }
                >审核拒绝</span>
              }
              {
                order.isShowAuditBtn &&
                <span onClick={() => this.auditPass(order.orderId, order.isEditing)}>审核通过</span>
              }
              {
                order.isShowCopySalesOrderBtn &&
                <span
                  onClick={() => {
                    this.$AppStore.queryShopOverdue(order.orderOrgId, () => alert("", "点击去购物车，商品将添加至购物中，部分商品会因已售罄导致添加失败请注意核对", [
                      { text: "关闭", onPress: () => console.log("cancel") },
                      {
                        text: "去购物车",
                        onPress: () => this.props.orderButton("capyOrder", order.orderId, order.orderOrgId, order.orderSchemeId)
                      },
                    ]));
                  }}>复制订单</span>
              }
              {
                order.isShowEditBtn &&
                <span
                  onClick={() => {
                    this.$AppStore.queryShopOverdue(order.orderOrgId, () => alert(`即将切换至${order.orderOrgName}门店${order.orderSchemeName}方案，是否继续？`, "", [
                      { text: "取消", onPress: () => console.log("cancel") },
                      { text: "继续", onPress: () => this.props.orderButton("edit", order.orderId) },
                    ]));
                  }}>编辑订单</span>
              }
              {
                order.isShowCancelBtn &&
                <span onClick={() => this.props.orderButton("cancel", order.orderId)}>取消订单</span>
              }
            </div>
          }
        </Order>
        <CustomModal
          header={"运费单"}
          confirm={this.close}
          content={this.renderContent()}
          confirmName={"确定"}
          visible={showFreight}
          close={this.close}
          isCancle={false}
        />
        <CustomModal
          header={"支付信息"}
          confirm={() => this.paymentFinish(order.orderId, order.isCreateFreightFeeDocument)}
          content={this.renderPayContent(isLimitPaymentMode)}
          confirmName={"付款"}
          visible={showPayInfo}
          close={this.closePay}
          isCancle={true}
        />
      </div>
    );
  }
}

const Order = styled.div`// styled
  & {
    width: 100%;
    /*height: 360px;*/
    background-color: #fff;
    box-sizing: border-box;
    // margin-bottom: 10px;
    .am-list-item {
      display: inline-block;
      padding-left: 0;
      min-height: 18px;
      position: relative;
      top: -3px;
    }
    .am-list-item .am-list-thumb:first-child {
      margin-right: 8px;
    }
    > div:nth-of-type(1) {
      > div:nth-of-type(1) {
        position: relative;
        // border-bottom: 1px solid #D8D8D8;
        padding: 10px 15px;
        > span:nth-of-type(1) {
          font-size: 14px;
          color: #333;
          font-family: "MicrosoftYaHei";
        }
        > .originType {
          display: inline-block;
          background: #ECF6FF;
          color: #307DCD;
          margin-left: 5px;
          text-align: center;
          border-radius: 2px;
          font-size: 10px;
          font-family: "MicrosoftYaHei";
        }
        > span:last-child {
          position: absolute;
          top: 10px;
          right: 15px;
          color: #FF3030;
          font-size: 12px;
          font-family: "MicrosoftYaHei";
        }
      }
      > div:nth-of-type(2) {
        position: relative;
        // border-bottom: 1px solid #D8D8D8;
        padding: 10px 15px;
        > p {
          margin-bottom: 10px;
        }
        > p {
          > span:nth-of-type(1) {
            color: #666666;
            font-size: 12px;
            font-family: "MicrosoftYaHei";
          }
          > span:nth-of-type(2) {
            color: #333333;
            font-size: 12px;
            font-family: "MicrosoftYaHei";
          }
          > p {
            display: inline-block;
            margin-bottom: 0;
            > span:nth-of-type(1) {
              color: #666666;
              font-size: 12px;
              font-family: "MicrosoftYaHei";
            }
            > span:nth-of-type(2) {
              color: #333333;
              font-size: 12px;
              font-family: "MicrosoftYaHei";
            }
          }
          > p:nth-of-type(2) {
            margin-left: 16px;
          }
        }
        > p:last-child {
          margin: 0;
        }
      }
      > div:nth-of-type(3) {
        position: relative;
        // border-bottom: 1px solid #D8D8D8;
        padding: 10px 15px;
        > p {
          margin-bottom: 10px;
        }
        .name {
          color: #666666;
          font-size: 12px;
        }
        .amount {
          color: #FF3030;
          font-size: 12px;
        }
        .status {
          position: absolute;
          top: 10px;
          right: 15px;
          color: #FF8627;
          font-size: 12px;
          font-family: "MicrosoftYaHei";
        }
        .payment {
          color: #FF7362;
          background: #FFEBE8;
          padding: 2px 6px;
          border: 1px solid transparent;
          border-radius: 2px;
          font-size: 10px;
        }
        > p:last-child {
          margin: 0;
        }
      }
      > div:nth-of-type(4) {
        position: relative;
        // border-bottom: 1px solid #D8D8D8;
        padding: 10px 15px;
        > p {
          margin-bottom: 10px;
        }
        .name {
          color: #666666;
          font-size: 12px;
        }
        .amount {
          color: #FF3030;
          font-size: 12px;
        }
        .status {
          position: absolute;
          top: 10px;
          right: 15px;
          color: #FF3030;
          font-size: 12px;
          font-family: "MicrosoftYaHei";
        }
        > p:last-child {
          margin: 0;
        }
      }
      > div {
        position: relative;
        :after {
          content: '';
          position: absolute;
          background-color: #D8D8D8 !important;
          display: block;
          z-index: 1;
          top: auto;
          right: auto;
          bottom: 0;
          left: 0;
          width: 100%;
          height: 1.2px;
          transform-origin: 50% 100%;
          transform: scaleY(0.5);
        }
      }
      .payment-type {
        width: 100%;
        height: 20px;
        span {
          display: inline-block;
          height: 20px;
          margin-right: 8px;
          color: #333333;
          font-size: 12px;
          line-height: 20px;
        }
        span:first-of-type {
          border: 1px solid #52C41A;
          color: #52C41A;
          line-height: 18px;
          font-size: 11px;
          padding: 0 6px;
          margin-right: 10px;
        }
        span:last-of-type {
          margin-right: 0;
        }
      }
    }
    > div:nth-of-type(2) {
      position: relative;
      padding: 10px 0px;
      height: 50px;
      > span {
        float: right;
        color: #307DCD;
        // border: 0.5px solid #307DCD;
        border-radius: 3px;
        width: 20%;
        height: 30px;
        line-height: 20px;
        padding: 5px;
        margin-right: 15px;
        font-size: 12px;
        font-family: "MicrosoftYaHei";
        text-align: center;
        position: relative;
      }
      > span:after {
        content: "  ";
        position: absolute;
        left: 0;
        top: 0;
        z-index: 1;
        width: 200%;
        height: 200%;
        border: 1px solid #307DCD;
        border-radius: 3px;
        -webkit-transform-origin: 0 0;
        transform-origin: 0 0;
        -webkit-transform: scale(.5, .5);
        transform: scale(.5, .5);
      }
      > span:first-child {
        margin-right: 10px;
      }
      .nowPay {
        color: #FF3030;
        font-size: 12px;
        font-family: "MicrosoftYaHei";
        // border: 1px solid #FF3030;
        position: relative;
      }
      .nowPay:after {
        content: "  ";
        position: absolute;
        left: 0;
        top: 0;
        z-index: 1;
        width: 200%;
        height: 200%;
        border: 1px solid #FF3030;
        border-radius: 3px;
        -webkit-transform-origin: 0 0;
        transform-origin: 0 0;
        -webkit-transform: scale(.5, .5);
        transform: scale(.5, .5);
      }
    }
    .freightFeeDocument {
      > span:first-child {
        font-size: 12px;
        font-family: SourceHanSansCN-Regular, SourceHanSansCN;
        font-weight: 400;
        color: rgba(48, 125, 205, 1);
      }
      > span:last-child {
        font-size: 12px;
        font-family: SourceHanSansCN-Regular, SourceHanSansCN;
        font-weight: 400;
        color: rgba(255, 48, 48, 1);
        float: right;
      }
    }
  }
`;

const DisabledSpan = styled.span`// styled
  & {
    > span:first-child {
      font-size: 13px;
      font-family: "SourceHanSansCN-Normal";
      font-weight: 400;
      color: rgba(51, 51, 51, 1);
      margin-right: 12px;
    }
    > span:last-child {
      font-size: 12px;
      font-family: "SourceHanSansCN-Normal";
      font-weight: 400;
      color: rgba(102, 102, 102, 1);
    }
  }
`;

const FreightContent = styled.div`// styled
  & {
    p {
      margin-bottom: 27px;
      line-height: 20px;
      > span:first-child {
        display: inline-block;
        width: 95px;
        margin-right: 24px;
        font-size: 14px;
        font-family: SourceHanSansCN-Regular, SourceHanSansCN;
        font-weight: 400;
        color: rgba(51, 51, 51, 1);
        vertical-align: top;
        .scmIconfont {
          margin-right: 8px;
        }
        .scm-bianma {
          color: #9254DE;
        }
        .scm-mendian {
          color: #307DCD;
        }
        .scm-kuaidi {
          color: #60B547;
        }
        .scm-dingdanjilu {
          color: #FF8627;
        }
      }
      .value {
        font-size: 13px;
        font-family: SourceHanSansCN-Regular, SourceHanSansCN;
        font-weight: 400;
        color: rgba(102, 102, 102, 1);
        margin-bottom: 16px;
      }
      .value:last-child {
        margin-bottom: 0;
      }
      .name {
        display: inline-block;
        width: calc(100% - 95px - 24px);
        text-overflow: ellipsis;
        overflow: hidden;
        white-space: nowrap;
      }
    }
    p:last-child {
      margin-bottom: 0;
    }
  }
`;

const PayContent = styled.div`// styled
  & {
    width: 100%;
    height: auto;
    .scm-dingdanjilu {
      color: #FF8627;
    }
    .scm-kuaidi {
      color: #60B547;
    }
    .scmIconfont {
      margin-right: 8px;
    }
    .title {
      font-size: 14px;
      font-family: SourceHanSansCN-Regular, SourceHanSansCN;
      font-weight: 400;
      color: rgba(51, 51, 51, 1);
      margin-right: 24px;
    }
    .toPay {
      font-size: 13px;
      font-family: SourceHanSansCN-Regular, SourceHanSansCN;
      font-weight: 400;
      color: #999999;
    }
    .amount {
      color: #333333;
    }
    .am-list-item .am-list-thumb:first-child {
      margin-right: 8px;
    }
    .am-list-item {
      padding-left: 0;
    }
    > div {
      > p:first-child {
        margin-bottom: 15px;
      }
      > p:last-child {
        margin-bottom: 15px;
      }
    }
    .total {
      border-top: 1px solid #EFEFEF;
      padding-top: 20px;
    }
    .sum {
      font-size: 14px;
      font-family: SourceHanSansCN-Regular, SourceHanSansCN;
      font-weight: 400;
      color: rgba(51, 51, 51, 1);
      margin-right: 8px;
    }
    .info {
      font-size: 12px;
      font-family: SourceHanSansCN-Regular, SourceHanSansCN;
      font-weight: 400;
      color: rgba(153, 153, 153, 1);
    }
    .Symbol {
      font-size: 12px;
      font-family: SourceHanSansCN-Regular, SourceHanSansCN;
      font-weight: 400;
      color: #FF3030;
    }
    .red {
      font-size: 16px;
      margin-right: 8px;
    }
    .black {
      color: #333333;
      font-size: 12px;
    }
    .title-notice {
      font-size: 16px;
      color: red;
      font-weight: bold;
    }
    .am-checkbox.am-checkbox-disabled {
      &.am-checkbox-checked {
        .am-checkbox-inner {
          border-color: #108ee9;
          background: #108ee9;

          &:after{
            border-color: #fff;
          }
        }
      }
    }
    .am-list-item .am-list-line {
      padding-right: 0px;
    }
  }
`;
