import { observer } from "mobx-react";
import React from "react";
import { withRouter } from "react-router";
import styled from "styled-components";

declare let window: any;
declare let require: any;

@withRouter
@observer
export class AmountShowComponent extends React.Component<any, any> {

  public render() {
    const { label, value, isShowRepaymentEnd } = this.props;
    return (
      <AmountShowWrap>
        {
          (isShowRepaymentEnd || (Number(value) > 0)) ?
            <div className="remain-amount">
              <i className="scmIconfont scm-icon-account" />
              <div>{label}</div>
              <div>{value >= 0 ? "￥" : "-￥"}<span>{value ? Math.abs(Number(value)).toFixed(2) : ""}</span></div>
            </div>
            :
            <div className="no-amount">
              <div>已还清</div>
              <div>好棒！点个赞</div>
            </div>
        }
      </AmountShowWrap>
    );
  }
}

const AmountShowWrap = styled.h1`// styled
  & {
    height: 68px;
    margin: 12px 12px 0px;
    background-image: url("https://order.fwh1988.cn:14501/static-img/scm/img_bg_returned_status.png");
    -webkit-background-size: 100% 68px;background-size: 100% 68px;
    background-repeat: no-repeat;
    background-position: center center;
    border-radius: 8px;
    box-shadow:0px 0px 3px 0px rgba(0,0,0,0.05);
    .remain-amount{
      width: 100%;
      height: 100%;
      padding: 12px 0 0 68px;
      position: relative;
      .scm-icon-account{
        position: absolute;
        top: 12px;
        left: 12px;
        display: inline-block;
        text-align: center;
        line-height: 44px;
        color: #307DCD;
        font-size: 44px;
      }
      div:first-of-type{
        font-size: 13px;
        height: 13px;
        line-height: 13px;
        margin-bottom: 7px;
      }
      div:nth-of-type(2){
        color: rgba(48, 125, 205, 1);
        font-size: 14px;
        line-height: 14px;
        height: 24px;
        >span{
          font-size: 24px;
          line-height: 24px;
        }
      }
    }
    .no-amount{
      padding: 17px 0;
      text-align: center;
      color: rgba(47, 47, 47, 1);
      font-size: 13px;
      line-height: 13px;
      >div:last-of-type{
        color: rgba(89, 89, 89, 1);
        font-size: 12px;
        line-height: 12px;
        margin-top: 8px;
      }
    }
  }
`;
