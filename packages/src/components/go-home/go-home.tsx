import * as React from "react";
import { observer } from "mobx-react";
import { SITE_PATH } from "../../modules/app";
import styled from "styled-components";
import { $AppStore, AppStoreKey } from "../../classes/stores/app-store-mv";
import { autowired } from "@classes/ioc/ioc";
@observer
export class GoHome extends React.Component<any, any> {
  @autowired($AppStore)
  public $AppStore: $AppStore;
  public goHome = () => {
    const { goHome } = this.props;
    goHome && goHome();
    this.$AppStore.clearPageMv(AppStoreKey.ORDERPARTYSELECTION);
    setTimeout(() => {
      window.location.href = `/${SITE_PATH}/select`;
    }, 50);
  }
  public render() {
    return (
      <GoHomeButton>
        <a onClick={this.goHome} className="gohome">
          <i className="scmIconfont scm-icon-shouye"/>
        </a>
      </GoHomeButton>
    );
  }
}

const GoHomeButton = styled.div`// styled
  & {
    .gohome {
      display: block;
      width: 55px;
      height: 55px;
      line-height: 55px;
      background: rgba(66, 65, 61, 0.90);
      border-radius: 55px;
      position: fixed;
      top: 75%;
      right: 17px;
      z-index: 100;
      text-align: center;
      color: #fff;
      .scm-icon-shouye {
        font-size: 24px;
      }
    }
    .gohome:active {
      color: rgba(255, 255, 255, 0.6);
    }
  }
`;
