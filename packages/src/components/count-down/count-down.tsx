import { observer } from "mobx-react";
import React from "react";
import { withRouter } from "react-router";
import styled from "styled-components";
import { $HomeMV } from "../../modules/home/<USER>";
import {autowired} from "@classes/ioc/ioc";
import {transaction} from "mobx";

declare let window: any;
declare let require: any;

@observer
export class CountDown extends React.Component<any, any> {
  @autowired($HomeMV)
  public $mv: $HomeMV;

  constructor(props) {
    super(props);
    this.state = {
      days: "",
      hours: "",
      minutes: "",
      seconds: "",
      timeDiff: 0,
      text: "距离活动结束",
    };
  }

  public componentDidMount() {
    const {timeDiff, suitId, type} = this.props;
    this.setState({
      timeDiff: timeDiff,
    });
    const timer = setInterval(() => {
      if (this.state.timeDiff < 1000) {
        this.$mv.changeSuitActive(suitId, type);
        clearInterval(timer);
      }
      this.setState({
        timeDiff: this.state.timeDiff - 1000,
      });
      this.changeTime(this.state.timeDiff);
    }, 1000);
  }

  public changeTime = (timeDiff) => {
    const days = parseInt(timeDiff / 1000 / 60 / 60 / 24 , 10); // 计算剩余的天数
    const hours = parseInt(timeDiff / 1000 / 60 / 60 % 24 , 10); // 计算剩余的小时
    const minutes = parseInt(timeDiff / 1000 / 60 % 60, 10); // 计算剩余的分钟
    const seconds = parseInt(timeDiff / 1000 % 60, 10);  // 计算剩余的秒数
    this.setState({
      days: this.checkTime(days),
      hours: this.checkTime(hours),
      minutes: this.checkTime(minutes),
      seconds: this.checkTime(seconds),
    });
  }

  public checkTime = (i) => { // 将0-9的数字前面加上0，例1变为01
    if (i < 10) {
      i = "0" + i;
    }
    return i;
  }

  public render() {
    const {days, hours, minutes, seconds} = this.state;
    return (
      <CountDownWrapper>
        <span className="text-red">{this.state.text}</span>
        <span className="time">{days}</span><span>:</span>
        <span className="time">{hours}</span><span>:</span>
        <span className="time">{minutes}</span><span>:</span>
        <span className="time">{seconds}</span>
      </CountDownWrapper>
    );
  }
}

const Title = styled.h1`// styled
  & {
    font-size: 14px;
    color:#999999;
  }
`;
const CountDownWrapper = styled.div`// styled
  & {
    font-size: 20px;
    height:40px;
    display:inline-block;
    background:url("https://order.fwh1988.cn:14501/static-img/scm/icon-showtimebg.png");
    background-size:cover;
    padding:6px 6px 6px 40px;
    span{
      float:left;
    }
    .text-red{
      color:#FF3030;
      margin-right:10px;
      float:left;
    }
    .time{
      display:block;
      float:left;
      height:24px;
      min-width:24px;
      line-height:24px;
      font-size:16px;
      color:white;
      background:#666666;
      text-align:center;
      position:relative;
      top:2px;
    }
  }
`;
