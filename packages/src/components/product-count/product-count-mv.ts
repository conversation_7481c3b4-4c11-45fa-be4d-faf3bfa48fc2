import { bean } from "@classes/ioc/ioc";
import { action, observable } from "mobx";

@bean($ProductCountMv)
export class $ProductCountMv {
  @observable public showModal: boolean = false;

  @observable public inputValue: number;

  @observable public isChange: boolean = false;

  @action
  public openModal(quantity) {
    this.showModal = true;
    console.log(quantity);
    this.inputValue = quantity || 0;
  }

  @action
  public closeModal() {
    this.showModal = false;
  }

  @action
  public setInputValue(value) {
    if (value === "") {
      this.inputValue = null;
    } else {
      this.inputValue = Number(value);
    }
    this.isChange = true;
  }

}
