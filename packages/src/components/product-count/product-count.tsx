import { autowired } from "@classes/ioc/ioc";
import { InputItem, Modal, Toast } from "antd-mobile";
import { debounce, findIndex } from "lodash";
import { toJS, transaction } from "mobx";
import { observer } from "mobx-react";
import React from "react";
import styled from "styled-components";
import { $CartType } from "../../classes/const/$cart-type";
import { $PromotionType } from "../../classes/const/$promotion-type";
import { $ComponentService } from "../../classes/service/$component-service";
import { $PromotionMatchService } from "../../classes/service/$promotion-match-service";
import { $proChooseGiftMv } from "../../modules/promotion-choose-products/pro-choose-gift-mv";
import { $PromotionMatchMv } from "../../modules/promotion-match/promotion-match-mv";
import { $CartMv } from "../../modules/shop-cart/cart-mv";
import { $ProductMv } from "../product/product-mv";
import { $ProductCountMv } from "./product-count-mv";
import "./product-count.less";
import { InputNumberItemComponent } from "../input-number-item-component/input-number-item-component";

const alert = Modal.alert;

@observer
export class ProductCount extends React.Component<any, any> {
  @autowired($CartMv)
  public $CartMv: $CartMv;

  @autowired($ProductMv)
  public $ProductMv: $ProductMv;

  @autowired($PromotionMatchMv)
  public $PromotionMatchMv: $PromotionMatchMv;

  public $ProductCountMv: $ProductCountMv = new $ProductCountMv();

  @autowired($ComponentService)
  public $componentService: $ComponentService;

  @autowired($PromotionMatchService)
  public $promotionMatchService: $PromotionMatchService;

  @autowired($proChooseGiftMv)
  public $proChooseGiftMV: $proChooseGiftMv;

  constructor(props) {
    super(props);
// this.onRemoveClick = debounce(this.onRemoveClick, 200);
  }

  public changeToCart = (data, operation) => {
    const { isDiscount } = this.props;
    data.orderId = sessionStorage.getItem("editOrderId") ? sessionStorage.getItem("editOrderId") : null;
    // console.log(data);
    data.operation = operation;
    this.$componentService.changeShopCart(data).then(() => {
      const { source } = this.props;
      if (source) {
        // const params = { source };
        this.$CartMv.fetchCarts();
      } else {
        this.$CartMv.fetchShopcartproductnum();
      }
    });
    // const splitParams = {
    //   openDiscountCheck: false,
    //   oldSalesOrderId: sessionStorage.getItem("editOrderId") === "null" ? null : sessionStorage.getItem("editOrderId"),
    //   shopCartItemList: this.$CartMv.getProductsParams,
    //   shopCartTotalAmount: this.$CartMv.totalAmount ? Number(this.$CartMv.totalAmount).toFixed(2) : 0,
    // };
    // this.$CartMv.fetchItemSplit(splitParams);
  }

  public changeToPromotion = (data) => {
    const { isPromotion } = this.props;
    const params = {
      promotionList: toJS(this.$PromotionMatchMv.promotionList),
      shopCartItemList: isPromotion ? JSON.parse(sessionStorage.getItem("productsParams")) : [],
    };
    this.$promotionMatchService.countPromotion(params).then((data) => {
      const { promotionList, errorMessage, orderTotalAmount } = data;
      if (data.errorCode === $PromotionType.ERRORCODEFORCEGIVE) {
        alert(data.errorMessage, "", [
          {
            text: "确认", onPress: () => {
              // window.location.href = document.referrer;
            },
          },
        ]);
      }
      this.$PromotionMatchMv.setCountPromotion(promotionList, orderTotalAmount);
      if (errorMessage !== "") {
        Toast.info(errorMessage);
      }
      this.$proChooseGiftMV.saveGiftObj = {};
    });
  }

  public onAddClick = (v = 0) => {
    const { data, isPromotion, orderSchemeType } = this.props || { data: {}, isPromotion: "", orderSchemeType: "" };
    if (isPromotion) { // 促销用
      const newPromotion = this.$PromotionMatchMv.addQuantityByProduct(data, v);
      this.changeToPromotion(newPromotion);
    } else {
      let amount = v;
      let startQuantity, multiple, limitQuantity, stockQuantity;
      if (data.activitySuit) {
        startQuantity = data.startQuantity;
        multiple = data.multiple;
        limitQuantity = data.limitQuantity;
        stockQuantity = null;
      } else if (data.product) {
        startQuantity = data.product.startQuantity;
        multiple = data.product.multiple;
        limitQuantity = data.product.limitQuantity;
        stockQuantity = data.product.stockQuantity;
      } else if (data.virtualSuit) {
        startQuantity = data.virtualSuit.startQuantity;
        multiple = data.virtualSuit.multiple;
        limitQuantity = data.virtualSuit.limitQuantity;
        stockQuantity = data.virtualSuit.stockQuantity;
      } else {
        startQuantity = data.startQuantity;
        multiple = data.multiple;
        limitQuantity = data.limitQuantity;
        stockQuantity = data.stockQuantity;
      }

      // 如果起订量和订货倍数都为空，按照1累加
      if (startQuantity === null && multiple === null) {
        amount += 1;
      }

      // 如果起订量存在，但是没有倍数的话，把最小值抬高
      if (startQuantity && multiple === null) {
        if (amount < startQuantity) {
          amount = startQuantity;
        } else {
          amount += 1;
        }
      }

      // 如果倍数存在，起订量不存在，则按照倍数增加
      // multi = 2 amount = 3 result = 4
      if (multiple && startQuantity === null) {
        // 整除 直接累加
        if (amount % multiple === 0) {
          amount += multiple;
        } else {
          // 不整除 抹去余数加倍数 3 - (3 % 2) + 2 = 4
          const remainder = amount % multiple;
          amount = amount - remainder + multiple;
        }
      }

      // 都存在
      if (multiple && startQuantity) {
        // 如果当前数目大于起订量
        if (amount >= startQuantity) {
          // 整除 直接累加
          if (amount % multiple === 0) {
            amount += multiple;
          } else {
            // 不整除 抹去余数加倍数 3 - (3 % 2) + 2 = 4
            const remainder = amount % multiple;
            amount = amount - remainder + multiple;
          }
        } else {
          // 获取上取整倍数乘以倍数
          const b = Math.ceil(startQuantity / multiple);
          amount = b * multiple;
        }
      }

      if (limitQuantity) {
        if (amount > limitQuantity) {
          Toast.info(`最多可购买数量为${limitQuantity}`, 3);
          return;
        }
      }

      if (stockQuantity && orderSchemeType !== $CartType.FUTURE_ORDER) {
        if (amount > stockQuantity) {
          Toast.info("库存不足", 3);
          return;
        }
      }
      const newProduct = this.$CartMv.addQuantityByProduct(data, amount);
      if (this.props.hasDiscount) {
        this.props.updateDiscount();
      }
      (debounce(() => this.changeToCart(newProduct, "add"), 200))();
    }
  }

  public onRemoveClick = () => {
    const { data, noCart, isPromotion } = this.props;
    const index = findIndex(this.$CartMv.products, { productSkuId: data.productSkuId });
    if (isPromotion) {
      const newPromotion = this.$PromotionMatchMv.minusQuantityByProduct(data);
      this.changeToPromotion(newPromotion);
    } else {
      let startQuantity, multiple, limitQuantity, stockQuantity;
      if (data.activitySuit) {
        startQuantity = data.startQuantity;
        multiple = data.multiple;
        limitQuantity = data.limitQuantity;
        stockQuantity = null;
      } else if (data.product) {
        startQuantity = data.product.startQuantity;
        multiple = data.product.multiple;
        limitQuantity = data.product.limitQuantity;
        stockQuantity = data.product.stockQuantity;
      } else if (data.virtualSuit) {
        startQuantity = data.virtualSuit.startQuantity;
        multiple = data.virtualSuit.multiple;
        limitQuantity = data.virtualSuit.limitQuantity;
        stockQuantity = data.virtualSuit.stockQuantity;
      } else {
        startQuantity = data.startQuantity;
        multiple = data.multiple;
        limitQuantity = data.limitQuantity;
        stockQuantity = data.stockQuantity;
      }
      if (startQuantity === null && multiple === null) {
        this.$CartMv.products[index].quantity -= 1;
      }

      if (startQuantity && multiple === null) {
        if (this.$CartMv.products[index].quantity <= startQuantity) {
          this.$CartMv.products[index].quantity = 0;
        } else {
          this.$CartMv.products[index].quantity -= 1;
        }
      }

      if (multiple && startQuantity === null) {
        this.$CartMv.products[index].quantity -= multiple;
      }

      if (multiple && startQuantity) {
        const b = Math.ceil(startQuantity / multiple);
        let c = 0;
        // 抬高到倍数
        c = b * multiple;
        // 如果减去倍数之后大于起订量 则直接减倍数 否则清零
        if (this.$CartMv.products[index].quantity - multiple >= startQuantity) {
          this.$CartMv.products[index].quantity -= multiple;
        } else {
          this.$CartMv.products[index].quantity = 0;
        }
      }
      const newProduct = this.$CartMv.minusQuantityByProduct(data);
      if (this.props.hasDiscount) {
        this.props.updateDiscount();
      }
      (debounce(() => this.changeToCart(newProduct, "reduce"), 200))();
    }

  }

  public onConfirm = () => {
    document.removeEventListener("touchmove", this.bodyScroll, { passive: false });
    const { data, isPromotion, orderSchemeType } = this.props || { data: {}, isPromotion: "", orderSchemeType: "" };
    const { quantity } = data;
    let { inputValue } = this.$ProductCountMv;
    console.log(inputValue);
    if (inputValue === undefined || inputValue === null) {
      inputValue = Number(quantity);
    } else if (inputValue === 0 && quantity === 0) {
      Toast.info("输入数量必须大于0", 3);
      return;
    } else if (inputValue === 0 && quantity !== 0) {
      inputValue = Number(quantity);
    }
    if (isPromotion) {
      this.changeToPromotion(this.$CartMv.addQuantityByProduct(data, inputValue));
    } else {
      let startQuantity, multiple, limitQuantity, stockQuantity;
      if (data.activitySuit) {
        startQuantity = data.startQuantity;
        multiple = data.multiple;
        limitQuantity = data.limitQuantity;
        stockQuantity = null;
      } else if (data.product) {
        startQuantity = data.product.startQuantity;
        multiple = data.product.multiple;
        limitQuantity = data.product.limitQuantity;
        stockQuantity = data.product.stockQuantity;
      } else if (data.virtualSuit) {
        startQuantity = data.virtualSuit.startQuantity;
        multiple = data.virtualSuit.multiple;
        limitQuantity = data.virtualSuit.limitQuantity;
        stockQuantity = data.virtualSuit.stockQuantity;
      } else {
        startQuantity = data.startQuantity;
        multiple = data.multiple;
        limitQuantity = data.limitQuantity;
        stockQuantity = data.stockQuantity;
      }
      if (startQuantity) {
        if (inputValue < startQuantity) {
          Toast.info(`最少需购买数量为${startQuantity}`, 3);
          return;
        }
      }
      if (multiple) {
        if (inputValue % multiple !== 0) {
          Toast.info(`请购买${multiple}的倍数`, 3);
          return;
        }
      }
      if (stockQuantity && orderSchemeType !== $CartType.FUTURE_ORDER) {
        if (inputValue > stockQuantity) {
          Toast.info(`库存不足`, 3);
          return;
        }
      }

      if (limitQuantity) {
        if (inputValue > limitQuantity) {
          Toast.info(`最多可购买数量为${limitQuantity}`, 3);
          return;
        }
      }
      transaction(() => {
        this.$ProductCountMv.closeModal();
      });

      if (inputValue > quantity) { // 加
        const newData = this.$CartMv.addQuantityByProduct(data, inputValue)
        if (this.props.hasDiscount) {
          this.props.updateDiscount();
        }
        (debounce(() => this.changeToCart(newData, "add"), 200))();
      } else { // 减
        const newData = this.$CartMv.addQuantityByProduct(data, inputValue)
        if (this.props.hasDiscount) {
          this.props.updateDiscount();
        }
        (debounce(() => this.changeToCart(newData, "reduce"), 200))();
      }
    }
  }

  public onBlur = () => {
    window.scroll(0, 0);
  }

  public bodyScroll = (e) => {
    e.preventDefault();
  }

  public closeModal = () => {
    document.removeEventListener("touchmove", this.bodyScroll, { passive: false });
    this.$ProductCountMv.closeModal();
  }

  public openModal = (quantity) => {
    document.addEventListener("touchmove", this.bodyScroll, { passive: false });
    this.$ProductCountMv.openModal(quantity);
  }

  public render() {
    const { data, isPromotion, orderSchemeType, fromWhere } = this.props;
    const { checkFlag, isActive, quantity, stockStatus, times } = data;
    const { showModal, inputValue, isChange } = this.$ProductCountMv;
    console.log(quantity);
    return (
      <Wrapper onClick={(e) => e.stopPropagation()}>
        {
          checkFlag ? <div>
            {
              isPromotion ? <i
                className="scmIconfont scm-icon-reduce"/> : quantity <= 0 || isActive === $CartType.ISACTIVE_KEY || stockStatus === $CartType.STOCKSTATUS || checkFlag !== $PromotionType.CHECKFLAG ?
                <i className="scmIconfont scm-icon-reduce"/> : <i
                  className="scmIconfont scm-icon-reduce scm-icon-active"
                  onClick={this.onRemoveClick}/>
            }
            <span className="input">
                <InputItem
                  type="number"
                  value={times > 0 ? String(times)
                    : times === 0 ? "0" : String(quantity)}
                  editable={false}
                  disabled={isPromotion ? true : isActive === $CartType.ISACTIVE_KEY || stockStatus === $CartType.STOCKSTATUS || checkFlag !== $PromotionType.CHECKFLAG}
                  onChange={() => {
                    // do nothing
                  }}
                  onClick={() => this.openModal(quantity)}
                />
          </span>
            {
              isPromotion ? <i
                className="scmIconfont scm-icon-increase"/> : isActive === $CartType.ISACTIVE_KEY || stockStatus === $CartType.STOCKSTATUS || checkFlag !== $PromotionType.CHECKFLAG ?
                <i className="scmIconfont scm-icon-increase"/> : <i
                  className="scmIconfont scm-icon-increase scm-icon-active"
                  onClick={() => this.onAddClick(quantity || times)}/>
            }
          </div> : isActive ? <div>
            {
              isPromotion ? <i
                className="scmIconfont scm-icon-reduce"/> : quantity <= 0 || isActive === $CartType.ISACTIVE_KEY || (orderSchemeType !== $CartType.FUTURE_ORDER && stockStatus === $CartType.STOCKSTATUS) ?
                <i className="scmIconfont scm-icon-reduce"/> : <i
                  className="scmIconfont scm-icon-reduce scm-icon-active"
                  onClick={this.onRemoveClick}
                />
            }
            <span className="input">
                <InputItem
                  type="number"
                  value={times > 0 ? String(times)
                    : times === 0 ? "0" : String(quantity)}
                  editable={false}
                  disabled={isPromotion ? true : isActive === $CartType.ISACTIVE_KEY || (orderSchemeType !== $CartType.FUTURE_ORDER && stockStatus === $CartType.STOCKSTATUS)}
                  onChange={() => {
                    // do nothing
                  }}
                  onClick={() => this.openModal(quantity)}
                />
          </span>
            {
              isPromotion ? <i
                className="scmIconfont scm-icon-increase"/> : isActive === $CartType.ISACTIVE_KEY || (orderSchemeType !== $CartType.FUTURE_ORDER && stockStatus === $CartType.STOCKSTATUS) ?
                <i className="scmIconfont scm-icon-increase"/> :
                <i className="scmIconfont scm-icon-increase scm-icon-active"
                   onClick={() => this.onAddClick(quantity || times)}/>
            }
          </div> : <div>
            {
              isPromotion ?
                <i
                  className="scmIconfont scm-icon-reduce"/> : quantity <= 0 || (orderSchemeType !== $CartType.FUTURE_ORDER && stockStatus === $CartType.STOCKSTATUS) ?
                <i className="scmIconfont scm-icon-reduce"/> : <i
                  className="scmIconfont scm-icon-reduce scm-icon-active"
                  onClick={this.onRemoveClick}/>
            }
            <span className="input">
                <InputItem
                  type="number"
                  value={times ? String(times) : String(quantity)}
                  editable={false}
                  disabled={isPromotion ? true : (orderSchemeType !== $CartType.FUTURE_ORDER && stockStatus === $CartType.STOCKSTATUS)}
                  onChange={() => {
                    // do nothing
                  }}
                  onClick={() => this.openModal(quantity)}
                />
          </span>
            {
              isPromotion ? <i
                className="scmIconfont scm-icon-increase"/> : (orderSchemeType !== $CartType.FUTURE_ORDER && stockStatus === $CartType.STOCKSTATUS) ?
                <i className="scmIconfont scm-icon-increase"/> :
                <i className="scmIconfont scm-icon-increase scm-icon-active"
                   onClick={() => this.onAddClick(quantity || times)}/>
            }
          </div>
        }
        <Modal
          className="model"
          title={<div>请输入数量</div>}
          visible={showModal}
          transparent={true}
          maskClosable={false}
          footer={[
            { text: "取消", onPress: () => this.closeModal() },
            { text: "确定", onPress: this.onConfirm },
          ]}
        >
          {/*<InputItem*/}
          {/*style={{ textAlign: "center" }}*/}
          {/*type="number"*/}
          {/*defaultValue={times > 0 ? String(times)*/}
          {/*: times === 0 ? "0" : String(quantity)}*/}
          {/*onChange={(value) => this.$ProductCountMv.setInputValue(Number(value))}*/}
          {/*onBlur={this.onBlur}*/}
          {/*/>*/}
          {
            fromWhere === $CartType.PRODUCT ? <InputNumberItemComponent
              style={{ textAlign: "center" }}
              type="money"
              onChange={(value) => this.$ProductCountMv.setInputValue(value)}
              defaultValue={times > 0 ? String(times)
                : times === 0 ? "0" : String(quantity)}
              value={inputValue === null ? null : String(inputValue)}
              onBlur={this.onBlur}
            /> : <InputNumberItemComponent
              style={{ textAlign: "center" }}
              type="money"
              defaultValue={times > 0 ? String(times)
                : times === 0 ? "0" : String(quantity)}
              onChange={(value) => this.$ProductCountMv.setInputValue(value)}
              value={inputValue && String(inputValue)}
              onBlur={this.onBlur}
            />
          }
        </Modal>
      </Wrapper>
    );
  }
}

const Wrapper = styled.div`// styled
  & {
    line-height: 30px;
    .scm-icon-reduce, .scm-icon-increase {
      font-size: 20px;
      color: #D8D8D8;
      position: relative;
      top: 5px;
    }
    .scm-icon-active {
      font-size: 20px;
      color: #307dcd;
    }
    .op {
      height: 24px;
      line-height: 20px;
      width: 24px;
      border-radius: 12px;
      display: inline-block;
      text-align: center;
      border: 1px solid transparent;
      font-size: 18px;
      color: #fff;
      background-color: #307dcd;
      position: relative;
      top: 2px;
    }
    .input {
      display: inline-block;
      width: 48px;
      height: 30px;
      line-height: 30px;
      margin: 0 10px;
      position: relative;
      .am-list-line {
        padding: 0;
      }
      .am-list-item.am-input-item {
        padding: 0;
        height: 31px;
        min-height: 30px;
      }
      .am-input-control input {
        text-align: center;
        height: 30px;
        line-height: 30px;
        border-radius: 3px;
        border: 0.5px solid #999;
        //font-family: PingFangSC-Regular;
        font-size: 14px;
        color: #307DCD;
      }
    }
    .am-button {
      &:before {
        width: 0 !important;
        height: 0 !important;
        border: none !important;
      }
    }
    .am-list-item .am-input-control input:disabled {
      color: #D2D2D2;
      border: 0.5px solid #D8D8D8;
    }
  }`;
