import { autowired } from "@classes/ioc/ioc";
import { Button, InputItem, Modal } from "antd-mobile";
import { transaction } from "mobx";
import { observer } from "mobx-react";
import React from "react";
import styled from "styled-components";
import { $ComponentService } from "../../classes/service/$component-service";
import { $CartMv } from "../../modules/shop-cart/cart-mv";
import { $ProductCountMv } from "./product-count-mv";

@observer
export class ProductCountHome extends React.Component<any, any> {
  @autowired($CartMv)
  public $CartMv: $CartMv;

  public $ProductCountMv: $ProductCountMv = new $ProductCountMv();

  @autowired($ComponentService)
  public $componentService: $ComponentService;

  constructor(props) {
    super(props);
    // this.onRemoveClick = debounce(this.onRemoveClick, 200);
  }

  public changeToCart = (data) => {
    this.$componentService.changeShopCart(data);
  }

  public onAddClick = (v) => {
    const { data } = this.props;
    const newProduct = this.$CartMv.addQuantityByProduct(data, v);
    this.changeToCart(newProduct);
  }

  public onRemoveClick = () => {
    const { data } = this.props;
    const newProduct = this.$CartMv.minusQuantityByProduct(data);
    this.changeToCart(newProduct);
  }

  public onConfirm = () => {
    const { data } = this.props;
    const { inputValue } = this.$ProductCountMv;
    transaction(() => {
      this.$ProductCountMv.closeModal();
    });
    this.changeToCart(this.$CartMv.addQuantityByProduct(data, inputValue));
  }

  public render() {
    const { data } = this.props;
    const { quantity } = data;
    const { showModal } = this.$ProductCountMv;

    return (
      <Wrapper>
        <Button className="op" onClick={this.onRemoveClick} disabled={quantity <= 0}>-</Button>
        <span className="input">
            <div onClick={() => this.$ProductCountMv.openModal()}>{quantity}</div>
          </span>
        <Button className="op" onClick={() => this.onAddClick(quantity + 1)}>+</Button>
        <Modal
          title={<div>请输入数量</div>}
          visible={showModal}
          transparent
          maskClosable={false}
          footer={[
            { text: "取消", onPress: () => this.$ProductCountMv.closeModal() },
            { text: "确定", onPress: this.onConfirm },
          ]}
        >
          <InputItem
            type="number" defaultValue={String(quantity)}
            onChange={(value) => this.$ProductCountMv.setInputValue(Number(value))}
          />
        </Modal>
      </Wrapper>
    );
  }
}

const Wrapper = styled.div`// styled
  & {
    display: flex;
    justify-content: flex-end;
    .am-button.op {
      flex: 0 0 24px;
      width: 24px;
      height: 24px;
      line-height: 21px !important;
      border-radius: 2px;
      background-color: transparent;
      border: 1px solid white;
      color: white;
      font-weight: normal;
      &:before {
        display: none;
      }
    }
    .input {
      margin: 0 4px;
      border-radius: 2px;
      border: 1px solid white;
      max-width: 48px;
      min-width: 48px;
      > div {
        color: white !important;
        text-align: center;
        line-height: 22px;
      }
    }
    .am-list-item.am-input-item.am-list-item-middle {
      min-height: 22px;
      height: 22px;
      border-radius: 2px;
      background-color: transparent;
      padding-left: 4px !important;
      .am-list-line {
        padding-right: 4px !important;
      }
    }
  }`;
