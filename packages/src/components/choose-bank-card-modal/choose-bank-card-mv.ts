import { autowired, bean } from "@classes/ioc/ioc";
import { action, observable } from "mobx";
import { ChooseBankcardService } from "./choose-bankcard-service";
import { $Bank } from "../../classes/entity/$bank";
import { $PaymentModeType } from "../../classes/const/$payment-mode-type";
import { beanMapper } from "../../helpers/bean-helpers";

@bean(ChooseBankCardMv)
export class ChooseBankCardMv {

  @autowired(ChooseBankcardService)
  public chooseBankcardService: ChooseBankcardService;

  @observable public bankList: $Bank[] = [];

  @observable public gatewayBindingCardAddress: string;

  @observable public tokenId: string;

  @observable public isSpin: boolean = false;
  @observable public scrollHeight: number = 0;

  @action
  public showSpin() {
    this.isSpin = true;
  }

  @action
  public hideSpin() {
    this.isSpin = false;
  }

  @action
  public loadPaymentTpBankList() {
    return this.chooseBankcardService.loadPaymentTpBankList().then((data) => {
      this.hideSpin();
      this.bankList = data.bankList.map((bank) => new $Bank(bank));
      this.bankList.map((lis) => {
        if (lis.bankCardTypeCode === $PaymentModeType.CREDITBANKCARTCODE) { // 信用卡
          lis.bankCardRate = $PaymentModeType.CREDITCARTRATE;
        } else { // 储值卡
          lis.bankCardRate = $PaymentModeType.STOREDVALUECARDRATE;
        }
      });
      console.log(this.bankList);
      this.gatewayBindingCardAddress = data.gatewayBindingCardAddress;
      this.tokenId = data.tokenId;
      return data;
    }).catch(() => {
      this.hideSpin();
    });
  }

  @action
  public paygatewayGetchanneluser() {
    return this.chooseBankcardService.paygatewayGetchanneluser();
  }

  @action
  public unbindBankCard(params) {
    return this.chooseBankcardService.unbindBankCard(params);
  }
  @action
  public queryOldData(obj) {
    beanMapper(obj, this);
    console.log(this);
  }
  @action
  public clearMVData() {
   this.bankList = [];
   this.gatewayBindingCardAddress = "";
   this.tokenId = "";
   this.isSpin = false;
   this.scrollHeight = 0;
  }
}
