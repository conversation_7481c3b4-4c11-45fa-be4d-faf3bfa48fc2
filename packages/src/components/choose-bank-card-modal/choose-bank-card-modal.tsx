import * as React from "react";
import { observer } from "mobx-react";
import styled from "styled-components";
import { autowired } from "@classes/ioc/ioc";
import { ChooseBankCardMv } from "./choose-bank-card-mv";

@observer
export class ChooseBankCardModal extends React.Component<any, any> {

  @autowired(ChooseBankCardMv)
  public chooseBankCardMv: ChooseBankCardMv;

  public constructor(props) {
    super(props);
    this.state = {};
  }

  public componentDidMount() {
    // this.chooseBankCardMv.loadPaymentTpBankList();
  }

  public gatewayBindingCardAddress = () => {
    const { gatewayBindingCardAddress, tokenId } = this.chooseBankCardMv;
    const { bankRedirectUrl, skipToBindCard, singlePaymentModeList } = this.props;
    // sessionStorage.setItem("singlePaymentModeList", JSON.stringify(singlePaymentModeList));
    // url 解码 decodeURI
    if (skipToBindCard) {
      skipToBindCard(() => {
        window.location.href = `${gatewayBindingCardAddress}&redirectUrl=${bankRedirectUrl}`;
      });
    } else {
      window.location.href = `${gatewayBindingCardAddress}&redirectUrl=${bankRedirectUrl}`;
    }
  }

  // ToDo: 银行卡信息多时效果
  public render() {
    const { goBack, isShow, chooseBank } = this.props;
    const { bankList } = this.chooseBankCardMv;
    return (
      <ChooseBankCardModalPage theme={{ isShow }}>
        <ModalCongtent>
          <ModalHeader>
            <i className={"scmIconfont scm-icon-jiantou-you"} onClick={goBack}/>
            <span>选择银行卡</span>
          </ModalHeader>
          <ModalScroll>
            {
              bankList.map((bank) => {
                return (
                  <div
                    key={bank.id}
                    onClick={() => chooseBank(bank)}
                    className={bank.isCheck ? "active" : ""}
                  >
                    <i className={"scmIconfont scm-yinhangka"}/>
                    <span>{bank.bankName.length <= 6 ? bank.bankName : bank.bankName.substring(0, 6) + "..."}{bank.bankCardTypeName}({bank.bankCardNo})</span>
                  </div>
                );
              })
            }
            <UseNewCard onClick={() => this.gatewayBindingCardAddress()}>
              <i className={"scmIconfont scm-qia"}/>
              使用新卡支付
            </UseNewCard>
          </ModalScroll>
        </ModalCongtent>
      </ChooseBankCardModalPage>
    );
  }
}

const ChooseBankCardModalPage = styled.div`// styled
  & {
    width: 100%;
    height: ${document.documentElement.clientHeight}px;
    background:rgba(0,0,0,0.5);
    padding: 105px 52px 0px 52px;
    display: ${(props) => props.theme.isShow ? "block" : "none"};
    position: fixed;
    top: 0;
    z-index: 1000;
    .scm-icon-jiantou-you {
       -webkit-transform: rotate(180deg);
       -moz-transform: rotate(180deg);
       -o-transform: rotate(180deg);
       -ms-transform: rotate(180deg);
       transform: rotate(180deg);
    }
  }
`;

const ModalHeader = styled.div`// styled
  & {
    width: 100%;
    height: 64px;
    line-height: 64px;
    text-align: center;
    position: relative;
    > .scmIconfont.scm-icon-jiantou-you {
      position: absolute;
      left: 0;
      top: 0;
    }
  }
`;

const ModalCongtent = styled.div`// styled
  & {
    width: 100%;
    max-height: ${document.documentElement.clientHeight - 210}px;
    border-radius:16px;
    background:rgba(255,255,255,1);
    padding: 0 21px 21px 21px;
  }
`;

const ModalScroll = styled.div`// styled
  & {
    width: 100%;
    max-height: ${document.documentElement.clientHeight - 64 - 24 - 210}px;
    position: sticky;
    overflow: auto;
    > div {
      border-radius:4px;
      border:1px solid rgba(221,221,221,1);
      color: #303030;
      font-size: 13px;
      margin-bottom: 10px;
      min-height: 36px;
      line-height: 36px;
      padding-left: 14px;
      >.scm-yinhangka{
        margin-right: 8px;
      }
      > span {
        display: inline-block;
        width: calc(100% - 40px);
        vertical-align: text-top;
        line-height: initial;
      }
    }
    .active {
      border-radius:4px;
      border:1px solid rgba(48,125,205,1);
      color: #307DCD;
    }
    .scm-yinhangka {
      position: relative;
      top: 2px;
    }
  }
`;

const UseNewCard = styled.div`// styled
  & {
    border-radius: 4px;
    border: 1px solid rgba(221, 221, 221, 1);
    color: #52C41A !important;
    margin-bottom: 10px;
    .scm-qia.scmIconfont{
      color: #52C41A !important;
      font-size: 13px;
      margin-right: 6px;
    }
  }
`;
