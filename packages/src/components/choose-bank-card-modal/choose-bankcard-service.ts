import { action } from "mobx";
import { post } from "../../helpers/ajax-helpers";
import { bean } from "@classes/ioc/ioc";

@bean(ChooseBankcardService)
export class ChooseBankcardService {
  @action
  public loadPaymentTpBankList() {
    return post(`/integration/scm/ordermall/payment/tp/bank/list`);
  }

  @action
  public paygatewayGettoken() {
    return post(`/integration/scm/paygateway/gettoken`);
  }

  @action
  public paygatewayGetchanneluser() {
    return post(`/integration/scm/paygateway/getchanneluser`);
  }

  @action
  public unbindBankCard(params) {
    return post(`/integration/scm/paygateway/unbindbankcard`, params);
  }
}
