import React, { Component } from "react";
import styled from 'styled-components';

export function ScrollTablePagination(importComponent) {
  class ScrollComponent extends Component {
    constructor(props) {
      super(props);

      this.state = {
        component: null,
        isLoading: false,
        isHaveScrollEnd: false,
        isScrollEnd: false,
      };
    }

    public componentDidMount() {
      this.setState({ component: importComponent });
    }

    public touchStart = (e) => {
      this.setState({
        startx: e.touches[0].pageX,
        starty: e.touches[0].pageY,
      });
    }
    public loadingEnd = (isLoading) => {
      console.log("加载结束", isLoading)
      this.setState({ isLoading: isLoading });
    }
    public touchEnd = (e) => {
      console.log(e);
      let endx, endy;
      endx = e.changedTouches[0].pageX;
      endy = e.changedTouches[0].pageY;
      const direction = this.getDirection(this.state.startx, this.state.starty, endx, endy);
      switch (direction) {
        case 0:
          console.log("未滑动！");
          break;
        case 1:
          console.log("向上！");
          if (this.state.isLoading) {
            return;
          }
          this.isScrollToEnd();
          break;
        case 2:
          console.log("向下！");
          break;
        case 3:
          console.log("向左！");
          this.onScrollRight();
          break;
        case 4:
          console.log("向右！");
          this.onScrollLeft();
          break;
        default:
      }
    }

    public onScrollRight = () => {
      console.log(8989);
      const antTableTbody = document.getElementsByClassName("ant-table");
      const length = antTableTbody && antTableTbody.length;
      for (let i = 0; i < length; i++) {
        $($(".ant-table").eq(i)).addClass("ant-table-scroll-position-right ant-table-scroll-position-middle");
        $($(".ant-table").eq(i)).removeClass("ant-table-scroll-position-left");
      }
    }

    public onScrollLeft = () => {
      console.log(8989);
      const antTableTbody = document.getElementsByClassName("ant-table");
      const length = antTableTbody && antTableTbody.length;
      for (let i = 0; i < length; i++) {
        $($(".ant-table").eq(i)).addClass("ant-table-scroll-position-left ant-table-scroll-position-middle");
        $($(".ant-table").eq(i)).removeClass("ant-table-scroll-position-right");
      }
    }

    public getAngle(angx, angy) {
      return Math.atan2(angy, angx) * 180 / Math.PI;
    }

    public isScrollToEnd() {
      const scrollAbilityWrap = $(".scroll-ability-wrap")[0];
      const dataWrapHeight = scrollAbilityWrap.clientHeight;
      const scrollAbility = scrollAbilityWrap.firstElementChild;
      const scrollAbilityHeight = $(scrollAbility).height();
      const scrollAbilityScroll = $(scrollAbility).position().top;
      const h = 10;
      const { isScrollEnd } = this.state;
      console.log(isScrollEnd, scrollAbilityHeight, scrollAbilityScroll, scrollAbilityHeight + scrollAbilityScroll - h, dataWrapHeight)
      if (scrollAbilityHeight + scrollAbilityScroll - h < dataWrapHeight) {
        if (!isScrollEnd) {
          console.log("isScrollEnd", isScrollEnd);
          this.setState({ isScrollEnd: true, isLoading: true }, () => {
            this.setState({ isScrollEnd: false });
          });
          console.log("进来了", true);
        }
      } else {
        if (isScrollEnd) {
          this.setState({ isScrollEnd: false });
          console.log("进来了", false);
        }
        console.log("还未到底");
      }
    }

    public getDirection(startx, starty, endx, endy) {
      const angx = endx - startx;
      const angy = endy - starty;
      let result = 0;

      // 如果滑动距离太短
      if (Math.abs(angx) < 2 && Math.abs(angy) < 2) {
        return result;
      }
      const angle = this.getAngle(angx, angy);
      if (angle >= -135 && angle <= -45) {
        result = 1;
      } else if (angle > 45 && angle < 135) {
        result = 2;
      } else if ((angle >= 135 && angle <= 180) || (angle >= -180 && angle < -135)) {
        result = 3;
      } else if (angle >= -45 && angle <= 45) {
        result = 4;
      }
      return result;
    }

    public render() {
      const C = this.state.component;
      const { isScrollEnd } = this.state;
      return C ?
        <ScrollAbility className="scroll-ability-wrap">
          <div className="scroll-ability" onTouchStart={this.touchStart} onTouchEnd={this.touchEnd}>
            <C {...this.props} isScrollEnd={isScrollEnd} loadingEnd={this.loadingEnd}/>
          </div>
        </ScrollAbility> : <div/>;
    }
  }

  return ScrollComponent;
}
const ScrollAbility = styled.div`// styled
  & {
    height: 100%;
    overflow-y: scroll;
    -webkit-overflow-scrolling: touch;
    overflow-scrolling: touch;
    .scroll-ability{
      height: auto;
      position: relative;
    }
  }
`;
