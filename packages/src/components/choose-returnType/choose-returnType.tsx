import * as React from "react";
import { observer } from "mobx-react";
import { Spin } from "antd";
import { Button, Checkbox, List, Toast } from "antd-mobile";
import styled from "styled-components";
import { ScrollAbilityWrapComponent } from "../scroll-ability/wrap";
import { LoadingTip } from "../loading-marked-words";
import { autowired } from "@classes/ioc/ioc";
import { ChooseItemMv } from "./choose-returnType-mv";
import { SITE_PATH } from "../../modules/app";
import { NoGoods } from "../../components/no-goods/no-goods";
import { $AppStore, AppStoreKey } from "../../classes/stores/app-store-mv";

const CheckboxItem = Checkbox.CheckboxItem;

@observer
class ChooseItemListWrapper extends React.Component<any, any> {

  @autowired($AppStore)
  public $AppStore: $AppStore;
  @autowired(ChooseItemMv)
  public chooseItemMv: ChooseItemMv;

  public constructor(props) {
    super(props);
    this.state = {
      finished: false,
      isHaveSelect: false,
      isShow: false,
      url: "",
    };
  }

  public componentDidMount() {
    document.title = "退货类型";
    this.chooseItemMv.page = 0;
    this.chooseItemMv.chooseItemList = [];
    console.log(this.props.location.state);
    if (this.props.location.state) {
      console.log(this.props.location.state.url);
      this.setState({
        url: this.props.location.state.url,
      }, () => {
        this.loadData();
      });
    }
  }

  public componentWillReceiveProps(nextProps: Readonly<any>, nextContext: any): void {
    const { isScrollEnd } = nextProps;
    console.log(isScrollEnd);
    if (isScrollEnd && isScrollEnd !== this.props.isScrollEnd) {
      this.loadData();
    }
  }

  public loadData = () => {
    const { finished } = this.state;
    if (finished) {
      return;
    }
    this.search();
  }

  public search = () => {
    const { url } = this.state;
    const { page } = this.chooseItemMv;
    const params = { pageSize: 10, pageIndex: page, isActive: "Y" };
    this.chooseItemMv.showSpin();
    this.chooseItemMv.loadData(url, params).then((data) => {
      this.chooseItemMv.hideSpin();
      data.dataList.map((item) => {
        item.checked = false;
        item.disabled = false;
      });
      console.log(data);
      const { loadingEnd } = this.props;
      if (data.dataList && data.dataList.length > 0) {
        this.chooseItemMv.chooseItemList = this.chooseItemMv.chooseItemList.concat(data.dataList);
        if (this.props.location.state.refundOrderTypeId) {
          if (this.chooseItemMv.chooseItemList.filter((item) => item.value === this.props.location.state.refundOrderTypeId).length > 0) {
            this.chooseItemMv.chooseItemList.filter((item) => item.value === this.props.location.state.refundOrderTypeId)[0].checked = true;
            this.setState({
              isHaveSelect: true,
            });
          }
        }
        this.chooseItemMv.changePage();
        this.setState({
          finished: this.chooseItemMv.chooseItemList.length >= data.totalCount,
          isShow: false,
        });
        loadingEnd && loadingEnd(this.chooseItemMv.chooseItemList.length >= data.totalCount);
      } else {
        this.setState({
          finished: true,
          isShow: false,
        });
        loadingEnd && loadingEnd(true);
      }
    }).catch(() => {
      this.chooseItemMv.hideSpin();
    });
  }

  public onChange = (e, value) => {
    console.log(e.target.checked);
    if (this.chooseItemMv.chooseItemList.filter((item) => item.value === value).length > 0) {
      this.setState({ isHaveSelect: e.target.checked })
      this.chooseItemMv.chooseItemList.filter((item) => item.value === value)[0].checked = e.target.checked;
      this.chooseItemMv.chooseItemList.filter((item) => item.value !== value).map((item) => {
        item.checked = false;
      });
    }
  }

  public submit = () => {
    const isChecked = this.chooseItemMv.chooseItemList.every((item) => item.checked === false);
    if (isChecked) {
      Toast.info("请选择");
      return;
    }
    if (this.chooseItemMv.chooseItemList.filter((item) => item.checked === true).length > 0) {
      this.$AppStore.clearPageMv(AppStoreKey.INITIATINGRETURNS);
      this.props.history.push({
        pathname: `/${SITE_PATH}/initiating-returns`,
        state: {
          refundOrderTypeName: this.chooseItemMv.chooseItemList.filter((item) => item.checked)[0].label,
          refundOrderTypeId: this.chooseItemMv.chooseItemList.filter((item) => item.checked)[0].value,
          isChooseType: this.props.location.state && this.props.location.state.isChooseType,
          selectProductSkuList: this.props.location.state && this.props.location.state.selectProductSkuList,
          fromWhere: this.props.location.state && this.props.location.state.fromWhere,
          memo: this.props.location.state && this.props.location.state.memo,
          isChoose: true,
          orderPartyName: this.props.location.state && this.props.location.state.orderPartyName,
          orgId: this.props.location.state && this.props.location.state.orgId,
        },
      });
    }
  }

  public render() {
    const { chooseItemList, isSpin } = this.chooseItemMv;
    const { finished, isShow, isHaveSelect } = this.state;
    return (
      <ChooseItemPage>
        <Spin spinning={isSpin}>
          {
            chooseItemList && chooseItemList.length > 0 && chooseItemList.map((item) => {
              return (
                <List key={item.value}>
                  <CheckboxItem
                    checked={item.checked}
                    disabled={item.disabled}
                    onChange={(e) => this.onChange(e, item.value)}
                    {...this.props}
                  >
                    {item.label}
                  </CheckboxItem>
                </List>
              );
            })
          }
          {
            chooseItemList && chooseItemList.length === 0 &&
            <NoGoods height={document.documentElement.clientHeight} title="暂无退货类型"/>
          }
          {
            chooseItemList && chooseItemList.length > 0 &&
            <LoadingTip
              isFinished={finished}
              isLoad={isShow}
            />
          }
        </Spin>
        {
          chooseItemList && chooseItemList.length > 0 &&
          <ConfirmButton>
            <Button type={"primary"} onClick={this.submit} disabled={!isHaveSelect}>确定</Button>
          </ConfirmButton>
        }
      </ChooseItemPage>
    );
  }
}

const ChooseReturnType = ScrollAbilityWrapComponent(ChooseItemListWrapper);
export default ChooseReturnType;

const ChooseItemPage = styled.div`// styled
  & {
    width: 100%;
    height: calc(${document.documentElement.clientHeight}px);
    // padding-bottom: 66px;
    overflow-y: scroll;
    background: #F2F2F2;
    .common-bottomTotal {
    margin-bottom: 66px;
    }
    @media (min-resolution: 2dppx) {
      html:not([data-scale]) .am-list-item:not(:last-child) .am-list-line {
         border: none !important;
      }
    }
    @media (min-resolution: 2dppx) {
      html:not([data-scale]) .am-list-body::after {
         display: none !important;
      }
    }
    .am-list-body::before {
         display: none !important;
    }
    .am-list-body::after {
         display: none !important;
    }
    .am-button-primary::before {
      display: none !important;
    }
    .am-button {
      border: none !important;
    }
    .am-list-body {
      border-bottom: 1PX solid #ddd !important;
    }
  }
`;

const ChooseItem = styled.div`// styled
  & {
    width: 100%;
    min-height: 44px;
    position: relative;
    :after {
      content: '';
      position: absolute;
      background-color: #D8D8D8 !important;
      display: block;
      z-index: 1;
      top: auto;
      right: auto;
      bottom: 0;
      left: 0;
      width: 100%;
      height: 1.2px;
      transform-origin: 50% 100%;
      transform: scaleY(0.5);
    }
    .am-checkbox {
      width: 18px;
      height: 18px;
      vertical-align: sub;
    }
    .am-checkbox-inner {
      width: 18px;
      height: 18px;
    }
    .am-list-item .am-list-line .am-list-content {
      font-size: 14px;
      font-family: SourceHanSansCN-Regular, SourceHanSansCN;
      font-weight: 400;
      color: rgba(47, 47, 47, 1);
      white-space: normal;
    }
    .am-list-item .am-list-thumb:first-child {
      margin-right: 12px;
    }
    .am-list-item {
      padding-left: 12px;
    }
    .am-list-item .am-list-line {
      padding-right: 12px;
    }
  }
`;

const ConfirmButton = styled.div`// styled
  & {
    width: 100%;
    position: fixed;
    bottom: 0;
    height: 66px;
    padding: 12px 16px;
    background: #fff;
    z-index: 999;
    .am-button-primary.am-button-disabled {
      background: #D9D9D9;
    }
    .am-button::before {
      display: none;
    }
  }
`;
