import * as React from "react";
import { observer } from "mobx-react";
import styled from "styled-components";

@observer
export class ExcessIndication extends React.Component<any, any> {
  public render() {
    const { overtop, orderOrgList, activekey } = this.props;
    return (
      <StyledModal style={{ display: overtop ? "block" : "none" }}>
        <div style={{ height: orderOrgList.length <= 1 && "200px" }}>
          <div className="model-content-wrap">
            <p>超出金额提示</p>
            {
              orderOrgList.length > 1 ? <p>
                您好！经系统检测，您提交的金额超出待付金额，超出金额会在财务审核通过后自动退回余额账户，请选择退回门店，谢谢~
              </p> : <p>
                您好！经系统检测，您提交的金额超出待付金额，超出金额会在财务审核通过后自动退回余额账户，谢谢~
              </p>
            }
            <div className="org">
              {
                orderOrgList.length > 1 ? orderOrgList.map((org) => {
                  return (
                    <p onClick={() => this.props.changeOrg(org.code)} className={activekey === org.code ? "active" : ""}>
                      <i className="scmIconfont scm-icon-shop"/>
                      <span>{org.name}</span>
                    </p>
                  );
                }) : null
              }
            </div>
          </div>
          <div>
            <span onClick={this.props.hideQuestionHelp}>取消</span>
            <span onClick={this.props.batchSave}>确定</span>
          </div>
        </div>
      </StyledModal>
    );
  }
}

const StyledModal = styled.div`// styled
  & {
    width: 100%;
    height: ${document.documentElement.clientHeight}px;
    position: fixed;
    top: 0;
    left: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.65);
    z-index: 99;
    > div {
      z-index: 100;
      width: 270px;
      height: 346px;
      background: #FFFFFF;
      border-radius: 16px;
      position: relative;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      text-align: center;
      padding: 20px 0 52px;
      .model-content-wrap {
        width: 100%;
        height: 100%;
        overflow-y: scroll;
        padding: 0 20px;
        > p {
          margin-bottom: 10px;
        }
        > p:nth-of-type(1) {
          font-family: "SinhalaSangamMN";
          font-size: 16px;
          color: #303030;
          text-align: center;
        }
        > p:nth-of-type(2) {
          font-family: "PingFangSC-Regular";
          font-size: 13px;
          color: #666666;
          text-align: center;
        }
        > .org {
          height: auto;
          padding-bottom: 10px;
          //overflow-y: auto;
          > p {
            position: relative;
            margin-bottom: 10px;
            padding: 7px 15px;
            text-align: left;
            border: 1px solid #DDDDDD;
            border-radius: 4px;
            > span {
              margin-left: 10px;
              font-family: "PingFangSC-Regular";
              font-size: 13px;
              color: #666666;
              position: relative;
              top: -1px;
            }
          }
          .active {
            border: 1px solid #1890FF;
            .scm-icon-shop {
              color: #1890FF;
            }
            > span {
              color: #1890FF;
            }
          }
        }
      }
      > div:nth-of-type(2) {
        position: absolute;
        bottom: 0;
        left: 0;
        width: 270px;
        height: 52px;
        line-height: 50px;
        border-top: 1px solid #d8d8d8;
        background: #fff;
        border-radius: 0px 0px 16px 16px;
        //position: relative;
        > span {
          font-family: "PingFangSC-Regular";
          font-size: 16px;
          color: #333333;
          letter-spacing: 0;
          text-align: center;
          display: inline-block;
          width: 50%;
        }
        > span:nth-of-type(2) {
          color: #1890FF;
          border-left: 1px solid #DCDEE3;
        }
      }
      //:after {
      //  content: '';
      //  position: absolute;
      //  background-color: #D8D8D8 !important;
      //  display: block;
      //  z-index: 1;
      //  top: auto;
      //  right: auto;
      //  bottom: 0;
      //  left: 0;
      //  width: 100%;
      //  height: 1px;
      //  transform-origin: 50% 100%;
      //  transform: scaleY(0.5);
      //}
    }
  }
`;
