import { InputItem, Modal, Toast } from "antd-mobile";
import { observer } from "mobx-react";
import React from "react";
import styled from "styled-components";
import "./single-count.less";
import { autowired } from "@classes/ioc/ioc";
import { InitiatingReturnsMv } from "../../modules/initiating-returns/initiating-returns-mv";
import { remove } from "lodash";
import { InputNumberItemComponent } from "../input-number-item-component/input-number-item-component";

const alert = Modal.alert;

@observer
export class SingleCount extends React.Component<any, any> {

  @autowired(InitiatingReturnsMv)
  public initiatingReturnsMv: InitiatingReturnsMv;

  constructor(props) {
    super(props);
    this.state = {
      changeCount: 0,
      isChangeCount: false,
      showModal: false,
    };
  }

  public onBlur = () => {
    window.scroll(0, 0);
  }

  public bodyScroll = (e) => {
    e.preventDefault();
  }

  public closeModal = () => {
    document.removeEventListener("touchmove", this.bodyScroll, { passive: false });
    this.setState({
      showModal: false,
      isChangeCount: false,
    });
  }

  public openModal = () => {
    document.addEventListener("touchmove", this.bodyScroll, { passive: false });
    this.setState({
      showModal: true,
    });
  }

  public add = () => {
    document.removeEventListener("touchmove", this.bodyScroll, { passive: false });
    const { item, isErrorList } = this.props;
    const { isChangeCount, changeCount } = this.state;
    if (isChangeCount) {
      if (isErrorList) { // 失败页
        if (changeCount > item.inventoryQuantity) {
          Toast.info(`库存数量为${item.inventoryQuantity}`);
          return;
        }
        if (this.initiatingReturnsMv.errorProductSkuList.filter((sku) => sku.oid === item.oid).length > 0) {
          if (Number(changeCount) === 0) {
            remove(this.initiatingReturnsMv.errorProductSkuList, (sku) => sku.oid === item.oid);
          } else {
            this.initiatingReturnsMv.errorProductSkuList.filter((sku) => sku.oid === item.oid)[0].quantity = Number(changeCount);
          }
        }
      } else {
        if (this.initiatingReturnsMv.selectProductSkuList.filter((sku) => sku.oid === item.oid).length > 0) {
          console.log(Number(changeCount) === 0);
          if (Number(changeCount) === 0) {
            remove(this.initiatingReturnsMv.selectProductSkuList, (sku) => sku.oid === item.oid);
          } else {
            this.initiatingReturnsMv.selectProductSkuList.filter((sku) => sku.oid === item.oid)[0].quantity = Number(changeCount);
          }
        }
      }
      this.setState({
        showModal: false,
        isChangeCount: false,
      });
    } else {
      if (this.state.showModal) {
        if (this.initiatingReturnsMv.errorProductSkuList.filter((sku) => sku.oid === item.oid).length > 0) {
          this.initiatingReturnsMv.errorProductSkuList.filter((sku) => sku.oid === item.oid)[0].quantity = item.quantity;
        }
        this.setState({
          showModal: false,
          isChangeCount: false,
        });
      } else {
        if (this.props.isErrorList && item.quantity < item.inventoryQuantity) {
          this.props.add(item);
        } else {
          this.props.add(item);
        }
      }
    }
  }

  public reduce = () => {
    const { item } = this.props;
    this.props.reduce(item);
  }

  public changeCount = (value) => {
    this.setState({
      isChangeCount: true,
    }, () => {
      console.log(value);
      if (value === "") {
        this.setState({
          changeCount: null,
        });
      } else {
        this.setState({
          changeCount: Number(value),
        });
      }
    });
  }

  public render() {
    const { showModal, isChangeCount, changeCount } = this.state;
    const { item, isErrorList } = this.props;
    console.log(item);
    return (
      <Wrapper onClick={(e) => e.stopPropagation()}>
        <div>
          {
            item.quantity === 0 ? <i
                className="scmIconfont scm-icon-reduce"/> :
              <i
                className="scmIconfont scm-icon-reduce scm-icon-active"
                onClick={() => {
                  this.reduce();
                }}
              />
          }
          <span className="input">
                <InputItem
                  type="number"
                  value={item.quantity > 0 ? isErrorList && item.quantity > item.inventoryQuantity ? item.inventoryQuantity : item.quantity : String(0)}
                  editable={false}
                  disabled={false}
                  onChange={() => {
                    // do nothing
                  }}
                  onClick={() => this.openModal()}
                />
          </span>
          {
            isErrorList && item.quantity >= item.inventoryQuantity ?
              <i
                className="scmIconfont scm-icon-increase"
              /> :
              <i
                className="scmIconfont scm-icon-increase scm-icon-active"
                onClick={() => this.add()}
              />
          }
        </div>
        <Modal
          className="model"
          title={<div>请输入数量</div>}
          visible={showModal}
          transparent={true}
          maskClosable={false}
          footer={[
            { text: "取消", onPress: () => this.closeModal() },
            {
              text: "确定", onPress: () => {
                this.add();
              },
            },
          ]}
        >
          {/*<InputItem*/}
          {/*style={{ textAlign: "center" }}*/}
          {/*type="number"*/}
          {/*defaultValue={isChangeCount ? changeCount : item.quantity > 0 ? isErrorList && item.quantity > item.inventoryQuantity ? item.inventoryQuantity : item.quantity : String(0)}*/}
          {/*onChange={(value) => {*/}
          {/*this.changeCount(value);*/}
          {/*}}*/}
          {/*onBlur={this.onBlur}*/}
          {/*/>*/}
          <InputNumberItemComponent
            style={{ textAlign: "center" }}
            type="money"
            onChange={(value) => {
              this.changeCount(value);
            }}
            value={isChangeCount ? changeCount === null ? null : String(changeCount) : item.quantity > 0 ? isErrorList && item.quantity > item.inventoryQuantity ? String(item.inventoryQuantity) : String(item.quantity) : String(0)}
            onBlur={this.onBlur}
          />
        </Modal>
      </Wrapper>
    );
  }
}

const Wrapper = styled.div`// styled
  & {
    line-height: 30px;
    .scm-icon-reduce, .scm-icon-increase {
      font-size: 20px;
      color: #D8D8D8;
      position: relative;
      top: 5px;
    }
    .scm-icon-active {
      font-size: 20px;
      color: #307dcd;
    }
    .op {
      height: 24px;
      line-height: 20px;
      width: 24px;
      border-radius: 12px;
      display: inline-block;
      text-align: center;
      border: 1px solid transparent;
      font-size: 18px;
      color: #fff;
      background-color: #307dcd;
      position: relative;
      top: 2px;
    }
    .input {
      display: inline-block;
      width: 48px;
      height: 30px;
      line-height: 30px;
      margin: 0 10px;
      position: relative;
      .am-list-line {
        padding: 0;
      }
      .am-list-item.am-input-item {
        padding: 0;
        height: 31px;
        min-height: 30px;
      }
      .am-input-control input {
        text-align: center;
        height: 30px;
        line-height: 30px;
        border-radius: 3px;
        border: 0.5px solid #999;
        //font-family: PingFangSC-Regular;
        font-size: 14px;
        color: #307DCD;
      }
    }
    .am-button {
      &:before {
        width: 0 !important;
        height: 0 !important;
        border: none !important;
      }
    }
    .am-list-item .am-input-control input:disabled {
      color: #D2D2D2;
      border: 0.5px solid #D8D8D8;
    }
  }`;
