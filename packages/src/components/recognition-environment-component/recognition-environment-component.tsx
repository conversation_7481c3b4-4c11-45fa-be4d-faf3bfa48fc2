import * as React from "react";
import { observer } from "mobx-react";
import { WxUploadImg } from "../wx-upload-img/wx-upload-img";
import { ImagePickerUpload } from "../Image-picker/image-picker";
import { autowired } from "@classes/ioc/ioc";
import { WxUploadImgMv } from "../wx-upload-img/wx-upload-img-mv";
import { $AccountType } from "../../classes/const/$account-type";
import { $PaymentType } from "@classes/const/$payment-type";

declare let window: any;

@observer
export class RecognitionEnvironmentComponent extends React.Component<any, any> {

  @autowired(WxUploadImgMv)
  public wxUploadImgMv: WxUploadImgMv;

  public constructor(props) {
    super(props);
    this.state = {
      isAudit: false,
      picRule: null,
    };
  }

  public componentWillMount() {
    this.isWeixin();
  }

  public isWeixin = () => {
    const ua = window.navigator.userAgent.toLowerCase();
    this.wxUploadImgMv.weixinIsupload().then((data) => {
      this.setState({
        picRule: data.picRule,
      });
      if ((data && data.WeixinUploadCtrl === $AccountType.WEIXINUPLOADCTRL) && (ua.match(/MicroMessenger/i) == "micromessenger")) { // 有微信上传权限且为微信环境
        this.setState({
          isAudit: true,
        });
      } else {
        this.setState({
          isAudit: false,
        });
      }
    });
  }

  public render() {
    const { setPics, pics, limitLength, isExchange, picsStyle, isWxUploadingPicture } = this.props;
    const { isAudit, picRule } = this.state;
    return (
      <div>
        {isAudit ? <WxUploadImg limitLength={picRule === $PaymentType.PICRULE ? limitLength : 1} picsStyle={picsStyle} isWxUploadingPicture={isWxUploadingPicture} picRule={picRule}/> :
          <ImagePickerUpload pics={pics} setPics={setPics} limitLength={picRule === $PaymentType.PICRULE ? limitLength : 1} isExchange={isExchange} picRule={picRule}/>}
      </div>
    );
  }
}
