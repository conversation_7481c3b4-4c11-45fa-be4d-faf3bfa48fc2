import { observer } from "mobx-react";
import * as React from "react";
import styled from "styled-components";
import PopupContainer from "../../modal/popup-container";

@observer
export default class MemberPredictModal extends React.Component<any, any> {

  public render() {
    const { onClose, data, isMainSaleProduct } = this.props;
    const {
      predictedConsumingMembers,
      months,
      oldMemberStorageQuantity,
      store_month_nxzh_target,
      productOwnershipRate,
      newMemberSaleQuantity,
    } = data;
    const totalNewMember = store_month_nxzh_target * months;
    const productRate = productOwnershipRate * 100;
    return (<PopupContainer
      title={"会员量预估"}
      onClose={onClose}
    >
      <Wrapper>
        <Title>
          <span>{predictedConsumingMembers}</span>
          个会员如何预估？
        </Title>
        <LineItem>
          <div>
            <div>老客寄存消耗</div>
            <div>预计<span>{oldMemberStorageQuantity}</span>人</div>
          </div>
          <div>近180天内有到店，且产品寄存余量≧1次</div>
        </LineItem>
        {isMainSaleProduct && <LineItem>
          <div>
            <div>新客销售</div>
            <div>预计<span>{newMemberSaleQuantity}</span>人</div>
          </div>
          <div>{`每月目标纳新转化${store_month_nxzh_target}人，${months}个月共计转化${totalNewMember}人\n按照拥有率${productRate}%：${newMemberSaleQuantity}人=${totalNewMember}人×${productRate}%`}</div>
        </LineItem>}
      </Wrapper>
      <Footer>
        <Button onClick={onClose}>关闭</Button>
      </Footer>
    </PopupContainer>);
  }
}

const Wrapper = styled.div`// styled
  & {
    font-family: PingFang SC;
    background-color: #FFF;
    padding: 10px 12px 16px;
  }
`;
const Title = styled.div`// styled
  & {
    font-size: 18px;
    font-weight: 500;
    line-height: 27px;
    color: #333;

    > span:first-child {
      color: #437DF0;
      margin-right: 2px;
    }
  }
`;
const Footer = styled.div`// styled
  & {
    border: 1px solid #EEEEEE;
    padding: 6px 12px;
  }
`;
const Button = styled.div`// styled
  & {
    background: #EBEBEB;
    border-radius: 22.5px;
    height: 45px;
    line-height: 45px;
    text-align: center;
    width: 100%;
    font-size: 15px;
    font-weight: 500;
  }
`;
const LineItem = styled.div`// styled
  & {
    margin-top: 12px;
    border-radius: 4px;
    padding: 9.5px 8px 8px;
    background: #F0F4FC;


    > div:first-child {
      display: flex;
      align-items: center;
      color: #333;

      > div:first-child {
        font-size: 16px;
        font-weight: 500;
        line-height: 24px;
      }

      > div:last-child {
        margin-left: auto;
        font-size: 14px;
        font-weight: normal;
        line-height: 24px;
        color: #666;

        > span {
          font-weight: 500;
          margin: 0 2px;
          color: #437DF0;
        }
      }
    }

    > div:last-child {
      margin-top: 5.5px;
      font-size: 12px;
      font-weight: normal;
      line-height: 18px;
      white-space: pre-wrap;
      color: #666;
    }
  }
`;
