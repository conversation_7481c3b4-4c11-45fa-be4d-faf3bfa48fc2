import { observer } from "mobx-react";
import * as React from "react";
import styled from "styled-components";
import {
  useProductCateByData,
  useProductIndexColor,
  useProductIndexValue,
  useProductIndexValueColor,
} from "../../../hooks";
import PopupContainer from "../../modal/popup-container";
import { DetailItem } from "./detail-item";

@observer
export default class StoreInventoryModal extends React.Component<any, any> {
  public render() {
    const { onClose, data, isHomeProduct, unit } = this.props;
    const {
      otherStoreConsumeInventory: consumeInventory,
      otherStoreSaleInventory: saleInventory,
      otherStoreInventoryList,
    } = data;
    const {
      totalTableRc,
      inventoryTip,
    } = useProductCateByData(isHomeProduct, {
      totalTableRc: (<TotalTable>
        <TCol>
          <div className={"title"}>门店实物总库存</div>
          <div className={"value"}>{saleInventory?.realInventoryQuantityTotal}<span>{unit}</span></div>
          <div className={"sub-value"}>店均<span>{saleInventory?.averageInventoryQuantity}</span>{unit}</div>
        </TCol>
        <TCol>
          <div className={"title"}>月均销量合计</div>
          <div className={"value"}>{saleInventory?.monthSaleQuantity}<span>{unit}</span></div>
          <div className={"sub-value"}>店均<span>{saleInventory?.averageMonthSaleQuantity}</span>{unit}</div>
        </TCol>
        <TCol>
          <div className={"title"}>库存可销售月数</div>
          <div
            style={{ color: useProductIndexValueColor(saleInventory?.inventoryMonths, saleInventory?.inventoryTip) }}
            className={"value"}
          >
            {useProductIndexValue(saleInventory?.inventoryMonths)}
            <span>个月</span>
          </div>
        </TCol>
      </TotalTable>),
      inventoryTip: saleInventory?.inventoryTip,
    }, {
      totalTableRc: (<TotalTable>
        <TCol>
          <div className={"title"}>门店实物总库存</div>
          <div className={"value"}>{consumeInventory?.realInventoryQuantityTotal}<span>{unit}</span></div>
          <div className={"sub-value"}>店均<span>{consumeInventory?.averageInventoryQuantity}</span>{unit}</div>
        </TCol>
        <TCol>
          <div className={"title"}>月均消耗合计</div>
          <div className={"value"}>{consumeInventory?.monthConsumeQuantity}<span>{unit}</span></div>
          <div className={"sub-value"}>店均<span>{consumeInventory?.averageMonthConsumeQuantity}</span>{unit}</div>
        </TCol>
        <TCol>
          <div className={"title"}>预计可消耗</div>
          <div
            style={{ color: useProductIndexValueColor(consumeInventory?.inventoryMonths, consumeInventory?.inventoryTip) }}
            className={"value"}
          >
            {useProductIndexValue(consumeInventory?.inventoryMonths)}
            <span>个月</span>
          </div>
        </TCol>
      </TotalTable>),
      inventoryTip: consumeInventory?.inventoryTip,
    });
    return (<PopupContainer
      title={"同法人各门店库存"}
      onClose={onClose}
    >
      <Wrapper>
        <TotalItem>
          <div className={"title"}>
            <div>库存合计<span>（同区域同法人）</span></div>
            <div style={{ color: useProductIndexColor(inventoryTip) }}>{inventoryTip}</div>
          </div>
          {totalTableRc}
        </TotalItem>
        <Title>门店库存明细</Title>
        {(otherStoreInventoryList || []).map((i) => <DetailItem
          key={i.shopCode}
          data={i}
          unit={unit}
          isHomeProduct={isHomeProduct}
        />)}
      </Wrapper>
      <Footer>
        <Button onClick={onClose}>关闭</Button>
      </Footer>
    </PopupContainer>);
  }
}

const Wrapper = styled.div`// styled
  & {
    font-family: PingFang SC;
    background-color: #FFF;
    padding: 10px 12px 16px;
    max-height: 526px;
    overflow-y: scroll;
  }
`;
const TotalItem = styled.div`// styled
  & {
    padding: 12px;
    background: #E8F0FF;
    border-radius: 4px;

    > .title {
      display: flex;
      align-items: center;

      > div:first-child {
        font-size: 16px;
        font-weight: 500;
        line-height: 24px;
        color: #333;

        > span {
          font-weight: normal;
          font-size: 14px;
        }
      }

      > div:last-child {
        margin-left: auto;
        font-size: 14px;
        line-height: 24px;
        color: #437DF0;

      }
    }
  }
`;
const TotalTable = styled.div`// styled
  & {
    display: flex;
    justify-content: space-between;
    margin-top: 8px;
  }
`;
const TCol = styled.div`// styled
  & {
    > .title {
      font-size: 12px;
      font-weight: normal;
      line-height: 18px;
      color: #666666;
    }

    > .value {
      font-size: 18px;
      font-weight: 500;
      line-height: 27px;
      color: #333333;

      > span {
        margin-left: 2px;
        color: #666666;
        font-weight: normal;
        font-size: 14px;
      }
    }

    > .sub-value {
      font-size: 12px;
      font-weight: normal;
      line-height: 18px;
      color: #666;

      > span {
        color: #333;
        margin: 0 4px;
      }
    }
  }
`;
const Title = styled.div`// styled
  & {
    margin-top: 16px;
    font-size: 16px;
    font-weight: 500;
    line-height: 24px;
    color: #333;
  }
`;
const Footer = styled.div`// styled
  & {
    border: 1px solid #EEEEEE;
    padding: 6px 12px;
  }
`;
const Button = styled.div`// styled
  & {
    background: #EBEBEB;
    border-radius: 22.5px;
    height: 45px;
    line-height: 45px;
    text-align: center;
    width: 100%;
    font-size: 15px;
    font-weight: 500;
  }
`;
