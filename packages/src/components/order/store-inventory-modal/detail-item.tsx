import { includes } from "lodash";
import React from "react";
import styled from "styled-components";
import {
  useProductCateByData,
  useProductIndexColor,
  useProductIndexValue,
  useProductIndexValueColor,
} from "../../../hooks";

enum ShopTypeEnum {
  SHOP = "本店",
  TOTAL_WAREHOUSE = "总仓店",
  DIRECT_STORE = "直营店",
}

export function DetailItem(props: any) {
  const {
    data,
    isHomeProduct,
    unit,
  } = props;
  const {
    shopName,
    shopTypeName,
    inventoryTip,
    realInventoryQuantityTotal,
    monthConsumeQuantity,
    inventoryMonths,
    monthSaleQuantity,
    inventoryDays,
  } = data;
  const { tableRc } = useProductCateByData(isHomeProduct, {
    tableRc: (<TotalTable>
      <TCol>
        <div className={"title"}>门店实物库存</div>
        <div className={"value"}>{realInventoryQuantityTotal}<span>{unit}</span></div>
      </TCol>
      <TCol>
        <div className={"title"}>月均销量</div>
        <div className={"value"}>{monthSaleQuantity}<span>{unit}</span></div>
      </TCol>
      <TCol>
        <div className={"title"}>库存可销售月数</div>
        <div
          className={"value blue"}
          style={{ color: useProductIndexValueColor(inventoryMonths, inventoryTip) }}
        >
          {useProductIndexValue(inventoryMonths)}
          <span>个月</span>
        </div>
      </TCol>
    </TotalTable>),
  }, {
    tableRc: (<TotalTable>
      <TCol>
        <div className={"title"}>门店实物库存</div>
        <div className={"value"}>{realInventoryQuantityTotal}<span>{unit}</span></div>
      </TCol>
      <TCol>
        <div className={"title"}>门店月均消耗</div>
        <div className={"value"}>{monthConsumeQuantity}<span>{unit}</span></div>
      </TCol>
      <TCol>
        <div className={"title"}>预计可消耗</div>
        <div
          style={{ color: useProductIndexValueColor(inventoryMonths, inventoryTip) }}
          className={"value blue"}
        >
          {useProductIndexValue(inventoryMonths)}
          <span>个月</span>
        </div>
      </TCol>
    </TotalTable>),
  });
  const shopTypeColor = includes([ShopTypeEnum.DIRECT_STORE, ShopTypeEnum.TOTAL_WAREHOUSE], shopTypeName) ? "#FF3030" : "#437DF0";
  return (<Wrapper
  >
    <div className={"title"}>
      <div>
        {shopName}
        <ShopNameType
          style={{ background: shopTypeColor }}
        >
          {shopTypeName}
        </ShopNameType>
      </div>
      <div style={{ color: useProductIndexColor(inventoryTip) }}>
        {inventoryTip || ""}
      </div>
    </div>
    {tableRc}
  </Wrapper>);
}

const Wrapper = styled.div`// styled
  & {
    border-radius: 4px;
    padding: 12px;
    border: 1px solid #E8E8E8;
    margin-top: 12px;

    > .title {
      display: flex;
      align-items: center;

      > div:first-child {
        font-size: 16px;
        font-weight: 500;
        line-height: 24px;
        color: #333;
      }

      > div:last-child {
        margin-left: auto;
        font-size: 14px;
        line-height: 24px;
        color: #437DF0;
        flex-shrink: 0;

        &[data-red="true"] {
          color: #FF3030;
        }
      }
    }
  }
`;
const ShopNameType = styled.span`// styled
  & {
    font-weight: normal;
    font-size: 11px;
    border-radius: 9px;
    padding: 2.5px 6px;
    margin-left: 4px;
    color: #FFF;
    vertical-align: bottom;
    white-space: nowrap;
  }
`;
const TotalTable = styled.div`// styled
  & {
    display: flex;
    justify-content: space-between;
    margin-top: 8px;
  }
`;
const TCol = styled.div`// styled
  & {
    > .title {
      font-size: 12px;
      font-weight: normal;
      line-height: 18px;
      color: #666666;
    }

    > .value {
      font-size: 18px;
      font-weight: 500;
      line-height: 27px;
      color: #333333;

      > span {
        margin-left: 2px;
        color: #666666;
        font-weight: normal;
        font-size: 14px;
      }
    }

    > .blue {
      color: #437DF0;

      &[data-red="true"] {
        color: #FF3030;
      }
    }

  }
`;
