import { observer } from "mobx-react";
import { autowired } from "@classes/ioc/ioc";
import React from "react";
import styled from "styled-components";
import moment from "moment";
import { DatePickerView, InputItem } from "antd-mobile";
import { $AccountInfoMv } from "../../modules/new-account-info/new-account-info-mv";
import { $AccountType } from "@classes/const/$account-type";
import { $SearchTimeType } from "@classes/const/$search-time-type";

interface DatePickerProps {
  visible: boolean;
  type: "one" | "range";
  pageType?: string;
  dateType?: number;
  endDate?: string;
  startDate?: string;
  onCancel: () => void;
  onConfirm: (startDate?: Date, endDate?: Date, dateType?: number) => void;
}

const defaultStartDate = new Date("2010-01-01");
const hfpzDefaultStartDate = new Date("2025-01-16");

@observer
export class CustomMonthModal extends React.Component<DatePickerProps, any> {

  @autowired($AccountInfoMv)
  public $myMv: $AccountInfoMv;
  
  public constructor(props) {
    super(props);
    this.state = {
      inputNumber: true,
    };
  }

  componentWillReceiveProps(nextProps: Readonly<DatePickerProps>, nextContext: any): void {
    const startDate = nextProps.startDate;
    const endDate = nextProps.endDate;
    const dateType = nextProps.dateType;
    const selectedMonth = new Date(startDate);
    this.setState({ dateType, selectedMonth, startDate, endDate })
  }

  public onValueChange = (val) => {
    const { inputNumber } = this.state;
    if (inputNumber) {
      this.setState({ startDate: moment(val).format("YYYY-MM-DD") })
    } else {
      this.setState({ endDate: moment(val).format("YYYY-MM-DD") })
    }
  };

  public onChangeDateType = (val) => {
    if (val === $SearchTimeType.MONTH) {
      const { selectedMonth } = this.state;
      const { pageType } = this.props;
      const showMonth = new Date(selectedMonth);
      let startDate;
      if (pageType === $AccountType.RETURN_FREE_DISTRIBUTION) {
        if (moment(showMonth).isSame(moment("2025-01"), "month")) {
          startDate = moment().format("YYYY-01-16");
        } else {
          startDate = moment(showMonth).startOf("month").format("YYYY-MM-DD");
        }  
      } else {
        startDate = moment(showMonth).startOf("month").format("YYYY-MM-DD");
      }
      const endDate = moment(showMonth).endOf("month").format("YYYY-MM-DD");
      this.setState({ startDate, endDate, selectedMonth })
    }
    this.setState({ dateType: val });
  }

  public onChangeMonth = (val) => {
    const { pageType } = this.props;
    let startDate;
    if (pageType === $AccountType.RETURN_FREE_DISTRIBUTION) {
      if (moment(val).isSame(moment("2025-01"), "month")) {
        startDate = moment().format("YYYY-01-16");
      } else {
        startDate = moment(val).startOf("month").format("YYYY-MM-DD");
      }  
    } else {
      startDate = moment(val).startOf("month").format("YYYY-MM-DD");
    }
    const selectedMonth = new Date(val);
    const endDate = moment(val).endOf("month").format("YYYY-MM-DD");
    this.setState({ startDate, endDate, selectedMonth })
  }

  public render() {
    const { visible, onCancel, onConfirm, pageType } = this.props;
    const { startDate, endDate, inputNumber, dateType, selectedMonth } = this.state;
    return (
      <Wrapper>
        {
          visible && <div className={"model"} >
            <div className={"model-bg"} onClick={() => onCancel()} />
            <div className={"date-content"} onClick={(e) => { e.preventDefault() }}>
              <div className={"date-content-top"}>
                <div className="date-tab">
                  <div className={ dateType === $SearchTimeType.MONTH ? "selected" : "unselected" } onClick={() => this.onChangeDateType($SearchTimeType.MONTH)}>
                    月份选择
                  </div>
                  <div className={ dateType === $SearchTimeType.CUSTOM ? "selected" : "unselected" } onClick={() => this.onChangeDateType($SearchTimeType.CUSTOM)}>
                    自定义时间
                  </div>
                </div>
                <i onClick={() => onCancel()} className="scmIconfont scm-icon-guanbi" />
              </div>
              <div className={"date-content-center"}>
                {
                  dateType === $SearchTimeType.MONTH && <div>
                    <DatePickerView
                      minDate={pageType === $AccountType.RETURN_FREE_DISTRIBUTION ? hfpzDefaultStartDate : defaultStartDate }
                      onChange={this.onChangeMonth}
                      value={selectedMonth}
                      mode="month"
                    />
                  </div>
                }
                {
                  dateType === $SearchTimeType.CUSTOM && <div>
                    <div className={"date-content-input"}>
                      <InputItem
                        value={startDate}
                        prefixListCls={inputNumber ? "onFocusInput" : "am-list"}
                        onFocus={() => { this.setState({ inputNumber: true }); document.activeElement.blur() }}
                        placeholder="请选择开始时间"
                        editable
                      />
                      <span>
                        至
                      </span>
                      <InputItem
                        prefixListCls={inputNumber ? "am-list" : "onFocusInput"}
                        onFocus={() => { this.setState({ inputNumber: false }); document.activeElement.blur() }}
                        value={endDate}
                        placeholder="请选择结束时间"
                        editable
                      />
                    </div>
                    <DatePickerView
                      onChange={this.onValueChange}
                      minDate={pageType === $AccountType.RETURN_FREE_DISTRIBUTION ? hfpzDefaultStartDate : defaultStartDate}
                      value={inputNumber ? new Date(startDate) : new Date(endDate)}
                      mode="date"
                    />
                  </div>
                }
              </div>
              <div className="date-confirm" onClick={() => {
                onCancel();
                onConfirm(startDate, endDate, dateType)
              }}>确定</div>
            </div>
          </div>
        }
      </Wrapper>
    );
  }
}


const Wrapper = styled.div`// styled
  & {
    .model {
      position: fixed;
      top: 0;
      left: 0;
      bottom: 0;
      right: 0;
      z-index: 999;

      .model-bg {
        position: fixed;
        top: 0;
        left: 0;
        bottom: 0;
        right: 0;
        background-color: rgba(0, 0, 0, 0.4);
      }

      .date-content{
        position: absolute;
        height: 400px;
        bottom: 0;
        left: 0;
        right: 0;
        background: #FFFFFF;

        .date-confirm {
          color: #FFFFFF;
          background: #437DF0;
          border-radius: 22.5px;
          padding: 8px 12px;
          display: flex;
          align-items: center;
          justify-content: center;
          position: absolute;
          bottom: 20px;
          width: 80vw;
          margin-inline-start: 10vw;
        }
      }
        
      .date-content-top {
        width: 100%;
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding:12px 16px;

        .date-tab {
          display: flex;

          > div {
            margin: 0 10px;
          }

          .selected {
            color: #437DF0;
          }

          .selected::after {
            content: "";
            position: absolute;
            background-color: #437DF0 !important;
            display: block;
            z-index: 1;
            margin-top: 0.2rem;
            right: auto;
            margin-left: 0.2rem;
            width: 3rem;
            height: 3px;
            transform-origin: 50% 50%;
            transform: scaleY(0.5);
          }

          .unselected {
            color: #333333;
          }
        }

        > img {
          width: 20px;
          height: 20px;
        }
      }

      .date-content-center {
        padding: 10px 24px;
      }

      .date-content-input {
        display: flex;
        align-items: center;

        > span {
          width: 30px;
          text-align: center;
          display: inline-block;
        }
          
        .onFocusInput-item {
          height: 40px !important;
          line-height: 0px;
          background: #F7FAFF;
          border-radius: 4px;
          border: 1px solid #437DF0;
          padding-left: 15px;
          color: #437DF0;
          width: 150px;

          .am-list-line {
            position: relative;
            display: flex;
            flex: 1;
            align-self: stretch;
            padding-right: 15px;
            overflow: hidden;
          }

          input {
            font-size: 14px;
            appearance: none;
            width: 100%;
            padding: 6px 0;
            border: 0;
            background-color: transparent;
            line-height: 2;
            box-sizing: border-box;
          }
        }

        .am-list-item {
          height: 32px !important;
          line-height: 0px;
          background: #FFFFFF;
          border-radius: 4px;
          border: 1px solid #D8D8D8;
          width: 150px;
          input{
            font-size: 14px;
          }
        }
      }

      .am-picker{
        left: 0;
        width: 100%;
      }
    }
    
    .am-calendar .header {
      height:40px;
      line-height:40px;
      margin-bottom:10px;
    }

    .am-calendar .header .right {
      display:inline-block;
      width:56px;
      height:22px;
      right:12px;
      top:14px;
      font-size: 12px;
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: 400;
      color: #437DF0;
      border:1px solid #437DF0;
      border-radius:11px;
      text-align:center;
      line-height:22px;
    }

    .am-calendar .header .left {
      top: 13px;
      color:#666;
    }

    .am-calendar .content {
      top:180px;
      border-radius:20px 20px 0 0;
      height:calc(100% - 190px);
    }

    .am-calendar .date-picker {
      padding-bottom:0;
    }

    .am-calendar .single-month .month-title {
      font-size: 14px;
      font-family: PingFangSC-Medium, PingFang SC;
      font-weight: 500;
      color: #333333;
    }

    .am-calendar .confirm-panel {
      background-color:#fff;
      border-top:#eee 1px solid;
      .button {
        width:180px;
        border-radius:20px;
        background-color:#437DF0;
      }
      .button-disable {
        background-color:#ddd !important;
      }
    }

    .am-calendar .week-panel .cell {
      font-size: 12px;
      font-family: PingFangSC-Medium, PingFang SC;
      font-weight: 500;
      color: #333333;
    }

    .am-calendar .single-month .row .cell .date-wrapper .date-selected ,.am-calendar .single-month .row .cell .date-wrapper .date {
      font-size: 12px;
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: 400;
      color: #606266;
    }

    .am-calendar .single-month .row .cell .info {
      height:0;
    }

    .am-calendar .confirm-panel .info p {
      font-size: 12px;
      font-family: PingFangSC-Medium, PingFang SC;
      font-weight: 500;
      color: #333333;
    }

    .am-calendar .single-month .row .cell .date-wrapper .date-selected {
      background-color:#E9F0FF;
    }
  }
`;
