import { autowired } from "@classes/ioc/ioc";
import { Checkbox, Modal, Toast } from "antd-mobile";
import { toJS } from "mobx";
import { observer } from "mobx-react";
import * as React from "react";
import { withRouter } from "react-router";
import { $PromotionType } from "../../classes/const/$promotion-type";
import { SITE_PATH } from "../../modules/app";
import { $CartCouponMv } from "../../modules/cart-coupon/cart-coupon-mv";
import { $proChooseGiftMv } from "../../modules/promotion-choose-products/pro-choose-gift-mv";
import { $PromotionMatchMv } from "../../modules/promotion-match/promotion-match-mv";
import { $CartMv } from "../../modules/shop-cart/cart-mv";
import { ProductCount } from "../product-count/product-count";
import { $ProductMv } from "../product/product-mv";
import { sum } from "lodash";
import styled from "styled-components";
import { $PromotionActionInfo } from "../../classes/entity/$promotion-action-info";

const CheckboxItem = Checkbox.CheckboxItem;
const alert = Modal.alert;

export const TYPE_SELECT = "select";

@withRouter
@observer
export class PromotionMatchItem extends React.Component<any, any> {
  @autowired($ProductMv)
  public $ProductMv: $ProductMv;

  @autowired($CartMv)
  public $CartMv: $CartMv;

  @autowired($PromotionMatchMv)
  public $PromotionMatchMv: $PromotionMatchMv;

  @autowired($proChooseGiftMv)
  public $proChooseGiftMV: $proChooseGiftMv;

  @autowired($CartCouponMv)
  public $cartCouponMv: $CartCouponMv;

  public constructor(props) {
    super(props);
    this.state = {
      adjustItemList: [],
      isShowButton: false,
      selectIndex: 2,
      selectVirtualIndex: 2,
      isShowVirtualButton: false,
    };
  }

  public componentDidMount() {
    const { data } = this.props;
    const { adjustItemList } = data;
    // data.setAdjustItemList(adjustItemList);
    // console.log(data);
    /*this.setState({
      // adjustItemList: data.adjustItemList,
      isShowButton: data.adjustItemList.length <= 2,
    });*/
  }

  public onChecked = (checked) => {
    const { data, itemIndex } = this.props;
    this.props.onItemClick(TYPE_SELECT, data, checked, itemIndex);
  }

  public showButton = (info: $PromotionActionInfo) => {
    console.log(info.checked);
    if (info.checked === undefined) {
      info.checked = false;
    }
    if (info.checked) {// 收起
      this.setState({
        checked: info.checked,
        selectIndex: 2,
      });
      info.selectLength = 2;
      info.checked = false;
      console.log(info.selectLength);
      console.log(info.checked);
    } else { // 展开
      this.setState({
        checked: info.checked,
        selectIndex: info.adjustItemList ? info.adjustItemList.length : 0,
      }, () => {
        info.selectLength = info.adjustItemList ? info.adjustItemList.length : 0;
        info.checked = true;
        console.log(info.selectLength);
        console.log(info.checked);
      });
    }
  }

  public showVirtualButton = (goods) => {
    if (goods.checked) {// 收起
      this.setState({
        selectVirtualIndex: 2,
      });
      goods.selectLength = 2;
      goods.checked = false;
    } else { // 展开
      this.setState({
        selectVirtualIndex: goods.virtualSuitList ? goods.virtualSuitList.length : 0,
      });
      goods.selectLength = goods.virtualSuitList ? goods.virtualSuitList.length : 0;
      goods.checked = true;
    }
  }

  public showLabel = (actionType) => {
    let type = "";
    switch (actionType) {
      case "Deduct":
        type = "减";
        break;
      case "Discount":
        type = "折";
        break;
      case "Special":
        type = "特";
        break;
      case "Gift":
        type = "赠";
        break;
      case "PartFree":
        type = "免";
        break;
      case "MutiDiscount":
        type = "折";
        break;
      case "TradeUp":
        type = "换";
        break;
      default:
        type = "";
        break;
    }
    return type;
  }

  public showTab = (actionType) => {
    let type = "";
    switch (actionType) {
      case "Deduct":
        type = "减价";
        break;
      case "Discount":
        type = "折扣";
        break;
      case "Special":
        type = "特价";
        break;
      case "Gift":
        type = "赠送";
        break;
      case "PartFree":
        type = "多免一";
        break;
      case "MutiDiscount":
        type = "X折";
        break;
      case "TradeUp":
        type = "加价购";
        break;
      default:
        type = "";
        break;
    }
    return type;
  }

  public showColor = (actionType) => {
    let color = "";
    switch (actionType) {
      case "Deduct":
        color = "#8DBD00";
        break;
      case "Discount":
        color = "#F7B51C";
        break;
      case "Special":
        color = "#F25EA9";
        break;
      case "Gift":
        color = "#FF7564";
        break;
      case "PartFree":
        color = "#B385F5";
        break;
      case "MutiDiscount":
        color = "#F7B51C";
        break;
      case "TradeUp":
        color = "#FF8627";
        break;
      default:
        color = "";
        break;
    }
    return color;
  }

  public chooseGift = (promotionId, actionType, name, times, data, actionId) => {
    const { couponCheckedData } = this.$cartCouponMv;
    if (data.marketingType === $PromotionType.COUPON
      && JSON.stringify(couponCheckedData) === undefined
      && (!data.usedCouponList || data.usedCouponList && data.usedCouponList.length === 0)) {
      Toast.info("请先选择优惠券", 2);
    } else {
      // console.log(this.props.location.state.hasPromotion);
      this.$PromotionMatchMv.setPromotionIdInfo(promotionId, actionType, name, times, data, actionId);
      this.props.history.push({
        pathname: `/${SITE_PATH}/ProChooseGift`, state: {
          promotionId,
          actionType,
          actionId,
          name,
          times,
          promotionInfo: toJS(data),
          shopCartTotalAmount: this.props.location.state && this.props.location.state.shopCartTotalAmount,
          backSource: this.props.location.state ? this.props.location.state.backSource : null,
          prePageSource: this.props.location.state ? this.props.location.state.prePageSource : "promotion",
          hasPromotion: this.props.location.state ? this.props.location.state.hasPromotion : null,
          promotionList: toJS(this.$PromotionMatchMv.promotionList),
        },
      });
    }
  }

  public chooseCoupon = (data) => {
    this.$PromotionMatchMv.setCouponData(data);
    this.props.history.push({
      pathname: `/${SITE_PATH}/cart-coupon`, state: {
        couponData: toJS(data),
        shopCartTotalAmount: this.props.location.state && this.props.location.state.shopCartTotalAmount,
        backSource: this.props.location.state ? this.props.location.state.backSource : null,
        prePageSource: this.props.location.state ? this.props.location.state.prePageSource : "promotion",
        hasPromotion: this.props.location.state ? this.props.location.state.hasPromotion : null,
        promotionList: toJS(this.$PromotionMatchMv.promotionList),
      },
    });
  }

  public renderPromotionTitle = (data, checkable, actionInfoList) => {
    return (
      <PromotionTitle>
        <Check>
          {checkable && <CheckboxItem checked={data.checkFlag === $PromotionType.CHECKFLAG}
                                      onChange={(e) => this.onChecked(e.target.checked)}
                                      disabled={true}/>}
        </Check>
        <div>
          {
            actionInfoList && actionInfoList.length === 1 ? <span
              className="tip"
              style={{ backgroundColor: this.showColor(actionInfoList[0].actionType) }}>
                  {this.showLabel(actionInfoList[0].actionType)}</span> : <span className="activity">促销活动</span>
          }
          <span>{data.name ? data.name.length > 8 ? `${data.name.slice(0, 8)}...` : data.name : null}</span>
          <span>
                {
                  data.marketingType === $PromotionType.COUPON ? null :
                    <ProductCount data={data} isPromotion={true} noCart={} source={""}/>
                }
                </span>
        </div>
      </PromotionTitle>
    );
  }

  public renderActionInfoList = (actionInfoList, data, saveGiftObj, totalCount, promotionId) => {
    const { selectIndex, selectVirtualIndex, isShowVirtualButton } = this.state;
    return (
      actionInfoList && actionInfoList.length > 0 && actionInfoList.map((info, index1) => {
        return (<ActionInfo key={index1}>
            {
              info.actionType === $PromotionType.GIFT || info.actionType === $PromotionType.TRADEUP || info.marketingType === $PromotionType.COUPON ?
                <ActionInfoInter
                  style={{ height: info.interactFlag === $PromotionType.INTERACTFLAG ? "65px" : "35px" }}
                >
                <span
                  className="tip"
                  style={{ backgroundColor: this.showColor(info.actionType) }}>
                  {this.showLabel(info.actionType)}</span>
                  <span>
                  <p>{this.renderRuleType(info.actionType, info.actionSubType)}</p>
                    {
                      data.marketingType === $PromotionType.COUPON ?
                        <p>
                          {/*已选择 {data.usedCouponList ? data.usedCouponList.length : (data.times ? data.times : 0)} 张优惠券*/}
                        </p> :
                        info.interactFlag === $PromotionType.INTERACTFLAG ?
                          <p>已选择 {
                            info.adjustItemList ? info.adjustItemList.length > 0 ? sum(info.adjustItemList.map((adjustItem) => {
                              return adjustItem.quantity;
                            })) : saveGiftObj && promotionId === saveGiftObj.promotionId && saveGiftObj.actionId === info.actionId ?
                              totalCount : 0 : 0
                          } 件商品</p>
                          : null
                    }
                </span>
                  {/*{*/}
                  {/*data.checkFlag === $PromotionType.CHECKFLAG && data.marketingType === $PromotionType.COUPON ?*/}
                  {/*<span*/}
                  {/*className="count"*/}
                  {/*onClick={() => {*/}
                  {/*this.chooseCoupon(data);*/}
                  {/*}}*/}
                  {/*>*/}
                  {/*选择优惠券*/}
                  {/*</span> : null*/}
                  {/*}*/}
                  {
                    info.interactFlag === $PromotionType.INTERACTFLAG && data.checkFlag === $PromotionType.CHECKFLAG ?
                      <span
                        onClick={() => {
                          this.chooseGift(data.promotionId, info.actionType, data.name, data.times, data, info.actionId);
                        }}
                      >
                      {
                        info.actionType === $PromotionType.GIFT
                        && info.interactFlag === $PromotionType.INTERACTFLAG ? "选择赠品" :
                          info.adjustItemList ?
                            info.adjustItemList.length > 0 ? "重新换购" : "去换购"
                            : null
                      }
                    </span> : <div/>
                  }
                </ActionInfoInter> : <ActionInfoNo>
                      <span
                        className="tip"
                        style={{ backgroundColor: this.showColor(info.actionType) }}>
                  {this.showLabel(info.actionType)}</span>
                  <span>{this.renderRuleType(info.actionType, info.subRuleType)}</span>
                </ActionInfoNo>
            }
            {
              info.adjustItemList ? info.adjustItemList.length > 0 ?
                <PromotionList>
                  {
                    info.adjustItemList.slice(0, info.selectLength === undefined ? info.selectLength = 2 : info.selectLength).map((adjustItem, index) => {
                      return (
                        <PromotionItem key={index}>
                          <div>
                            <img
                              src={adjustItem.imageUrl ? adjustItem.imageUrl : "https://order.fwh1988.cn:14501/static-img/scm/ico-nopic.png"}
                              alt=""
                            />
                            {adjustItem.isActive === $PromotionType.ISACTIVE ?
                              <span>无效</span> : adjustItem.tags && adjustItem.tags.length > 0 && adjustItem.tags.indexOf($PromotionType.NOTSUITMULTIPLE) > -1 ?
                                <span className="noSuitMultiple">赠送未达标</span> : null}
                          </div>
                          <div>
                            <p>
                              <span>{adjustItem.name ? adjustItem.name.length > 18 ? `${adjustItem.name.slice(0, 10)}...` : adjustItem.name : null}</span>
                              <span>x{adjustItem.quantity}</span>
                            </p>
                            <p>
                              <span
                                className="check">{adjustItem.multiple > 1 ? `依次${adjustItem.multiple}倍增加` : null}</span>
                            </p>
                            <p>
                              {
                                adjustItem.orderPrice !== null && <span className={"redprice"}>
                                <span style={{ fontSize: "10px" }}>￥</span>
                                  {adjustItem.orderPrice}
                                  <span className={"blackprice"}>
                                  {adjustItem.unitOfMeasure && `/${adjustItem.unitOfMeasure}`}
                                  </span>
                                </span>
                              }
                              {
                                adjustItem.retailPrice !== null && <span className={"blackprice"}>
                                {adjustItem.orderPrice !== null && adjustItem.retailPrice !== null && " ("}零售 ￥{adjustItem.retailPrice}{adjustItem.orderPrice !== null && adjustItem.retailPrice !== null && ")"}{adjustItem.orderPrice === null && adjustItem.retailPrice !== null &&
                                <span className={"blackprice"}>
                                  {adjustItem.unitOfMeasure && `/${adjustItem.unitOfMeasure}`}
                                  </span>}
                                </span>
                              }
                            </p>
                            <p>
                            <span
                              style={{
                                border: `1px solid ${this.showColor(info.actionType)}`,
                                color: this.showColor(info.actionType),
                              }}
                            >{this.showTab(info.actionType)}</span>
                            </p>
                            {
                              adjustItem && adjustItem.virtualSuitList && adjustItem.virtualSuitList.length > 0 &&
                              <ShopDetail>
                                {
                                  adjustItem.virtualSuitList.slice(0, adjustItem.selectLength === undefined ? adjustItem.selectLength = 2 : adjustItem.selectLength).map((product, index1) => {
                                    return (
                                      <p key={index1}>
                                        {
                                          adjustItem.isActive !== $PromotionType.ISACTIVE && product.tags && product.tags.length > 0 && product.tags.indexOf($PromotionType.NOTSUITMULTIPLE) > -1 ?
                                            <span className="noSuitMultiple">赠送未达标</span> : <span>套装明细</span>
                                        }
                                        <span>{product.name ? product.name.length > 10 ? product.name.slice(0, 10) + "..." : product.name : null}</span>
                                        <span>x{product.quantity}</span>
                                      </p>
                                    );
                                  })
                                }
                                {
                                  adjustItem.virtualSuitList ? adjustItem.virtualSuitList.length > 2 ?
                                    <ShowVirtualButton onClick={() => this.showVirtualButton(adjustItem)}>
                                      {adjustItem.checked ? "点击收起" : "展开更多"}
                                      {adjustItem.checked ? <i className="scmIconfont scm-icon-jiantou-shang"/> :
                                        <i className="scmIconfont scm-icon-arrow-down"/>}
                                    </ShowVirtualButton> : null : null}
                              </ShopDetail>
                            }
                          </div>
                        </PromotionItem>
                      );
                    })
                  }
                  {
                    info.adjustItemList ? info.adjustItemList.length > 2 ?
                      <ShowButton onClick={() => this.showButton(info)}>
                        {info.checked === true ? "收起" : "展开更多+"}
                      </ShowButton> : null : null}
                </PromotionList>
                :
                null
                :
                null
            }
          </ActionInfo>
        );
      })
    );
  }

  public renderRuleType = (actionType, subRuleType) => {
    let title = "";
    if (subRuleType) {
      switch (subRuleType) {
        case "Range":
          title = "指定范围商品";
          break;
        case "Category":
          title = "指定分类商品";
          break;
        case "FixedGroup":
          title = "固定商品组合";
          break;
        case "OptionalGroup":
          title = "可选商品组合";
          break;
        case "FixedAmount":
          title = "固定金额的产品";
          break;
      }
    } else {
      switch (actionType) {
        case "Deduct":
          title = "减价";
          break;
        case "Discount":
          title = "折扣";
          break;
        case "Special":
          title = "特价";
          break;
        case "PartFree":
          title = "多免一";
          break;
        case "MutiDiscount":
          title = "第N件X折";
          break;
        case "TradeUp":
          title = "加价换购";
          break;
      }
    }
    return title;
  }

  public render() {
    const { checkable, data } = this.props;
    const { promotionId, actionInfoList } = data;
    const { totalCount, saveGiftObj } = this.$proChooseGiftMV;
    // const { couponCheckedData } = this.$cartCouponMv;

    return (
      <ItemWrapper>
        {
          this.renderPromotionTitle(data, checkable, actionInfoList)
        }
        {
          (actionInfoList && actionInfoList.length === 1) && (actionInfoList[0].actionType !== $PromotionType.GIFT) && (actionInfoList[0].actionType !== $PromotionType.TRADEUP) ? null :
            <InfoItem>
              <ActionInfoList>
                {
                  this.renderActionInfoList(actionInfoList, data, saveGiftObj, totalCount, promotionId)
                }
              </ActionInfoList>
            </InfoItem>
        }
      </ItemWrapper>
    );
  }
}

const ItemWrapper = styled.div`// styled
  & {
    display: block;
    height: auto;
    background: #fff;
    position: relative;
    margin-bottom: 10px;
    // overflow-x: hidden;
    overflow: hidden;
    .am-list-item {
      padding-left: 0;
    }
    .am-list-line {
      border-bottom: 0 !important;
    }
  }
`;

const ShowVirtualButton = styled.div`// styled
  & {
    font -size: 9px;
    font-family: "PingFangSC-Regular";
    font-weight: 400;
    color: rgba(117, 117, 117, 1);
    text-align: right;
    .scmIconfont {
      color: #999999;
      position: relative;
      top: 2px;
    }
  }
`;

const ShopDetail = styled.div`// styled
  & {
    position: relative;
    padding: 9px 0;
    margin-top: 8px;
    :before {
      content: '';
      position: absolute;
      background-color: #D8D8D8 !important;
      display: block;
      z-index: 1;
      top: 0;
      right: auto;
      bottom: 0;
      left: 0;
      width: 100%;
      height: 1.2px;
      transform-origin: 50% 100%;
      transform: scaleY(0.5);
    }
    > p {
      margin-bottom: 8px;
      > span {
        display: inline-block;
        height: 100%;
        line-height: 21px;
      }
      > span:nth-of-type(1) {
        border-radius: 11px;
        border: 1px solid rgba(255, 134, 39, 1);
        font-size: 8px;
        font-family: "PingFangSC-Regular";
        font-weight: 400;
        color: rgba(255, 134, 39, 1);
        margin-right: 16px;
        width: 48px;
        height: 16px;
        line-height: 16px;
        text-align: center;
      }
      > span:nth-of-type(2) {
        //width: 100px;
        text-align: left;
        font-size: 9px;
        font-family: "PingFangSC-Regular";
        font-weight: 400;
        color: rgba(117, 117, 117, 1);
        //margin-right: 5px;
      }
      > span:nth-of-type(3) {
        width: 40px;
        text-align: right;
        font-size: 9px;
        font-family: "PingFangSC-Regular";
        font-weight: 400;
        color: rgba(117, 117, 117, 1);
        float: right;
        //margin-right: 6px;
      }
      > span:nth-of-type(4) {
        width: calc(100% - 48px - 150px - 6px);
        text-align: right;
        font-size: 9px;
        font-family: "PingFangSC-Regular";
        font-weight: 400;
        color: rgba(117, 117, 117, 1);
      }
      > .noSuitMultiple {
        display: inline-block;
        width: 48px !important;
        height: 16px !important;
        background: rgba(0, 0, 0, 0.5) !important;
        border-radius: 11px !important;
        font-size: 8px !important;
        font-family: SourceHanSansCN-Normal !important;
        font-weight: 400 !important;
        color: rgba(255, 255, 255, 1) !important;
        border: none !important;
      }
    }
  }
`;

const PromotionTitle = styled.div`// styled
  & {
    width: 100%;
    height: 47px;
    box-sizing: border-box;
    position: relative;
    padding: 10px 0;
    :after {
      content: '';
      position: absolute;
      background-color: #D8D8D8 !important;
      display: block;
      z-index: 1;
      top: auto;
      right: auto;
      bottom: 0;
      left: 0;
      width: 100%;
      height: 1.2px;
      transform-origin: 50% 100%;
      transform: scaleY(0.5);
    }
    > div:nth-of-type(2) {
      display: inline-block;
      width: calc(100% - 50px);
      margin-left: 53px;
      vertical-align: text-top;
      > .tip {
        font-size: 10px;
        font-family: "SourceHanSansCN-Normal";
        font-weight: 400;
        color: rgba(255, 255, 255, 1);
        border-radius: 2px;
        display: inline-block;
        width: 16px;
        height: 16px;
        text-align: center;
        margin-right: 8px;
      }
      > span:last-child {
        position: absolute;
        top: 6px;
        right: 16px;
      }
    }
    .activity {
      font-size: 8px;
      font-family: "SourceHanSansCN-Normal";
      font-weight: 400;
      color: rgba(247, 181, 28, 1);
      border-radius: 2px;
      border: 1px solid rgba(247, 181, 28, 1);
      padding: 4px;
      margin-right: 8px;
    }
  }
`;

const PromotionList = styled.div`// styled
  & {
    width: 100%;
    display: block;
  }
`;

const ActionInfoNo = styled.div`// styled
  & {
    position: relative;
    height: 44px;
    padding-top: 10px;
    :after {
      content: '';
      position: absolute;
      background-color: #D8D8D8 !important;
      display: block;
      z-index: 1;
      top: auto;
      right: auto;
      bottom: 0;
      left: 0;
      width: 100%;
      height: 1.2px;
      transform-origin: 50% 100%;
      transform: scaleY(0.5);
    }
    > .tip {
      font-size: 10px;
      font-family: "SourceHanSansCN-Normal";
      font-weight: 400;
      color: rgba(255, 255, 255, 1);
      border-radius: 2px;
      display: inline-block;
      width: 16px;
      height: 16px;
      text-align: center;
      margin-right: 8px;
    }
    > span:nth-of-type(2) {
      font-size: 13px;
      font-family: "SourceHanSansCN-Normal";
      font-weight: 400;
      color: rgba(117, 117, 117, 1);
    }
  }
`;

const Check = styled.div`// styled
  & {
    position: absolute;
    top: 0;
    left: 0;
    z-index: 99;
  }
`;

const PromotionItem = styled.div`// styled
  & {
    margin-top: 10px;
    min-height: 90px;
    vertical-align: middle;
    position: relative;
    > div:first-child {
      width: 80px;
      height: 80px;
      position: absolute;
      top: 0;
      left: 0;
      border: none;
      > img {
        width: 80px;
        height: 80px;
        border-radius: 3px;
      }
      > span {
        display: block;
        width: 50px;
        height: 50px;
        line-height: 50px;
        text-align: center;
        border: 1px solid transparent;
        border-radius: 25px;
        background: rgba(0, 0, 0, 0.50);
        position: absolute;
        top: 40px;
        left: 40px;
        margin-top: -25px;
        margin-left: -25px;
        color: #fff;
      }
      > .noSuitMultiple {
        display: block;
        width: 80px;
        height: 16px;
        line-height: 18px;
        text-align: center;
        background: rgba(0, 0, 0, 0.5);
        border-radius: 0px 0px 4px 4px;
        position: absolute;
        top: 64px;
        left: 0;
        bottom: 0;
        font-size: 10px;
        font-family: SourceHanSansCN-Normal;
        font-weight: 400;
        color: rgba(255, 255, 255, 1);
        border: none;
        margin: 0;
      }
    }
    > div:last-child {
      display: inline-block;
      margin-left: 90px;
      width: calc(100% - 90px - 16px);
      > p {
        margin: 0;
      }
      > p:nth-of-type(1) {
        > span:first-child {
          color: #333;
          font-size: 12px;
        }
        > span:last-child {
          color: #999;
          position: absolute;
          top: 0;
          right: 16px;
          font-size: 12px;
        }
      }
      > p:nth-of-type(2) {
        > .check {
          font-size: 10px;
          color: #999999;
          margin-top: 4px;
        }
      }
      > p:nth-of-type(3) {
        .redprice {
          font-size: 14px;
          color: #ff3030;
          font-family: SourceHanSansCN-Normal, SourceHanSansCN;
        }
        .blackprice {
          font-size: 10px;
          color: #999999;
          font-family: SourceHanSansCN-Normal, SourceHanSansCN;
        }
      }
      > p:nth-of-type(4) {
        > span {
          display: inline-block;
          height: 14px;
          line-height: 10px;
          border-radius: 2px;
          text-align: center;
          font-size: 8px;
          padding: 2px 5px 5px 5px;
          box-sizing: border-box;
        }
      }
    }
  }
`;

const InfoItem = styled.div`// styled
  & {
    width: calc(100% - 53px);
    height: auto;
    box-sizing: border-box;
    margin-left: 53px;
    background: #fff;
  }
`;

const ActionInfoList = styled.div`// styled
  & {
    width: 100%;
    height: auto;
  }
`;

const ActionInfo = styled.div`// styled
  & {
    width: 100%;
    height: auto;
  }
`;

const ActionInfoInter = styled.div`// styled
  & {
    position: relative;
    height: 65px;
    :after {
      content: '';
      position: absolute;
      background-color: #D8D8D8 !important;
      display: block;
      z-index: 1;
      top: auto;
      right: auto;
      bottom: 0;
      left: 0;
      width: 100%;
      height: 1.2px;
      transform-origin: 50% 100%;
      transform: scaleY(0.5);
    }
    > .tip {
      font-size: 10px;
      font-family: "SourceHanSansCN-Normal";
      font-weight: 400;
      color: rgba(255, 255, 255, 1);
      border-radius: 2px;
      display: inline-block;
      width: 16px;
      height: 16px;
      text-align: center;
      margin-right: 8px;
      position: absolute;
      top: 10px;
    }
    > span:nth-of-type(2) {
      display: inline-block;
      position: absolute;
      top: 9px;
      left: 22px;
      > p {
        font-size: 13px;
        font-family: "SourceHanSansCN-Normal";
        font-weight: 400;
        color: rgba(117, 117, 117, 1);
        margin-bottom: 10px;
      }
    }
    > span:last-child {
      font-size: 12px;
      font-family: "SourceHanSansCN-Normal";
      font-weight: 400;
      color: rgba(48, 125, 205, 1);
      position: relative;
      top: 18px;
      left: 70%;
      padding: 8px 16px;
      :after {
        content: "  ";
        position: absolute;
        left: 0;
        top: 0px;
        z-index: 1;
        width: 200%;
        height: 200%;
        border: 1px solid rgba(48, 125, 205, 1);
        border-radius: 4px;
        padding: 8px 16px;
        -webkit-transform-origin: 0 0;
        transform-origin: 0 0;
        -webkit-transform: scale(.5, .5);
        transform: scale(.5, .5);
      }
    }
  }
`;

const ShowButton = styled.div`// styled
  & {
    color: #307DCD;
    width: 84px;
    height: 24px;
    text-align: center;
    border: 0.5px solid #D2D2D2;
    border-radius: 3px;
    padding: 2px 8px;
    font-size: 14px;
    box-sizing: border-box;
    margin: 10px auto;
  }
`;
