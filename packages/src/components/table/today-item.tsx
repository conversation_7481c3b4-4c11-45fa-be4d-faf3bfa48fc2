import { autowired } from "@classes/ioc/ioc";
import { Checkbox } from "antd-mobile";
import { observer } from "mobx-react";
import * as React from "react";
import { withRouter } from "react-router";
import styled from "styled-components";
import { SITE_PATH } from "../../modules/app";
import { $ProductListMv } from "../../modules/product-list/$product-list-mv";
import { $CartMv } from "../../modules/shop-cart/cart-mv";
import { ProductCount } from "../product-count/product-count";
import { $ProductMv } from "../product/product-mv";
import { $CartType } from "../../classes/const/$cart-type";

const CheckboxItem = Checkbox.CheckboxItem;

export const TYPE_SELECT = "select";

@withRouter
@observer
export class TodayItem extends React.Component<any, any> {
  @autowired($ProductMv)
  public $ProductMv: $ProductMv;

  @autowired($CartMv)
  public $CartMv: $CartMv;

  @autowired($ProductListMv)
  public $productListMv: $ProductListMv;

  public constructor(props) {
    super(props);
    this.state = {
      isShowButton: false,
      selectIndex: 2,
    };
  }

  public onChecked = (checked) => {
    const { data } = this.props;
    this.props.onItemClick(TYPE_SELECT, data, checked);
  }

  public onClick = (data, isDetail) => {
    if (isDetail) {
      return false;
    }
    this.$ProductMv.setProduct(data);
    const { pageSkip } = this.props;
    pageSkip && pageSkip();
    setTimeout(() => {
      this.props.history.push({ pathname: `/${SITE_PATH}/product-detail` });
    }, 50);
  }

  public toDetail = (data) => {
    this.$productListMv.setDetailObject(data);
    const { pageSkip } = this.props;
    pageSkip && pageSkip();
    // this.props.history.push({ pathname: `/${SITE_PATH}/commodity-details/${data.productSkuId}` });
    setTimeout(() => {
      if (data.skuType === $CartType.VIRTUALSUIT) {
        window.location.href = `/${SITE_PATH}/virtualsuit-detail/${data.productSkuId}`;
      } else {
        window.location.href = `/${SITE_PATH}/commodity-details/${data.productSkuId}`;
      }
    }, 50);
  }

  public showButton = () => {
    const { data } = this.props;
    const { isShowButton } = this.state;
    if (isShowButton) {// 收起
      this.setState({
        isShowButton: false,
        selectIndex: 2,
      });
    } else { // 展开
      this.setState({
        isShowButton: true,
        selectIndex: data.virtualSuitList ? data.virtualSuitList.length : 0,
      });
    }
  }

  public render() {
    const { data, noCart, hideCount, pricePermission, retailPriceViewPermission, orderSchemeType } = this.props;
    const { isShowButton, selectIndex } = this.state;
    return (
      <div>
        <ItemWrapper style={hideCount ? { marginBottom: "0px" } : {}}>
          <div className="product-img" onClick={() => this.toDetail(data)}>
            {
              data.imageUrl ? <img src={data.imageUrl} alt=""/> :
                <img src="https://order.fwh1988.cn:14501/static-img/scm/ico-nopic.png" alt=""/>}
          </div>
          <div className="product-item-text">
            <h5 onClick={() => this.toDetail(data)}>{data.name}</h5>
            <div className="check" onClick={() => this.toDetail(data)}>
              {data.startQuantity ? `最少购买数量为${data.startQuantity}` : null}
              {data.startQuantity && data.multiple ? "," : null}
              {data.multiple ? `依次${data.multiple}倍增加` : null}
            </div>
            <div className="price" onClick={() => this.toDetail(data)}>
              {
                (pricePermission === $CartType.ORDERPRICEVIEWPERMISSION) && (data.orderPrice !== null) ?
                  <span className="redprice">
                    <span style={{ color: "#FF3030", fontSize: "10px" }}>￥</span>
                    {data.orderPrice}
                    <small className="blackprice">{data.unitOfMeasure && `/${data.unitOfMeasure}`}</small></span> : null
              }
              {
                (retailPriceViewPermission === $CartType.RETAILPRICEVIEWPERMISSION) && (data.retailPrice !== null) ?
                  <span className="blackprice">
                  {(((pricePermission === $CartType.ORDERPRICEVIEWPERMISSION) && (data.orderPrice !== null))) && (data.retailPrice !== null) ? " (" : null}零售 ￥{data.retailPrice}{(((pricePermission === $CartType.ORDERPRICEVIEWPERMISSION) && (data.orderPrice !== null))) && (data.retailPrice !== null) ? ")" : null}{(((pricePermission !== $CartType.ORDERPRICEVIEWPERMISSION) || (data.orderPrice === null))) && (data.retailPrice !== null) ?
                    <small className="blackprice">{data.unitOfMeasure && `/${data.unitOfMeasure}`}</small> : null}
                  </span> : <span/>
              }
            </div>
            <div className="check" onClick={() => this.toDetail(data)}>{data.stockStatusDesc}</div>
            <div style={{ position: "relative" }}>
              <div
                className="choose-num"
              >
                <ProductCount
                  data={data}
                  noCart={noCart}
                  orderSchemeType={orderSchemeType}
                  source={""}
                  fromWhere={"product"}
                />
              </div>
            </div>
            {
              data.skuType === $CartType.VIRTUALSUIT ? <ShopDetail>
                {
                  data.virtualSuitList.slice(0, selectIndex).map((product, index) => {
                    return (
                      <p key={index}>
                        <span>套装明细</span>
                        <span>{product.name ? product.name.length > 14 ? product.name.slice(0, 14) + "..." : product.name : null}</span>
                        <span>x{product.quantity}</span>
                      </p>
                    );
                  })
                }
                {
                  data.virtualSuitList ? data.virtualSuitList.length > 2 ?
                    <ShowButton onClick={this.showButton}>
                      {isShowButton ? "点击收起" : "展开更多"}
                      {isShowButton ? <i className="scmIconfont scm-icon-jiantou-shang"/> :
                        <i className="scmIconfont scm-icon-arrow-down"/>}
                    </ShowButton> : null : null}
              </ShopDetail> : null
            }
          </div>
        </ItemWrapper>
      </div>
    );
  }
}

const ItemWrapper = styled.div`// styled
  & {
    padding-left: 90px;
    position: relative;
    margin-bottom: 20px;
    min-height: 80px;
    &:first-child {
      margin-top: 12px;
    }
    .hide {
      display: none;
    }
    .product-img {
      width: 80px;
      height: 80px;
      position: absolute;
      left: 0;
      top: 0;
      img {
        width: 100%;
        height: 100%;
        border-radius: 3px;
      }
      .img-tags {
        position: absolute;
        bottom: -2.5px;
        right: 0;
        span {
          font-size: 8px;
          color: #FFFFFF;
          background: #ff7564;
          margin-left: 5px;
          padding: 0px 2px;
        }
      }
    }
    .product-item-text {
      width: 100%;
      h5 {
        color: #202020;
        overflow: hidden;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 2;
        max-height: 32px;
        line-height: 16px;
        margin: 0;
      }
      .check {
        font-size: 10px;
        color: #999999;
        margin-top: 4px;
      }
      .price {
        height: 14px;
        line-height: 14px;
        margin-top: 4px;
        .redprice {
          font-size: 14px;
          color: #ff3030;
          font-family: SourceHanSansCN-Normal, SourceHanSansCN;
        }
        .blackprice {
          font-size: 10px;
          color: #999999;
          font-family: SourceHanSansCN-Normal, SourceHanSansCN;
        }
      }
      .small {
        color: #ff3030;
      }
      .tags {
        height: 18px;
        line-height: 18px;
        span {
          font-size: 8px;
          padding: 0px 4px;
          color: #FF7362;
          border: 0.5px solid #FF7362;
          border-radius: 2px;
          margin-right: 10px;
        }
      }
      p {
        height: 22px;
        line-height: 22px;
        color: #999999;
        font-size: 10px;
        margin: 0;
        padding: 0;
      }
      .choose-num {
        width: 100%;
        text-align: right;
        position: absolute;
        bottom: 0;
        //right: 0;
      }
    }
  }
`;

const ShopDetail = styled.div`// styled
  & {
    position: relative;
    padding: 9px 0;
    margin-top: 8px;
    :before {
      content: '';
      position: absolute;
      background-color: #D8D8D8 !important;
      display: block;
      z-index: 1;
      top: 0;
      right: auto;
      bottom: 0;
      left: 0;
      width: 100%;
      height: 1.2px;
      transform-origin: 50% 100%;
      transform: scaleY(0.5);
    }
    > p {
      margin-bottom: 8px;
      > span:nth-of-type(1) {
        border-radius: 11px;
        border: 1px solid rgba(255, 134, 39, 1);
        font-size: 8px;
        font-family: "PingFangSC-Regular";
        font-weight: 400;
        color: rgba(255, 134, 39, 1);
        margin-right: 16px;
        display: inline-block;
        width: 48px;
        height: 16px;
        line-height: 16px;
        text-align: center;
      }
      > span:nth-of-type(2) {
        display: inline-block;
        width: 130px;
        text-align: left;
        font-size: 9px;
        font-family: "PingFangSC-Regular";
        font-weight: 400;
        color: rgba(117, 117, 117, 1);
        margin-right: 5px;
      }
      > span:nth-of-type(3) {
        display: inline-block;
        width: calc(100% - 48px - 160px);
        text-align: right;
        font-size: 9px;
        font-family: "PingFangSC-Regular";
        font-weight: 400;
        color: rgba(117, 117, 117, 1);
      }
    }
  }
`;

const ShowButton = styled.div`// styled
  & {
    font-size: 9px;
    font-family: "PingFangSC-Regular";
    font-weight: 400;
    color: rgba(117, 117, 117, 1);
    text-align: right;
    .scmIconfont {
      color: #999999;
      position: relative;
      top: 2px;
    }
  }
`;
