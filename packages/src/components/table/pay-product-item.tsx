import { autowired } from "@classes/ioc/ioc";
import { Checkbox } from "antd-mobile";
import { observer } from "mobx-react";
import * as React from "react";
import { withRouter } from "react-router";
import styled from "styled-components";
import { SITE_PATH } from "../../modules/app";
import { $CartMv } from "../../modules/shop-cart/cart-mv";
import { ProductCount } from "../product-count/product-count";
import { $ProductMv } from "../product/product-mv";
import { $CartType } from "../../classes/const/$cart-type";

const CheckboxItem = Checkbox.CheckboxItem;

export const TYPE_SELECT = "select";

@withRouter
@observer
export class PayProductItem extends React.Component<any, any> {
  @autowired($ProductMv)
  public $ProductMv: $ProductMv;

  @autowired($CartMv)
  public $CartMv: $CartMv;

  public constructor(props) {
    super(props);
    this.state = {
      isShowButton: false,
      selectIndex: 2,
    };
  }

  public onChecked = (checked) => {
    const { data } = this.props;
    this.props.onItemClick(TYPE_SELECT, data, checked);
  }

  public onClick = (data, isDetail) => {
    if (isDetail) {
      return false;
    }
    this.$ProductMv.setProduct(data);
    this.props.history.push({ pathname: `/${SITE_PATH}/product-detail` });
  }

  public showSaleType = (saleType) => {
    // console.log(saleType);
    let type = "";
    switch (saleType) {
      case "GIVE":
        type = "「赠品」";
        break;
      case "TradeUp":
        type = "「加价购」";
        break;
    }
    return type;
  }

  public showButton = () => {
    const { data } = this.props;
    const { isShowButton } = this.state;
    if (isShowButton) {// 收起
      this.setState({
        isShowButton: false,
        selectIndex: 2,
      });
    } else { // 展开
      this.setState({
        isShowButton: true,
        selectIndex: data.virtualSuitList ? data.virtualSuitList.length : 0,
      });
    }
  }

  public render() {
    const { data, noCart, hideCount, pricePermission, retailPriceViewPermission } = this.props;
    const { isShowButton, selectIndex } = this.state;
    return (
      <ItemWrapper
        style={{ height: data && data.virtualSuitList && data.virtualSuitList.length > 0 ? "auto" : "100px" }}>
        <div className="product-img">
          {
            data.imageUrl ? <img src={data.imageUrl} alt="" /> :
              <img src="https://order.fwh1988.cn:14501/static-img/scm/ico-nopic.png" alt="" />}
          {
            hideCount ? null :
              <div className="img-tags">
                {
                  data.productTagList.map((v, i) => {
                    return <span key={i}>{v}</span>;
                  })
                }
              </div>
          }
        </div>
        <div className="product-item-text">
          <div>
            <h5>
              {
                this.showSaleType(data.saleType)
              }
              {data.name}
            </h5>
            <span>x{data.quantity}</span>
          </div>
          <div className="price">
            {
              data.deductionAmountFlag && <span className="redprice">
                <span style={{ color: "#FFF", border: "1px solid #417BEE", background: "#417BEE", padding: "1px 8px", borderRadius: "4px", fontSize: "11px" }}>额度值</span>
                <span style={{ fontSize: "10px" }}>￥</span>{Number(data.deductionAmount / data.quantity).toFixed(2)}
                <small className="blackprice">
                  {data.unitOfMeasure && `/${data.unitOfMeasure}`}
                </small>
              </span>
            }
            {
              (pricePermission === $CartType.ORDERPRICEVIEWPERMISSION && data.orderPrice !== null && data.orderPriceType === 'DEFAULT' && !data.deductionAmountFlag) ?
                <span className="redprice">
                  <span style={{ color: "#FFA630", border: "1px solid #FFA630", background: "rgba(255, 166, 48, 0.10)", padding: "1px 8px", borderRadius: "24px", fontSize: "11px" }}>常规</span>
                  <span style={{ fontSize: "10px" }}>￥</span>{data.orderPrice}
                  <small className="blackprice">
                    {data.unitOfMeasure && `/${data.unitOfMeasure}`}
                  </small>
                </span> : null
            }
            {
              (pricePermission === $CartType.ORDERPRICEVIEWPERMISSION && data.orderPriceType === 'DISCOUNT' && data.orderPrice > 0 && !data.deductionAmountFlag) && <p>
                <span className="redprice">
                  <span style={{ color: "#FF3636", border: "1px solid #FF3636", background: "rgba(255, 66, 49, 0.10)", padding: "1px 8px", borderRadius: "24px", fontSize: "11px" }}>优惠</span>
                  <span style={{ fontSize: "10px" }}>￥</span>{data.orderPrice}
                  <small className="blackprice">
                    {data.unitOfMeasure && `/${data.unitOfMeasure}`}
                  </small>
                </span>
              </p>
            }
            {
              (retailPriceViewPermission === $CartType.RETAILPRICEVIEWPERMISSION) && (data.retailPrice !== null) ?
                <span className="blackprice">
                  {((pricePermission === $CartType.ORDERPRICEVIEWPERMISSION) && (data.orderPrice !== null)) ? " (" : null}零售 ￥{data.retailPrice}{((pricePermission === $CartType.ORDERPRICEVIEWPERMISSION) && (data.orderPrice !== null)) ? ")" : null}{((pricePermission !== $CartType.ORDERPRICEVIEWPERMISSION) || (data.orderPrice === null)) ?
                    <small className="blackprice">{data.unitOfMeasure && `/${data.unitOfMeasure}`}</small> : null}
                </span> : <span />
            }
          </div>
          {
            hideCount ? null :
              <div>
                <div className="tags">
                  {

                    data.promotionTagList.map((v, i) => {
                      return <span key={i}>{v}</span>;
                    })
                  }
                </div>
                {/*<p>{data.stockStatusDesc}</p>*/}
                <div className="choose-num">
                  <ProductCount data={data} noCart={noCart} source={""} />
                </div>
              </div>
          }
          {
            data && data.virtualSuitList && data.virtualSuitList.length > 0 && <ShopDetail>
              {
                data.virtualSuitList.slice(0, selectIndex).map((product, index) => {
                  return (
                    <p key={index}>
                      <span>套装明细</span>
                      <span>{product.name ? product.name.length > 12 ? product.name.slice(0, 12) + "..." : product.name : null}</span>
                      <span>x{product.quantity}</span>
                    </p>
                  );
                })
              }
              {
                data.virtualSuitList ? data.virtualSuitList.length > 2 ?
                  <ShowButton onClick={this.showButton}>
                    {isShowButton ? "点击收起" : "展开更多"}
                    {isShowButton ? <i className="scmIconfont scm-icon-jiantou-shang" /> :
                      <i className="scmIconfont scm-icon-arrow-down" />}
                  </ShowButton> : null : null}
            </ShopDetail>
          }
        </div>
      </ItemWrapper>
    );
  }
}

const ItemWrapper = styled.div`// styled
  & {
    padding-left: 106px;
    position: relative;
    margin-bottom: 0px;
    .hide {
      display: none;
    }
    .product-img {
      width: 80px;
      height: 80px;
      position: absolute;
      left: 16px;
      top: 16px;
      img {
        width: 100%;
        height: 100%;
      }
      .img-tags {
        position: absolute;
        bottom: -2.5px;
        right: 0;
        span {
          font-size: 8px;
          color: #FFFFFF;
          background: #ff7564;
          margin-left: 5px;
          padding: 0px 2px;
        }
      }
    }
    .product-item-text {
      //width: calc(100% - 90px);
      //white-space: normal !important;
      padding-top: 16px;
      > div:nth-of-type(1) {
        position: relative;
        > span {
          position: absolute;
          right: 15px;
          top: -5px;
          color: rgba(117, 117, 117, 1);
          font-size: 9px;
        }
      }
      h5 {
        color: #333333;
        overflow: hidden;
        max-height: 32px;
        line-height: 16px;
        font-size: 12px;
        margin: 0;
      }
      .price {
        margin-top: 8px;
        line-height: 16px;
        .redprice {
          font-size: 10px;
          color: #ff3030;
        }
        .blackprice {
          font-size: 8px;
          color: #3F3F3F;
        }
      }
      .small {
        color: #ff3030;
      }
      .tags {
        height: 18px;
        line-height: 18px;
        span {
          font-size: 8px;
          padding: 0px 4px;
          color: #FF7362;
          border: 0.5px solid #FF7362;
          border-radius: 2px;
          margin-right: 10px;
        }
      }
      p {
        height: 22px;
        line-height: 22px;
        color: #999999;
        font-size: 10px;
        margin: 0;
        padding: 0;
      }
      .choose-num {
        width: 100%;
        text-align: right;
      }
    }
  }
`;

const ShopDetail = styled.div`// styled
  & {
    position: relative;
    padding: 9px 0;
    margin-top: 8px;
    padding-right: 15px;
    :before {
      content: '';
      position: absolute;
      background-color: #D8D8D8 !important;
      display: block;
      z-index: 1;
      top: 0;
      right: auto;
      bottom: 0;
      left: 0;
      width: 100%;
      height: 1.2px;
      transform-origin: 50% 100%;
      transform: scaleY(0.5);
    }
    > p {
      margin-bottom: 8px;
      > span:nth-of-type(1) {
        border-radius: 11px;
        border: 1px solid rgba(255, 134, 39, 1);
        font-size: 8px;
        font-family: "PingFangSC-Regular";
        font-weight: 400;
        color: rgba(255, 134, 39, 1);
        margin-right: 16px;
        display: inline-block;
        width: 48px;
        height: 16px;
        line-height: 16px;
        text-align: center;
      }
      > span:nth-of-type(2) {
        display: inline-block;
        width: 140px;
        text-align: left;
        font-size: 9px;
        font-family: "PingFangSC-Regular";
        font-weight: 400;
        color: rgba(117, 117, 117, 1);
        margin-right: 5px;
      }
      > span:nth-of-type(3) {
        display: inline-block;
        width: calc(100% - 48px - 150px - 16px);
        text-align: right;
        font-size: 9px;
        font-family: "PingFangSC-Regular";
        font-weight: 400;
        color: rgba(117, 117, 117, 1);
      }
    }
  }
`;

const ShowButton = styled.div`// styled
  & {
    font-size: 9px;
    font-family: "PingFangSC-Regular";
    font-weight: 400;
    color: rgba(117, 117, 117, 1);
    text-align: right;
    .scmIconfont {
      color: #999999;
      position: relative;
      top: 2px;
    }
  }
`;
