import { autowired } from "@classes/ioc/ioc";
import { Checkbox } from "antd-mobile";
import { observer } from "mobx-react";
import * as React from "react";
import { withRouter } from "react-router";
import styled from "styled-components";
import { SITE_PATH } from "../../modules/app";
import { $proChooseGiftMv } from "../../modules/promotion-choose-products/pro-choose-gift-mv";
import { $CartMv } from "../../modules/shop-cart/cart-mv";
import { GiftCount } from "../gift-count/gift-count";
import { $ProductMv } from "../product/product-mv";
import { $PromotionType } from "../../classes/const/$promotion-type";

const CheckboxItem = Checkbox.CheckboxItem;

export const TYPE_SELECT = "select";

@withRouter
@observer
export class GiftItem extends React.Component<any, any> {
  @autowired($ProductMv)
  public $ProductMv: $ProductMv;

  @autowired($CartMv)
  public $CartMv: $CartMv;

  @autowired($proChooseGiftMv)
  public $proChooseGiftMv: $proChooseGiftMv;

  public constructor(props) {
    super(props);
    this.state = {
      isShowButton: false,
      selectIndex: 2,
    };
  }

  public onChecked = (pindex, index) => {
    this.$proChooseGiftMv.checkGift(pindex, index);
  }

  public onClick = (data, isDetail) => {
    if (isDetail) {
      return false;
    }
    this.$ProductMv.setProduct(data);
    this.props.history.push({ pathname: `/${SITE_PATH}/product-detail` });
  }

  public showButton = () => {
    const { data } = this.props;
    const { isShowButton } = this.state;
    if (isShowButton) {// 收起
      this.setState({
        isShowButton: false,
        selectIndex: 2,
      });
    } else { // 展开
      this.setState({
        isShowButton: true,
        selectIndex: data.virtualSuitList ? data.virtualSuitList.length : 0,
      });
    }
  }

  public render() {
    const { data, pindex, index, orderSchemeType } = this.props;
    const { isShowButton, selectIndex } = this.state;
    return (
      <ItemWrapper>
        <CheckboxItem onChange={() => this.onChecked(pindex, index)}
                      checked={data.checkFlag === "Y" ? true : false}></CheckboxItem>
        <div className="product-img">
          {
            data.imageUrl ? <img src={data.imageUrl} alt=""/> :
              <img src="https://order.fwh1988.cn:14501/static-img/scm/ico-nopic.png" alt=""/>}
          {
            data.isActive === "Y" ? data.isActive !== $PromotionType.ISACTIVE && data.tags && data.tags.length > 0 && data.tags.indexOf($PromotionType.NOTSUITMULTIPLE) > -1 ?
              <div className="noSuitMultiple">赠送未达标</div> : null : <div className="nothing">已换完</div>
          }
        </div>
        <div className="product-item-text">
          <h5>{data.name}</h5>
          <div className="check">
            {data.multiple > 1 ? `依次${data.multiple}倍增加` : null}
          </div>
          <div className="price">
            {
              data.orderPrice !== null && <span className={"redprice"}>
                                <span style={{ fontSize: "10px" }}>￥</span>
                {data.orderPrice}
                <span className={"blackprice"}>
                                  {data.unitOfMeasure && `/${data.unitOfMeasure}`}
                                  </span>
                                </span>
            }
            {
              data.retailPrice !== null && <span className={"blackprice"}>
                                {data.orderPrice !== null && data.retailPrice !== null && " ("}零售 ￥{data.retailPrice}{data.orderPrice !== null && data.retailPrice !== null && ")"}{data.orderPrice === null && data.retailPrice !== null &&
              <span className={"blackprice"}>
                                  {data.unitOfMeasure && `/${data.unitOfMeasure}`}
                                  </span>}
                                </span>
            }
          </div>
          <div className="tags">
            {
              data.promotionTagList ? data.promotionTagList.map((v, i) => {
                return <span key={i}>{v}</span>;
              }) : null
            }
          </div>
          <div className="choose-num">
            <GiftCount data={data} pindex={pindex} index={index} orderSchemeType={orderSchemeType}/>
          </div>
          {
            data.virtualSuitList && data.virtualSuitList.length > 0 && <ShopDetail>
              {
                data.virtualSuitList.slice(0, selectIndex).map((product, index) => {
                  return (
                    <p key={index}>
                      {
                        data.isActive !== $PromotionType.ISACTIVE && product.tags && product.tags.length > 0 && product.tags.indexOf($PromotionType.NOTSUITMULTIPLE) > -1 ?
                          <span className="noSuitMultiple">赠送未达标</span> : <span>套装明细</span>
                      }
                      <span>{product.name ? product.name.length > 10 ? product.name.slice(0, 10) + "..." : product.name : null}</span>
                      <span>x{product.quantity}</span>
                    </p>
                  );
                })
              }
              {
                data.virtualSuitList ? data.virtualSuitList.length > 2 ?
                  <ShowButton onClick={this.showButton}>
                    {isShowButton ? "点击收起" : "展开更多"}
                    {isShowButton ? <i className="scmIconfont scm-icon-jiantou-shang"/> :
                      <i className="scmIconfont scm-icon-arrow-down"/>}
                  </ShowButton> : null : null}
            </ShopDetail>
          }
        </div>
      </ItemWrapper>
    );
  }
}

const ItemWrapper = styled.div`// styled
  & {
    padding-left: 140px;
    padding-right: 15px;
    position: relative;
    margin-bottom: 15px;
    .hide {
      display: none;
    }
    .am-checkbox-item {
      position: absolute;
      left: 0;
      top: 15px;
    }
    .product-img {
      width: 80px;
      height: 80px;
      position: absolute;
      left: 50px;
      top: 0;
      img {
        width: 100%;
        height: 100%;
      }
      .nothing {
        position: absolute;
        left: 10px;
        top: 10px;
        width: 60px;
        height: 60px;
        line-height: 60px;
        text-align: center;
        border-radius: 50%;
        background: rgba(0, 0, 0, 0.50);
        color: white;
      }
      .img-tags {
        position: absolute;
        bottom: -2.5px;
        right: 0;
        span {
          font-size: 8px;
          color: #FFFFFF;
          background: #ff7564;
          margin-left: 5px;
          padding: 0px 2px;
        }
      }
      .noSuitMultiple {
        display: block;
        width: 80px;
        height: 16px;
        line-height: 18px;
        text-align: center;
        background: rgba(0, 0, 0, 0.5);
        border-radius: 0px 0px 4px 4px;
        position: absolute;
        top: 64px;
        left: 0;
        bottom: 0;
        font-size: 10px;
        font-family: SourceHanSansCN-Normal;
        font-weight: 400;
        color: rgba(255, 255, 255, 1);
        border: none;
        margin: 0;
      }
    }
    .product-item-text {
      width: 100%;
      h5 {
        color: #202020;
        overflow: hidden;
        max-height: 32px;
        line-height: 16px;
        margin: 0;
      }
      .check {
        font-size: 10px;
        color: #999999;
        margin-top: 4px;
      }
      .price {
        height: 16px;
        line-height: 16px;
        margin-top: 6px;
        .redprice {
          font-size: 14px;
          color: #ff3030;
          font-family: SourceHanSansCN-Normal, SourceHanSansCN;
        }
        .blackprice {
          font-size: 10px;
          color: #999999;
          font-family: SourceHanSansCN-Normal, SourceHanSansCN;
        }
      }
      .tags {
        height: 18px;
        line-height: 18px;
        span {
          font-size: 8px;
          padding: 0px 4px;
          color: #FF7362;
          border: 0.5px solid #FF7362;
          border-radius: 2px;
          margin-right: 10px;
        }
      }
      p {
        height: 22px;
        line-height: 22px;
        color: #999999;
        font-size: 10px;
        margin: 0;
        padding: 0;
      }
      .choose-num {
        width: 100%;
        text-align: right;
      }
    }
  }
`;
const ShopDetail = styled.div`// styled
  & {
    position: relative;
    padding: 9px 0;
    margin-top: 8px;
    :before {
      content: '';
      position: absolute;
      background-color: #D8D8D8 !important;
      display: block;
      z-index: 1;
      top: 0;
      right: auto;
      bottom: 0;
      left: 0;
      width: 100%;
      height: 1.2px;
      transform-origin: 50% 100%;
      transform: scaleY(0.5);
    }
    > p {
      margin-bottom: 8px;
      > span:nth-of-type(1) {
        border-radius: 11px;
        border: 1px solid rgba(255, 134, 39, 1);
        font-size: 8px;
        font-family: "PingFangSC-Regular";
        font-weight: 400;
        color: rgba(255, 134, 39, 1);
        margin-right: 16px;
        display: inline-block;
        width: 48px;
        height: 16px;
        line-height: 16px;
        text-align: center;
      }
      > span:nth-of-type(2) {
        display: inline-block;
        width: 95px;
        text-align: left;
        font-size: 9px;
        font-family: "PingFangSC-Regular";
        font-weight: 400;
        color: rgba(117, 117, 117, 1);
        margin-right: 5px;
      }
      > span:nth-of-type(3) {
        display: inline-block;
        width: calc(100% - 48px - 120px);
        text-align: right;
        font-size: 9px;
        font-family: "PingFangSC-Regular";
        font-weight: 400;
        color: rgba(117, 117, 117, 1);
      }
      > .noSuitMultiple {
        display: inline-block;
        width: 48px !important;
        height: 16px !important;
        background: rgba(0, 0, 0, 0.5) !important;
        border-radius: 11px !important;
        font-size: 8px !important;
        font-family: SourceHanSansCN-Normal !important;
        font-weight: 400 !important;
        color: rgba(255, 255, 255, 1) !important;
        border: none !important;
      }
    }
  }
`;

const ShowButton = styled.div`// styled
  & {
    font-size: 9px;
    font-family: "PingFangSC-Regular";
    font-weight: 400;
    color: rgba(117, 117, 117, 1);
    text-align: right;
    .scmIconfont {
      color: #999999;
      position: relative;
      top: 2px;
    }
  }
`;
