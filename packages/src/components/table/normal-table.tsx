import { autowired } from "@classes/ioc/ioc";
import { Checkbox, List, Modal } from "antd-mobile";
import { observer } from "mobx-react";
import * as React from "react";
import styled from "styled-components";
import { $CartType } from "../../classes/const/$cart-type";
import { $ComponentService } from "../../classes/service/$component-service";
import { $CartMv } from "../../modules/shop-cart/cart-mv";
import { NoGoods } from "../no-goods/no-goods";
import { TYPE_SELECT } from "./product-item";
import { ShopCartItem } from "./shopcart-item";
import { NewCustomModal } from "../custom-modal/new-custom-modal";
import { toFixedOptimizing } from "@classes/utils/DateUtils";
import { isEmpty, sum } from "lodash";
import { $TransferHintMv } from "@classes/entity/$transfer-hint-mv";
import { message } from "antd";
import { $AccountType } from "@classes/const/$account-type";

const CheckboxItem = Checkbox.CheckboxItem;

@observer
export class Table extends React.Component<any, any> {

  @autowired($ComponentService)
  public $componentService: $ComponentService;

  @autowired($CartMv)
  public $CartMv: $CartMv;

  @autowired($TransferHintMv)
  public $TransferHintMv: $TransferHintMv;

  componentDidMount(): void {
    this.state = {
      isShowTransferModal: false
    }
  }

  public onItemClick = (clickType, data, checked?) => {
    if (clickType === TYPE_SELECT) {
      const params = {
        checked,
        productIdentifier: data.productIdentifier,
        productSkuId: data.productSkuId,
        productUomId: data.productUomId,
        // quantity: data.itemType === $CartType.ITEMTYPE ? data.product.quantity : data.activitySuit.quantity,
        quantity: data.skuType === $CartType.ITEMTYPE ? data.product.quantity : data.skuType === $CartType.VIRTUALSUIT ? data.virtualSuit.quantity : data.activitySuit.quantity,
        itemType: data.itemType,
        skuType: data.skuType,
      };
      this.$componentService.changeShopCart(params).then((res) => {
        if (res && res.result) {
          data.setChecked(checked);
          this.onCheckedDiscount(this.$CartMv.isShowDiscount)
        }
      });
    }
  }

  public clearShopCart = () => {
    Modal.alert("是否清空所有商品？", "", [
      {
        text: "取消", onPress: () => {
          console.log("cancel");
        },
      },
      {
        text: "清空", onPress: () => {
          this.shopcartBatchDelete();
        },
      },
    ]);
  }

  public shopcartBatchDelete = async () => {
    const params = { shopCart: this.$CartMv.products };
    await this.$CartMv.shopcartBatchDelete(params);
    if (this.$CartMv.getProductsParams.length > 0) {
      this.onCheckedDiscount(false);
    }
  }

  public showTransferList = (canTransferAmount) => {
    if (canTransferAmount <= 0) {
      return;
    }
    const { shopSchemeInfo } = this.props;
    const params = {
      accountId: shopSchemeInfo.accountId,
      transferInOrgId: shopSchemeInfo.orderPartyId,
      amount: canTransferAmount
    };
    this.$CartMv.loadTransferInfo(params)
      .then(() => {
        this.$CartMv.showTransferModal();
      });
  }

  public changeTransferOut = (inIndex, outIndex, value) => {
    console.log('xxxx')
    // const { transferList } = this.$transferHintMv;
    // const newTransferList = cloneDeep(toJS(transferList));
    // set(newTransferList, `[${inIndex}].transferOutList.[${outIndex}].checked`, value);
    // this.$transferHintMv.transferList = newTransferList;
  }

  public renderTransferInfo = (orderModeNeedAmount) => {
    const { shopcartTransferInfo } = this.$CartMv;
    const { transferList } = shopcartTransferInfo
    return <TransferInfo>
      {
        !isEmpty(transferList) && transferList.map((transferIn: any, inIndex) => {
          const { transferInOrgId, transferInOrgName, transferInAccountType, transferInAccountAmount, transferInAmount, transferOutList } = transferIn;
          let waitTransferAmount: number;
          if (transferInAccountType === $AccountType.DIVIDEND_REBATE_DISCOUNT_LIMIT ||
            transferInAccountType === $AccountType.DEDUCTION_LIMIT ||
            transferInAccountType === $AccountType.RETURN_FREE_DISTRIBUTION
          ) {
            waitTransferAmount = toFixedOptimizing(sum(transferOutList
              .map((item) => item.transferOutAmount),
            ));
          } else {
            waitTransferAmount = toFixedOptimizing(sum(transferOutList
              .filter((item) => item.checked)
              .map((item) => item.transferOutAmount),
            ));
          }
          const maxTransferAmount = toFixedOptimizing(sum(transferOutList.map((item) => item.transferOutAmount)));
          return <TransferBox key={transferInOrgId}>
            <div className={"org-in-name"}>
              {transferInOrgName}
              {
                maxTransferAmount < transferInAmount &&
                <span className={"max-transfer-tip"}>（最多可转入¥{maxTransferAmount}）</span>
              }
            </div>
            <div className={"org-in-info"}>
              <div>
                余额<span className={"amount grey"}>{transferInAccountAmount}</span>，
                需转入<span className={"amount grey"}>{orderModeNeedAmount > 0 ? orderModeNeedAmount : transferInAmount}</span>，
              </div>
              <div>待转入<span className={"amount"}>{toFixedOptimizing(waitTransferAmount)}</span></div>
            </div>
            <div className={"org-out-info"}>
              {
                !isEmpty(transferOutList) && transferOutList.map((transferOut: any, outIndex) => {
                  const { transferOutOrgName, transferOutAmount, transferOutAvailAmount} = transferOut;
                  return <CheckboxItem
                    key={`${transferInOrgId}`}
                    onChange={(e) => this.changeTransferOut(inIndex, outIndex , e.target.checked)}
                    checked={false}
                  >
                    <div className={"info"}>
                      <div>转出门店：</div><div className={"counter"}>{transferOutOrgName},</div>&nbsp;
                      <div className={"amount"}>已选{transferOutAmount}/{transferOutAvailAmount}额度</div>
                    </div>
                  </CheckboxItem>
                })
              }
            </div>
          </TransferBox>;
        })
      }
    </TransferInfo>;
  }

  public submitTransfer = () => {
    const { shopcartTransferInfo } = this.$CartMv;
    const { transferList } = shopcartTransferInfo;
    const submitTransferList = [];
    transferList.map((transferIn: any) => {
      const { transferInOrgId, transferInAccountId, salesOrderIds, transferInAccountType, transferOutList } = transferIn;
      const submitTransferOutList = [];
      transferOutList.map((transferOut: any) => {
        const { transferOutOrgId, transferOutAccountId, transferOutAmount } = transferOut;
        submitTransferOutList.push({ transferOutOrgId, transferOutAccountId, amount: transferOutAmount });
      });
      if (submitTransferOutList.length > 0) {
        submitTransferList.push({
          transferOutList: submitTransferOutList,
          transferInOrgId, transferInAccountId, salesOrderIds, accountType: transferInAccountType,
        });
      }
    });
    if (submitTransferList.length <= 0) {
      message.warning("请选择转出门店");
      return;
    }
    const params = { transferList: submitTransferList };
    this.$CartMv.hideTransferModal();
    this.$TransferHintMv.submitTransferInfo(params)
      .then((res) => {
        const { result, errorMsg } = res;
        if (result) {
          this.$CartMv.hideTransferModal();
          message.success("转账成功", .5, () => {
            window.location.reload();
          });
        } else {
          message.error(errorMsg, .5, () => {
            window.location.reload();
          });
        }
      });
  }

  public calcSelectAmount = () => {
    const { products, accountTypeCode } = this.$CartMv;
    this.$CartMv.selectedAmount = products.reduce((total, item: any) => {
      if (item.checked) {
        if (item.skuType === $CartType.VIRTUALSUIT) {
          const price = item?.virtualSuit?.deductionAmount || 0 ;
          return total + (item.virtualSuit.quantity * price);
        } else {
          const price = item?.product?.deductionAmount || 0 ;
          return total + (item.product.quantity * price);
        }
      }
      return total;
    }, 0);
  }

  public onCheckedDiscount = (checked) => {
    this.calcSelectAmount();
    if (checked) {
      this.$CartMv.openDiscount();
    } else {
      this.$CartMv.closeDiscount();
      this.$CartMv.resetSplitTotalAmount();
      this.$CartMv.resetTotalDiscountAmount();
      this.$CartMv.resetExpectedDiscountAmount();
    }

    const productsParams = this.$CartMv.getProductsParams;
    if (productsParams.length === 0) {
      this.$CartMv.resetSplitTotalAmount();
      this.$CartMv.resetTotalDiscountAmount();
      this.$CartMv.resetTotalDeductionAmount();
      this.$CartMv.resetExpectedDiscountAmount();
    }
    if (productsParams.length > 0) {
      const params = {
        openDiscountCheck: checked,
        oldSalesOrderId: sessionStorage.getItem("editOrderId") === "null" ? null : sessionStorage.getItem("editOrderId"),
        shopCartItemList: productsParams,
        shopCartTotalAmount: this.$CartMv.totalAmount ? Number(this.$CartMv.totalAmount).toFixed(2) : 0,
      };
      this.$CartMv.fetchItemSplit(params);
    }
  }

  public updateDiscount = () => {
    console.log('33222')
    if (this.$CartMv.getProductsParams.length > 0) {
      this.onCheckedDiscount(this.$CartMv.isShowDiscount);
    }
    // const { shopSchemeInfo } = this.props
    // if (shopSchemeInfo && shopSchemeInfo.openDiscountBtn) {
    //   this.onCheckedDiscount(this.$CartMv.isShowDiscount)
    // } else if (shopSchemeInfo && shopSchemeInfo.orderMode) {
    //   this.onCheckedDiscount(this.$CartMv.isShowDiscount)
    // }
  }

  public getSmallNum = (a, b) => {
    const res = a < b ? a : b;
    // console.log(res, a, b);
    return res ? res : 0;
  }

  public getCantransferAmount = () => {
    const { shopSchemeInfo } = this.props;
    const { totalDiscountAmount, totalDeductionAmount } = this.$CartMv;
    if (shopSchemeInfo && shopSchemeInfo.openDiscountBtn) {
      const differenceAmount = totalDiscountAmount >= shopSchemeInfo.availableAmount ? (totalDiscountAmount - shopSchemeInfo.availableAmount).toFixed(2) : 0;
      return this.getSmallNum(differenceAmount, shopSchemeInfo.transferAmount);
    } else if (shopSchemeInfo && shopSchemeInfo.orderMode) {
      const needTranferAmount = totalDeductionAmount > shopSchemeInfo.availableAmount ? (totalDeductionAmount - shopSchemeInfo.availableAmount).toFixed(2) : 0;
      return this.getSmallNum(needTranferAmount, shopSchemeInfo.transferAmount);
    }
  }

  public getAccountName = (type) => {
    if (type === 'SYFZ') {
      return '返利账户';
    } else if (type === 'HFPZ') {
      return '返配赠账户';
    }
  }

  public render() {
    const { data, checkable, isCart, pricePermission, retailPriceViewPermission, shopcartTransferInfo,
       orderSchemeType, shopSchemeInfo, isShowDiscount } = this.props;
    const { isShowTransferModal, totalDiscountAmount, expectedDiscountAmount, totalDeductionAmount } = this.$CartMv;
    const isTransferOutEmpty = shopcartTransferInfo && shopcartTransferInfo.transferList.every((transfer) => isEmpty(transfer.transferOutList));
    const canTransferAmount = this.getCantransferAmount();
    const isDiscount = data && data.length > 0 && shopSchemeInfo && (shopSchemeInfo.openDiscountBtn || shopSchemeInfo.orderMode)
    && pricePermission === $CartType.ORDERPRICEVIEWPERMISSION;

    const listTemplate = (
      <List
        style={{
          marginTop: data && data.length && this.$CartMv.productMessage ? 60 : data && data.length > 0 ? 31 : 0,
          height: "100%",
        }}
      >
        {data ? data.length > 0 ? data.map((item, index) =>
          <ShopCartItem
            key={index}
            checkable={checkable}
            isCart={isCart}
            isDiscount={isDiscount}
            onItemClick={this.onItemClick}
            updateDiscount={this.updateDiscount}
            data={item}
            pricePermission={pricePermission}
            retailPriceViewPermission={retailPriceViewPermission}
            orderSchemeType={orderSchemeType}
          />) : <NoGoods title="暂无任何商品~"
            height={document.documentElement.clientHeight - 100}
        /> : null}
      </List>
    );
    const orderModeNeedAmount = ( totalDeductionAmount > 0 && shopSchemeInfo && totalDeductionAmount > shopSchemeInfo.availableAmount) ? (totalDeductionAmount - shopSchemeInfo.availableAmount).toFixed(2) : 0

    return (
      <Page>
        <ListWrapper>
          {
            data && data.length > 0 && <CartHeader>
              <div>
                <span>商品列表</span>
                <span onClick={this.clearShopCart}>清空购物车</span>
              </div>
              {
                this.$CartMv.productMessage && <div>
                  {this.$CartMv.productMessage}
                </div>
              }
            </CartHeader>
          }
          {
            isDiscount && <PreferentialAmount>
              {
                shopSchemeInfo.openDiscountBtn && <Checkbox.CheckboxItem checked={isShowDiscount} onChange={e => this.onCheckedDiscount(e.target.checked)}>
                  <div className="show-info">
                    <div className="preferential-info">
                      <div>可用优惠额度： <span>{shopSchemeInfo.availableAmount}</span></div>
                      <div>已选商品优惠额度合计 {totalDiscountAmount.toFixed(2) || 0}</div>
                      <div>本单预计扣减 {expectedDiscountAmount.toFixed(2) || 0}</div>
                    </div>
                  </div>
                  <div className={ canTransferAmount <= 0 ? 'disable-btn' : 'show-btn'} onClick={() => this.showTransferList(canTransferAmount)}>
                    <div>可转入额度</div>
                    <div>({canTransferAmount})</div>
                  </div>
                </Checkbox.CheckboxItem>
              }
              {
                shopSchemeInfo.orderMode && <div className="order-mode-info">
                  <div className="show-info">
                    <div className="preferential-info">
                      <div>{this.getAccountName(shopSchemeInfo.accountTypeCode)}：可用额度 <span>{shopSchemeInfo.availableAmount}</span></div>
                      <div>注：所需金额可从其他门店转入，转入后会更新余额</div>
                      <div>本单还需额度 <span>{ orderModeNeedAmount }</span></div>
                      {/* <div>已选商品优惠额度合计 {totalDiscountAmount.toFixed(2) || 0}</div>
                      <div>本单预计扣减 {expectedDiscountAmount.toFixed(2) || 0}</div> */}
                    </div>
                  </div>
                  <div className={ canTransferAmount <= 0 ? 'disable-btn' : 'show-btn'} onClick={() => this.showTransferList(canTransferAmount)}>
                    <div>可转入额度</div>
                    <div>({canTransferAmount})</div>
                  </div>
                </div>
              }
            </PreferentialAmount>
          }
          {listTemplate}
        </ListWrapper>
        {
          isShowTransferModal && <NewCustomModal
            style={{top: "40%"}}
            header={"转账详情"}
            hideConfirm={isTransferOutEmpty}
            confirm={this.submitTransfer}
            content={this.renderTransferInfo(orderModeNeedAmount)}
            confirmName={"确认转账"}
            visible={isShowTransferModal}
            close={() => this.$CartMv.hideTransferModal()}
          />
        }
      </Page>
    );

  }
}

// @formatter:off
const Page = styled.div`// styled
  & {
    height: inherit;
  }
`;

const PreferentialAmount = styled.div`
  & {
    margin: 42px 12px -15px;
    border: 1px solid #FFA630;
    background: rgba(255,166,48,0.1);
    border-radius: 4px;
    display: flex;
    justify-content: space-between;

    .am-list-item {
      width: 100%;
      background: rgba(255, 166, 48, 0.10);

      .am-list-content {
        display: flex;
        justify-content: space-between;
        padding-top: 0 !important;
        padding-bottom: 0 !important;
      }
    }

    .show-info {
      display: flex;
      align-items: center;

      .preferential-check {
        margin-left: 12px;
        background-color: #FFF;
        border: 1px solid #D9D9D9;
        width: 18px;
        height: 18px;
      }

      .preferential-info {
        margin-left: 8px;
        text-align: left;
        font-size: 14px;

        > div:nth-child(1) {
          color: #333333;

          > span {
            color: #FF3636;
          }
        }

        > div:nth-child(2) {
          font-size: 12px;
          color: #666666;
        }

        > div:nth-child(3) {
          font-size: 12px;
          color: #666666;
        }
      }
    }

    .show-btn {
      background: #417BEE;
      border-radius: 4px;
      font-size: 12px;
      color: #FFF;
      font-family: PingFang SC, PingFang SC;
      padding: 4px 8px;
      margin: 16px 0;
      text-align: center;
    }

    .disable-btn {
      background: #999;
      border-radius: 4px;
      font-size: 12px;
      color: #FFF;
      font-family: PingFang SC, PingFang SC;
      padding: 4px 8px;
      margin: 16px 12px;
      text-align: center;
    }

    .order-mode-info {
      display: flex;
      width: 100%;
      justify-content: space-around;
      padding: 12px 0;

      .show-info {
        width: 68%;

        .preferential-info {

          >div:nth-child(2) {
            font-size: 10px;
          }

          >div:nth-child(3) {
            > span {
              color: #FF3636;
            }
          }
        }
      }
    }
  }
`

const CartHeader = styled.div`// styled
  & {
    width: 100%;
    height: auto;
    > div:first-child {
      padding: 6px 16px;
      position: fixed;
      z-index: 99;
      width: 100%;
      height: 32px;
      background: #fff;
      border-bottom: 1px solid #D8D8D8;
      > span:first-child {
        font-size: 13px;
        font-family: SourceHanSansCN-Normal;
        font-weight: 400;
        color: rgba(117, 117, 117, 1);
      }
      > span:last-child {
        font-size: 13px;
        font-family: SourceHanSansCN-Normal;
        font-weight: 400;
        color: rgba(48, 125, 205, 1);
        float: right;
      }
    }
    > div:nth-of-type(2) {
      color: #FF3030;
      width: 100%;
      height: 28px;
      background: #fff2f2;
      font-size: 12px;
      line-height: 12px;
      box-sizing: border-box;
      position: fixed;
      z-index: 99;
      padding: 10px 16px;
      margin-top: 32px;
    }
  }
`;

const ListWrapper = styled.div`// styled
  & {
    height: 100%;
    overflow: auto;
    margin-bottom: 90px;
    -webkit-overflow-scrolling: touch;
    .am-list-body::after {
      display: none !important;
    }
  }
`;

const TransferInfo = styled.div`// styled
  &{
    width: 100%;
    .amount {
      // color: #FF3030;
    }
    .amount:before {
      // content: "¥";
      zoom: .85;
      font-size: 12px;
      margin-left: 5px;
    }
    .tip {
      display: block;
      font-size: 12px;
      color: #666666;
      margin-bottom: 10px;
    }
  }
`;
const TransferBox = styled.div`// styled
  & {
    width: 100%;
    padding: 12px 0;
    border-top: 1px solid rgba(0, 0, 0, .12);
    .org-in-name {
      font-weight: 500;
      color: #333333;
      margin-bottom: 10px;
      .max-transfer-tip {
        margin-left: 10px;
        font-size: 12px;
        color: #FFA630;
      }
    }
    .org-in-info {
      font-size: 12px;
      margin-bottom: 10px;
      color: #333333;
      .grey {
        color: #333333;
      }
      > div:nth-child(1) {
        color: #333333;
      }
    }
    .org-out-info {
      margin-bottom: 10px;
      .info {
        display: flex;
        font-size: 12px;
        color: #333333;
        .counter {
          white-space: break-spaces;
          max-width: 60%;
        }
      }
      .am-list-item {
        padding: 0;
      }
      .am-list-item .am-list-thumb:first-child {
        margin-right: 5px;
      }
      .am-checkbox-inner {
        width: 19px;
        height: 19px;
      }
      .am-checkbox-inner:after {
        top: 2.5px;
        right: 6px;
      }
      .am-checkbox.am-checkbox-checked .am-checkbox-inner {
        border-color: #437DF0;
        background: #437DF0;
      }
    }
  }
`;
