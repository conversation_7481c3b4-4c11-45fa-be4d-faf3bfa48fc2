import {isEmpty, merge, remove} from "lodash";
import {observer} from "mobx-react";
import * as React from "react";
import {AutoSizer, InfiniteLoader, List} from "react-virtualized";
import styled from "styled-components";
import {ProductItem, TYPE_SELECT} from "./product-item";
import {ProductList} from "../../modules/product-list";

export const HEADER_HEIGHT = 46;
const pageSize = 10;

@observer
export class InfiniteTable extends React.Component<any, any> {

  // lazy table
  public isRowLoaded = ({index}) => {
    const {data} = this.props;
    return !!data[index + 1];
  }

  public loadMoreItems = ({startIndex, stopIndex}) => {
    const {onPageChange} = this.props;
    const current = Math.ceil((stopIndex + 1) / pageSize) + 1;
    onPageChange(current, pageSize);
  }

  public rowRenderer = ({index, key, style}) => {
    const {data, checkable, rowRenderer, isCart} = this.props;
    const RowRenderer = row<PERSON><PERSON><PERSON>;
    return (
      <div key={key} style={style}>
        {row<PERSON><PERSON><PERSON> ?
          <RowRenderer key={key} data={data[index]}/> :
          <ProductItem
            key={key}
            checkable={checkable}
            isCart={isCart}
            onItemClick={this.onItemClick}
            data={data[index]}
            onSelect={this.select}
          />}
      </div>
    );
  }

  public onItemClick = (clickType, data, checked?) => {
    if (clickType === TYPE_SELECT) {
      this.select(data, checked);
    }
  }

  public onClick = (data) => {
    const {onItemClicked} = this.props;
    onItemClicked(data);
  }

  public select = (selectedRow, checked) => {
    const {onSelect, selectedRows} = this.props;
    if (!isEmpty(selectedRow)) {
      let selectedRowsCopy = selectedRows.slice();
      if (checked) {
        selectedRowsCopy.push(merge({}, selectedRow, {checked: true}));
      } else {
        selectedRowsCopy = remove(selectedRowsCopy, (row: any) => row.productIdentifier !== selectedRow.productIdentifier);
      }
      onSelect(selectedRowsCopy);
    }
  }


  public render() {
    const {data, rowHeight, checkable, selectedRows, suppressSearchMenuBar} = this.props;
    const {clientHeight} = document.documentElement;

    return (
        <h6>暂无</h6>
    );
  }
}
