import { autowired } from "@classes/ioc/ioc";
import { List, Modal, Toast } from "antd-mobile";
import { subtract, sum } from "lodash";
import { toJS } from "mobx";
import { observer } from "mobx-react";
import * as React from "react";
import styled from "styled-components";
import { $PromotionType } from "../../classes/const/$promotion-type";
import { $PromotionMatchService } from "../../classes/service/$promotion-match-service";
import { $PromotionMatchMv } from "../../modules/promotion-match/promotion-match-mv";
import { NoGoods } from "../no-goods/no-goods";
import { TYPE_SELECT } from "./product-item";
import { PromotionMatchItem } from "./promotion-match-item";

const alert = Modal.alert;

@observer
export class PromotionMatchTable extends React.Component<any, any> {
  @autowired($PromotionMatchService)
  public $promotionMatchService: $PromotionMatchService;

  @autowired($PromotionMatchMv)
  public $PromotionMatchMv: $PromotionMatchMv;

  public onItemClick = (clickType, data, checked?, index) => {
    if (clickType === TYPE_SELECT) {
      data.setCheckFlag(checked);
      if (checked) {
        const params = { promotionList: toJS(this.$PromotionMatchMv.promotionList), shopCartItemList: JSON.parse(sessionStorage.getItem("productsParams")) };
        this.$promotionMatchService.countPromotion(params).then((resData) => {
          const { promotionList, errorMessage, orderTotalAmount } = resData;
          if (data.errorCode === $PromotionType.ERRORCODEFORCEGIVE) {
            alert(data.errorMessage, "", [
              {
                text: "确认", onPress: () => {
                  // window.location.href = document.referrer;
                },
              },
            ]);
          }
          if (errorMessage !== "") {
            Toast.info(errorMessage);
          }
          this.$PromotionMatchMv.setCountPromotion(promotionList, orderTotalAmount);
        });
      } else {
        data.setTimes();
        data.setCheckFlag(checked);
        if (data.interactFlag !== $PromotionType.INTERACTFLAG) {
          if (data.adjustItemList) {
            if (data.adjustItemList.length > 0) {
              const orderTotalAmount = subtract(this.$PromotionMatchMv.orderTotalAmount, sum(this.$PromotionMatchMv.promotionList[index].adjustItemList.map((adjustItem) => {
                if (adjustItem.isActive !== $PromotionType.ISACTIVE) {
                  return adjustItem.quantity * adjustItem.orderPrice;
                }
              })));
              this.$PromotionMatchMv.setOrderTotalAmount(orderTotalAmount);
            }
          }
        }
      }
    }
  }

  public render() {
    const { data, checkable, isCart } = this.props;

    const listTemplate = (
      <List>
        {data ? data.length > 0 ? data.map((item, index) =>
          <PromotionMatchItem
            key={index}
            itemIndex={index}
            checkable={checkable}
            isCart={isCart}
            onItemClick={this.onItemClick}
            data={item}
          />) : <NoGoods title="暂无记录"/> : <NoGoods title="暂无记录"/>}
      </List>
    );

    return (
      <Page>
        <ListWrapper className="promotion-list-wrap">
          {listTemplate}
        </ListWrapper>
      </Page>
    );

  }
}

// @formatter:off
const Page = styled.div`// styled
  & {
    height: inherit;
    .am-list-body {
      background-color: #F2F2F2;
    }
    .am-list .am-list-body::before {
      height: 0 !important;
    }
  }
`;

const ListWrapper = styled.div`// styled
  & {
    height: 100%;
    overflow: auto;
    -webkit-overflow-scrolling: touch;
  }
`;
