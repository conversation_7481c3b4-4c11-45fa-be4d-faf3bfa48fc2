import * as React from "react";
import styled from "styled-components";

export class OrderItem extends React.Component<any, any> {
  constructor(props) {
    super(props);
  }

  public render() {
    const { data } = this.props;
    return (
      <ItemWrapper>
        <InfoItem>
          <Content>
            <Up>
              <div>时间：{data.orderDate}</div>
              <div>共 {data.totalSku}种 {data.totalSkuCount}件</div>
            </Up>
            <Down>
              <div style={{ color: "#202020" }}>订单：{data.docNo}</div>
              <div style={{ color: "#202020" }}>总金额：{data.totalAmount}</div>
            </Down>
          </Content>
        </InfoItem>
      </ItemWrapper>
    );
  }
}

const ItemWrapper = styled.div`// styled
  & {
    display: flex;
    height: 64px;
    padding: 10px 15px;
    background: #fff;
    border-bottom: 1px solid #D8D8D8;
    .am-list-item {
      padding-left: 0;
    }
  }
`;
const InfoItem = styled.div`// styled
  & {
    width: 100%;
  }
`;

const Content = styled.div`// styled
  & {
    font-size: 13px;
    color: #666666;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    height: 100%;
  }
`;

const Up = styled.div`// styled
  & {
    display: flex;
    flex: 1;
    justify-content: space-between;
    .order-color {
      font-size: 14px;
      color: #FF3030;
    }
  }
`;

const Down = styled.div`// styled
  & {
    display: flex;
    flex: 1;
    justify-content: space-between;
    .order-color {
      font-size: 14px;
      color: #FF3030;
    }
  }
`;
