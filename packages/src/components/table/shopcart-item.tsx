import { autowired } from "@classes/ioc/ioc";
import { Checkbox, Modal, SwipeAction } from "antd-mobile";
import { observer } from "mobx-react";
import * as React from "react";
import { withRouter } from "react-router";
import styled from "styled-components";
import { $CartType } from "../../classes/const/$cart-type";
import { SITE_PATH } from "../../modules/app";
import { $CartMv } from "../../modules/shop-cart/cart-mv";
import { ProductCount } from "../product-count/product-count";
import { $ProductMv } from "../product/product-mv";
import { $AccountType } from "@classes/const/$account-type";

const CheckboxItem = Checkbox.CheckboxItem;

export const TYPE_SELECT = "select";

@withRouter
@observer
export class ShopCartItem extends React.Component<any, any> {
  @autowired($ProductMv)
  public $ProductMv: $ProductMv;

  @autowired($CartMv)
  public $CartMv: $CartMv;

  public constructor(props) {
    super(props);
    this.state = {
      isShowButton: false,
      selectIndex: 2,
    };
  }

  public onChecked = (checked) => {
    const { data } = this.props;
    this.props.onItemClick(TYPE_SELECT, data, checked);
  }

  public onClick = (data, isDetail) => {
    if (isDetail) {
      return false;
    }
    this.$ProductMv.setProduct(data);
    this.props.history.push({ pathname: `/${SITE_PATH}/product-detail` });
  }

  public deleteProduct = (productSkuId, itemType, skuType) => {
    const params = { productSkuId, itemType, skuType };
    this.$ProductMv.removeById(params).then((data) => {
      if (data.result) {
        // window.location.reload();
        // const paramsCart = { source: "shopCart" };
        // this.$CartMv.fetchCarts();
        this.$CartMv.removeById(productSkuId);
        this.props.updateDiscount();
        this.onCheckedDiscount(this.$CartMv.isShowDiscount);
      }
    });
  }

  public calcSelectAmount = () => {
    const { products, accountTypeCode } = this.$CartMv;
    this.$CartMv.selectedAmount = products.reduce((total, item: any) => {
      if (item.checked) {
        const price = accountTypeCode === $AccountType.RETURN_FREE_DISTRIBUTION
          ? item.product.deductionAmount
          : item.product.orderPrice;
        return total + (item.product.quantity * price);
      }
      return total;
    }, 0);
  }

  public onCheckedDiscount = (checked) => {
    this.calcSelectAmount();
    if (checked) {
      this.$CartMv.openDiscount();
    } else {
      this.$CartMv.closeDiscount();
      this.$CartMv.resetSplitTotalAmount();
      this.$CartMv.resetTotalDiscountAmount();
      this.$CartMv.resetExpectedDiscountAmount();
    }

    const productsParams = this.$CartMv.getProductsParams;
    if (productsParams.length === 0) {
      this.$CartMv.resetSplitTotalAmount();
      this.$CartMv.resetTotalDiscountAmount();
      this.$CartMv.resetTotalDeductionAmount();
      this.$CartMv.resetExpectedDiscountAmount();
    }
    if (productsParams.length > 0) {
      const params = {
        openDiscountCheck: checked,
        oldSalesOrderId: sessionStorage.getItem("editOrderId") === "null" ? null : sessionStorage.getItem("editOrderId"),
        shopCartItemList: productsParams,
        shopCartTotalAmount: this.$CartMv.totalAmount ? Number(this.$CartMv.totalAmount).toFixed(2) : 0,
      };
      this.$CartMv.fetchItemSplit(params);
    }
  }

  public showButton = () => {
    const { data } = this.props;
    const { isShowButton } = this.state;
    if (isShowButton) {// 收起
      this.setState({
        isShowButton: false,
        selectIndex: 2,
      });
    } else { // 展开
      this.setState({
        isShowButton: true,
        selectIndex: data.virtualSuit.productList ? data.virtualSuit.productList.length : 0,
      });
    }
  }

  public showColor = (actionType) => {
    let color = "";
    switch (actionType) {
      case "Deduct":
        color = "#8DBD00";
        break;
      case "Discount":
        color = "#F7B51C";
        break;
      case "Special":
        color = "#F25EA9";
        break;
      case "Gift":
        color = "#FF7564";
        break;
      case "PartFree":
        color = "#B385F5";
        break;
      case "MutiDiscount":
        color = "#F7B51C";
        break;
      case "TradeUp":
        color = "#FF8627";
        break;
      default:
        color = "";
        break;
    }
    return color;
  }

  public renderProduct = (data, checkable, pricePermission, retailPriceViewPermission, orderSchemeType) => {
    const { isShowDiscount } = this.$CartMv;
    const { isDiscount } = this.props;
    return (
      <div>
        <ItemWrapper>
          {checkable && <CheckboxItem
            className="checkItem"
            checked={data.checked}
            disabled={data.isActive === $CartType.ISACTIVE_KEY}
            onChange={(e) => this.onChecked(e.target.checked)}/>}
          <InfoItem>
            <ImageWrapper>
              <img
                src={data.product ? data.product.imageUrl ? data.product.imageUrl : "https://order.fwh1988.cn:14501/static-img/scm/ico-nopic.png" : ""}
                alt=""/>
              {
                data.isActive === "N" ? <p>
                  无效
                </p> : null
              }
              {data ? data.product ? data.product.productTagList ? data.product.productTagList.map((item, index) => {
                return (
                  <span key={index}>{item}</span>
                );
              }) : null : null : null}
            </ImageWrapper>
            <ContentWrapper>
              <Title checkable={checkable}>{data.product ? data.product.name : null}</Title>
              <Content>
                <div className="check">
                  {data.product ? data.product.startQuantity ? `最少购买数量为${data.product.startQuantity}` : null : null}
                  {data.product ? data.product.startQuantity && data.product.multiple ? "," : null : null}
                  {data.product ? data.product.multiple ? `依次${data.product.multiple}倍增加` : null : null}
                </div>
                <div>
                  { pricePermission === $CartType.ORDERPRICEVIEWPERMISSION ?
                      data.product ? (data.product.orderPrice !== null) ?
                      <div className="show-price">
                        {
                          data.product.deductionAmountFlag ? <span className="order-price">
                            <span style={{ color: "#FFF", border: "1px solid #417BEE", background: "#417BEE", padding: "1px 8px", borderRadius: "2px", fontSize: "11px" }}>额度值</span>
                            <span style={{ fontSize: "10px" }}>￥</span>
                            {data.product.deductionAmount}
                            <span className="retail-price">{data.product.unitOfMeasure && `/${data.product.unitOfMeasure}`}</span>
                          </span> : <span className="order-price">
                            <span style={{ color: "#FFA630", border: "1px solid #FFA630", background: "rgba(255, 166, 48, 0.10)", padding: "1px 8px", borderRadius: "24px", fontSize: "11px" }}>常规</span>
                            <span style={{ fontSize: "10px" }}>￥</span>
                            {data.product.orderPrice}
                            <span className="retail-price">{data.product.unitOfMeasure && `/${data.product.unitOfMeasure}`}</span>
                          </span>
                        }

                        {
                          isShowDiscount && data.product.discountOrderPrice !== null && <span className="order-price">
                            <span style={{ color: "#FF3636", border: "1px solid #FF3636", background: "rgba(255, 66, 49, 0.10)", padding: "1px 8px", borderRadius: "24px", fontSize: "11px" }}>优惠</span>
                            <span style={{ fontSize: "10px" }}>￥</span>
                            {data.product.discountOrderPrice}
                            <span className="retail-price">{data.product.unitOfMeasure && `/${data.product.unitOfMeasure}`}</span>
                          </span>
                        }
                      </div> : null : null : null
                  }
                  {
                    retailPriceViewPermission === $CartType.RETAILPRICEVIEWPERMISSION ?
                      data.product ? data.product.retailPrice !== null ? <span className="gray-price">
                      { (pricePermission === $CartType.ORDERPRICEVIEWPERMISSION && data.product.orderPrice !== null) ?
                        " (" : null}零售 ￥{data.product.retailPrice}{((pricePermission === $CartType.ORDERPRICEVIEWPERMISSION) && (data.product.orderPrice !== null)) ? ")" : null
                      }
                      { (pricePermission !== $CartType.ORDERPRICEVIEWPERMISSION || data.product.orderPrice === null) ?
                        <span className="gray-price">{data.product.unitOfMeasure && `/${data.product.unitOfMeasure}`}</span> : null
                      }
                    </span> : null : null : null
                  }
                </div>
                <div>
                  {data ? data.product ? data.product.promotionTagList ? data.product.promotionTagList.slice(0, 2).map((item, index) => {
                    return (
                      <span key={index} style={{
                        border: `0.5px solid ${this.showColor(item.promotionTagCode)}`,
                        color: `${this.showColor(item.promotionTagCode)}`,
                      }}>{item.promotionTagLabel}</span>
                    );
                  }) : null : null : null}
                  {
                    data ? data.product ? data.product.promotionTagList ? data.product.promotionTagList.length > 2 ?
                      <span>...</span> : null : null : null : null
                  }
                </div>
                <div>{data.product ? data.product.stockStatusDesc : null}</div>
                <Counter>
                  <ProductCount isDiscount={isDiscount} hasDiscount={true} updateDiscount={this.props.updateDiscount} data={data} source={"shopCart"} orderSchemeType={orderSchemeType}/>
                </Counter>
              </Content>
            </ContentWrapper>
          </InfoItem>
        </ItemWrapper>
        <Spacing/>
      </div>
    );
  }

  public renderActivitySuit = (data, checkable, pricePermission, retailPriceViewPermission, orderSchemeType) => {
    const { isShowDiscount } = this.$CartMv;
    const { isDiscount } = this.props;
    return (
      <SuitPage>
        <SuitHeader>
          {checkable && <CheckboxItem
            className="checkItem"
            checked={data.checked}
            disabled={data.isActive === $CartType.ISACTIVE_KEY}
            onChange={(e) => this.onChecked(e.target.checked)}/>}
          <div>套装</div>
          <div>{data.activitySuit.name ? data.activitySuit.name.length > 12 ? data.activitySuit.name.slice(0, 12) + "..." : data.activitySuit.name : null}</div>
        </SuitHeader>
        <ItemWrapper className="suitDetail">
          <InfoItem style={{ width: "100%" }}>
            {
              data.activitySuit.productList ? data.activitySuit.productList.map((product, index) => {
                return (
                  <div key={index} style={{ width: "100%", position: "relative", marginBottom: 15 }}>
                    <ImageWrapper>
                      <img
                        src={product.imageUrl ? product.imageUrl : "https://order.fwh1988.cn:14501/static-img/scm/ico-nopic.png"}
                        alt=""/>
                      {
                        product.isActive === "N" ? <p>
                          无效
                        </p> : null
                      }
                    </ImageWrapper>
                    <ContentWrapper className="suitContent">
                      <Title checkable={checkable} style={{ position: "relative" }}>
                        {product.name ? product.name.length > 12 ? product.name.slice(0, 12) + "..." : product.name : null}
                        <span style={{
                          position: "absolute",
                          top: "0px",
                          right: "15px",
                          fontSize: "12px",
                          color: "#999",
                        }}>x{product.quantity}</span>
                      </Title>
                      <Content>
                        <div/>
                        <div>
                          {
                            (pricePermission === $CartType.ORDERPRICEVIEWPERMISSION) && (product.orderPrice !== null) ?
                            <div className="show-price">
                              <span className="order-price">
                                <span style={{ color: "#FFA630", border: "1px solid #FFA630", background: "rgba(255, 166, 48, 0.10)", padding: "1px 8px", borderRadius: "24px", fontSize: "11px" }}>常规</span>
                                <span style={{ fontSize: "10px" }}>￥</span>
                                {product.orderPrice}
                                <span className="retail-price">{product.unitOfMeasure && `/${product.unitOfMeasure}`}</span>
                                {
                                  isShowDiscount && product.discountOrderPrice !== null && <span className="order-price">
                                    <span style={{ color: "#FF3636", border: "1px solid #FF3636", background: "rgba(255, 66, 49, 0.10)", padding: "1px 8px", borderRadius: "24px", fontSize: "11px" }}>优惠</span>
                                    <span style={{ fontSize: "10px" }}>￥</span>
                                    {product.discountOrderPrice}
                                    <span className="retail-price">{product.unitOfMeasure && `/${product.unitOfMeasure}`}</span>
                                  </span>
                                }
                              </span>
                            </div> : null
                          }
                          {
                            (retailPriceViewPermission === $CartType.RETAILPRICEVIEWPERMISSION) && (product.retailPrice !== null) ?
                              <span className="gray-price">
                  {((pricePermission === $CartType.ORDERPRICEVIEWPERMISSION) && (product.orderPrice !== null)) ? " (" : null}零售 ￥{product.retailPrice}{((pricePermission === $CartType.ORDERPRICEVIEWPERMISSION) && (product.orderPrice !== null)) ? ")" : null}{((pricePermission !== $CartType.ORDERPRICEVIEWPERMISSION) || (product.orderPrice === null)) ?
                                <span className="gray-price">
                                  {product.unitOfMeasure && `/${product.unitOfMeasure}`}</span> : null}
                  </span> : <span/>
                          }
                        </div>
                        <div style={{
                          borderRadius: "2px",
                          fontSize: "8px",
                          color: "#FF8627",
                          textAlign: "center",
                          border: "1px solid #FF8627",
                          padding: "0 5px",
                          width: "36px",
                        }}>
                          套装
                        </div>
                        <div style={{ color: "#999", fontSize: 10 }}>{product.stockStatusDesc}</div>
                      </Content>
                    </ContentWrapper>
                  </div>
                );
              }) : null
            }
          </InfoItem>
        </ItemWrapper>
        <SuitFooter>
          <div>{pricePermission === $CartType.ORDERPRICEVIEWPERMISSION || retailPriceViewPermission === $CartType.RETAILPRICEVIEWPERMISSION ? "套装价格" : ""}</div>
          {
            pricePermission === $CartType.ORDERPRICEVIEWPERMISSION ? <div>{data.activitySuit.amount}</div> :
              retailPriceViewPermission === $CartType.RETAILPRICEVIEWPERMISSION ?
                <div>{data.activitySuit.retailAmount}</div> : <div/>
          }
          <div>
            <ProductCount isDiscount={isDiscount} hasDiscount={true} updateDiscount={this.props.updateDiscount} data={data} source={"shopCart"} orderSchemeType={orderSchemeType}/>
          </div>
        </SuitFooter>
        <Spacing/>
      </SuitPage>
    );
  }

  public renderVirtualSuit = (data, checkable, pricePermission, retailPriceViewPermission, isShowButton, selectIndex, orderSchemeType) => {
    const { isShowDiscount } = this.$CartMv;
    const { isDiscount } = this.props;
    return (
      <VirtualSuitWrapper>
        <ItemWrapper style={{ height: "auto" }}>
          {checkable && <CheckboxItem
            className="checkItem suitItem"
            checked={data.checked}
            disabled={data.isActive === $CartType.ISACTIVE_KEY}
            onChange={(e) => this.onChecked(e.target.checked)}/>}
          <InfoItem>
            <ImageWrapper style={{ marginLeft: "58px" }}>
              <img
                src={data.virtualSuit ? data.virtualSuit.imageUrl ? data.virtualSuit.imageUrl : "https://order.fwh1988.cn:14501/static-img/scm/ico-nopic.png" : ""}
                alt=""/>
              {
                data.isActive === "N" ? <p>
                  无效
                </p> : null
              }
              {data ? data.virtualSuit ? data.virtualSuit.productTagList ? data.virtualSuit.productTagList.map((item, index) => {
                return (
                  <span key={index}>{item}</span>
                );
              }) : null : null : null}
            </ImageWrapper>
            <ContentWrapper>
              <Title checkable={checkable}>{data.virtualSuit ? data.virtualSuit.name : null}</Title>
              <Content style={{ position: "relative" }}>
                <div className="check">
                  {data.virtualSuit ? data.virtualSuit.startQuantity ? `最少购买数量为${data.virtualSuit.startQuantity}` : null : null}
                  {data.virtualSuit ? data.virtualSuit.startQuantity && data.virtualSuit.multiple ? "," : null : null}
                  {data.virtualSuit ? data.virtualSuit.multiple ? `依次${data.virtualSuit.multiple}倍增加` : null : null}
                </div>
                <div>
                  {
                    (pricePermission === $CartType.ORDERPRICEVIEWPERMISSION) ?
                      data.virtualSuit ? (data.virtualSuit.orderPrice !== null) ?
                      <div className="show-price">
                        {
                          data.virtualSuit.deductionAmountFlag ? <span className="order-price">
                            <span style={{ color: "#FFF", border: "1px solid #417BEE", background: "#417BEE", padding: "1px 8px", borderRadius: "2px", fontSize: "11px" }}>额度值</span>
                            <span style={{ fontSize: "10px" }}>￥</span>
                            {data.virtualSuit.deductionAmount}
                            <span className="retail-price">{data.virtualSuit.unitOfMeasure && `/${data.virtualSuit.unitOfMeasure}`}</span>
                          </span> : <span className="order-price">
                            <span style={{ color: "#FFA630", border: "1px solid #FFA630", background: "rgba(255, 166, 48, 0.10)", padding: "1px 8px", borderRadius: "24px", fontSize: "11px" }}>常规</span>
                            <span style={{ fontSize: "10px" }}>￥</span>
                            {data.virtualSuit.orderPrice}
                            <span className="retail-price">{data.virtualSuit.unitOfMeasure && `/${data.virtualSuit.unitOfMeasure}`}</span>
                          </span>
                        }
                        {
                          isShowDiscount && data.virtualSuit.discountOrderPrice !== null && <span className="order-price">
                            <span style={{ color: "#FF3636", border: "1px solid #FF3636", background: "rgba(255, 66, 49, 0.10)", padding: "1px 8px", borderRadius: "24px", fontSize: "11px" }}>优惠</span>
                            <span style={{ fontSize: "10px" }}>￥</span>
                            {data.virtualSuit.discountOrderPrice}
                            <span className="retail-price">{data.virtualSuit.unitOfMeasure && `/${data.virtualSuit.unitOfMeasure}`}</span>
                          </span>
                        }
                      </div>: null : null : null
                  }
                  {
                    retailPriceViewPermission === $CartType.RETAILPRICEVIEWPERMISSION ? data.virtualSuit ? data.virtualSuit.retailPrice !== null ?
                      <span className="gray-price">
                  {((pricePermission === $CartType.ORDERPRICEVIEWPERMISSION) && (data.virtualSuit.orderPrice !== null)) ? " (" : null}零售 ￥{data.virtualSuit.retailPrice}{((pricePermission === $CartType.ORDERPRICEVIEWPERMISSION) && (data.virtualSuit.orderPrice !== null)) ? ")" : null}{((pricePermission !== $CartType.ORDERPRICEVIEWPERMISSION) || (data.virtualSuit.orderPrice === null)) ?
                        <span className="gray-price">{data.virtualSuit.unitOfMeasure && `/${data.virtualSuit.unitOfMeasure}`}</span> : null}
                  </span> : null : null : null
                  }
                </div>
                <div>
                  {data ? data.virtualSuit ? data.virtualSuit.promotionTagList ? data.virtualSuit.promotionTagList.slice(0, 2).map((item, index) => {
                    return (
                      <span key={index} style={{
                        border: `0.5px solid ${this.showColor(item.promotionTagCode)}`,
                        color: `${this.showColor(item.promotionTagCode)}`,
                      }}>{item.promotionTagLabel}</span>
                    );
                  }) : null : null : null}
                  {
                    data ? data.virtualSuit ? data.virtualSuit.promotionTagList ? data.virtualSuit.promotionTagList.length > 2 ?
                      <span>...</span> : null : null : null : null
                  }
                </div>
                <div>{data.virtualSuit ? data.virtualSuit.stockStatusDesc : null}</div>
                <Counter style={{ bottom: "-30px", right: "5px" }}>
                  <ProductCount isDiscount={isDiscount} hasDiscount={true} updateDiscount={this.props.updateDiscount} data={data} source={"shopCart"} orderSchemeType={orderSchemeType}/>
                </Counter>
              </Content>
              <ShopDetail>
                {
                  data.virtualSuit.productList.slice(0, selectIndex).map((product, index) => {
                    return (
                      <p key={index}>
                        <span>套装明细</span>
                        <span>{product.name ? product.name.length > 10 ? product.name.slice(0, 10) + "..." : product.name : null}</span>
                        <span>x{product.quantity}</span>
                      </p>
                    );
                  })
                }
                {
                  data.virtualSuit.productList ? data.virtualSuit.productList.length > 2 ?
                    <ShowButton onClick={this.showButton}>
                      {isShowButton ? "点击收起" : "展开更多"}
                      {isShowButton ? <i className="scmIconfont scm-icon-jiantou-shang"/> :
                        <i className="scmIconfont scm-icon-arrow-down"/>}
                    </ShowButton> : null : null}
              </ShopDetail>
            </ContentWrapper>
          </InfoItem>
        </ItemWrapper>
        <Spacing/>
      </VirtualSuitWrapper>
    );
  }

  public render() {
    const { checkable, data, isCart, isDetail, pricePermission, retailPriceViewPermission, orderSchemeType } = this.props;
    const { isShowButton, selectIndex } = this.state;
    return (
      <Page>
        <SwipeAction
          style={{ backgroundColor: "gray" }}
          autoClose={true}
          right={[
            {
              text: "删除",
              onPress: () => {
                Modal.alert("删除", "是否确认删除此商品", [
                  { text: "取消", onPress: () => console.log("cancel") },
                  { text: "删除", onPress: () => this.deleteProduct(data.productSkuId, data.itemType, data.skuType) },
                ]);
              },
              style: { backgroundColor: "#ff4242", color: "white" },
            },
          ]}
          onOpen={() => console.log("global open")}
          onClose={() => console.log("global close")}
        >
          {
            data.itemType === $CartType.ITEMTYPE ? this.renderProduct(data, checkable, pricePermission, retailPriceViewPermission, orderSchemeType) : data.itemType === $CartType.VIRTUALSUIT ? this.renderVirtualSuit(data, checkable, pricePermission, retailPriceViewPermission, isShowButton, selectIndex, orderSchemeType) : this.renderActivitySuit(data, checkable, pricePermission, retailPriceViewPermission, orderSchemeType)
          }
        </SwipeAction>
      </Page>
    );
  }
}

const Page = styled.div`// styled
  & {
    /*.checkItem .am-list-thumb {
      position: relative;
      top: -20px;
      left: -8px;
    }*/
    .am-swipe-btn {
      width: 66px;
    }
  }
`;

const ShowButton = styled.div`// styled
  & {
    font-size: 9px;
    font-family: "PingFangSC-Regular";
    font-weight: 400;
    color: rgba(117, 117, 117, 1);
    text-align: right;
    .scmIconfont {
      color: #999999;
      position: relative;
      top: 2px;
    }
  }
`;

const VirtualSuitWrapper = styled.div`// styled
  & {
    .suitItem {
      position: absolute;
      top: 16px;
    }
  }
`;

const SuitPage = styled.div`// styled
  & {
    width: 100%;
    height: auto;
    .suitDetail {
      border-top: 1px solid #d8d8d8;
      border-bottom: 1px solid #d8d8d8;
      height: auto;
      > div {
        display: inline-block;
      }
      padding-left: 44px;
      .suitContent {
        width: 215px;
        position: absolute;
        top: 0px;
        right: 0px;
      }
    }
  }
`;

const SuitHeader = styled.div`// styled
  & {
    width: 100%;
    height: 40px;
    position: relative;
    /*.am-list-item {
      padding-left: 8px;
      top: 10px;
    }*/
    > div:nth-of-type(2) {
      position: absolute;
      top: 13px;
      left: 52px;
      display: inline-block;
      border-radius: 2px;
      font-size: 8px;
      color: #FF8627;
      text-align: center;
      border: 1px solid #FF8627;
      padding: 0 5px
    }
    > div:nth-of-type(3) {
      position: absolute;
      top: 13px;
      left: 105px;
      font-family: PingFangSC-Regular;
      font-size: 14px;
      color: #333333;
    }
  }
`;

const SuitFooter = styled.div`// styled
  & {
    width: 100%;
    height: 50px;
    position: relative;
    > div:nth-of-type(1) {
      position: absolute;
      top: 17px;
      left: 52px;
      font-size: 12px;
      color: #666666;
    }
    > div:nth-of-type(2) {
      position: absolute;
      top: 13px;
      left: 110px;
      font-size: 16px;
      color: #FF3030;
    }
    > div:nth-of-type(3) {
      position: absolute;
      top: 10px;
      right: 15px;
    }
  }
`;

const Spacing = styled.div`// styled
  & {
    width: 100%;
    height: 10px;
    background: #F2F2F2;
  }
`;

const ItemWrapper = styled.div`// styled
  & {
    display: flex;
    align-items: center;
    // height: 120px;
    padding: 10px 8px 10px 0px;
    background: #fff;
    position: relative;
    /*.am-list-item {
      padding-left: 0;
    }*/
    .am-list-line {
      border-bottom: 0 !important;
    }
  }
`;
const ImageWrapper = styled.div`// styled
  & {
    width: 80px;
    height: 80px;
    margin-right: 16px;
    align-items: center;
    position: relative;
    > img {
      width: 80px;
      height: 80px;
      border-radius: 3px;
    }
    > p {
      position: absolute;
      top: 15px;
      left: 15px;
      display: block;
      width: 50px;
      height: 50px;
      line-height: 50px;
      border: 1px solid transparent;
      border-radius: 25px;
      background: rgba(0, 0, 0, 0.5);
      color: #fff;
      text-align: center;
      z-index: 99;
    }
    > span:last-child {
      position: absolute;
      right: 0;
      bottom: 0;
      display: block;
      width: 40px;
      background: #ff7362;
      color: #fff;
      text-align: center;
    }
  }
`;
const ContentWrapper = styled.div`// styled
  & {
    width: 80%;
    height: 100%;
    box-sizing: border-box;
  }
`;

const InfoItem = styled.div`// styled
  & {
    width: 100%;
    display: flex;
  }
`;
const Title: any = styled.div`// styled
  & {
    font-size: 12px;
    color: #333;
    padding: 2px 0;
    text-overflow: ellipsis;
    overflow: hidden;
    white-space: nowrap;
    max-width: ${(props: any) => document.documentElement.clientWidth - 76 - 30 - (props.checkable ? 45 : 0)}px;
  }
`;
const Info: any = styled.div`// styled
  & {
    font-size: 11px;
    color: #666666;
    padding: 4px 0;
    text-overflow: ellipsis;
    overflow: hidden;
    white-space: nowrap;
    max-width: ${(props: any) => document.documentElement.clientWidth - 76 - 30 - (props.checkable ? 45 : 0)}px;
  }
`;

const ShopDetail = styled.div`// styled
  & {
    position: relative;
    padding: 9px 0;
    margin-top: 40px;
    :before {
      content: '';
      position: absolute;
      background-color: #D8D8D8 !important;
      display: block;
      z-index: 1;
      top: 0;
      right: auto;
      bottom: 0;
      left: 0;
      width: 100%;
      height: 1.2px;
      transform-origin: 50% 100%;
      transform: scaleY(0.5);
    }
    > p {
      margin-bottom: 8px;
      > span:nth-of-type(1) {
        border-radius: 11px;
        border: 1px solid rgba(255, 134, 39, 1);
        font-size: 8px;
        font-family: "PingFangSC-Regular";
        font-weight: 400;
        color: rgba(255, 134, 39, 1);
        margin-right: 16px;
        display: inline-block;
        width: 48px;
        height: 16px;
        line-height: 16px;
        text-align: center;
      }
      > span:nth-of-type(2) {
        display: inline-block;
        width: 95px;
        text-align: left;
        font-size: 9px;
        font-family: "PingFangSC-Regular";
        font-weight: 400;
        color: rgba(117, 117, 117, 1);
        margin-right: 5px;
      }
      > span:nth-of-type(3) {
        display: inline-block;
        width: calc(100% - 48px - 120px);
        text-align: right;
        font-size: 9px;
        font-family: "PingFangSC-Regular";
        font-weight: 400;
        color: rgba(117, 117, 117, 1);
      }
    }
  }
`;

const Content = styled.div`// styled
  & {
    .check {
      font-size: 10px;
      color: #999999;
      margin-top: 4px;
    }
    > div:nth-of-type(2) {
      .order-price {
        margin-top: 4px;
        color: #ff3030;
        font-size: 14px;
        font-family: SourceHanSansCN-Normal, SourceHanSansCN;
      }
      .retail-price {
        color: #ff3030;
        font-size: 10px;
        font-family: SourceHanSansCN-Normal, SourceHanSansCN;
      }

      .gray-price {
        color: #999999;
        font-size: 10px;
        font-family: SourceHanSansCN-Normal, SourceHanSansCN;
      }
    }
    > div:nth-of-type(3) {
      > span {
        display: inline-block;
        border-radius: 2px;
        text-align: center;
        margin-right: 5px;
        font-size: 8px;
        padding: 0 5px;
      }
    }
    > div:nth-of-type(4) {
      color: #999;
      font-size: 10px;
    }

    .show-price {
      display: flex;
      flex-direction: column;
    }
  }
`;
const Counter = styled.div`// styled
  & {
    position: absolute;
    right: 15px;
    bottom: 10px;
  }
`;

const Add = styled.div`// styled
  & {

  }
`;
