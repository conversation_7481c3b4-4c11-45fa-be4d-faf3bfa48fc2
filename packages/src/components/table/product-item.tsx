import { autowired } from "@classes/ioc/ioc";
import { Checkbox } from "antd-mobile";
import { observer } from "mobx-react";
import * as React from "react";
import { withRouter } from "react-router";
import styled from "styled-components";
import { SITE_PATH } from "../../modules/app";
import { $ProductListMv } from "../../modules/product-list/$product-list-mv";
import { $CartMv } from "../../modules/shop-cart/cart-mv";
import { ProductAnchorEnum } from "../product-component";
import { ProductCount } from "../product-count/product-count";
import { $ProductMv } from "../product/product-mv";
import { $CartType } from "../../classes/const/$cart-type";
import { $AppStore, AppStoreKey } from "../../classes/stores/app-store-mv";

const CheckboxItem = Checkbox.CheckboxItem;

export const TYPE_SELECT = "select";

@withRouter
@observer
export class ProductItem extends React.Component<any, any> {
  @autowired($ProductMv)
  public $ProductMv: $ProductMv;

  @autowired($CartMv)
  public $CartMv: $CartMv;

  @autowired($ProductListMv)
  public $productListMv: $ProductListMv;

  @autowired($AppStore)
  public $appStore: $AppStore;

  constructor(props) {
    super(props);
    this.state = {
      isShowButton: false,
      selectIndex: 2,
    };
  }

  public onChecked = (checked) => {
    const { data } = this.props;
    this.props.onItemClick(TYPE_SELECT, data, checked);
  }

  public onClick = (data, isDetail) => {
    if (isDetail) {
      return false;
    }
    this.$ProductMv.setProduct(data);
    this.props.history.push({ pathname: `/${SITE_PATH}/product-detail` });
  }

  public toDetail = (data, query: string = "") => {
    this.$productListMv.setDetailObject(data);
    const { leaveCurrentPage } = this.props;
    leaveCurrentPage && leaveCurrentPage();
    setTimeout(() => {
      if (data.skuType === $CartType.VIRTUALSUIT) {
        window.location.href = `/${SITE_PATH}/virtualsuit-detail/${data.productSkuId}${query}`;
      } else {
        window.location.href = `/${SITE_PATH}/commodity-details/${data.productSkuId}${query}`;
      }
    }, 50);
  }

  public toDetailByOrder = (e) => {
    const { data } = this.props;
    e.stopPropagation();
    this.toDetail(data, `?anchor=${ProductAnchorEnum.ORDER}`);
  }

  public filterTagInfo(n) {
    switch (n) {
      case "Deduct":
        return "#8DBD00";
      case "Discount":
        return "#F7B51C";
      case "Special":
        return "#F25EA9";
      case "PartFree":
        return "#B385F5";
      case "Gift":
        return "#FF7564";
      case "MutiDiscount":
        return "#F7B51C";
      case "TradeUp":
        return "#FF8627";
    }
  }

  public showButton = () => {
    const { data } = this.props;
    const { isShowButton } = this.state;
    if (isShowButton) {// 收起
      this.setState({
        isShowButton: false,
        selectIndex: 2,
      });
    } else { // 展开
      this.setState({
        isShowButton: true,
        selectIndex: data.virtualSuitList ? data.virtualSuitList.length : 0,
      });
    }
  }

  public render() {
    const { data, noCart, hideCount, isGift, pricePermission, retailPriceViewPermission, orderSchemeType, showOrder } = this.props;
    const { isShowButton, selectIndex } = this.state;
    const { realInventoryQuantity, suggestOrderQuantity } = data;
    const showOrderBox = showOrder && (realInventoryQuantity !== "" && realInventoryQuantity !== undefined && realInventoryQuantity !== null);
    return (
      <ItemWrapper style={hideCount ? { marginBottom: "0px" } : {}}>
        <div className="product-img" onClick={() => this.toDetail(data)}>
          {
            data.imageUrl ? <img src={data.imageUrl} alt=""/> :
              <img src="https://order.fwh1988.cn:14501/static-img/scm/ico-nopic.png" alt=""/>}
          {
            hideCount || isGift ? null :
              <div className="img-tags">
                {
                  data.productTagList ? data.productTagList.length > 0 ?
                    <span>{data.productTagList[0]}</span> : null : null
                }
              </div>
          }
        </div>
        <div className="product-item-text">
          <h5 onClick={() => this.toDetail(data)}>{data.name}</h5>
          <div className="check" onClick={() => this.toDetail(data)}>
            {data.startQuantity ? `最少购买数量为${data.startQuantity}` : null}
            {data.startQuantity && data.multiple ? "," : null}
            {data.multiple ? `依次${data.multiple}倍增加` : null}
          </div>
          <div className="price" onClick={() => this.toDetail(data)}>
            {
              data.deductionAmountFlag && <span className="dedeuction-tag">额度值</span>
            }
            {
              (pricePermission === $CartType.ORDERPRICEVIEWPERMISSION) && (data.orderPrice !== null) ?
                <span className="redprice">
                  <span style={{fontSize: "10px"}}>￥</span>{ data.deductionAmountFlag ? data.deductionAmount : data.orderPrice}
                  <small className="blackprice">{data.unitOfMeasure && `/${data.unitOfMeasure}`}</small>
                </span> : null
            }
            {
              (retailPriceViewPermission === $CartType.RETAILPRICEVIEWPERMISSION) && (data.retailPrice !== null) ?
                <span className="blackprice">
                  {((pricePermission === $CartType.ORDERPRICEVIEWPERMISSION) && (data.orderPrice !== null)) ? " (" : null}零售 ￥{data.retailPrice}{((pricePermission === $CartType.ORDERPRICEVIEWPERMISSION) && (data.orderPrice !== null)) ? ")" : null}{((pricePermission !== $CartType.ORDERPRICEVIEWPERMISSION) || (data.orderPrice === null)) ? <small className="blackprice">{data.unitOfMeasure && `/${data.unitOfMeasure}`}</small> : null}
                  </span> : <span/>
            }
          </div>
          {showOrderBox && <div className={"order-quantity-box"}>
            <div className={"order-quantity"}>
              门店实物库存：<span>{realInventoryQuantity}</span>
            </div>
            <div className={"order-quantity"}>
              建议订货量：<span>{suggestOrderQuantity}</span>
              <div onClick={this.toDetailByOrder} className={"more"}>查看数据></div>
            </div>
          </div>}
          {/*{*/}
          {/*pricePermission === $CartType.ORDERPRICEVIEWPERMISSION ? <div className="price">*/}
          {/*<span>￥{data.orderPrice} </span>*/}
          {/*<span><small>{data.unitOfMeasure}</small> / 零售 ￥{data.retailPrice}</span>*/}
          {/*</div> : <div className="price">*/}
          {/*<span>零售 ￥{data.retailPrice}</span>*/}
          {/*</div>*/}
          {/*}*/}
          {
            hideCount ? <div className="tags">
                <span>{data.itemTypeName}</span>
              </div> :
              <div>
                <div className="tags">
                  {
                    data.promotionTagList && data.promotionTagList.length > 2 ? data.promotionTagList.slice(0, 2).map((v, i) => {
                      const color = this.filterTagInfo(v.promotionTagCode);
                      return <span key={i} style={{
                        color: `${color}`,
                        border: `0.5px solid ${color}`,
                      }}>{v.promotionTagLabel}</span>;
                    }) : data.promotionTagList.map((v, i) => {
                      const color = this.filterTagInfo(v.promotionTagCode);
                      return <span key={i} style={{
                        color: `${color}`,
                        border: `0.5px solid ${color}`,
                      }}>{v.promotionTagLabel}</span>;
                    })
                  }
                  {
                    data.promotionTagList && data.promotionTagList.length > 2 && <span>...</span>
                  }
                </div>
                <p>{data.stockStatusDesc}</p>
                <div className="choose-num">
                  <ProductCount data={data} noCart={noCart} orderSchemeType={orderSchemeType} source={""}
                                fromWhere={"product"}/>
                </div>
              </div>
          }
          {
            data.skuType === $CartType.VIRTUALSUIT ? <ShopDetail>
              {
                data.virtualSuitList.slice(0, selectIndex).map((product, index) => {
                  return (
                    <p key={index}>
                      <span>套装明细</span>
                      <span>{product.name ? product.name.length > 6 ? product.name.slice(0, 6) + "..." : product.name : null}</span>
                      <span>x{product.quantity}</span>
                    </p>
                  );
                })
              }
              {
                data.virtualSuitList ? data.virtualSuitList.length > 2 ?
                  <ShowButton onClick={this.showButton}>
                    {isShowButton ? "点击收起" : "展开更多"}
                    {isShowButton ? <i className="scmIconfont scm-icon-jiantou-shang"/> :
                      <i className="scmIconfont scm-icon-arrow-down"/>}
                  </ShowButton> : null : null}
            </ShopDetail> : null
          }
        </div>
      </ItemWrapper>
    );
  }
}

const ItemWrapper = styled.div`// styled
  & {
    padding-left: 89px;
    padding-right: 15px;
    position: relative;
    margin-bottom: 15px;
    .hide {
      display: none;
    }
    .product-img {
      width: 80px;
      height: 80px;
      position: absolute;
      left: 0;
      top: 0;
      img {
        width: 100%;
        height: 100%;
        border-radius: 3px;
      }
      .img-tags {
        position: absolute;
        bottom: 0px;
        right: 0;
        height: 14px;
        line-height: 14px;
        span {
          font-size: 8px;
          color: #FFFFFF;
          background: #ff7564;
          margin-left: 5px;
          padding: 0px 2px;
        }
      }
    }
    .product-item-text {
      width: 100%;
      h5 {
        color: #202020;
        overflow: hidden;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 2;
        max-height: 32px;
        line-height: 16px;
        margin: 0;
      }
      .check {
        font-size: 10px;
        color: #999999;
        margin-top: 4px;
      }
      .price {
        height: 18px;
        line-height: 18px;

        .dedeuction-tag {
          background: #417BEE;
          color: #FFFFFF;
          font-size: 11px;
          border-radius: 2px;
          padding: 2px 4px;
        }

        .redprice {
          font-size: 14px;
          color: #ff3030;
          font-family: SourceHanSansCN-Normal, SourceHanSansCN;
        }
        .blackprice {
          font-size: 10px;
          color: #999999;
          font-family: SourceHanSansCN-Normal, SourceHanSansCN;
        }
      }
      .order-quantity-box {
        font-size: 12px;

        > .order-quantity {
          color: #999;
          display: flex;
          align-items: center;
          margin-top: 4px;
          line-height: 18px;

          > span {
            color: rgb(141, 189, 0);
          }

          > .more {
            margin-left: auto;
            color: #437DF0;
            font-size: 13px;
            flex-shrink: 0;
          }
        }
      }
      .small {
        color: #ff3030;
      }
      .tags {
        height: 16px;
        line-height: 16px;
        span {
          font-size: 8px;
          padding: 0px 4px;
          color: #FF7362;
          border: 0.5px solid #FF7362;
          border-radius: 2px;
          margin-right: 10px;
        }
      }
      p {
        height: 22px;
        line-height: 22px;
        color: #999999;
        font-size: 10px;
        margin: 0;
        padding: 0;
      }
      .choose-num {
        width: 100%;
        text-align: right;
        margin-top: -5px;
      }
    }
  }
`;

const ShopDetail = styled.div`// styled
  & {
    position: relative;
    padding: 9px 0;
    margin-top: 8px;
    :before {
      content: '';
      position: absolute;
      background-color: #D8D8D8 !important;
      display: block;
      z-index: 1;
      top: 0;
      right: auto;
      bottom: 0;
      left: 0;
      width: 100%;
      height: 1.2px;
      transform-origin: 50% 100%;
      transform: scaleY(0.5);
    }
    > p {
      margin-bottom: 8px;
      > span:nth-of-type(1) {
        border-radius: 11px;
        border: 1px solid rgba(255, 134, 39, 1);
        font-size: 8px;
        font-family: "PingFangSC-Regular";
        font-weight: 400;
        color: rgba(255, 134, 39, 1);
        margin-right: 6px;
        display: inline-block;
        width: 48px;
        height: 16px;
        line-height: 16px;
        text-align: center;
      }
      > span:nth-of-type(2) {
        display: inline-block;
        width: 70px;
        text-align: left;
        font-size: 9px;
        font-family: "PingFangSC-Regular";
        font-weight: 400;
        color: rgba(117, 117, 117, 1);
        margin-right: 5px;
      }
      > span:nth-of-type(3) {
        display: inline-block;
        width: calc(100% - 48px - 85px);
        text-align: right;
        font-size: 9px;
        font-family: "PingFangSC-Regular";
        font-weight: 400;
        color: rgba(117, 117, 117, 1);
      }
    }
  }
`;

const ShowButton = styled.div`// styled
  & {
    font-size: 9px;
    font-family: "PingFangSC-Regular";
    font-weight: 400;
    color: rgba(117, 117, 117, 1);
    text-align: right;
    .scmIconfont {
      color: #999999;
      position: relative;
      top: 2px;
    }
  }
`;
