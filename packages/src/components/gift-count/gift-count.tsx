import { autowired } from "@classes/ioc/ioc";
import { Button, InputItem, Modal, Toast } from "antd-mobile";
import { transaction } from "mobx";
import { observer } from "mobx-react";
import React from "react";
import styled from "styled-components";
import { $CartType } from "../../classes/const/$cart-type";
import { $proChooseGiftMv } from "../../modules/promotion-choose-products/pro-choose-gift-mv";
import { $ProductCountMv } from "../product-count/product-count-mv";
import { $ProductMv } from "../product/product-mv";
import { $PromotionType } from "../../classes/const/$promotion-type";
import "../product-count/product-count.less";
import { InputNumberItemComponent } from "../input-number-item-component/input-number-item-component";

@observer
export class GiftCount extends React.Component<any, any> {
  @autowired($proChooseGiftMv)
  public $proChooseGiftMv: $proChooseGiftMv;

  @autowired($ProductMv)
  public $ProductMv: $ProductMv;

  public $ProductCountMv: $ProductCountMv = new $ProductCountMv();

  constructor(props) {
    super(props);
    // this.onRemoveClick = debounce(this.onRemoveClick, 200);
  }

  public onAddClick = (v, pindex, index) => {
    const { data } = this.props;
    this.$proChooseGiftMv.addQuantityByProduct(v, pindex, index);
  }

  public onRemoveClick = (pindex, index) => {
    const { data } = this.props;
    this.$proChooseGiftMv.minusQuantityByProduct(pindex, index);
  }

  public onConfirm = (pindex, index) => {
    document.removeEventListener("touchmove", this.bodyScroll, { passive: false });
    let multiple;
    multiple = this.$proChooseGiftMv.promotionGiftGroup[pindex].giftList[index].multiple;
    if (multiple) {
      if (this.$ProductCountMv.inputValue % multiple !== 0) {
        Toast.info(`请购买${multiple}的倍数`, 3);
        return;
      }
    }
    if (this.$ProductCountMv.inputValue && this.$ProductCountMv.isChange) {
      const differ = this.$ProductCountMv.inputValue - this.$proChooseGiftMv.promotionGiftGroup[pindex].giftList[index].quantity;
      const { donationAmountType, donationAmount, quantity, hadSelect, optionalType, repeatType, amountType, amount, usePriceType, scopeType, donationQuantityType } = this.$proChooseGiftMv.promotionGiftGroup[pindex].groupInfo;
      const canNum = quantity - hadSelect;
      if ((scopeType === $PromotionType.SCOPERANGE) || (scopeType === $PromotionType.OPTIONALGROUP)) {   // 数量控制
        if ((optionalType && optionalType !== "Many") || (repeatType === "No")) {
          if (scopeType === $PromotionType.OPTIONALGROUP) {
            if (this.$ProductCountMv.inputValue > 1 * this.$proChooseGiftMv.times) {
              Toast.info(`每件商品只可选择${1 * this.$proChooseGiftMv.times}件`, 3);
              return;
            }
          } else {
            if (this.$ProductCountMv.inputValue > 1 * this.$proChooseGiftMv.times) {
              Toast.info(`每件商品只可选择${1 * this.$proChooseGiftMv.times}件`, 3);
              return;
            }
          }
        }
        if ((differ > canNum) && (donationQuantityType === $PromotionType.DONATIONQUANTITYTYPELimit || donationQuantityType === null)) {
          // Toast.info(`最多可选择${quantity}件`, 3);
          Toast.info(`剩余可选商品数量为${canNum}个，请合理选购`, 3);
          return;
        }
      } else {
        if (quantity > 0) {
          if (differ > canNum) {
            // Toast.info(`最多可选择${quantity}件`, 3);
            Toast.info(`剩余可选商品数量为${canNum}个，请合理选购`, 3);
            return;
          }
        }
        
        if (amountType === "InPrice" || amountType === null) {    // 金额控制
          if (this.$ProductCountMv.inputValue) { // 当用户改变输入框值时
            if (usePriceType && usePriceType === $PromotionType.USERETAILPRICE) {
              if (this.$ProductCountMv.inputValue >= this.$proChooseGiftMv.promotionGiftGroup[pindex].giftList[index].quantity) {
                this.$proChooseGiftMv.promotionGiftGroup[pindex].groupInfo.hadMoney += this.$proChooseGiftMv.promotionGiftGroup[pindex].giftList[index].retailPrice * (this.$ProductCountMv.inputValue - this.$proChooseGiftMv.promotionGiftGroup[pindex].giftList[index].quantity);
              } else {
                this.$proChooseGiftMv.promotionGiftGroup[pindex].groupInfo.hadMoney -= this.$proChooseGiftMv.promotionGiftGroup[pindex].giftList[index].retailPrice * (this.$ProductCountMv.inputValue - this.$proChooseGiftMv.promotionGiftGroup[pindex].giftList[index].quantity);
              }
              console.log(this.$proChooseGiftMv.promotionGiftGroup[pindex].groupInfo.hadMoney);
              if (amount) {
                if (amount < this.$proChooseGiftMv.promotionGiftGroup[pindex].groupInfo.hadMoney) {
                  if (this.$ProductCountMv.inputValue >= this.$proChooseGiftMv.promotionGiftGroup[pindex].giftList[index].quantity) {
                    this.$proChooseGiftMv.promotionGiftGroup[pindex].groupInfo.hadMoney -= this.$proChooseGiftMv.promotionGiftGroup[pindex].giftList[index].retailPrice * (this.$ProductCountMv.inputValue - this.$proChooseGiftMv.promotionGiftGroup[pindex].giftList[index].quantity);
                  }
                  Toast.info(`所选商品价格超出${amount ? Number(amount).toFixed(2) : 0}元，请合理选购`, 3);
                  return;
                }
              } else {
                if (donationAmount < this.$proChooseGiftMv.promotionGiftGroup[pindex].groupInfo.hadMoney) {
                  if (this.$ProductCountMv.inputValue >= this.$proChooseGiftMv.promotionGiftGroup[pindex].giftList[index].quantity) {
                    this.$proChooseGiftMv.promotionGiftGroup[pindex].groupInfo.hadMoney -= this.$proChooseGiftMv.promotionGiftGroup[pindex].giftList[index].retailPrice * (this.$ProductCountMv.inputValue - this.$proChooseGiftMv.promotionGiftGroup[pindex].giftList[index].quantity);
                  }
                  Toast.info(`所选商品价格超出${donationAmount ? Number(donationAmount).toFixed(2) : 0}元，请合理选购`, 3);
                  return;
                }
              }
            } else {
              if (this.$ProductCountMv.inputValue >= this.$proChooseGiftMv.promotionGiftGroup[pindex].giftList[index].quantity) {
                this.$proChooseGiftMv.promotionGiftGroup[pindex].groupInfo.hadMoney += this.$proChooseGiftMv.promotionGiftGroup[pindex].giftList[index].orderPrice * (this.$ProductCountMv.inputValue - this.$proChooseGiftMv.promotionGiftGroup[pindex].giftList[index].quantity);
              } else {
                this.$proChooseGiftMv.promotionGiftGroup[pindex].groupInfo.hadMoney -= this.$proChooseGiftMv.promotionGiftGroup[pindex].giftList[index].orderPrice * (this.$ProductCountMv.inputValue - this.$proChooseGiftMv.promotionGiftGroup[pindex].giftList[index].quantity);
              }
              console.log(this.$proChooseGiftMv.promotionGiftGroup[pindex].groupInfo.hadMoney);
              if (amount) { // 固定金额
                if (amount < this.$proChooseGiftMv.promotionGiftGroup[pindex].groupInfo.hadMoney) {
                  if (this.$ProductCountMv.inputValue >= this.$proChooseGiftMv.promotionGiftGroup[pindex].giftList[index].quantity) {
                    this.$proChooseGiftMv.promotionGiftGroup[pindex].groupInfo.hadMoney -= this.$proChooseGiftMv.promotionGiftGroup[pindex].giftList[index].orderPrice * (this.$ProductCountMv.inputValue - this.$proChooseGiftMv.promotionGiftGroup[pindex].giftList[index].quantity);
                  }
                  Toast.info(`所选商品价格超出${amount ? Number(amount).toFixed(2) : 0}元，请合理选购`, 3);
                  return;
                }
              }
            }
          }
        }
      }
      if (donationAmountType === "Limited") { // 当促销活动为赠送且为指定范围产品中赠送金额限制时
        console.log(this.$proChooseGiftMv.isDonationAmont);
        const initQuantity = this.$proChooseGiftMv.promotionGiftGroup[pindex].giftList[index].quantity;
        const initSelectDonationAmount = this.$proChooseGiftMv.selectDonationAmount;
        if (this.$ProductCountMv.inputValue) {
          if (this.$ProductCountMv.inputValue >= this.$proChooseGiftMv.promotionGiftGroup[pindex].giftList[index].quantity) {
            if (usePriceType && usePriceType === $PromotionType.USERETAILPRICE) {
              this.$proChooseGiftMv.selectDonationAmount += this.$proChooseGiftMv.promotionGiftGroup[pindex].giftList[index].retailPrice * (this.$ProductCountMv.inputValue - this.$proChooseGiftMv.promotionGiftGroup[pindex].giftList[index].quantity);
            } else {
              this.$proChooseGiftMv.selectDonationAmount += this.$proChooseGiftMv.promotionGiftGroup[pindex].giftList[index].orderPrice * (this.$ProductCountMv.inputValue - this.$proChooseGiftMv.promotionGiftGroup[pindex].giftList[index].quantity);
            }
            this.$proChooseGiftMv.promotionGiftGroup[pindex].giftList[index].quantity = this.$ProductCountMv.inputValue;
            console.log(8989898);
          } else {
            console.log(this.$proChooseGiftMv.isDonationAmont);
            if (this.$proChooseGiftMv.isDonationAmont === false) {
              if (usePriceType && usePriceType === $PromotionType.USERETAILPRICE) {
                this.$proChooseGiftMv.selectDonationAmount -= this.$proChooseGiftMv.promotionGiftGroup[pindex].giftList[index].retailPrice;
              } else {
                this.$proChooseGiftMv.selectDonationAmount -= this.$proChooseGiftMv.promotionGiftGroup[pindex].giftList[index].orderPrice;
              }
            }
            if (usePriceType && usePriceType === $PromotionType.USERETAILPRICE) {
              this.$proChooseGiftMv.selectDonationAmount -= this.$proChooseGiftMv.promotionGiftGroup[pindex].giftList[index].retailPrice * (this.$proChooseGiftMv.promotionGiftGroup[pindex].giftList[index].quantity - this.$ProductCountMv.inputValue);
            } else {
              this.$proChooseGiftMv.selectDonationAmount -= this.$proChooseGiftMv.promotionGiftGroup[pindex].giftList[index].orderPrice * (this.$proChooseGiftMv.promotionGiftGroup[pindex].giftList[index].quantity - this.$ProductCountMv.inputValue);
            }
            this.$proChooseGiftMv.promotionGiftGroup[pindex].giftList[index].quantity = this.$ProductCountMv.inputValue;
            console.log(6767676);
          }
          let sum = 0;
          if (usePriceType && usePriceType === $PromotionType.USERETAILPRICE) {
            this.$proChooseGiftMv.promotionGiftGroup[pindex].giftList.map((list) => {
              sum += list.retailPrice * list.quantity;
            })
            console.log(sum);
          } else {
            this.$proChooseGiftMv.promotionGiftGroup[pindex].giftList.map((list) => {
              sum += list.orderPrice * list.quantity;
            })
            console.log(sum);
          }
          console.log(this.$proChooseGiftMv.selectDonationAmount);
          if ((amountType === "InPrice" || amountType === null) && sum > (donationAmount * this.$proChooseGiftMv.times)) { // amountType === "PriceDiff" 指超过指定金额后补差价
            /*Toast.info(`所选赠送金额最多为${(donationAmount * this.$proChooseGiftMv.times) ? Number(donationAmount * this.$proChooseGiftMv.times).toFixed(2) : 0}元，请合理选购`, 3);*/
            this.$proChooseGiftMv.selectDonationAmount = initSelectDonationAmount;
            console.log(this.$proChooseGiftMv.selectDonationAmount);
            this.$proChooseGiftMv.promotionGiftGroup[pindex].giftList[index].quantity = initQuantity;
            Toast.info(`剩余可选金额${Number(donationAmount * this.$proChooseGiftMv.times - this.$proChooseGiftMv.selectDonationAmount).toFixed(2)}元，请合理选购`, 3);
            // this.$proChooseGiftMv.isDonationAmont = false;
            return;
          }
        }
      }
      this.$proChooseGiftMv.promotionGiftGroup[pindex].groupInfo.hadSelect += differ;
      if (amountType === "PriceDiff") {
        if (usePriceType && usePriceType === $PromotionType.USERETAILPRICE) {
          const differMoney = this.$proChooseGiftMv.promotionGiftGroup[pindex].giftList[index].retailPrice * differ;
          this.$proChooseGiftMv.promotionGiftGroup[pindex].groupInfo.hadMoney += differMoney;
        } else {
          const differMoney = this.$proChooseGiftMv.promotionGiftGroup[pindex].giftList[index].orderPrice * differ;
          this.$proChooseGiftMv.promotionGiftGroup[pindex].groupInfo.hadMoney += differMoney;
        }
      }
    }
    if (this.$ProductCountMv.inputValue) {
      if (this.$ProductCountMv.inputValue > 0) {
        this.$proChooseGiftMv.promotionGiftGroup[pindex].giftList[index].checkFlag = "Y";
      } else {
        this.$proChooseGiftMv.promotionGiftGroup[pindex].giftList[index].checkFlag = "N";
      }
    }
    if (this.$ProductCountMv.inputValue) {
      if (this.$ProductCountMv.isChange) {
        this.$proChooseGiftMv.promotionGiftGroup[pindex].giftList[index].quantity = this.$ProductCountMv.inputValue;
        this.$ProductCountMv.isChange = false;
        console.log(66666);
        this.$proChooseGiftMv.isDonationAmont = true;
      }
    }
    transaction(() => {
      this.$ProductCountMv.closeModal();
    });
  }

  public onBlur = () => {
    window.scroll(0, 0);
  }

  public bodyScroll = (e) => {
    e.preventDefault();
  }

  public closeModal = () => {
    document.removeEventListener("touchmove", this.bodyScroll, { passive: false });
    this.$ProductCountMv.closeModal();
  }

  public openModal = () => {
    document.addEventListener("touchmove", this.bodyScroll, { passive: false });
    this.$ProductCountMv.openModal();
  }

  public render() {
    const { data, pindex, index, orderSchemeType } = this.props;
    const { quantity, isActive, stockStatus } = data;
    const { showModal, inputValue } = this.$ProductCountMv;
    return (
      <Wrapper onClick={(e) => e.stopPropagation()}>
        <Button className="op" onClick={() => this.onRemoveClick(pindex, index)} disabled={
          quantity <= 0 || isActive === $CartType.ISACTIVE_KEY || (orderSchemeType !== $CartType.FUTURE_ORDER && stockStatus === $CartType.STOCKSTATUS)}
        >-</Button>
        <span className="input">
                <InputItem
                  type="number"
                  value={String(quantity)}
                  editable={false}
                  disabled={isActive === $CartType.ISACTIVE_KEY || (orderSchemeType !== $CartType.FUTURE_ORDER && stockStatus === $CartType.STOCKSTATUS)}
                  onChange={() => {
                    // do nothing
                  }}
                  onClick={() => this.openModal()}
                />
          </span>
        <Button className="op" onClick={() => this.onAddClick(quantity + 1, pindex, index)}
                disabled={isActive === $CartType.ISACTIVE_KEY || (orderSchemeType !== $CartType.FUTURE_ORDER && stockStatus === $CartType.STOCKSTATUS)}>+</Button>
        <Modal
          title={<div>请输入数量</div>}
          visible={showModal}
          transparent={true}
          maskClosable={false}
          className="model"
          footer={[
            { text: "取消", onPress: () => this.closeModal() },
            { text: "确定", onPress: () => this.onConfirm(pindex, index) },
          ]}
        >
          {/*<InputItem*/}
            {/*type="number" defaultValue={String(quantity)}*/}
            {/*onChange={(value) => this.$ProductCountMv.setInputValue(Number(value))}*/}
            {/*style={{ textAlign: "center" }}*/}
            {/*onBlur={this.onBlur}*/}
          {/*/>*/}
          <InputNumberItemComponent
            style={{ textAlign: "center" }}
            type="money"
            defaultValue={String(quantity)}
            onChange={(value) => this.$ProductCountMv.setInputValue(value)}
            value={inputValue && String(inputValue)}
            onBlur={this.onBlur}
          />
        </Modal>
      </Wrapper>
    );
  }
}

const Wrapper = styled.div`// styled
  & {
    .op {
      height: 24px;
      line-height: 20px;
      width: 24px;
      border-radius: 12px;
      display: inline-block;
      text-align: center;
      border: 1px solid transparent;
      font-size: 18px;
      color: #fff;
      background-color: #307dcd;
    }
    .input {
      display: inline-block;
      width: 60px;
      height: 24px;
      line-height: 24px;
      margin: 0 4px;
      position: relative;
      top: -5px;
      .am-list-item.am-input-item {
        height: 25px;
        min-height: 22px;
        padding: 0;
      }
      .am-list-line {
        padding: 0;
      }
      .am-input-control input {
        text-align: center;
        font-size: 14px;
        color: #202020;
        height: 23px;
        border-radius: 5px;
        border: 1px solid #ddd;
      }
    }
    .am-button {
      &:before {
        width: 0 !important;
        height: 0 !important;
        border: none !important;
      }
    }
  }`;
