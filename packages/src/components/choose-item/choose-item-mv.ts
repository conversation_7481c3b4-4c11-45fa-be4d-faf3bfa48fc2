import { autowired, bean } from "@classes/ioc/ioc";
import { action, observable } from "mobx";
import { ChooseItemService } from "./choose-item-service";

@bean(ChooseItemMv)
export class ChooseItemMv {
  @autowired(ChooseItemService)
  public chooseItemService: ChooseItemService;

  @observable public chooseItemList: any[] = [];

  @observable public isSpin: boolean = false;

  @observable public page = 0;

  @action
  public showSpin() {
    this.isSpin = true;
  }

  @action
  public hideSpin() {
    this.isSpin = false;
  }

  @action
  public changePage() {
    this.page++;
  }

  @action
  public loadData(url, params) {
    return this.chooseItemService.loadData(url, params);
  }
}
