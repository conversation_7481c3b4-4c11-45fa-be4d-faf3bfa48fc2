import * as React from "react";
import { observer } from "mobx-react";
import { InputItem } from "antd-mobile";
import "./style.less";

@observer
export class InputItemComponent extends React.Component<any, any> {

  public formatValue = (value) => {
    let offLineTransferAmount = value;
    const regStrs = [
      [/[^\d.]/g, ""], // 清除"数字"和"."以外的字符
      [/\.{2,}/g, "."], // 验证第一个字符是数字而不是
      [".", "$#$"], // 只保留第一个. 清除多余的
      [/\./g, ""], //
      ["$#$", "."], //
      [/^(\-)*(\d+)\.(\d\d).*$/, "$1$2.$3"], // 只能输入两个小数
    ];
    for (let i = 0; i < regStrs.length; i++) {
      offLineTransferAmount = String(offLineTransferAmount).replace(regStrs[i][0], regStrs[i][1]);
    }
    console.log(1111);
    console.log(offLineTransferAmount);
    // offLineTransferAmount = value.replace(/[^\d.]/g, "").replace(/\.{2,}/g, ".").replace(".", "$#$").replace(/\./g, "").replace("$#$", ".").replace(/^(\-)*(\d+)\.(\d\d).*$/, "$1$2.$3").replace()
    // offLineTransferAmount = offLineTransferAmount ? String(parseFloat(offLineTransferAmount)) : offLineTransferAmount; todo
    console.log(2222);
    console.log(offLineTransferAmount);
    if ((offLineTransferAmount == 0 && offLineTransferAmount.indexOf(".") < 0) || offLineTransferAmount === "") {
      console.log(3333);
      return offLineTransferAmount !== "" ? Number(offLineTransferAmount) : offLineTransferAmount;
    } else if (offLineTransferAmount !== "" && offLineTransferAmount.indexOf(".") < 0) {
      console.log(4444);
      offLineTransferAmount = offLineTransferAmount.slice(0, 7);
    } else if (offLineTransferAmount !== "" && offLineTransferAmount.indexOf(".") >= 0) { // 最大七位整数保证只有一个小数点
      let integer = offLineTransferAmount.split(".")[0];
      integer = integer && integer.slice(0, 7);
      offLineTransferAmount = integer + "." + offLineTransferAmount.split(".")[1];
      console.log(55555);
      console.log(offLineTransferAmount);
    }
    // return Number(offLineTransferAmount);
    return offLineTransferAmount;
  }

  public onChange = (value) => {
    console.log(value);
    const filterValue = this.formatValue(value);
    console.log(filterValue);
    this.props.onChange(String(filterValue));
  }

  public onBlur = () => {
    window.scrollTo(0, 0);
  }

  public onFocus = () => {
    window.scrollTo(0, 0);
    $("body").css("height", "100%");
  }

  public render() {
    const { type, value, placeholder, defaultValue, disabled, clear, maxLength, label, pattern } = this.props;
    const isIPhone = new RegExp("\\biPhone\\b|\\biPod\\b", "i").test(window.navigator.userAgent);
    let moneyKeyboardWrapProps;
    if (isIPhone) {
      moneyKeyboardWrapProps = {
        onTouchStart: (e) => e.preventDefault(),
      };
    }
    return (
      <InputItem
        type={type}
        value={value}
        placeholder={placeholder}
        defaultValue={defaultValue}
        disabled={disabled}
        clear={clear}
        maxLength={maxLength}
        onChange={this.onChange}
        pattern={pattern}
        onBlur={this.onBlur}
        onFocus={this.onFocus}
        moneyKeyboardAlign="left"
        moneyKeyboardWrapProps={moneyKeyboardWrapProps}
      >
        {label}
      </InputItem>
    );
  }
}
