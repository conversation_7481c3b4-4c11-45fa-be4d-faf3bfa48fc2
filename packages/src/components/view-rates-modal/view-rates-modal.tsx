import * as React from "react";
import {observer} from "mobx-react";
import styled from "styled-components";

@observer
export class ViewRatesModal extends React.Component<any, any> {
  public render() {
    const {showModal, hideRateModal, selectRateList} = this.props;
    console.log(selectRateList, "selectRateList");
    return (
      <ViewRatesModalWrapper theme={{ showModal }}>
        <ModalContent>
          <ModalHeader>
            查看费率
          </ModalHeader>
          <ModalScroll>
            <div className={"name"}>通联支付-{selectRateList && selectRateList.rateName}</div>
            <ModalTable>
              <TableTitle>
                <span>支付金额下限</span>
                <span>支付金额上限</span>
                <span>费率</span>
              </TableTitle>
              <TableContent>
                {
                  selectRateList && selectRateList.rateBookList.map((item, index) => {
                    return (
                      <div style={{background: index % 2 === 0 && "#fff"}}>
                        <span>{item.beginValue}</span>
                        <span>{item.endValue}</span>
                        <span>{item.rate}</span>
                      </div>
                    );
                  })
                }
              </TableContent>
            </ModalTable>
          </ModalScroll>
          <ModalFooter onClick={hideRateModal}>
            我知道了
          </ModalFooter>
        </ModalContent>
      </ViewRatesModalWrapper>
    );
  }
}

const ViewRatesModalWrapper = styled.div`// styled
  & {
    width: 100%;
    height: ${document.documentElement.clientHeight}px;
    background:rgba(0,0,0,0.5);
    padding: 105px 18px 0px 18px;
    display: ${(props) => props.theme.showModal ? "block" : "none"};
    position: fixed;
    top: 0;
    z-index: 1000;
  }
`;

const ModalHeader = styled.div`// styled
  & {
    width: 100%;
    height: 48px;
    line-height: 48px;
    text-align: center;
    font-size:16px;
    font-family:SourceHanSansCN-Regular,SourceHanSansCN;
    font-weight:400;
    color:rgba(51,51,51,1);
  }
`;

const ModalContent = styled.div`// styled
  & {
    width: 100%;
    height: 324px;
    border-radius: 16px;
    background: rgba(255, 255, 255, 1);
  }
`;

const ModalFooter = styled.div`// styled
  & {
    width: 100%;
    height: 50px;
    line-height: 50px;
    text-align: center;
    font-size:16px;
    font-family:SourceHanSansCN-Regular,SourceHanSansCN;
    font-weight:400;
    color:rgba(102,102,102,1);
  }
`;

const ModalScroll = styled.div`// styled
  & {
    width: 100%;
    height: 226px;
    padding: 24px;
    background: rgba(81,118,172,0.4);
    .name {
      font-size:14px;
      font-family:SourceHanSansCN-Regular,SourceHanSansCN;
      font-weight:400;
      color:rgba(51,51,51,1);
    }
  }
`;

const ModalTable = styled.div`// styled
  & {
    width: 100%;
    height: 148px;
    border-radius: 8px;
    margin-top: 16px;
  }
`;

const TableTitle = styled.div`// styled
  & {
    width: 100%;
    height: 40px;
    background: #F2F2F2;
    padding: 14px 24px;
    font-size:12px;
    font-family:SourceHanSansCN-Medium,SourceHanSansCN;
    font-weight:500;
    color:rgba(51,51,51,1);
    border-radius:8px 8px 0px 0px;
    > span {
      display: inline-block;
      width: calc(100% / 3);
    }
    > span:last-child {
      display: inline-block;
      margin-right: 0;
    }
  }
`;

const TableContent = styled.div`// styled
  & {
    width: 100%;
    max-height: 108px;
    overflow-y: auto;
    border-radius:0px 0px 8px 8px;
    > div {
      border-top: 1px solid #D8D8D8;
      width: 100%;
      height: 36px;
      line-height: 36px;
      font-size:12px;
      font-family:SourceHanSansCN-Normal,SourceHanSansCN;
      font-weight:400;
      color:rgba(51,51,51,1);
      background: #F8F8F8;
      padding: 0 24px;
      > span {
        display: inline-block;
        width: calc(100% / 3);
      }
      > span:last-child {
        margin-right: 0;
      }
    }
  }
`;
