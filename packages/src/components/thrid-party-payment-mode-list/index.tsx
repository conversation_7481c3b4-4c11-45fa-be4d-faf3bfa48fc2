import { $PaymentType } from "@classes/const/$payment-type";
import * as React from "react";
import { observer } from "mobx-react";
import styled from "styled-components";
import { sum } from "lodash";
import { Checkbox } from "antd-mobile";
import { $PaymentModeType } from "../../classes/const/$payment-mode-type";
const CheckboxItem = Checkbox.CheckboxItem;
@observer
export class ThridPartyPaymentModeList extends React.Component<any, any> {

  public render() {
    const { thridPartyPaymentModeList, thridPartyModeChange, thridPartyKey, currentThridParityAmount, currentThridParityServerAmount, adjustAmount, showChooseBankCardModal, selectedBankName, selectedBankCardType, selectedBankCardNo, unPayableAmount, beforeThirdParityModePayAmount, renderIcon, showRateModal, selectedSubMerchant, showPaymentLine, subMerchantList } = this.props;
    return (
      <PaymentList>
        {
          thridPartyPaymentModeList.map((item) => {
            console.log(currentThridParityServerAmount, currentThridParityAmount, currentThridParityAmount + currentThridParityServerAmount);
            console.log("tp", item);
            const disabled = !thridPartyKey && thridPartyKey !== item.paymentCode && beforeThirdParityModePayAmount >= unPayableAmount;
            return (
              <CheckboxItem
                key={item.oid}
                onChange={() => thridPartyModeChange(item)}
                checked={thridPartyKey === item.paymentCode}
                disabled={disabled}
              >
                <div
                  onClick={() => {
                    if (!disabled) {
                      thridPartyModeChange(item);
                    }
                  }}
                >
                  <i className={renderIcon(item.paymentCode)} />
                  <div className="payment-name">{item.paymentName}</div>
                  {
                    thridPartyKey === item.paymentCode &&
										  <span onClick={adjustAmount} style={{ float: "right", color: "#307DCD", fontSize: "12px" }}>调整金额</span>
                  }
                </div>
                {
                  thridPartyKey === item.paymentCode &&
                    <div style={{ fontSize: "12px", paddingBottom: "8px", paddingRight: "16px"}}>
                      预计支付金额:
                      <span className="red">
                        ￥{(currentThridParityAmount + currentThridParityServerAmount).toFixed(2)}
                        {currentThridParityServerAmount ? `（含服务费：${currentThridParityServerAmount}元）` : ""}
                      </span>
                      <span onClick={() => showRateModal(item)} style={{ float: "right", color: "#307DCD", fontSize: "12px" }}>查看费率</span>
                    </div>
                }
                {
                  thridPartyKey === item.paymentCode && item.paymentCode === $PaymentModeType.ALLINPAY_BANK_CARD &&
                  <TLBK>
										<div className="bank-info">
                      <span>{selectedBankName}{selectedBankCardType}{selectedBankCardNo && `（${selectedBankCardNo}）`}</span>
                      <span onClick={showChooseBankCardModal}>
                        切换
                        <i className={"scmIconfont scm-icon-jiantou-you"}/>
                      </span>
                    </div>
									</TLBK>
                }
                {
                  thridPartyKey === item.paymentCode && item.paymentCode === $PaymentModeType.ALLINPAY_WECHAT && subMerchantList.length > 0 &&
                  <div className={"payment-channel"}>
                    <div>付款线路  {selectedSubMerchant || "请选择付款线路"}</div>
                    <div onClick={showPaymentLine}>切换
                      <i className={"scmIconfont scm-icon-jiantou-you"}/>
                    </div>
                  </div>
                }
              </CheckboxItem>
            );
          })
        }
      </PaymentList>
    );
  }
}

const PaymentList = styled.div`// styled
  & {
    width: 100%;
    height: auto;
    background-color: #fff;
    margin-top: 10px;
    color: #333333;
    .ant-radio-group{
      width: 100%;
      .ant-radio-wrapper{
        display: block;
        width: 100%;
        height: 40px;
        background-color: #ccc;
      }
    }
    .red{
      color: #FF3030;
    }
    .scm-yinhangka{
      color: #52C41A;
    }
    .scm-Fill{
      color: #108EE9;
    }
    .payment-name{
      display: inline-block;
      margin-left: 10px;
      color: #333333;
    }
    .payment-channel {
      display: flex;
      align-items: center;
      justify-content: space-between;
      height: 30px;
      border-top: 1px solid #D8D8D8;
      padding: 10px 18px 10px 0;
      font-size: 12px;
      > div:nth-child(1) {
        width: 70%;
        white-space: nowrap;
        text-overflow: ellipsis;
        overflow: hidden;
      }
    }
    .am-list-item{
      padding-left: 16px;
      //height: auto;
      .am-list-line{
        padding-left: 23px;
        padding-right: 0px;
        border: none!important;
        :after{
          display: none !important;
        }
        .am-list-content{
          padding: 0;
          font-size: 13px;
          color: #333333;
          >div:first-of-type{
            height: 50px;
            line-height: 50px;
            padding-right: 16px;
          }
        }
      }
      .am-list-thumb{
        position: absolute;
        top: 15px;
        width: 18px;
        height: 18px;
        line-height: 18px;
        .am-checkbox{
          width: 100%;
          height: 100%;
          .am-checkbox-inner{
            width: 100%;
            height: 100%;
            :after{
              top: 1px;
              right: 6px;
            }
          }
        }
      }
    }
    .am-list-item .am-list-line .am-list-content {
      text-overflow: unset;
      white-space: normal;
    }
  }
`;
const TLBK = styled.div`// styled
  & {
    > .bank-info {
      display: block;
      width: 100%;
      height: 40px;
      line-height: 40px;
      border-top: 1px solid #D8D8D8;
      font-size: 13px;
      color: #333333;
      padding-right: 16px;
      > span:last-child {
        float: right;
      }
      .scm-icon-jiantou-you{
        font-size: 12px;
        margin-left: 10px;
      }
    }
  }
`;
