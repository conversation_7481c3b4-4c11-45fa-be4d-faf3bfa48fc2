import * as React from "react";
import { observer } from "mobx-react";
import styled from "styled-components";
import { InputItemComponent } from "../input-item-component/input-item-component";
import { ButtonComponent } from "../button-component/button-component";

@observer
export class AdjustAmountModal extends React.Component<any, any> {

  public constructor(props) {
    super(props);
    this.state = {};
  }

  public changeAdjustAmount = (value) => {
    this.props.changeAdjustAmount(value);
  }

  public render() {
    const { hideAmountModal, showAmountModal, transferAmount, mostAmount, confirmAdjustAmount } = this.props;
    console.log(transferAmount);
    return (
      <AdjustAmountModalPage theme={{ showAmountModal }}>
        <ModalCongtent>
          <ModalHeader>
            <i className={"scmIconfont scm-icon-jiantou-you"} onClick={hideAmountModal}/>
            <span>调整金额</span>
          </ModalHeader>
          <ModalScroll>
            <InputItemComponent
              type="money"
              value={String(transferAmount)}
              clear={true}
              onChange={this.changeAdjustAmount}
              label={"¥"}
            />
            <p>本次最多支付{mostAmount}元</p>
          </ModalScroll>
          <ButtonComponent
            label={"确定调整"}
            confirm={confirmAdjustAmount}
            type={"primary"}
          />
        </ModalCongtent>
      </AdjustAmountModalPage>
    );
  }
}

const AdjustAmountModalPage = styled.div`// styled
  & {
    width: 100%;
    height: ${document.documentElement.clientHeight}px;
    background:rgba(0,0,0,0.5);
    padding: 105px 52px 0px 52px;
    display: ${(props) => props.theme.showAmountModal ? "block" : "none"};
    position: fixed;
    top: 0;
    z-index: 1000;
  }
`;

const ModalHeader = styled.div`// styled
  & {
    width: 100%;
    height: 64px;
    line-height: 64px;
    text-align: center;
    position: relative;
    >.scm-icon-jiantou-you{
      position: absolute;
      left: 0;
      top: 0;
    }
    > span {
      font-size: 16px;
      font-family: SourceHanSansCN-Regular, SourceHanSansCN;
      font-weight: 400;
      color: rgba(47, 47, 47, 1);
    }
    .scm-icon-jiantou-you {
      -webkit-transform: rotate(180deg);
      -moz-transform: rotate(180deg);
      -o-transform: rotate(180deg);
      -ms-transform: rotate(180deg);
      transform: rotate(180deg);
    }
  }
`;

const ModalCongtent = styled.div`// styled
  & {
    width: 100%;
    height: auto;
    border-radius: 16px;
    background: rgba(255, 255, 255, 1);
    padding: 0 21px 21px 21px;
    .am-button-primary {
      width: 136px;
      height: 36px;
      line-height: 36px;
      border-radius: 18px;
      margin: 10px auto 0 auto;
      font-size: 14px;
      font-family: SourceHanSansCN-Regular, SourceHanSansCN;
      font-weight: 400;
      color: rgba(255, 255, 255, 1);
    }
  }
`;

const ModalScroll = styled.div`// styled
  & {
    width: 100%;
    height: auto;
    position: sticky;
    overflow: auto;
    > div {
      border-radius: 4px;
      border: 1px solid rgba(221, 221, 221, 1);
      color: #303030;
      margin-bottom: 14px;
    }
    .active {
      border-radius: 4px;
      border: 1px solid rgba(48, 125, 205, 1);
      color: #307DCD;
    }
    .am-list-item .am-input-label.am-input-label-5 {
      width: unset;
      color: #999999;
    }
    > p {
      font-size: 13px;
      font-family: SourceHanSansCN-Regular, SourceHanSansCN;
      font-weight: 400;
      color: rgba(48, 48, 48, 1);
    }
    .am-input-clear{
      margin-right: 5px;
    }
  }
`;
