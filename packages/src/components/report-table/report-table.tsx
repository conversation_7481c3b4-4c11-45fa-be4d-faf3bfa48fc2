import * as React from "react";
import { Observer, observer } from "mobx-react";
import { Table } from "antd";
import "./style.less";
import styled from "styled-components";
import { ReportInnerTable } from "./report-inner-table";
import { AutoSizer, InfiniteLoader, List, WindowScroller } from "react-virtualized";
import { NoGoods } from "../no-goods/no-goods";
import { debounce } from "lodash";

@observer
export class ReportTable extends React.Component<any, any> {
  private loadedRowsMap = {};

  private timer = null;

  public constructor(props) {
    super(props);
    this.state = {
      height: document.documentElement.clientHeight - 158, // 表格上面的高度
      tableBodyList: [],
    };
  }

  public componentDidMount(): void {
    const evt = "onorientationchange" in window ? "orientationchange" : "resize";
    // window.addEventListener(evt, () => {
    //     this.resize(true);
    // }, false);
    //
    // this.resize(true);
    window.addEventListener(evt, debounce(this.resize, 300), false);

  }

  public componentDidUpdate() {
    console.log("componentDidUpdate");
    this.resize(false);
  }

  public resize = (flas) => {
    const { contentData } = this.props;
    // chong xin ding yi List kuan du
    if (flas && $(".ReactVirtualized__Grid .ReactVirtualized__List")) {
      const documentWidth = document.documentElement.clientWidth;
      $(".ReactVirtualized__Grid .ReactVirtualized__List").width(documentWidth);
    }
    // chong xin she zhi gu ding lie kuan du
    const firstTh = $(".ant-table-scroll .ant-table-fixed .ant-table-thead tr th")[0];
    const firstThWidth = $(firstTh).get(0).offsetWidth;
    $(".ant-table-fixed-left").width(firstThWidth);
    $(".ant-table-fixed-left .ant-table-fixed").width(firstThWidth);
    // ba gu dong lie yin cang
    $(firstTh).css("visibility", "hidden");
    const otherTr = $(".ant-table-scroll .ant-table-fixed .ant-table-tbody tr");
    if (otherTr && otherTr.length > 0) {
      $(".ant-table-scroll .ant-table-fixed .ant-table-tbody tr").each((index, item) => {
        const otherTd = $(item).find("td")[0];
        $(otherTd).find("div").text("");
      });
    }
    const scrollTitleItem = $(".titleTable .ant-table-scroll .ant-table-body")[0];
    $(scrollTitleItem).unbind("scroll");
    $(".contentTable .ant-table-scroll .ant-table-body").unbind("scroll");
    $(".contentTable .ant-table-scroll .ant-table-body").each((index, item) => {
      // console.log("5:", $(scrollTitleItem).scrollLeft());
      $(item).scrollLeft($(scrollTitleItem).scrollLeft());
    });
    if (this.timer) {
      window.clearTimeout(this.timer);
    }
    this.timer = setTimeout(() => {
      // $(scrollTitleItem).unbind("scroll");
      // $(".contentTable .ant-table-scroll .ant-table-body").unbind("scroll");
      // $(".contentTable .ant-table-scroll .ant-table-body").each((index, item) => {
      //   // console.log("5:", $(scrollTitleItem).scrollLeft());
      //   $(item).scrollLeft($(scrollTitleItem).scrollLeft());
      // });
    // 重新监听滚动事件
    if (contentData && contentData.length > 0) {
      // scrollLeft会触发其他滚动事件监听，计算存在优化空间
      let calcIndex = 0
      // 滚动报表头部
      console.log("timer", this.timer);
      $(scrollTitleItem).scroll(debounce(() => {
        // console.log("----");
        $(".contentTable .ant-table-scroll .ant-table-body").each((index, item) => {
          calcIndex++;
          // console.log("1:",  $(scrollTitleItem).scrollLeft(), $(scrollTitleItem));
          $(item).scrollLeft($(scrollTitleItem).scrollLeft());
        });
      }, 50));
      // 滚动报表内容
      $(".contentTable .ant-table-scroll .ant-table-body").scroll(debounce((e) => {
        // console.log("----");
        $(".titleTable .ant-table-scroll .ant-table-body").each((index, item) => {
          calcIndex++;
          // console.log("2:",  $(e.target).scrollLeft(), $(e.target));
          $(item).scrollLeft($(e.target).scrollLeft());
        });
        $(".contentTable .ant-table-scroll .ant-table-body").each((index, item) => {
          calcIndex++;
          // console.log("3:",  $(e.target).scrollLeft(), $(e.target));
          $(item).scrollLeft($(e.target).scrollLeft());
        });
      }, 50));
    }
    }, 300);
  }

  public getRowHeight = (obj) => {
    const { contentData, finished } = this.props;
    let tableItemHeight = 0;
    if (contentData && contentData[obj.index] && contentData[obj.index].itemList) {
      const { itemList } = contentData[obj.index];
      const bottomHeight = finished && contentData.length - 1 === obj.index ? 51 : 0;
      tableItemHeight = 48 + 37 * (itemList ? itemList.length : 0) + bottomHeight; // 48 表格头部高度， 37 表格每行高度 // 41底部提示高度
      console.log("getRowHeight", obj.index, contentData.length, bottomHeight);
    }
    return tableItemHeight;
  }

  public handleInfiniteOnLoad = ({ startIndex, stopIndex }) => {
    console.log("stopIndex", stopIndex);
    const { ordermallOweGoodsQuery, finished, isLoading } = this.props;
    for (let i = startIndex; i <= stopIndex; i++) {
      this.loadedRowsMap[i] = 1;
    }
    // 此处判断是否还需要加载数据
    console.log("finished", finished);
    console.log("是否加载数据", isLoading);
    if (!finished && !isLoading) {
      ordermallOweGoodsQuery();
    }
  }

  public isRowLoaded = ({ index }) => {
    return !!this.loadedRowsMap[index];
  }

  public renderItem = ({ index, key, style }) => {
    const { contentData, columns, finished, isChoose } = this.props;
    // const { columns } = this.state;
    const item = contentData[index];
    // console.log("renderItem", contentData.length - 1 === index);
    return <ReportInnerTable
      key={key}
      style={style}
      item={item}
      isShowBottom={finished}
      columns={columns}
      resize={this.resize}
      isChoose={isChoose}
    />;
  }
  public onScroll = (obj) => {
    console.log(obj);
    // this.resize(false);
  }
  public infiniteLoader = () => {
    const { height } = this.state;
    const { contentData } = this.props;
    return <InfiniteLoader
      isRowLoaded={this.isRowLoaded}
      loadMoreRows={this.handleInfiniteOnLoad}
      rowCount={1000000000}
    >
      {({ onRowsRendered }) => {
        return <AutoSizer
          disableHeight={false}
        >
          {({ width }) => {
            return <List
              height={height}
              overscanRowCount={10}
              rowCount={contentData.length}
              rowHeight={this.getRowHeight}
              onScroll={this.onScroll}
              rowRenderer={({ index, key, style }) => <Observer>{() => this.renderItem({
                index,
                key,
                style,
              })}</Observer>}
              onRowsRendered={onRowsRendered}
              width={width}
            />;
          }}
        </AutoSizer>;
      }}
    </InfiniteLoader>;
  }

  public render() {
    const { contentData, columns, isChoose } = this.props;
    return (
      <ReportTableWrapper className={"reportTableWrapper"} theme={{ isChoose }}>
        <Table
          columns={columns}
          dataSource={[]}
          scroll={{ x: 410, y: false }}
          pagination={false}
          className={"titleTable"}
        />
        {
          contentData && contentData.length > 0 ?
            <WindowScroller height={667}>{() => this.infiniteLoader()}</WindowScroller>
            :
            <NoGoods title="暂无数据" height={document.documentElement.clientHeight - 158}/> // 表格上面的高度
        }
      </ReportTableWrapper>
    );
  }
}

const ReportTableWrapper = styled.div`// styled
  & {
    width: 100%;
    height: auto;
    .frSDBe {background-color:#E6EFF9}
    .titleTable {
      .ant-table-fixed-header .ant-table-scroll .ant-table-header {
        padding: 0;
        margin: 0;
        overflow: hidden;
      }

      .ant-table-placeholder {
        display: none;
      }
    }
    //.ReactVirtualized__Grid{
    //  overflow-y: scroll;
    //  -webkit-overflow-scrolling: touch;
    //  overflow-scrolling: touch;
    //}
    .contentTable {
      .ant-table-header {
        display: none;
      }
    }

    p {
      margin-bottom: 0;
      line-height: 18px;
      font-size: 12px;
      height: 18px;
    }

    .ant-table-thead > tr > th {
      padding: 0;
      box-sizing: border-box;
      border-right: 1px solid #859FC5;
      text-align: center;
    }

    .ant-table-tbody > tr > td {
      padding: 0;
      text-align: right;
      border-bottom: 1px solid #D8D8D8;
    }
    .ant-table-tbody > tr:nth-of-type(even) {
      background-color: #F2F2F2;
    }

    .ant-table-tbody > tr:nth-of-type(odd) {
      background-color: #fff;
    }
    //.scm-inner-td{
    //  width: 100%;
    //  >p{
    //    width: 100%;
    //    overflow: hidden;
    //    text-overflow: ellipsis;
    //    white-space: nowrap;
    //  }
    //}
    .ant-table-fixed-left .ant-table-thead > tr:first-child > th:last-child,
    .ant-table-fixed-left .ant-table-tbody > tr > td:first-child {
      text-align: left;
      padding-left: 6px;
    }

    .ant-table-thead > tr > th > span > div > p:first-of-type {
      border-bottom: 1px solid #859FC5;
    }

    .ant-table-thead > tr > th {
      font-size: 12px;
      font-family: SourceHanSansCN-Normal, SourceHanSansCN;
      font-weight: 400;
      color: rgba(255, 255, 255, 1);
      background: #307DCD;
    }
    .ant-table-body {
      &::-webkit-scrollbar {
        display: none !important;
        visibility: hidden;
        //color: #ff7279;
        //background-color: #ff7279;
      }
    }
    .ant-table-thead > tr:first-child > th:first-child {
      border-radius: 0;
    }

    .ant-table-thead > tr:first-child > th:last-child {
      border-radius: 0;
    }

    .ant-table-fixed-left {
      // width: 120px;

      .ant-table-tbody > tr > td:first-child {
        border-right: 1px solid #307DCD;
        font-size: 12px;
        line-height: 14px;
      }
    }
  }
`;
