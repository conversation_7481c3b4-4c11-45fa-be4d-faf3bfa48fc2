import * as React from "react";
import { observer } from "mobx-react";
import { Table } from "antd";
import "./style.less";
import styled from "styled-components";
import { LoadingTip } from "../loading-marked-words";

@observer
export class ReportInnerTable extends React.Component<any, any> {

  public constructor(props) {
    super(props);
    this.state = {};
  }

  public componentDidMount(): void {
    const { resize } = this.props;
    resize && resize(false);
  }

  public render() {
    const { item, columns, style, isShowBottom } = this.props;
    const { salesOrderDocNo, salesOrderDocDate, orderScheme, itemList, salesOrderId } = item || {};
    const newColumns = columns.map((item, index) => {
      if (index === 0) {
        return {
          ...item,
          width: $(".titleTable .productSkuName")[0].offsetWidth,
        };
      }
      return item;
    });
    return (
      <TableItem style={style}>
        <TableInfo>
          <span>订单号：{salesOrderDocNo}</span>
          <span>下单日期：{salesOrderDocDate}</span>
          <span>订货方案：{orderScheme}</span>
        </TableInfo>
        <Table
          key={salesOrderId}
          columns={newColumns}
          showHeader={false}
          dataSource={itemList ? [...itemList] : []}
          scroll={{ x: 410 }}
          pagination={false}
          className={"contentTable"}
        />
        {
          isShowBottom &&
          <LoadingTip
            isFinished={true}
            isLoad={false}
          />
        }
      </TableItem>
    );
  }
}

const TableInfo = styled.div`// styled
  & {
    width: 100%;
    height: 48px;
    padding: 4px 6px;
    background-color: rgba(230, 230, 230, 1);
    line-height: 20px;
    font-size: 12px;
    color: #595959;
    > span {
      width: 50%;
      float: left;
      text-align: left;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }

    > span:nth-of-type(even) {
      float: right;
      text-align: right;
    }

    > span:last-of-type {
      width: 100%;
    }
  }
`;

const TableItem = styled.div`// styled
  & {
    width: 100%;
    height: auto;
    .scm-shop-item {
      overflow: hidden;
      text-overflow: ellipsis;
      display: -webkit-box;
      -webkit-line-clamp: 2; // 控制多行的行数
      -webkit-box-orient: vertical;
      line-height: 14px;
    }
    .common-bottomTotal {
      padding: 10px 0px 10px !important;
      height: auto !important;
    }
    .ant-table-thead > tr.ant-table-row-hover > td,
    .ant-table-tbody > tr.ant-table-row-hover > td,
    .ant-table-thead > tr:hover > td,
    .ant-table-tbody > tr:hover > td {
      background-color: transparent;
    }
    .ant-table-row-level-0 {
      height: 37px !important;
    }
    .ant-table-body {
      overflow: auto hidden !important;
      -webkit-overflow-scrolling: touch;
      padding-bottom: 10px;
      &::-webkit-scrollbar {
        display: none !important;
      }
    }
    .ant-table-scroll .ant-table-body .ant-table-fixed .ant-table-tbody {
      .productSkuName {
        color: transparent;
      }
    }
  }
`;
