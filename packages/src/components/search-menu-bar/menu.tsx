import { autowired } from "@classes/ioc/ioc";
import React from "react";
import styled from "styled-components";
import { $ComponentService } from "../../classes/service/$component-service";

declare let window: any;

export class Menu extends React.Component<any, any> {
  @autowired($ComponentService)
  public $componentService: $ComponentService;

  constructor(props) {
    super(props);
    this.state = {
      categoryList: [],
    };
  }

  public componentDidMount() {
    this.$componentService.queryProductCategory().then((data) => {
      const { productCategroyList } = data;
      this.setState({ categoryList: productCategroyList, activeItem: productCategroyList[0] });
    });
  }

  public renderLvl1Menu = (menu) => {
    const { activeItem } = this.state;
    return menu.map((item) => {
      return <li key={item.id} className={activeItem && activeItem.id === item.id ? "active" : ""}
                 onClick={() => this.setState({ activeItem: item })}>{item.name}</li>;
    });
  }

  public renderLvl2Menu = (menu) => {
    return (
      <List key={menu.id}>
        {
          menu.children && menu.children.map((item) => {
            if (item.children && item.children.length > 0) {
              return <List key={item.id}>
                <Title>{item.name}</Title>
                <Content>
                  {this.renderLvl3Menu(item)}
                </Content>
              </List>;
            } else {
              return <List key={item.id}><Title>{item.name}</Title></List>;
            }
          })
        }
      </List>
    );
  }

  public renderLvl3Menu = (menu) => {
    return (
      menu.children && menu.children.map((item) => {
        return <div key={item.id} onClick={() => this.onSelect(item)}>{item.name}</div>;
      })
    );
  }

  public onSelect = (item) => {
    this.props.showMenuChange(false);
    this.props.onChange(item);
  }

  public render() {
    const { activeItem, categoryList } = this.state;

    return (
      <Wrapper>
        <LeftItem>
          <ul>
            {activeItem && this.renderLvl1Menu(categoryList)}
          </ul>
        </LeftItem>
        <RightItem>
          {activeItem && this.renderLvl2Menu(activeItem)}
        </RightItem>
      </Wrapper>
    );
  }
}

const Wrapper = styled.div`// styled
  & {
    position: absolute;
    display: flex;
    width: 100%;
    height: 100%;
    z-index: 2;
    background: #F2F2F2;
  }
`;
const LeftItem: any = styled.div`// styled
  & {
    background: #FFFFFF;
    width: 90px;
    min-height: 100%;
    overflow: auto;
    ul {
      list-style: none;
      li {
        border-bottom: 1px solid #D8D8D8;
        height: 40px;
        line-height: 40px;
        text-align: center;
        color: #666666;

        &.active {
          background: #F2F2F2;
          color: #307DCD;
        }
      }
    }
  }
`;
const RightItem: any = styled.div`// styled
  & {
    padding: 12px;
    flex: 1 1 auto;
  }
`;
const List: any = styled.div`// styled
  & {
    margin-bottom: 24px;
  }
`;
const Title = styled.div`// styled
  & {
    height: 16px;
    margin-bottom: 12px;
    color: #202020;
  }
`;
const Content: any = styled.div`// styled
  & {
    display: flex;
    flex: 1 1 auto;
    flex-wrap: wrap;
    justify-content: space-between;
    background: #ffffff;
    > div {
      flex: 0 0 calc((100% - 30px) / 3);
      max-width: calc((100% - 30px) / 3);
      padding: 12px 0;
      text-align: center;
    }
  }
`;
