import { autowired } from "@classes/ioc/ioc";
import { SearchBar } from "antd-mobile";
import React from "react";
import styled from "styled-components";
import { $ComponentService } from "../../classes/service/$component-service";
import { Menu } from "./menu";
import { SITE_PATH } from "../../modules/app";
import { withRouter } from "react-router";
import { $ProductListMv } from "../../modules/product-list/$product-list-mv";
import { SearchList } from "../../modules/product-list/search";
import { $AppStore, AppStoreKey} from "../../classes/stores/app-store-mv";

declare let require: any;

const IconClassify = require("../svg/ico_classify.svg");

@withRouter
export class SearchMenuBar extends React.Component<any, any> {
  @autowired($AppStore)
  public $AppStore: $AppStore;
  @autowired($ComponentService)
  public $componentService: $ComponentService;

  @autowired($ProductListMv)
  public $mv: $ProductListMv;

  @autowired(SearchList)
  public searchList: SearchList;

  constructor(props) {
    super(props);
    this.state = {
      focus: false,
      keyword: "",
      showMenuPage: false,
    };
  }

  public componentDidMount() {
    if (this.props.location.pathname.indexOf("/shop/list") > -1 || this.props.match.params.keyword === "fromHome") {
      this.setState({
        keyword: "",
      });
    } else {
      // console.log("sdsdasjaksfnjbfsnj", this.props.match.params.keyword);
      this.setState({
        keyword: this.props.match.params.keyword,
      });
    }
  }

  public onCategoryChange = (e) => {
    this.props.onChange(e.name, e.id);
    this.setState({ keyword: e.name, categoryId: e.id });
  }

  public onClear = () => {
    this.setState({ keyword: "", categoryId: null });
    if (this.props.location.pathname.indexOf("SearchList") > -1) {
      history.go(-1);
    }
  }

  public onSearch = (keyword) => {
    this.setState({ keyword, categoryId: null });
    this.$mv.setKeyword(keyword);
  }

  public onSubmit = (keyword) => {
    if (/\S/.test(keyword)) {
      this.$mv.setKeyword(keyword);
      if (this.props.location.pathname.indexOf("SearchList") > -1) {
        window.location.href = `/${SITE_PATH}/SearchList/${keyword}`; // 这里相当于搜索页面重新reload
        // this.props.history.push({pathname: `/${SITE_PATH}/SearchList/${keyword}`});

      } else {
        this.$AppStore.clearPageMv(AppStoreKey.SEARCHSHOPLIST);
        this.props.history.push({ pathname: `/${SITE_PATH}/SearchList/${keyword}` });
      }
    }
  }

  public render() {
    const { showMenuPage, keyword, focus } = this.state;
    // console.log(keyword);
    const placeholder = "输入商品关键词"; // hack to fix antd-mobile searchBar not completely show placeholder
    return (
      <Wrapper>
        <BarWrapper>
          <SearchBar value={keyword}
                     placeholder={!focus ? placeholder : ""}
                     onFocus={() => this.setState({ focus: !focus })}
                     onBlur={() => this.setState({ focus: !focus })}
                     onChange={this.onSearch}
                     onCancel={() => this.onClear()}
                     onSubmit={this.onSubmit}
          />
        </BarWrapper>
        {showMenuPage &&
        <Menu showMenuChange={(v) => this.setState({ showMenuPage: v })} onChange={this.onCategoryChange}/>}
      </Wrapper>
    );
  }
}

const Wrapper = styled.div`// styled
  & {

  }
`;
const BarWrapper = styled.div`// styled
  & {
    height: 44px;
    padding: 8px;
    background: #FFFFFF;
    display: flex;
    align-items: center;
    border-bottom: 1px solid #D8D8D8;
    > svg {
      width: 28px;
      height: 28px;
      path {
        fill: #666666;
      }
    }
    .am-search {
      width: 100%;
      height: 100%;
      background-color: #FFFFFF;
    }
    .am-search-input {
      background-color: #f2f2f2;
      border-radius: 14px;
    }
    .am-search-input .am-search-clear {
      font-size: 14px;
      top: 6px;
      right: 7px;
    }
  }
`;
