import { Select } from "antd";
import React from "react";
import { autowired } from "@classes/ioc/ioc";
import { $ExchangeGoodsMv } from "../../modules/exchange-goods-detail/$exchange-goods-detail-mv";
import { $ExchangeGoodsDetailEditMv } from "../../modules/exchange-goods-detail-edit/$exchange-goods-detail-edit-mv";

const Option = Select.Option;

export class SearchInput extends React.Component<any, any> {
  @autowired($ExchangeGoodsMv)
  public $exchangeGoodsMv: $ExchangeGoodsMv;

  @autowired($ExchangeGoodsDetailEditMv)
  public $exchangeGoodsDetailEditMv: $ExchangeGoodsDetailEditMv;

  public state = {
    data: [],
    value: undefined,
  };

  public componentWillReceiveProps() {
    // console.log(909090);
    /* const { showVal } = this.props;
     console.log(this.$exchangeGoodsMv.product);
     console.log(showVal);
     if (showVal) {
       this.setState({ value: undefined });
     }*/
  }

  public handleSearch = (value) => {
    const params = { keyword: value };
    if (this.props.edit) {
      this.$exchangeGoodsDetailEditMv.fetchProductLoad(params).then((data) => {
        this.setState({ data: data.productList });
        this.$exchangeGoodsDetailEditMv.setProductList(data.productList);
      });
    } else {
      this.$exchangeGoodsMv.fetchProductLoad(params).then((data) => {
        this.setState({ data: data.productList });
        this.$exchangeGoodsMv.setProductList(data.productList);
      });
    }
  }

  public handleChange = (value) => {
    // console.log(value);
    this.setState({ value });
    if (this.props.edit) {
      this.$exchangeGoodsDetailEditMv.setSku(value);
      this.$exchangeGoodsDetailEditMv.setProduct(value);
    } else {
      this.$exchangeGoodsMv.setSku(value);
      this.$exchangeGoodsMv.setProduct(value);
    }
  }

  public clearSku = () => {
    this.setState({ value: undefined });
  }

  public render() {
    const { data, value } = this.state;
    const { modelData } = this.props;
    const options = data.map((d) => <Option key={d.skuId}>{d.name}</Option>);
    return (
      <div>
        <Select
          showSearch={true}
          value={modelData ? String(modelData.name) : value}
          placeholder={this.props.placeholder}
          style={this.props.style}
          defaultActiveFirstOption={false}
          showArrow={false}
          filterOption={false}
          onSearch={this.handleSearch}
          onChange={this.handleChange}
          notFoundContent={null}
        >
          {options}
        </Select>
        <i className="scmIconfont scm-icon-del" onClick={this.clearSku}/>
      </div>
    );
  }
}
