.am-list-item .am-input-control .fake-input-container .fake-input.focus:after {
  top: 0;
}

.scm-input-modal .am-list-item .am-input-control .fake-input-container-left .fake-input {
  text-align: center;
  border: 1px solid #ddd;
  height: 44px;
}

.scm-input-modal .am-list-item .am-input-control {
  height: 44px;
  line-height: 44px;
}

.scm-input-modal .am-list-item .am-input-control .fake-input-container .fake-input.focus:after {
  right: unset;
  top: 0;
  height: 50%;
}

.model .am-list-item .am-input-control {
  height: 44px;
}

.model .am-list-item .am-input-control .fake-input-container .fake-input {
  border: 1px solid #ddd;
  text-align: center;
  height: 44px;
}

.scm-input-modal, .model {
  min-height: 163px;
}

.scm-input-modal .am-modal-content .am-modal-body {
  min-height: 60px;
  height: 60px;
}

.model .am-modal-content .am-modal-body {
  min-height: 60px;
  height: 60px;
}

.model .am-list-item .am-input-control .fake-input-container .fake-input.focus:after {
  right: unset;
  top: 25%;
  height: 50%;
}

.model .am-list-item .am-input-control .fake-input-container, .scm-input-modal .am-list-item .am-input-control .fake-input-container {
  height: 44px;
  line-height: unset;
}

.model .am-list-item .am-input-control {
  line-height: 44px !important;
}
.am-number-keyboard-wrapper {
  height: auto;
}
.am-number-keyboard-wrapper.am-number-keyboard-wrapper-hide {
  bottom: -500px;
}
