import * as React from "react";
import { observer } from "mobx-react";
import { InputItem } from "antd-mobile";
import "./style.less";

@observer
export class InputNumberItemComponent extends React.Component<any, any> {

  public formatValue = (value) => {
    let offLineTransferAmount = value;
    if (offLineTransferAmount) {
      offLineTransferAmount = offLineTransferAmount.replace(/\D/g, "");
    }
    return offLineTransferAmount;
  }

  public onChange = (value) => {
    console.log(value);
    const filterValue = this.formatValue(value);
    console.log(filterValue);
    this.props.onChange(filterValue && String(filterValue));
  }

  public onBlur = () => {
    window.scrollTo(0, 0);
  }

  public onFocus = () => {
    window.scrollTo(0, 0);
    $("body").css("height", "100%");
  }

  public render() {
    const { type, value, placeholder, defaultValue, disabled, clear, maxLength, label, pattern, moneyKeyboardAlign, style, className } = this.props;
    const isIPhone = new RegExp("\\biPhone\\b|\\biPod\\b", "i").test(window.navigator.userAgent);
    let moneyKeyboardWrapProps;
    if (isIPhone) {
      moneyKeyboardWrapProps = {
        onTouchStart: (e) => e.preventDefault(),
      };
    }
    return (
      <InputItem
        className={className}
        style={style}
        type={type}
        placeholder={placeholder}
        defaultValue={defaultValue}
        value={value}
        disabled={disabled}
        clear={clear}
        maxLength={maxLength}
        onChange={this.onChange}
        pattern={pattern}
        onBlur={this.onBlur}
        onFocus={this.onFocus}
        moneyKeyboardAlign={moneyKeyboardAlign}
        moneyKeyboardWrapProps={moneyKeyboardWrapProps}
      >
        {label}
      </InputItem>
    );
  }
}
