import { observer } from "mobx-react";
import React from "react";
import { withRouter } from "react-router";
import { Spin } from "antd";

declare let window: any;
declare let require: any;

@withRouter
@observer
export class LoadingTip extends React.Component<any, any> {

  public render() {
    const { isFinished, isLoad } = this.props;
    return (
      <div className="common-bottomTotal" style={{ textAlign: "center", padding: "20px 0" }}>
        {
          isFinished ? <span>我们也是有底线的</span> :
            isLoad ? <Spin tip="加载中"></Spin> : <span>上拉加载更多</span>
        }
      </div>
    );
  }
}
