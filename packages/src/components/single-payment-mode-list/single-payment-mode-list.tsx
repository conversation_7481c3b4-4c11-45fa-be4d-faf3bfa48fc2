import * as React from "react";
import { observer } from "mobx-react";
import styled from "styled-components";
import { $PaymentModeType } from "../../classes/const/$payment-mode-type";

import { InternalPaymentMethod } from "./internal-payment-method";
import { TpMethod } from "./tp-method";
import { $PaymentType } from "../../classes/const/$payment-type";
import { $CartType } from "@classes/const/$cart-type";

@observer
export class SinglePaymentModeList extends React.Component<any, any> {

  public renderIcon = (code) => {
    let icon = "";
    switch (code) {
      case $PaymentModeType.STOREDVALUE:
        icon = "scmIconfont payment-icon-list-item scm-icon-caiwu";
        break;
      case $PaymentModeType.CREDIT:
        icon = "scmIconfont payment-icon-list-item scm-icon-xinyongqia";
        break;
      case $PaymentModeType.REBATEDEDUCTION:
        icon = "scmIconfont payment-icon-list-item scm-icon-fanli";
        break;
      case $PaymentModeType.BANKTRANSFER:
        icon = "scmIconfont payment-icon-list-item scm-icon-zhuanzhang";
        break;
      case $PaymentType.ALLINPAY_BANK_CARD:
        icon = "scmIconfont payment-icon-list-item scm-yinhangka";
        break;
      case $PaymentType.ALLINPAY_WECHAT:
        icon = "scmIconfont payment-icon-list-item scm-Fill";
        break;
      case $PaymentModeType.DIVIDE_REBATE:
        icon = "scmIconfont payment-icon-list-item scm-icon-caiwu";
        break;
      default:
        break;
    }
    return icon;
  }

  public render() {
    const { paymentModeList, showAccountMabel, bankName, accountCode, paymentDate, changeDate, pics, setPics, transferAmount,
       isChooseAccount, showChooseBankCardModal, isShow, goBack, chooseBank, selectedBankName, selectedBankCardType,
        selectedBankCardNo, onChangeTP, changeInternalPaymentMethod, tpTransferAmount, adjustAmount, changeAdjustAmount,
         showAmountModal, hideAmountModal, changeTransferAmount, isShowChooseBankCardModal, confirmAdjustAmount, mostAmount,
          surplusAmount, tpAmount, bankRedirectUrl, tpRate, singlePaymentModeList, skipToBindCard, freightPayInfo, showRate,
           showRateModal, hideRateModal, selectRateList, showPaymentLine, selectedSubMerchant, subMerchantList, disabledDivideRebate,
           disabledTransfer, showCreditModal, validityTypeName, isShowMoreValidityType, disabledCredit, showCreditTips
          } = this.props;
    return (
      <SinglePaymentModeListWrapper>
        <ModeHeader>
          <span>支付方式</span>
          {
            freightPayInfo && freightPayInfo.isCreateFreightFeeDocument === $CartType.ISCREATEFREIGHTFEEDOCUMENT && <span className={"freight"}>注：首付款需 ≥ 运费金额 <span>{freightPayInfo.freightNotPayAmount}</span> 元</span>
          }
        </ModeHeader>
        <InternalPaymentMethod
          disabledDivideRebate={disabledDivideRebate}
          disabledTransfer={disabledTransfer}
          paymentModeList={paymentModeList}
          renderIcon={this.renderIcon}
          isChooseAccount={isChooseAccount}
          showAccountMabel={showAccountMabel}
          transferAmount={transferAmount}
          bankName={bankName}
          accountCode={accountCode}
          paymentDate={paymentDate}
          changeDate={changeDate}
          pics={pics}
          setPics={setPics}
          changeInternalPaymentMethod={changeInternalPaymentMethod}
          changeTransferAmount={changeTransferAmount}
          isShowChooseBankCardModal={isShowChooseBankCardModal}
          showCreditModal={showCreditModal}
          validityTypeName={validityTypeName}
          isShowMoreValidityType={isShowMoreValidityType}
          disabledCredit={disabledCredit}
          showCreditTips={showCreditTips}
        />
        <TpMethod
          paymentModeList={paymentModeList}
          tpTransferAmount={tpTransferAmount}
          isShow={isShow}
          goBack={goBack}
          chooseBank={chooseBank}
          showChooseBankCardModal={showChooseBankCardModal}
          onChangeTP={onChangeTP}
          renderIcon={this.renderIcon}
          selectedBankName={selectedBankName}
          selectedBankCardType={selectedBankCardType}
          selectedBankCardNo={selectedBankCardNo}
          adjustAmount={adjustAmount}
          changeAdjustAmount={changeAdjustAmount}
          showAmountModal={showAmountModal}
          hideAmountModal={hideAmountModal}
          isShowChooseBankCardModal={isShowChooseBankCardModal}
          confirmAdjustAmount={confirmAdjustAmount}
          mostAmount={mostAmount}
          bankRedirectUrl={bankRedirectUrl}
          tpRate={tpRate}
          skipToBindCard={skipToBindCard}
          singlePaymentModeList={singlePaymentModeList}
          showRate={showRate}
          showPaymentLine={showPaymentLine}
          showRateModal={showRateModal}
          hideRateModal={hideRateModal}
          selectRateList={selectRateList}
          selectedSubMerchant={selectedSubMerchant}
          subMerchantList={subMerchantList}
        />
        <div style={{ width: "100%", height: "40px", lineHeight: "40px" }}>
          <span
            style={{
              float: "right",
              fontSize: "13px",
              fontFamily: "SourceHanSansCN-Normal",
              color: "#333333",
              marginRight: "15px",
            }}>剩余支付：￥{(surplusAmount - transferAmount) >= 0 ? Number(surplusAmount - transferAmount).toFixed(2) : 0}</span>
        </div>
      </SinglePaymentModeListWrapper>
    );
  }
}

const SinglePaymentModeListWrapper = styled.div`// styled
  & {
    margin-top: 10px;
    .scm-icon-caiwu {
      color: #FF8627;
    }
    .scm-icon-xinyongqia {
      color: #1890FF;
    }
    .scm-icon-zhuanzhang {
      color: #52C41A;
    }
    .scm-icon-fanli {
      color: #FF3030;
    }
    .am-list-item {
      vertical-align: unset;
      align-items: unset;
    }
    .am-list-item.am-list-item-active {
      background: #fff;
    }
    .payment-icon-list-item {
      margin-right: 5px;
    }
    .am-list-item:not(:last-child) .am-list-line::after {
      display: none !important;
    }
    .am-list-item .am-list-line .am-list-content {
      font-size: 13px;
      font-family: SourceHanSansCN-Normal, SourceHanSansCN;
      font-weight: 400;
      color: rgba(51, 51, 51, 1);
      // margin-top: 3px;
    }
    .am-list-item .am-list-thumb:first-child {
      margin-right: 4px;
    }
    .am-list-item .am-list-line {
      padding-right: 0 !important;
    }
  }
`;

const ModeHeader = styled.div`// styled
  & {
    width: 100%;
    height: 40px;
    border-bottom: 1px solid #D8D8D8;
    background: #fff;
    line-height: 40px;
    padding-left: 15px;
    font-size: 13px;
    font-family: SourceHanSansCN-Normal, SourceHanSansCN;
    font-weight: 400;
    color: rgba(51, 51, 51, 1);
    .freight {
      float: right;
      font-size:12px;
      font-family:SourceHanSansCN-Regular,SourceHanSansCN;
      font-weight:400;
      color:rgba(48,125,205,1);
      margin-right: 16px;
      > span {
        color: #FF3030;
      }
    }
  }
`;
