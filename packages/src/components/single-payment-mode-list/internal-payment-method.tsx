import * as React from "react";
import { observer } from "mobx-react";
import { $PaymentType } from "../../classes/const/$payment-type";
import styled from "styled-components";
import { Checkbox, List } from "antd-mobile";
import { OfflineTransferComponent } from "../offline-transfer-component/offline-transfer-component";
import { $PaymentModeType } from "../../classes/const/$payment-mode-type";
import { $ValidityType, ValidityTypeName } from "@classes/const/$validity-type";

const CheckboxItem = Checkbox.CheckboxItem;

@observer
export class InternalPaymentMethod extends React.Component<any, any> {

  public renderAccountInfo = (code) => {
    let tip = "";
    switch (code) {
      case $PaymentModeType.STOREDVALUE:
        tip = "账户余额:";
        break;
      case $PaymentModeType.CREDIT:
        tip = "可用额度:";
        break;
      case $PaymentModeType.REBATEDEDUCTION:
        tip = "账户余额:";
        break;
      case $PaymentModeType.DIVIDE_REBATE:
        tip = "分账返利余额:";
        break
      default:
        break;
    }
    return tip;
  }

  public renderPaymentTip = (code) => {
    let tip = "";
    switch (code) {
      case $PaymentModeType.STOREDVALUE:
        tip = "预计支付金额:";
        break;
      case $PaymentModeType.CREDIT:
        tip = "预计支付金额:";
        break;
      case $PaymentModeType.REBATEDEDUCTION:
        tip = "预计抵扣金额:";
        break;
      case $PaymentModeType.DIVIDE_REBATE:
        tip = "预计支付金额:";
        break;
      default:
        break;
    }
    return tip;
  }

  public renderNoValueTip = (code) => {
    let tip = "";
    switch (code) {
      case $PaymentModeType.STOREDVALUE:
        tip = "暂无余额";
        break;
      case $PaymentModeType.CREDIT:
        tip = "暂无可用信用";
        break;
      case $PaymentModeType.REBATEDEDUCTION:
        tip = "暂无可用返利";
        break;
      case $PaymentModeType.DIVIDE_REBATE:
        tip = "暂无可用分账";
        break;
      default:
        break;
    }
    return tip;
  }

  public changeInternalPaymentMethod = (payment) => {
    this.props.changeInternalPaymentMethod(payment);
  }

  public getSmallNum = (a, b) => {
    const res = a < b ? a : b;
    return res ? res : 0;
  }

  public getCreditAvailableAmount = (payment) => {
    const { validityTypeName } = this.props;
    if (payment.code === $PaymentModeType.CREDIT) {
      return validityTypeName === ValidityTypeName[$ValidityType.NEW_CREDIT] ? (payment.availableTotalAmountForNoLimitPeriod || 0) : payment.availableTotalAmount;
    } else {
      return payment.availableTotalAmount;
    }
  }

  public getCreditDeductAmount = (payment) => {
    const { validityTypeName } = this.props;
    if (payment.code === $PaymentModeType.CREDIT) {
      return validityTypeName === ValidityTypeName[$ValidityType.NEW_CREDIT] ? Number(payment.expectedPaymentAmount || payment.availableOrderAmountForNoLimitPeriod || 0).toFixed(2) : Number(payment.expectedPaymentAmount || payment.availableOrderAmount).toFixed(2);
    } else {
      return Number(payment.expectedPaymentAmount || payment.availableOrderAmount).toFixed(2);
    }
  }

  public render() {
    const { paymentModeList, renderIcon, showAccountMabel, bankName, accountCode, paymentDate, changeDate, pics, setPics,
      isChooseAccount, transferAmount, changeTransferAmount, disabledDivideRebate, disabledTransfer, showCreditModal,
      validityTypeName, isShowMoreValidityType, disabledCredit, showCreditTips } = this.props;
    return (
      <InternalPaymentMethodWrapper>
        <List>
          {
            paymentModeList.filter((mode) => mode.isTPPymt !== $PaymentType.ISTPYMT).map((payment) => {
              return (
                <CheckboxItem
                  key={payment.oid}
                  onChange={() => this.changeInternalPaymentMethod(payment)}
                  checked={payment.isCheck}
                  disabled={
                    (payment.code === $PaymentModeType.STOREDVALUE && payment.availableTotalAmount < 0) ||
                    (payment.code === $PaymentModeType.BANKTRANSFER && disabledDivideRebate) ||
                    (payment.code === $PaymentModeType.DIVIDE_REBATE && (disabledTransfer || (payment.transferAmount <= 0 && payment.availableOrderAmount <= 0))) ||
                    (payment.code === $PaymentModeType.CREDIT && payment.availableTotalAmount === 0 && payment.availableTotalAmountForNoLimitPeriod === 0) ||
                    (payment.code !== $PaymentModeType.STOREDVALUE && payment.code !== $PaymentModeType.BANKTRANSFER && payment.code !== $PaymentModeType.CREDIT && payment.code !== $PaymentModeType.DIVIDE_REBATE && payment.availableTotalAmount === 0) ||
                    (payment.code === $PaymentModeType.CREDIT && disabledCredit)
                  }
                >
                  <div style={{ display: "flex", alignItems: "center" }}>
                    <i
                      className={renderIcon(payment.code)}
                      style={{ position: "relative", top: payment.code === $PaymentType.BANK_TRANSFER ? payment.isCheck ? "0px" : "-2px" : "0" }}
                    />
                    <span style={{ position: "relative", top: payment.code === $PaymentType.BANK_TRANSFER ? payment.isCheck ? "-2px" : "-4px" : "-2px" }}>{payment.name}</span>
                    {
                      payment.code === $PaymentModeType.CREDIT && disabledCredit &&  <i className={"scmIconfont scm-icon-shuoming"} style={{ marginLeft: "5px", color: "rgba(48,125,205,1)"  }} onClick={() => showCreditTips(true)}/>
                    }
                    {
                      (payment.isCheck && payment.code === $PaymentModeType.DIVIDE_REBATE) && <span className="more-text">(最多可抵扣货款￥{payment.availableOrderAmount})</span>
                    }
                    {
                      (payment.isCheck && payment.code === $PaymentModeType.CREDIT) && <span style={{ marginLeft: 'auto', marginRight: 40}}
                        onClick={() => isShowMoreValidityType ? showCreditModal(true) : null}
                      >
                        {validityTypeName}&nbsp;
                        {
                          isShowMoreValidityType && <i
                            className={"scmIconfont scm-icon-jiantou-you"}
                            style={{ position: "relative", fontSize: 13 }}
                          />
                        }
                        </span>
                    }
                  </div>
                  { payment.code !== $PaymentModeType.BANKTRANSFER && (
                      ((payment.availableTotalAmount <= 0 && payment.code !== $PaymentModeType.CREDIT  && payment.code !== $PaymentModeType.STOREDVALUE && payment.code !== $PaymentModeType.DIVIDE_REBATE)
                        || (payment.code === $PaymentModeType.DIVIDE_REBATE && payment.transferAmount <= 0 && payment.availableOrderAmount <= 0)) ?
                        <div style={{ marginLeft: "20px", color: "#FF3030" }}>{this.renderNoValueTip(payment.code)}</div> :
                        <div style={{ marginLeft: "20px" }}>
                          <span>{this.renderAccountInfo(payment.code)}￥{
                            this.getCreditAvailableAmount(payment)
                          }</span>
                          {
                            payment.isCheck && <span style={{ marginLeft: "8px" }}>
                              {this.renderPaymentTip(payment.code)}￥
                              <span style={{ color: "#FF3030" }}>{
                                this.getCreditDeductAmount(payment)
                              }</span>
                            </span>
                          }
                        </div>
                    )
                  }
                  {
                    payment.isCheck && payment.code === $PaymentType.BANK_TRANSFER && <OfflineTransferComponent
                      isChooseAccount={isChooseAccount}
                      showAccountMabel={showAccountMabel}
                      transferAmount={transferAmount}
                      bankName={bankName}
                      accountCode={accountCode}
                      paymentDate={paymentDate}
                      changeDate={changeDate}
                      pics={pics}
                      setPics={setPics}
                      changeTransferAmount={changeTransferAmount}
                      isInput={true}
                    />
                  }
                </CheckboxItem>
              );
            })
          }
        </List>
      </InternalPaymentMethodWrapper>
    );
  }
}

const InternalPaymentMethodWrapper = styled.div`// styled
  & {
    width: 100%;
    height: auto;
    background: #fff;
    .am-list-body::before {
      display: none !important;
    }
    .ant-checkbox-wrapper {
      display: block;
      margin-left: 16px !important;
    }
    .am-checkbox {
      width: 18px;
      height: 18px;
    }
    .am-checkbox-inner {
      width: 18px;
      height: 18px;
      border-radius: unset;
    }
    .am-checkbox-input {
      width: 30%;
    }
    .more-text {
      color: #888 !important;
      margin-left: 10px;
    }
    .am-list-item.am-input-item {
      display: inline-flex;
    }
    .am-list-item .am-list-line .am-list-content {
      margin-top: 3px;
    }
  }
`;
