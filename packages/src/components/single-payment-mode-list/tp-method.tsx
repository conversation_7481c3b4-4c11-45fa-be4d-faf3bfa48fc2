import * as React from "react";
import { observer } from "mobx-react";
import { $PaymentType } from "../../classes/const/$payment-type";
import styled from "styled-components";
import { Checkbox as CheckboxAMobile } from "antd-mobile";
import { ChooseBankCardModal } from "../choose-bank-card-modal/choose-bank-card-modal";
import { AdjustAmountModal } from "../adjust-amount-modal/adjust-amount-modal";
import { $PaymentModeType } from "../../classes/const/$payment-mode-type";
import { ViewRatesModal } from "../view-rates-modal/view-rates-modal";

const CheckboxItem = CheckboxAMobile.CheckboxItem;

@observer
export class TpMethod extends React.Component<any, any> {

  public onChangeTP = (mode) => {
    this.props.onChangeTP(mode);
  }

  public renderColor = (code) => {
    let color = null;
    switch (code) {
      case $PaymentType.BANK_TRANSFER:
        color = "#52C41A";
        break;
      case $PaymentType.ALLINPAY_BANK_CARD:
        color = "#108EE9";
        break;
      case $PaymentType.ALLINPAY_WECHAT:
        color = "#52C41A";
        break;
      case $PaymentModeType.STOREDVALUE:
        color = "#FF8627";
        break;
      default:
        break;
    }
    return color;
  }

  public render() {
    const { paymentModeList, transferAmount, showChooseBankCardModal, isShow, goBack, chooseBank, selectedBankName, selectedBankCardType, selectedBankCardNo, renderIcon, adjustAmount, changeAdjustAmount, showAmountModal, hideAmountModal, confirmAdjustAmount, mostAmount, tpTransferAmount, bankRedirectUrl, tpRate, singlePaymentModeList, skipToBindCard, showRate, showRateModal, hideRateModal, selectRateList, showPaymentLine, selectedSubMerchant, subMerchantList } = this.props;
    return (
      <TpMethodWrapper>
        {
          paymentModeList && paymentModeList.filter((lis) => lis.code !== $PaymentType.ALLINPAY).filter((mode) => mode.isTPPymt === $PaymentType.ISTPYMT).map((lis) => {
            console.log("tp", lis);
            return (
              <CheckboxItem
                checked={lis.isCheck}
                key={lis.oid}
                onChange={(e) => this.onChangeTP(lis)}
                disabled={lis.isDisabled}
              >
                <span
                  style={{
                    position: "relative", top: lis.isCheck ? "3px" : 0,
                    display: "inline-block",
                    width: "calc(100% - 62px)",
                  }}
                  onClick={(e) => !lis.isDisabled && this.onChangeTP(lis)}
                >
                  <i className={renderIcon(lis.paymentCode)} style={{ color: this.renderColor(lis.paymentCode) }}/>
                  <span style={{ position: "relative", top: "-2px" }}>{lis.paymentName}</span>
                </span>
                {
                  lis.isCheck && <div className={"options-line"}>
                    <div onClick={adjustAmount} style={{ color: "#307DCD", marginBottom: "2px" }}>调整金额</div>
                    <div onClick={() => showRateModal(lis)} style={{ color: "#307DCD" }}>查看费率</div>
                  </div>
                }
                {
                  lis.isCheck && lis.paymentCode === $PaymentType.ALLINPAY_BANK_CARD && <TLBK>
                    <span style={{ width: "80%" }}>
                      预计支付金额:￥
                      <span
                        style={{ color: "#FF3030" }}>{Number(Number(paymentModeList.filter((pay) => pay.code === $PaymentType.ALLINPAY)[0].expectedPaymentAmount) + Number(Number(tpRate).toFixed(2))).toFixed(2)}</span>
                      <span style={{ color: "#FF3030", float: "initial" }}>
                        {
                          tpRate ? Number(tpRate).toFixed(2) !== "0.00" && `（含服务费:${Number(tpRate).toFixed(2)}元）` : null}
                      </span>
                    </span>
                    <span style={{ border: "none" }}>
                      <span>{selectedBankName}{selectedBankCardType}（{selectedBankCardNo}）</span>
                      <span onClick={showChooseBankCardModal}>
                        切换
                        <i className={"scmIconfont scm-icon-jiantou-you"}/>
                      </span>
                  </span>
                  </TLBK>
                }

                {
                  lis.isCheck && lis.paymentCode === $PaymentType.ALLINPAY_WECHAT && <TLBK>
                    <span style={{ border: "none", width: "80%" }}>
                      预计支付金额:￥
                      <span style={{ color: "#FF3030" }}>
                        {Number(Number(paymentModeList.filter((pay) => pay.code === $PaymentType.ALLINPAY)[0] && paymentModeList.filter((pay) => pay.code === $PaymentType.ALLINPAY)[0].expectedPaymentAmount) + Number(Number(tpRate).toFixed(2))).toFixed(2)}
                      </span>
                      <span style={{ color: "#FF3030", float: "initial" }}>
                        {
                          tpRate ? Number(tpRate).toFixed(2) !== "0.00" && `（含服务费:${Number(tpRate).toFixed(2)}元）` : null}
                      </span>
                    </span>
                  </TLBK>
                }
                {
                  lis.isCheck && lis.paymentCode === $PaymentType.ALLINPAY_WECHAT && subMerchantList.length > 1 &&
                  <div className={"payment-channel"}>
                    <div>付款线路  {selectedSubMerchant || "请选择付款线路"}</div>
                    <div onClick={showPaymentLine}>切换
                      <i className={"scmIconfont scm-icon-jiantou-you"}/>
                    </div>
                  </div>
                }
              </CheckboxItem>
            );
          })
        }
        <ChooseBankCardModal
          isShow={isShow}
          goBack={goBack}
          chooseBank={chooseBank}
          bankRedirectUrl={bankRedirectUrl}
          singlePaymentModeList={singlePaymentModeList}
          skipToBindCard={skipToBindCard}
        />
        <AdjustAmountModal
          changeAdjustAmount={changeAdjustAmount}
          showAmountModal={showAmountModal}
          transferAmount={tpTransferAmount}
          hideAmountModal={hideAmountModal}
          confirmAdjustAmount={confirmAdjustAmount}
          mostAmount={mostAmount}
        />
        <ViewRatesModal
          showModal={showRate}
          hideRateModal={hideRateModal}
          selectRateList={selectRateList}
        />
      </TpMethodWrapper>
    );
  }
}

const TpMethodWrapper = styled.div`// styled
  & {
    width: 100%;
    height: auto;
    background: #fff;
    margin-top: 10px;
    .payment-channel {
      display: flex;
      align-items: center;
      justify-content: space-between;
      height: 30px;
      border-top: 1px solid #D8D8D8;
      padding: 10px 18px 10px 0;
      font-size: 12px;
      > div:nth-child(1) {
        width: 70%;
        white-space: nowrap;
        text-overflow: ellipsis;
        overflow: hidden;
      }
    }
    .options-line {
      float: right;
      margin: 5px 10px 5px 0;
    }
    .am-checkbox {
      width: 18px;
      height: 18px;
      position: relative;
      top: 8px;
    }
    .scm-icon-jiantou-you {
      color: #999999;
      //position: relative;
      //top: 2px;
    }
    .am-list-item .am-list-line .am-list-content {
      text-overflow: unset;
      white-space: normal;
    }
  }
`;

const TLBK = styled.div`// styled
  & {
    > span {
      padding: 5px 0;
      display: block;
      width: 100%;
      border-bottom: 1px solid #D8D8D8;
      font-size: 12px;
      > span:last-child {
        float: right;
      }
    }
  }
`;
