import { bean } from "@classes/ioc/ioc";
import { action } from "mobx";
import { post } from "../../helpers/ajax-helpers";

@bean(WxUploadImgService)
export class WxUploadImgService {
  @action
  public loadWxConfigParams(params) {
    return post(`/integration/scm/uploadimages/wxconfig/query`, params);
  }

  @action
  public uploadimagesDownloadimages(params) {
    return post(`/integration/scm/uploadimages/downloadimages`, params);
  }

  @action
  public weixinIsupload() {
    return post(`/integration/scm/weixin/isupload`, {});
  }
}
