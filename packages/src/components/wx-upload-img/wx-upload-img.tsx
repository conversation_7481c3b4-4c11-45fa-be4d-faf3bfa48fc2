import * as React from "react";
import { observer } from "mobx-react";
import { autowired } from "@classes/ioc/ioc";
import { WxUploadImgMv } from "./wx-upload-img-mv";
import styled from "styled-components";
import { Toast } from "antd-mobile";
import { $AppService } from "@classes/service/$app-service";
import { $PaymentType } from "@classes/const/$payment-type";

declare let window: any;

@observer
export class WxUploadImg extends React.Component<any, any> {

  public readyFlag = false;

  @autowired(WxUploadImgMv)
  public wxUploadImgMv: WxUploadImgMv;

  @autowired($AppService)
  public $AppService: $AppService;

  constructor(props) {
    super(props);
    this.state = {
      uploadPictureNumber: 0,
    };
  }

  public componentDidMount() {
    console.log(window.location.href);
    // url必须是window.location.href跳转过来的
    const params = { url: window.location.href };
    this.wxUploadImgMv.loadWxConfigParams(params).then((data) => {
      console.log(data);
      this.wxReady();
      const info = {
        url: window.location.href,
        appId: data.appId,
        timestamp: data.timestamp,
        nonceStr: data.nonceStr,
        signature: data.signature,
      };
      const obj = {
        type: "wxUploadImgInfo",
        path: "",
        pagePath: window.location.href,
        reason: JSON.stringify(info),
      };
      this.$AppService.logRecord({ logInfo: obj });
      this.wxConfig(data.appId, data.timestamp, data.nonceStr, data.signature);
      this.wxUploadImgMv.setIsWx();
    });
  }

  public wxReady = () => {
    wx.ready(() => {
      console.log("wxReady");
      this.readyFlag = true;
    });
  }

  public wxConfig = (appId, timestamp, nonceStr, signature) => {
    console.log("wxconfig");
    wx.config({
      debug: false, // 开启调试模式,调用的所有api的返回值会在客户端alert出来，若要查看传入的参数，可以在pc端打开，参数信息会通过log打出，仅在pc端时才会打印。
      appId, // 必填，公众号的唯一标识
      timestamp, // 必填，生成签名的时间戳
      nonceStr, // 必填，生成签名的随机串
      signature, // 必填，签名
      jsApiList: ["chooseImage", "uploadImage"], // 必填，需要使用的JS接口列表
    });
    wx.error((res) => {
      // config信息验证失败会执行error函数，如签名过期导致验证失败，具体错误信息可以打开config的debug模式查看，也可以在返回的res参数中查看，对于SPA可以在这里更新签名。
      console.log(res);
      const obj = {
        type: "wxConfigError",
        path: "",
        pagePath: window.location.href,
        reason: JSON.stringify(res),
      };
      this.$AppService.logRecord({ logInfo: obj });
    });
  }

  public wxChooseImage = () => {
    const { picRule } = this.props;
    // uploadImage怎么传多图（目前只支持一次上传一张，多张图片需等前一张图片上传之后再调用该接口）
    // uploadImage在chooseImage的回调中有时候Android会不执行，Android6.2会解决此问题，若需支持低版本可以把调用uploadImage放在setTimeout中延迟100ms解决
    // const { isWxUploadingPicture } = this.props;
    if (!this.readyFlag) {
      console.log("====== wait");
      return;
    }
    this.setState({ uploadPictureNumber: this.wxUploadImgMv.imgList.length });
    setTimeout(() => {
      wx.chooseImage({
        count: picRule === $PaymentType.PICRULE ? 9 : 1, // 默认9
        // sizeType: ["original", "compressed"], // 可以指定是原图还是压缩图，默认二者都有
        sizeType: ["compressed"],
        sourceType: ["album", "camera"], // 可以指定来源是相册还是相机，默认二者都有
        success: (res) => {
          console.log(res);
          const localIds = res.localIds; // 返回选定照片的本地ID列表，localId可以作为img标签的src属性显示图片¬
          // isWxUploadingPicture && isWxUploadingPicture(true);
          this.wxUploadImage(localIds);
        },
        fail: (res) => {
          console.log(res);
          const obj = {
            type: "wxUploadImgChooseImgError",
            path: "",
            pagePath: window.location.href,
            reason: JSON.stringify(res),
          };
          this.$AppService.logRecord({ logInfo: obj });
          Toast.fail(res.errMsg);
        },
      });
    }, 100);
  }

  public wxUploadImage = (localIds) => {
    console.log(89898, localIds);
    if (!localIds.length) {
      // const { isWxUploadingPicture } = this.props;
      // isWxUploadingPicture && isWxUploadingPicture(false);
      console.log("上传成功");
    } else {
      const localId = localIds.pop();
      wx.uploadImage({
        localId: localId, // 需要上传的图片的本地ID，由chooseImage接口获得
        isShowProgressTips: 1, // 默认为1，显示进度提示
        success: (res) => {
          console.log(res);
          const serverId = res.serverId; // 返回图片的服务器端ID
          const params = {
            mediaIds: serverId,
          };
          this.wxUploadImgMv.uploadimagesDownloadimages(params).then(() => {
            this.changeTipPosition();
          });
          const { uploadPictureNumber } = this.state;
          console.log("uploadPictureNumber", uploadPictureNumber);
          const { limitLength } = this.props;
          if (uploadPictureNumber >= limitLength - 1) {
            return;
          }
          this.setState({ uploadPictureNumber: uploadPictureNumber + 1 });
          this.wxUploadImage(localIds);
        },
        fail: (res) => {
          console.log(res);
          const obj = {
            type: "wxUploadImgUploadImgError",
            path: "",
            pagePath: window.location.href,
            reason: JSON.stringify(res),
          };
          this.$AppService.logRecord({ logInfo: obj });
          Toast.fail(res.errMsg);
        },
      });
    }
  }

  public changeTipPosition = () => {
    const { imgList } = this.wxUploadImgMv;
    const { picRule } = this.props;
    console.log("imgList", imgList);
    if (imgList && imgList.length === 0) {
      document.getElementsByClassName("paymentVoucherTip")[0].style.marginLeft = "0";
    } else if ((imgList && imgList.length === 1) && (picRule === $PaymentType.PICRULE)) {
      document.getElementsByClassName("paymentVoucherTip")[0].style.marginLeft = "56px";
    }
  }

  public removeImg = (img) => {
    console.log(img);
    const { imgList } = this.wxUploadImgMv;
    if (imgList && imgList.length > 0) {
      // remove(imgList, (lis) => lis.uid === imgUid);
      const index = imgList.findIndex((item) => item === img);
      console.log(index);
      imgList.splice(index, 1);
    }
    console.log(imgList);
    this.wxUploadImgMv.imgList = imgList;
    this.changeTipPosition();
  }

  public render() {
    const { imgList } = this.wxUploadImgMv;
    const { limitLength, picsStyle } = this.props;
    // http://**************/static/348882512/68c23ae7c209266afa9d356e4c61e492.jpg
    return (
      <WxUploadImgPage style={picsStyle}>
        <ImgList>
          {
            imgList && imgList.map((img, index) => {
              return (
                <div key={index}>
                  <img src={img.url} alt=""/>
                  <span onClick={() => this.removeImg(img)}>
                    <i className="scmIconfont scm-icon-guanbi"/>
                  </span>
                </div>
              );
            })
          }
          {
            imgList && imgList.length < limitLength && <div>
              <i className="scmIconfont scm-icon-shangchuantupian" onClick={this.wxChooseImage}/>
            </div>
          }
          <span className="clear"/>
        </ImgList>
      </WxUploadImgPage>
    );
  }
}

const WxUploadImgPage = styled.div`// styled
  & {
    width: 100%;
    height: 100%;
    margin-bottom: 100px;
    .scm-icon-shangchuantupian {
      font-size: 47px;
      color: #ccc;
      position: relative;
      top: 0px;
    }
  }
`;

const ImgList = styled.div`// styled
  & {
    width: 100%;
    height: auto;
    margin-top: 13px;
    padding-top: 10px;
    > div {
      position: relative;
      width: 47px;
      height: 47px;
      line-height: 47px;
      display: inline-block;
      margin-right: 9px;
      float: left;
      > img {
        width: 100%;
        height: 100%;
        border-radius: 3px;
        vertical-align: bottom;
      }
      > span {
        position: absolute;
        top: -7px;
        right: 0px;
        display: block;
        width: 16px;
        height: 16px;
        line-height: 16px;
        border-radius: 8px;
        background-color: #d9d9d9;
        text-align: center;
        .scm-icon-guanbi {
          color: #666;
          font-size: 12px;
          margin-right: 0;
        }
      }
    }
    .clear {
      display: block;
      clear: both;
    }
  }
`;
