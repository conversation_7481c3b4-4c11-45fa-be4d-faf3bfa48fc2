import { autowired, bean } from "@classes/ioc/ioc";
import { action, observable } from "mobx";
import { WxUploadImgService } from "./wx-upload-img-service";
import { $OrderType } from "../../classes/const/$order-type";

@bean(WxUploadImgMv)
export class WxUploadImgMv {

  @autowired(WxUploadImgService)
  public wxUploadImgService: WxUploadImgService;

  @observable public imgList: any[] = [];

  @observable public isWx: boolean = false;

  @action
  public setIsWx() {
    this.isWx = true;
  }

  @action
  public weixinIsupload() {
    return this.wxUploadImgService.weixinIsupload();
  }

  @action
  public loadWxConfigParams(params) {
    return this.wxUploadImgService.loadWxConfigParams(params);
  }
  @action
  public recordOldImgList(imgList, length) {
    this.imgList = imgList;
    if (document.getElementsByClassName("paymentVoucherTip")[0]) {
      if (length === 0) {
        document.getElementsByClassName("paymentVoucherTip")[0].style.marginLeft = "0";
      } else if (length === 1) {
        document.getElementsByClassName("paymentVoucherTip")[0].style.marginLeft = "56px";
      }
    }
  }

  @action
  public uploadimagesDownloadimages(params) {
    return this.wxUploadImgService.uploadimagesDownloadimages(params).then((data) => {
      console.log("uploadimagesDownloadimages", data);
      if (data.errorCode === $OrderType.SUCCESSCODE) {
        data.imgList.map((img) => {
          this.imgList.push(img);
        });
      }
      // this.imgList = this.imgList.slice(0, 10);
    });
  }
}
