import React, { Component } from "react";
import styled from "styled-components";

export function ScrollAbilityModuleComponent(importComponent) {
  class ScrollComponent extends Component {
    constructor(props) {
      super(props);

      this.state = {
        component: null,
        isScrollEnd: false,
        isLoading: false,
      };
    }

    public componentDidMount() {
      this.setState({ component: importComponent });
    }

    public componentWillReceiveProps(nextProps: Readonly<any>, nextContext: any): void {
      const { isLoad } = nextProps;
      console.log("是否还在加载", isLoad);
      if (isLoad) {
        return;
      }
      this.setState({ isLoading: isLoad });
    }

    public touchStart = (e) => {
      this.setState({
        startx: e.touches[0].pageX,
        starty: e.touches[0].pageY,
      });
    }

    public touchEnd = (e) => {
      let endx, endy;
      endx = e.changedTouches[0].pageX;
      endy = e.changedTouches[0].pageY;
      const direction = this.getDirection(this.state.startx, this.state.starty, endx, endy);
      switch (direction) {
        case 0:
          console.log("未滑动！");
          break;
        case 1:
          console.log("向上！");
          if (this.state.isLoading) {
            return;
          }
          this.isScrollToEnd();
          break;
        case 2:
          console.log("向下！");
          break;
        case 3:
          console.log("向左！");
          break;
        case 4:
          console.log("向右！");
          break;
        default:
      }
    }

    public getAngle(angx, angy) {
      return Math.atan2(angy, angx) * 180 / Math.PI;
    }

    public isScrollToEnd() {
      const { loadData } = this.props;
      const scrollAbility = $(".module-scroll-ability-wrap")[0];
      const dataHeight = scrollAbility.clientHeight;
      const scrollAbilityContent = scrollAbility.firstElementChild;
      const scrollAbilityContentHeight = $(scrollAbilityContent).height();
      const scrollAbilityContentScroll = $(scrollAbilityContent).position().top;
      const h = 10;
      const { isScrollEnd } = this.state;
      console.log(isScrollEnd, scrollAbilityContentHeight, scrollAbilityContentScroll, scrollAbilityContentHeight + scrollAbilityContentScroll - h, dataHeight)
      if (scrollAbilityContentHeight + scrollAbilityContentScroll - h < dataHeight) {
        if (!isScrollEnd) {
          this.setState({ isScrollEnd: true, isLoading: true }, () => {
            this.setState({ isScrollEnd: false });
            loadData && loadData();
          });
          console.log("进来了", true);
        }
      } else {
        console.log("还未到底");
      }
    }

    public getDirection(startx, starty, endx, endy) {
      const angx = endx - startx;
      const angy = endy - starty;
      let result = 0;

      // 如果滑动距离太短
      if (Math.abs(angx) < 2 && Math.abs(angy) < 2) {
        return result;
      }
      const angle = this.getAngle(angx, angy);
      if (angle >= -135 && angle <= -45) {
        result = 1;
      } else if (angle > 45 && angle < 135) {
        result = 2;
      } else if ((angle >= 135 && angle <= 180) || (angle >= -180 && angle < -135)) {
        result = 3;
      } else if (angle >= -45 && angle <= 45) {
        result = 4;
      }
      return result;
    }

    public render() {
      const C = this.state.component;
      return C ?
        <ScrollAbility className="module-scroll-ability-wrap">
          <div className="module-scroll-ability" onTouchStart={this.touchStart} onTouchEnd={this.touchEnd}>
            <C {...this.props} />
          </div>
        </ScrollAbility>
          : <div/>;
    }
  }

  return ScrollComponent;
}

const ScrollAbility = styled.div`// styled
  & {
    height: 100%;
    position: relative;
    overflow-y: scroll;
    -webkit-overflow-scrolling: touch;
    overflow-scrolling: touch;
    .module-scroll-ability{
      height: auto;
      position: relative;
    }
  }
`;
