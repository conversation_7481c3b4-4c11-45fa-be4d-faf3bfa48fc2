import * as React from "react";
import { observer } from "mobx-react";
import styled from "styled-components";
import { InputItemComponent } from "../input-item-component/input-item-component";
import { ButtonComponent } from "../button-component/button-component";

@observer
export class OrderPaymentAccountShow extends React.Component<any, any> {

  public constructor(props) {
    super(props);
    this.state = {};
  }

  public render() {
    const { payableAmount, auditedAmount, unAuditedAmount, unPayableAmount } = this.props;
    return (
      <ComponentWrap>
        <PaymentAmount>
          <p>
            ￥{payableAmount}
          </p>
          <p>
            应付金额
          </p>
        </PaymentAmount>
        <PaymentAmount>
          <p>
            ￥{auditedAmount}
          </p>
          <p>
            已确认金额
          </p>
        </PaymentAmount>
        <PaymentAmount>
          <p>
            ￥{unAuditedAmount}
          </p>
          <p>
            待确认金额
          </p>
        </PaymentAmount>
        <PaymentAmount>
          <p>
            ￥{unPayableAmount}
          </p>
          <p>
            未支付金额
          </p>
        </PaymentAmount>
      </ComponentWrap>
    );
  }
}
const ComponentWrap = styled.div`// styled
  & {
    width: 100%;
    height: 72px;
    background: #ECF6FF;
    padding: 15px 0px 10px;
    > div:last-child {
      border: 0;
    }
  }
`;

const PaymentAmount = styled.div`// styled
  & {
    display: inline-block;
    width: 25%;
    height: 50px;
    box-sizing: border-box;
    border-right: 1px solid #CDDAE6;
    text-align: center;

    > p:nth-of-type(1) {
      color: #FF3030;
      font-size: 16px;
      margin-bottom: 5px;
    }

    > p:nth-of-type(2) {
      color: #333333;
      font-size: 12px;
      margin: 0;
    }
  }
`;
