import { observer } from "mobx-react";
import React from "react";
import styled from "styled-components";

interface ModelProps{
  children?:any;
  contentStyle?:any;
}

@observer
export class ModelWapper extends React.Component<ModelProps, any> {

  public render() {
    const {children,contentStyle} = this.props;
    return (
      <Wapper >
            <div style={contentStyle}>
                {children}
            </div>
      </Wapper>
    );
  }
}


const Wapper = styled.div`// styled
  & {
      position:fixed;
      width:100%;
      height:${document.documentElement.clientHeight}px;
      top:0;
      left:0;
      z-index:999;
      background:rgba(0,0,0,0.5);
      >div{
        position: relative;
        top:50%;
        width:90%;
        background-color:#fff;
        margin:-50% auto 0;
        height:100px;
        border-radius:10px;
      }
  }
`;

