import * as React from "react";
import { observer } from "mobx-react";
import {cloneDeep} from "lodash";
import styled from "styled-components";

@observer
export class ScreeningStores extends React.Component<any, any> {
  constructor(props) {
    super(props);
    this.state = ({
      orderPartyList: [],
      salesOrderStatusList: [],
      schemeList: [],
    });
  }
  public componentDidMount(): void {
    this.resetData();
  }

  public resetData = () => {
    this.setState({
      orderPartyList: cloneDeep(this.props.orderPartyList || []),
      salesOrderStatusList: cloneDeep(this.props.salesOrderStatusList || []),
      schemeList: cloneDeep(this.props.schemeList || []),
    });
  }
  public changeOrderParty = (orderParty) => {
    const { orderPartyList } = this.state;
    orderPartyList.map((status, index) => {
      if (status.oid === orderParty) {
        status.checked = !status.checked;
        status.showActiveOrderparty = !status.showActiveOrderparty;
      }
    });
    this.setState({ orderPartyList });
  }
  public chooseStatus = (value) => {
    const { salesOrderStatusList } = this.state;
    salesOrderStatusList.map((status, index) => {
      if (status.value === value) {
        status.checked = !status.checked;
        status.showActiveOrderparty = !status.showActiveOrderparty;
      }
    });
    this.setState({ salesOrderStatusList });
  }
  public chooseScheme = (value) => {
    const { schemeList } = this.state;
    schemeList.map((scheme, index) => {
      if (scheme.value === value) {
        scheme.checked = !scheme.checked;
      }
    });
    this.setState({ schemeList });
  }
  public render() {
    const { isHideOrderStatus, isHideScheme } = this.props;
    const { orderPartyList, salesOrderStatusList, schemeList } = this.state;
    return (
      <OrderPop className={"not-touch"}>
        <PopLeft onClick={this.props.canclePop} className={"not-touch"}/>
        <PopRight>
          {
            !isHideOrderStatus &&
              <SearchStatus>
                <p>筛选订单状态</p>
                <p>
                  {
                    salesOrderStatusList.map((status, index) => {
                      return (
                        <span
                          key={index}
                          className={status.checked ? "active" : null}
                          onClick={() => this.chooseStatus(status.value)}
                        >
                          {status.text}
                          <span
                            style={{ display: status.showActiveOrderparty ? "block" : "none" }}
                          >
                            <i className="scmIconfont scm-icon-guanbi"/>
                          </span>
                        </span>
                      );
                    })
                  }
                </p>
              </SearchStatus>
          }
          {
            !isHideScheme &&
              <SelectParty>
                <div>筛选订货方案</div>
                <div className={"partys"}>
                  {
                    schemeList.length > 0 ? schemeList.map((item) => {
                      return (
                        <p
                          key={item.value}
                          onClick={() => this.chooseScheme(item.value)}
                          className={item.checked ? "activeOrderParty" : ""}
                        >
                          <i className="scmIconfont scm-icon-shop"/>
                          <span>
                            {item.text}
                            <span style={{ display: item.checked ? "block" : "none" }}>
                              <i className="scmIconfont scm-icon-guanbi"/>
                            </span>
                          </span>
                        </p>
                      );
                    }) : <p>暂无订货方案</p>
                  }
                </div>
              </SelectParty>
          }
          <SelectParty isHideScheme={isHideScheme}>
            <div>
              筛选门店
            </div>
            <div className={"partys"}>
              {
                orderPartyList.length > 0 ? orderPartyList.map((item, index) => {
                  return (
                    <p
                      key={index}
                      onClick={() => this.changeOrderParty(item.oid)}
                      className={item.checked ? "activeOrderParty" : ""}
                    >
                      <i className="scmIconfont scm-icon-shop"/>
                      <span>
                            {item.name && item.name.length > 17 ? item.name.slice(0, 17) + "..." : item.name}
                        <span
                          style={{ display: item.showActiveOrderparty ? "block" : "none" }}
                        >
                          <i className="scmIconfont scm-icon-guanbi"/>
                        </span>
                      </span>
                    </p>
                  );
                }) : <p>暂无门店</p>
              }
            </div>
          </SelectParty>
          <PopButton>
            <span onClick={this.resetData}>重置</span>
            <span onClick={() => this.props.confirmSearch({ salesOrderStatusList, orderPartyList, schemeList })}>确定</span>
          </PopButton>
        </PopRight>
      </OrderPop>
    );
  }
}

const OrderPop = styled.div`// styled
  & {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    width: 100%;
    height: 100%;
    z-index: 99;
    .am-search {
      height: 88px;
      background-color: #fff;
      padding: 0;
      position: relative;
      :after {
        content: '';
        position: absolute;
        background-color: #D8D8D8 !important;
        display: block;
        z-index: 1;
        top: auto;
        right: auto;
        bottom: 0;
        left: 0;
        width: 100%;
        height: 1px;
        transform-origin: 50% 100%;
        transform: scaleY(0.5);
      }
    }
    .am-search-input {
      margin: 44px 8px 16px 8px;
      background: #F4F4F4;
      border-radius: 13px;
    }
    .am-search-cancel {
      line-height: 72px;
    }
    .am-search-input .am-search-synthetic-ph-placeholder {
      font-size: 13px;
      font-family: "SourceHanSansCN-Normal";
      font-weight: 400;
      color: rgba(153, 153, 153, 1);
    }
  }
`;

const PopLeft = styled.div`// styled
  & {
    display: inline-block;
    width: calc(100% - 272px);
    height: 100%;
    background: rgba(0, 0, 0, 0.3);
  }
`;

const PopRight = styled.div`// styled
  & {
    display: inline-block;
    width: 272px;
    height: 100%;
    background: #fff;
    z-index: 99;
    position: fixed;
    overflow: hidden;
  }
`;

const SelectParty = styled.div`// styled
  & {
    width: 272px;
    height: auto;
    padding: 16px 8px;
    > div:first-child {
      font-size: 12px;
      font-family: "SourceHanSansCN-Normal";
      font-weight: 400;
      color: rgba(117, 117, 117, 1);
    }
    > div:last-child {
      width: 272px;
      height: ${(props: any) => props.isHideScheme ?
        `calc(${document.documentElement.clientHeight}px - 126px)` :
        `calc(${document.documentElement.clientHeight / 2}px - 83px)`};
      overflow-y: auto;
      margin-top: 16px;
      padding-bottom: ${(props: any) => props.isHideScheme ? "155px" : "0"};
      -webkit-overflow-scrolling: auto;
      > p {
        width: 256px;
        height: 32px;
        background: rgba(244, 244, 244, 1);
        border-radius: 4px;
        padding: 5px 8px;
        font-size: 12px;
        font-family: "SourceHanSansCN-Normal";
        font-weight: 400;
        color: rgba(51, 51, 51, 1);
        > i {
          margin-right: 8px;
        }
      }
      .activeOrderParty {
        color: #307DCD;
        background: rgba(48, 125, 205, 0.1);
        position: relative;
        > span span {
          display: block;
          width: 0;
          height: 0;
          border-bottom: 20px solid #307DCD;
          border-left: 20px solid transparent;
          position: absolute;
          bottom: 0;
          right: 0;
          border-bottom-right-radius: 0 4px;
          > i {
            font-size: 5px;
            color: #fff;
            position: absolute;
            right: 2px;
            top: 10px;
          }
        }
      }
    }
  }
`;

const PopButton = styled.div`// styled
  & {
    width: 272px;
    height: 48px;
    position: fixed;
    bottom: 0;
    padding: 6px 12px;
    text-align: right;
    border-top: 1px solid #D8D8D8;
    z-index: 100;
    background: #fff;
    > span {
      font-size: 14px;
      font-family: "SourceHanSansCN-Normal";
      font-weight: 400;
      color: rgba(255, 255, 255, 1);
    }
    > span:first-child {
      display: inline-block;
      width: 88px;
      height: 36px;
      background: rgba(82, 196, 26, 1);
      border-radius: 20px 0px 0px 20px;
      padding: 10px 30px;
    }
    > span:last-child {
      display: inline-block;
      width: 88px;
      height: 36px;
      background: rgba(48, 125, 205, 1);
      border-radius: 0px 20px 20px 0px;
      padding: 10px 30px;
    }
  }
`;

const SearchStatus = styled.div`// styled
  & {
    width: 100%;
    height: auto;
    padding: 16px 0 8px 8px;
    //border-top: 1px solid #D8D8D8;
    border-bottom: 1px solid #D8D8D8;

    > p {
      margin-bottom: 16px;
      font-size: 12px;
      font-family: "SourceHanSansCN-Normal";
      font-weight: 400;
      color: rgba(117,117,117,1);
    }

    > p:last-child {
      margin-bottom: 0;
      > span {
        display: inline-block;
        width: 80px;
        height: 32px;
        border-radius: 4px;
        padding: 0px 10px;
        line-height: 32px;
        box-sizing: border-box;
        margin-right: 8px;
        background: #F0F0F0;
        color: #999;
        text-align: center;
        margin-bottom: 10px;
        font-size: 12px;
        font-family: "SourceHanSansCN-Normal";
        font-weight: 400;
      }

      .active {
        color: #307DCD;
        background: #ECF6FF;
        position: relative;
        > span {
          display: block;
          width: 0;
          height: 0;
          border-bottom: 20px solid #307DCD;
          border-left: 20px solid transparent;
          position: absolute;
          bottom: 0;
          right: 0;
          border-bottom-right-radius: 0 4px;
          > i {
            font-size: 5px;
            color: #fff;
            position: absolute;
            right: 2px;
            top: -2px;
          }
        }
      }

      > span:last-child {
        margin-right: 0;
      }
    }
  }
`;
