import * as React from "react";
import { observer } from "mobx-react";
import styled from "styled-components";
import { autowired } from "@classes/ioc/ioc";
import { ChooseShopMv } from "./choose-shop-mv";
import { LoadingTip } from "../loading-marked-words";
import { ScrollAbilityWrapComponent } from "../scroll-ability/wrap";
import { SITE_PATH } from "../../modules/app";
import { NoGoods } from "../no-goods/no-goods";
import { getQueryString } from "../../classes/utils/UrlUtils";

@observer
class ChooseShopComponent extends React.Component<any, any> {

  @autowired(ChooseShopMv)
  public chooseShopMv: ChooseShopMv;

  public constructor(props) {
    super(props);
    this.state = {
      finished: false,
      isShow: false,
      startx: 0,
      starty: 0,
    };
  }

  public componentDidMount() {
    document.title = "欠货查询选择门店";
    this.chooseShopMv.pageIndex = 0;
    this.chooseShopMv.orderPartyList = [];
    this.loadData();
  }

  public componentWillReceiveProps(nextProps: Readonly<any>, nextContext: any): void {
    const { isScrollEnd } = nextProps;
    console.log(isScrollEnd);
    if (isScrollEnd && isScrollEnd !== this.props.isScrollEnd) {
      this.loadData();
    }
  }

  public loadData = () => {
    const { finished } = this.state;
    if (finished) {
      return;
    }
    this.loadOrderpartyList();
  }

  public loadOrderpartyList = () => {
    const { pageIndex, pageSize } = this.chooseShopMv;
    const params = {
      pageIndex,
      pageSize,
    };
    this.setState({ isShow: true });
    this.chooseShopMv.showSpin();
    this.chooseShopMv.loadOrderpartyList(params).then((data) => {
      this.chooseShopMv.hideSpin();
      const { loadingEnd } = this.props;
      if (data.orderPartyList && data.orderPartyList.length > 0) {
        this.chooseShopMv.orderPartyList = this.chooseShopMv.orderPartyList.concat(data.orderPartyList);
        this.chooseShopMv.orderPartyList.map((lis) => {
          lis.isCheck = false;
        });
        const selectedShop = getQueryString("shopInfo") && JSON.parse(getQueryString("shopInfo"));
        console.log(selectedShop);
        if (selectedShop) {
          if (this.chooseShopMv.orderPartyList.filter((lis) => lis.oid === selectedShop.oid).length > 0) {
            this.chooseShopMv.orderPartyList.filter((lis) => lis.oid === selectedShop.oid)[0].isCheck = true;
          }
        }
        this.chooseShopMv.changePage();
        this.setState({
          finished: this.chooseShopMv.orderPartyList.length >= data.itemCount,
          isShow: false,
        });
        loadingEnd && loadingEnd(this.chooseShopMv.orderPartyList.length >= data.itemCount);
      } else {
        this.setState({
          finished: true,
          isShow: false,
        });
        loadingEnd && loadingEnd(true);
      }
    }).catch(() => {
      this.chooseShopMv.hideSpin();
    });
  }

  public chooseItem = (item) => {
    if (this.chooseShopMv.orderPartyList.filter((lis) => lis.oid === item.oid).length > 0) {
      this.chooseShopMv.orderPartyList.filter((lis) => lis.oid === item.oid)[0].isCheck = true;
      window.location.href = `/${SITE_PATH}/owe-goods-select?shopInfo=${JSON.stringify(item)}`;
    }
  }

  public render() {
    const { orderPartyList } = this.chooseShopMv;
    const { finished, isShow } = this.state;
    return (
      <ChooseShopWrapper>
        {
          orderPartyList && orderPartyList.length > 0 && <ChooseShopHeader>
            <span/>
            <span>请选择门店</span>
            <span/>
          </ChooseShopHeader>
        }
        <ChooseShopList>
          {
            orderPartyList && orderPartyList.length > 0 ? orderPartyList.map((item) => {
              return (
                <ChooseShopItem key={item.oid} theme={{ length: item.name && item.name.length }}
                                onClick={() => this.chooseItem(item)}>
                  <i className={"scmIconfont scm-icon-shop"}/>
                  <span>{item.name && item.name.length > 35 ? item.name.slice(0, 35) + "..." : item.name}</span>
                  <i className={item.isCheck && "scmIconfont scm-dagou2"}/>
                </ChooseShopItem>
              );
            }) : <NoGoods title="暂无门店" height={document.documentElement.clientHeight - 12 - 12 - 12}/>
          }
          {
            orderPartyList && orderPartyList.length > 0 &&
            <LoadingTip
              isFinished={finished}
              isLoad={isShow}
            />
          }
        </ChooseShopList>
      </ChooseShopWrapper>
    );
  }
}

const chooseShop = ScrollAbilityWrapComponent(ChooseShopComponent);
export default chooseShop;

const ChooseShopWrapper = styled.div`// styled
  & {
    width: 100%;
    height: ${document.documentElement.clientHeight}px;
    overflow-y: auto;
    padding: 12px;
    background: #F2F2F2;
  }
`;

const ChooseShopHeader = styled.div`// styled
  & {
    width: 100%;
    height: 14px;
    background: #F2F2F2;
    > span:first-child {
      width: calc((100% - 70px - 12px - 12px) / 2);
      height: 1px;
      background: #D8D8D8;
      display: inline-block;
      vertical-align: middle;
    }
    > span:nth-of-type(2) {
      display: inline-block;
      width: 70px;
      margin: 0 10px;
      font-size: 14px;
      font-family: SourceHanSansCN-Regular, SourceHanSansCN;
      font-weight: 400;
      color: rgba(47, 47, 47, 1);
    }
    > span:nth-of-type(3) {
      display: inline-block;
      width: calc((100% - 70px - 12px - 12px) / 2);
      height: 1px;
      background: #D8D8D8;
      vertical-align: middle;
    }
  }
`;

const ChooseShopList = styled.div`// styled
  & {
    width: 100%;
    height: auto;
    padding-top: 12px;
    background: #F2F2F2;
  }
`;

const ChooseShopItem = styled.div`// styled
  & {
    width: 100%;
    height: 44px;
    background: #fff;
    border-radius: 4px;
    padding: 0px 16px 0px 13px;
    margin-bottom: 12px;
    font-size: 13px;
    font-family: SourceHanSansCN-Normal, SourceHanSansCN;
    font-weight: 400;
    color: rgba(47, 47, 47, 1);
    line-height: ${(props) => props.theme.length > 20 ? "18px" : "44px" };
    .scm-dagou2 {
      color: #307DCD;
      float: right;
      margin-top: ${(props) => props.theme.length > 20 ? "15px" : "0" };
    }
    .scm-icon-shop {
      margin-right: 8px;
      position: relative;
      top: ${(props) => props.theme.length > 20 ? "0" : "1px" };
      vertical-align: ${(props) => props.theme.length > 20 ? "super" : "unset" };
    }
    > span {
      display: inline-block;
      width: calc(100% - 16px - 13px - 24px);
      margin-top: ${(props) => props.theme.length > 20 ? "4px" : "0px" };
    }
  }
`;
