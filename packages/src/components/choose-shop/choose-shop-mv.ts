import { autowired, bean } from "@classes/ioc/ioc";
import { action, observable } from "mobx";
import { ChooseShopService } from "./choose-shop-service";

@bean(ChooseShopMv)
export class ChooseShopMv {

  @autowired(ChooseShopService)
  public chooseShopService: ChooseShopService;

  @observable public orderPartyList: any[] = [];
  @observable public dataList: any[] = [];

  @observable public isSpin: boolean = false;

  @observable public pageIndex: number = 0;

  @observable public pageSize: number = 20;

  @action
  public showSpin() {
    this.isSpin = true;
  }

  @action
  public hideSpin() {
    this.isSpin = false;
  }

  @action
  public changePage() {
    this.pageIndex++;
  }

  @action
  public loadOrderpartyList(params) {
    return this.chooseShopService.loadOrderpartyList(params);
  }
}
