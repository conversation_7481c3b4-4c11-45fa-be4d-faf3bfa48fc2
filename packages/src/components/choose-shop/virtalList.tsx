import { message, <PERSON><PERSON>, Spin } from "antd";
import {<PERSON>Sizer, InfiniteLoader, List, WindowScroller} from "react-virtualized";
import * as React from "react";
import { observer } from "mobx-react";
import { withRouter } from "react-router";
import { autowired } from '@classes/ioc/ioc';
import { ChooseShopMv } from './choose-shop-mv';
import styled from 'styled-components';
import { SITE_PATH } from '../../modules/app';
import { getQueryString } from '../../classes/utils/UrlUtils';
import { NoGoods } from '../no-goods/no-goods';
import { LoadingTip } from '../loading-marked-words';
// import { autowired } from '@classes/ioc/ioc';
// import { ChooseShopMv } from './choose-shop-mv';
// import { observable } from 'mobx';
// import { getQueryString } from '../../classes/utils/UrlUtils';
// import styled from 'styled-components';
// import { NoGoods } from '../no-goods/no-goods';
// import { LoadingTip } from '../loading-marked-words';
@withRouter
@observer
class VirtualizedExampleWrap extends React.Component<any, any> {
  @autowired(ChooseShopMv)
  public myMv: ChooseShopMv;
  private loadedRowsMap = {};
  public constructor(props) {
    super(props);
    this.state = {
      finished: false,
      isShow: false,
      startx: 0,
      starty: 0,
    };
  }

  public loadData = () => {
    const { finished } = this.state;
    if (finished) {
      return;
    }
    this.loadOrderpartyList();
  }
  public componentDidMount() {
    document.title = "欠货查询";
    this.myMv.pageIndex = 0;
    this.myMv.orderPartyList = [];
    this.loadData();
  }
  public loadOrderpartyList = () => {
    const { pageIndex, pageSize } = this.myMv;
    const params = {
      pageIndex,
      pageSize,
    };
    this.setState({ isShow: true });
    this.myMv.showSpin();
    this.myMv.loadOrderpartyList(params).then((data) => {
      this.myMv.hideSpin();
      if (data.orderPartyList && data.orderPartyList.length > 0) {
        this.myMv.orderPartyList = this.myMv.orderPartyList.concat(data.orderPartyList);
        this.myMv.orderPartyList.map((lis) => {
          lis.isCheck = false;
        });
        const selectedShop = getQueryString("shopInfo") && JSON.parse(getQueryString("shopInfo"));
        console.log(selectedShop);
        if (selectedShop) {
          if (this.myMv.orderPartyList.filter((lis) => lis.oid === selectedShop.oid).length > 0) {
            this.myMv.orderPartyList.filter((lis) => lis.oid === selectedShop.oid)[0].isCheck = true;
          }
        }
        this.myMv.changePage();
        this.setState({
          finished: this.myMv.orderPartyList.length >= data.itemCount,
          isShow: false,
        });
      } else {
        this.setState({
          finished: true,
          isShow: false,
        });
      }
    }).catch(() => {
      this.myMv.hideSpin();
    });
  }
  public chooseItem = (item) => {
    if (this.myMv.orderPartyList.filter((lis) => lis.oid === item.oid).length > 0) {
      this.myMv.orderPartyList.filter((lis) => lis.oid === item.oid)[0].isCheck = true;
      window.location.href = `/${SITE_PATH}/owe-goods-select?shopInfo=${JSON.stringify(item)}`;
    }
  }
  public handleInfiniteOnLoad = ({ startIndex, stopIndex }) => {
    const { finished } = this.state;
    this.myMv.isSpin = true;
    console.log("startIndex=", startIndex, "stopIndex=", stopIndex);
    for (let i = startIndex; i <= stopIndex; i++) {
      // 1 means loading
      this.loadedRowsMap[i] = 1;
    }
    // 此处判断是否还需要加载数据
    if (finished) {
      message.warning("加载结束");
      return;
    }
    this.loadOrderpartyList();
  }

  public isRowLoaded = ({ index }) => !!this.loadedRowsMap[index];
  public renderItem = ({ index, key, style }) => {
    const { orderPartyList } = this.myMv;
    const item = orderPartyList[index];
    return (
      <ChooseShopItem
        key={key}
        style={style}
        theme={{ length: item.name && item.name.length }}
        onClick={() => this.chooseItem(item)}
      >
        <i className={"scmIconfont scm-icon-shop"}/>
        <span>{item.name && item.name.length > 35 ? item.name.slice(0, 35) + "..." : item.name}{index}</span>
        <i className={item.isCheck && "scmIconfont scm-dagou2"}/>
      </ChooseShopItem>
    );
  }
  public render() {
    const { orderPartyList } = this.myMv;
    const { finished, isShow } = this.state;
    const vlist = ({ height, onRowsRendered, width }) => (
      <List
        height={height}
        overscanRowCount={2}
        rowCount={orderPartyList.length}
        rowHeight={56}
        rowRenderer={this.renderItem}
        onRowsRendered={onRowsRendered}
        width={width}
      />
    );
    const autoSize = ({ height, onRowsRendered }) => (
      <AutoSizer
        disableHeight={false}
      >
        {({ width }) =>
          vlist({
            height,
            onRowsRendered,
            width,
          })
        }
      </AutoSizer>
    );
    const infiniteLoader = ({ height }) => {

      return <InfiniteLoader
        isRowLoaded={this.isRowLoaded}
        loadMoreRows={this.handleInfiniteOnLoad}
        rowCount={1000000000}
      >
        {({ onRowsRendered }) =>
          autoSize({
            height,
            onRowsRendered,
          })
        }
      </InfiniteLoader>;
    }
    return (
			<ChooseShopWrapper>
        {
          orderPartyList && orderPartyList.length > 0 && <ChooseShopHeader>
						<span/>
						<span>请选择门店</span>
						<span/>
					</ChooseShopHeader>
        }
				<ChooseShopList>
          {
            orderPartyList && orderPartyList.length > 0 ?
              <WindowScroller>{infiniteLoader}</WindowScroller>
              : <NoGoods title="暂无门店" height={document.documentElement.clientHeight - 12 - 12 - 12}/>
          }
          {
            orderPartyList && orderPartyList.length > 0 &&
						<LoadingTip
							isFinished={finished}
							isLoad={isShow}
						/>
          }
				</ChooseShopList>
			</ChooseShopWrapper>
    );
  }
}

const VirtualizedExample = VirtualizedExampleWrap;
export default VirtualizedExample;

const ChooseShopWrapper = styled.div`// styled
  & {
    width: 100%;
    height: ${document.documentElement.clientHeight}px;
    overflow-y: auto;
    padding: 12px;
    background: #F2F2F2;
  }
`;

const ChooseShopHeader = styled.div`// styled
  & {
    width: 100%;
    height: 14px;
    background: #F2F2F2;
    > span:first-child {
      width: calc((100% - 70px - 12px - 12px) / 2);
      height: 1px;
      background: #D8D8D8;
      display: inline-block;
      vertical-align: middle;
    }
    > span:nth-of-type(2) {
      display: inline-block;
      width: 70px;
      margin: 0 10px;
      font-size: 14px;
      font-family: SourceHanSansCN-Regular, SourceHanSansCN;
      font-weight: 400;
      color: rgba(47, 47, 47, 1);
    }
    > span:nth-of-type(3) {
      display: inline-block;
      width: calc((100% - 70px - 12px - 12px) / 2);
      height: 1px;
      background: #D8D8D8;
      vertical-align: middle;
    }
  }
`;

const ChooseShopList = styled.div`// styled
  & {
    width: 100%;
    height: auto;
    padding-top: 12px;
    background: #F2F2F2;
  }
`;

const ChooseShopItem = styled.div`// styled
  & {
    width: 100%;
    height: 44px;
    background: #fff;
    border-radius: 4px;
    padding: 0px 16px 0px 13px;
    margin-bottom: 12px;
    font-size: 13px;
    font-family: SourceHanSansCN-Normal, SourceHanSansCN;
    font-weight: 400;
    color: rgba(47, 47, 47, 1);
    line-height: ${(props) => props.theme.length > 20 ? "18px" : "44px" };
    .scm-dagou2 {
      color: #307DCD;
      float: right;
      margin-top: ${(props) => props.theme.length > 20 ? "15px" : "0" };
    }
    .scm-icon-shop {
      margin-right: 8px;
      position: relative;
      top: ${(props) => props.theme.length > 20 ? "0" : "1px" };
      vertical-align: ${(props) => props.theme.length > 20 ? "super" : "unset" };
    }
    > span {
      display: inline-block;
      width: calc(100% - 16px - 13px - 24px);
      margin-top: ${(props) => props.theme.length > 20 ? "4px" : "0px" };
    }
  }
`;
