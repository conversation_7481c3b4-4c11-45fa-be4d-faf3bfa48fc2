import { observer } from "mobx-react";
import React from "react";
import { withRouter } from "react-router";
import styled from "styled-components";

declare let window: any;
declare let require: any;

@observer
export class NoGoods extends React.Component<any, any> {

  public render() {
    const { title, height } = this.props;
    return (
      <NoGoodsWrapper className="noGoods" theme={{ height }}>
        <img src="https://order.fwh1988.cn:14501/static-img/scm/ico_default.png" alt=""/>
        <Title>{title}</Title>
      </NoGoodsWrapper>
    );
  }
}

const Title = styled.h1`// styled
  & {
    font-size: 14px;
    color: #999999;
  }
`;
const NoGoodsWrapper = styled.div`// styled
  & {
    text-align:center;
    width:100%;
    height: ${(props) => props.theme.height}px;
    img{
      width: 13%;
      margin-bottom:10px;
      margin-top: 80px;
    }
  }
`;
