import * as React from "react";
import { observer } from "mobx-react";
import styled from "styled-components";
import { Modal } from "antd-mobile";
import { $OrderType } from "../../classes/const/$order-type";

const alert = Modal.alert;

@observer
export class SinglePaymentRecordInfo extends React.Component<any, any> {

  public render() {
    const { record } = this.props;
    const { amount, code, docStatus, paymentModeName, weChatPayFlowNo, isReSubmitShow, paymentTime, orderPaymentId, memo, serviceCharge } = record;
    return (
      <RecordWrap>
        <Record style={{
          background: "#F2F2F2",
          padding: "10px",
        }}>
          <p>
            <span className="RecordName">付款金额：</span>
            <span className="RecordValue">￥{amount !== null ? Number(amount).toFixed(2) : null}</span>
            <span
              style={{
                fontSize: 12,
                color:
                  code === $OrderType.CODE ?
                    "#FF3030" : "#999",
              }}>
                            {docStatus}
                            </span>
          </p>
          {
            serviceCharge !== null && <p>
              <span className="RecordName">服务费：</span>
              {
                (serviceCharge || serviceCharge === 0) ?
                  <span>￥{Number(serviceCharge).toFixed(2)}</span>
                  :
                  <span>{serviceCharge}</span>
              }
            </p>
          }
          <p>
            <span className="RecordName">付款时间：</span>
            <span>{paymentTime}</span>
          </p>
          <p>
            <span className="RecordName">支付方式：</span>
            <span>{paymentModeName}</span>
          </p>
          <p>
            <span className="RecordName">流水号：</span>
            <span>{weChatPayFlowNo}</span>
          </p>
          {
            memo && <p>
              <span className="RecordName">备注：</span>
              <span>{memo}</span>
            </p>
          }
        </Record>
        {/*{*/}
        {/*  isReSubmitShow ?*/}
        {/*    <ReSubmitBtn style={{ background: "#F2F2F2" }}>*/}
        {/*      <Button className="btn"*/}
        {/*              style={{*/}
        {/*                border: "0.5px solid #307DCD",*/}
        {/*                color: "#307DCD",*/}
        {/*              }}*/}
        {/*              onClick={() => {*/}
        {/*                reConfirm && reConfirm(orderPaymentId, paymentTime);*/}
        {/*              }}>重新提交</Button>*/}
        {/*    </ReSubmitBtn> : null*/}
        {/*}*/}
      </RecordWrap>
    );
  }
}

const RecordWrap = styled.div`
  & {
    margin-top: 16px;
  }
`
const Record = styled.div`// styled
  & {
    width: 100%;
    font-size: 12px;
    color: #333;
    border-radius: 3px;
    .RecordName {
      color: #666;
      font-size: 12px;
    }

    .RecordValue {
      color: #FF3030;
      font-size: 12px;
    }

    > p {
      margin-bottom: 10px;
    }

    > p:first-child {
      position: relative;

      > span:last-child {
        position: absolute;
        top: 0;
        right: 0;
        color: #999;
      }
    }

    > p:last-of-type {
      display: flex;
      margin-bottom: 0;
      span:first-of-type {
        white-space: nowrap;
      }

      span:nth-of-type(2) {
        flex: 1;
        word-wrap: break-word;
        min-width: 1px;
      }
    }
  }
`;
const ReSubmitBtn = styled.div`// styled
  & {
    width: 100%;
    height: 50px;
    padding: 10px;
    position: relative;

    .am-button {
      float: right;
      width: 88px;
      height: 30px;
      border-radius: 3px;
      font-size: 12px;
      font-family: "PingFangSC-Regular";
      line-height: 30px;
    }

    .am-button-primary {
      background-color: #307DCD;
    }
  }
`;
