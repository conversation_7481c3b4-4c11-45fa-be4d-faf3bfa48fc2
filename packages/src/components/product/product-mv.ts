import {autowired, bean} from "@classes/ioc/ioc";
import {action, observable} from "mobx";
import { $Page } from "../../classes/entity/$pagination";
import { $Product } from "../../classes/entity/$product";
import { $ProductService } from "../../classes/service/$product-service";

@bean($ProductMv)
export class $ProductMv {
  @autowired($ProductService)
  public $productService: $ProductService;

  @observable public products: $Product[] = [];
  @observable public page: $Page = new $Page();
  @observable public loaded: boolean = false;
  @observable public keyword: string;
  @observable public product: $Product;

  @action
  public removeById(id) {
    return this.$productService.deleteShopCartById(id);
  }

  @action
  public fetchProducts(params: any) {
    return this.$productService.queryProducts(params);
  }

  @action
  public setData(products: $Product[]) {
    this.products = products;
  }

  @action
  public pushData(products: $Product[]) {
    this.products = this.products.concat(products.map((product) => new $Product(product)));
  }

  @action
  public setLoaded(condition: boolean) {
    this.loaded = condition;
  }

  @action
  public setKeyword(keyword: string) {
    this.keyword = keyword;
  }

  @action
  public setProduct(product: $Product) {
    this.product = product;
  }
}
