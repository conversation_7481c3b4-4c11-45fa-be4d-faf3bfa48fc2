import * as React from "react";
import { observer } from "mobx-react";
import { Checkbox, DatePicker, InputItem, List } from "antd-mobile";
import { $PaymentModeType } from "../../classes/const/$payment-mode-type";
import styled from "styled-components";
import { sum } from "lodash";
import { OfflineTransferComponent } from "../offline-transfer-component/offline-transfer-component";
import { $ValidityType, ValidityTypeName } from "@classes/const/$validity-type";
const CheckboxItem = Checkbox.CheckboxItem;
const Item = List.Item;
const Brief = Item.Brief;

@observer
export class PaymentModeList extends React.Component<any, any> {

  public renderNoValueTip = (code) => {
    let tip = "";
    switch (code) {
      case $PaymentModeType.STOREDVALUE:
        tip = "暂无余额";
        break;
      case $PaymentModeType.CREDIT:
        tip = "暂无可用信用";
        break;
      case $PaymentModeType.REBATEDEDUCTION:
        tip = "暂无可用返利";
        break;
      case $PaymentModeType.DIVIDE_REBATE:
        tip = "暂无可用分账";
        break;
      default:
        break;
    }
    return tip;
  }

  public renderName = (code) => {
    let tip = "";
    switch (code) {
      case $PaymentModeType.STOREDVALUE:
        tip = "余额支付";
        break;
      case $PaymentModeType.CREDIT:
        tip = "信用支付";
        break;
      case $PaymentModeType.REBATEDEDUCTION:
        tip = "返利抵扣";
        break;
      case $PaymentModeType.BANKTRANSFER:
        tip = "线下转账";
        break;
      case $PaymentModeType.DIVIDE_REBATE:
        tip = "分账返利支付";
        break;
      default:
        break;
    }
    return tip;
  }
  public changeSelectStatus = (mode, disabled) => {
    if (!disabled) {
      this.props.changePayment(mode);
    }
  }

  public getSmallNum = (a, b) => {
    const res = a < b ? a : b;
    return res ? res : 0;
  }
  public render() {
    const { shopListInfo, paymentModeList, selectCapitalAccountInfo, date, pics, isChooseAccount, showAccountMabel, showCreditModal,
      changeOffLineTransferAmount, offLineTransferAmount, changeDate, setPics, subMerchantList, disabledDivideRebate, disabledTransfer,
      validityTypeName, validityType, disabledCredit, showCreditTips
     } = this.props;
    // console.log("paymentModeList",paymentModeList);
    const paymentLineIndex = subMerchantList && subMerchantList.length > 0 ? subMerchantList.findIndex((line) => line.checked === true) : -1;
    const { bankName, accountCode  } = selectCapitalAccountInfo;
    return (
      <PaymentList>
        {
          paymentModeList.map((mode) => {
            const isNewValidity  = validityType === $ValidityType.NEW_CREDIT; // 是否新模式
            const disabled = (mode.code !== $PaymentModeType.STOREDVALUE && mode.code !== $PaymentModeType.CREDIT && mode.code !== $PaymentModeType.DIVIDE_REBATE && mode.availableTotalAmount === 0) ||
              (mode.code === $PaymentModeType.CREDIT && mode.availableTotalAmountForNoLimitPeriod <= 0 && mode.availableTotalAmount <= 0 ) ||
              (mode.code === $PaymentModeType.STOREDVALUE && mode.availableTotalAmount < 0) ||
              (mode.code === $PaymentModeType.BANKTRANSFER && disabledDivideRebate) ||
              (mode.code === $PaymentModeType.DIVIDE_REBATE && (disabledTransfer || (mode.transferAmount <= 0 && mode.availableOrderAmount <= 0))) ||
              (mode.code === $PaymentModeType.CREDIT && disabledCredit)
            return (
              mode.code !== $PaymentModeType.ALLINPAY && <CheckboxItem
                key={mode.oid}
                onChange={() => this.changeSelectStatus(mode, disabled)}
                checked={mode.isCheck}
                disabled={disabled}
                //className={mode.isCheck ? "" : "noShow"}
              >
                {
                  ([$PaymentModeType.STOREDVALUE, $PaymentModeType.CREDIT, $PaymentModeType.REBATEDEDUCTION, $PaymentModeType.DIVIDE_REBATE].indexOf(mode.code) > -1) ?
                    <div>
                      {
                        (mode.availableTotalAmount > 0 || mode.availableTotalAmountForNoLimitPeriod > 0 || mode.transferAmount > 0 || mode.availableOrderAmount > 0) && mode.isCheck ?
                          <div style={{ display: "flex", alignItems: "center" }}>
                            <i onClick={() => this.changeSelectStatus(mode, disabled)}
                              className={(mode.code === $PaymentModeType.STOREDVALUE || mode.code === $PaymentModeType.DIVIDE_REBATE)
                                ? "scmIconfont payment-list-icon scm-icon-caiwu scm-icon-caiwu-self" : mode.code === $PaymentModeType.CREDIT
                                ? "scmIconfont payment-list-icon scm-icon-xinyongqia" : mode.code === $PaymentModeType.BANKTRANSFER
                                ? "scmIconfont payment-list-icon scm-icon-zhuanzhang" : "scmIconfont payment-list-icon scm-icon-fanli"}/>
                            <span>{mode.name}</span>
                            {
                              mode.code === $PaymentModeType.CREDIT && disabledCredit &&  <i className={"scmIconfont scm-icon-shuoming"} style={{ marginLeft: "5px", color: "rgba(48,125,205,1)" }} onClick={() => showCreditTips(true)}/>
                            }
                            {
                              mode.code === $PaymentModeType.DIVIDE_REBATE && <span className="more-text">(最多可抵扣货款￥{mode.availableOrderAmount})</span>
                            }
                            {
                              (mode.isCheck && mode.code === $PaymentModeType.CREDIT) && <span style={{ marginLeft: 'auto', marginRight: 20}}
                                  onClick={(e) => {
                                    e.stopPropagation();
                                    showCreditModal(true)
                                  }}
                                > {validityTypeName}&nbsp;<i className={"scmIconfont scm-icon-jiantou-you"} style={{ position: "relative", fontSize: 13 }}/>
                                </span>
                            }
                          </div>
                          :
                          <div >
                            <i onClick={() => this.changeSelectStatus(mode, disabled)}
                              className={(mode.code === $PaymentModeType.STOREDVALUE || mode.code === $PaymentModeType.DIVIDE_REBATE)
                              ? "scmIconfont payment-list-icon scm-icon-caiwu" : mode.code === $PaymentModeType.CREDIT
                              ? "scmIconfont payment-list-icon scm-icon-xinyongqia" : mode.code === $PaymentModeType.BANKTRANSFER
                              ? "scmIconfont payment-list-icon scm-icon-zhuanzhang" : "scmIconfont payment-list-icon scm-icon-fanli"}/>
                            <span>{mode.name}</span>
                            {
                              mode.code === $PaymentModeType.CREDIT && disabledCredit &&  <i className={"scmIconfont scm-icon-shuoming"} style={{ marginLeft: "5px", color: "rgba(48,125,205,1)" }} onClick={() => showCreditTips(true)}/>
                            }
                          </div>
                      }
                      {
                        (
                          (mode.code === $PaymentModeType.DIVIDE_REBATE && mode.transferAmount <= 0 && mode.availableOrderAmount <= 0) ||
                          (mode.code === $PaymentModeType.CREDIT && mode.availableTotalAmountForNoLimitPeriod <= 0 && mode.availableTotalAmount <= 0) ||
                          (mode.code !== $PaymentModeType.CREDIT && mode.code !== $PaymentModeType.STOREDVALUE && mode.code !== $PaymentModeType.DIVIDE_REBATE && mode.availableTotalAmount <= 0)
                        ) &&
                        <p
                          style={{
                            color: "#FF3030",
                            marginBottom: 0,
                            marginLeft: "25px",
                          }}>{this.renderNoValueTip(mode.code)}</p>
                      }
                      {
                        (
                          (mode.code === $PaymentModeType.CREDIT && (mode.availableTotalAmount > 0 || mode.availableTotalAmountForNoLimitPeriod > 0))
                          || (mode.code === $PaymentModeType.DIVIDE_REBATE && mode.availableOrderAmount > 0)
                          || (mode.code === $PaymentModeType.STOREDVALUE && mode.availableTotalAmount <= 0)
                          || (mode.code !== $PaymentModeType.DIVIDE_REBATE && mode.availableTotalAmount > 0)
                        ) &&
                         mode.isCheck &&
                        <div className={mode.showMore ? "shopinfo shopinfo-line" : "shopinfo"}>
                          预计有{shopListInfo.filter((item) => item[mode.code].isCheck).length}家门店使用{
                            mode.code === $PaymentModeType.STOREDVALUE ? "余额储值" :
                            mode.code === $PaymentModeType.DIVIDE_REBATE ? "分账返利" : mode.code === $PaymentModeType.CREDIT ? "信用" : "返利"}账户支付
                          <span className="red">￥{mode.totalAmount}</span>
                        </div>
                      }
                      {
                        (mode.availableTotalAmount > 0 || mode.availableTotalAmountForNoLimitPeriod > 0) && mode.isCheck && mode.showMore && shopListInfo && shopListInfo.length > 0 &&
                        <ShopList>
                          {
                            shopListInfo && mode.showMore && shopListInfo.map((pay) => {
                              const shopCurrentPayment = pay[mode.code];
                              console.log('shopCurrentPaymentshopCurrentPayment', shopCurrentPayment)
                              const availableTotalAmount = validityType === $ValidityType.NEW_CREDIT ? shopCurrentPayment.availableTotalAmountForNoLimitPeriod : shopCurrentPayment.availableTotalAmount;
                              console.log('availableTotalAmount', availableTotalAmount)
                              return (
                                <CheckboxItem
                                  key={pay.orgOid}
                                  className="noline"
                                  checked={shopCurrentPayment.isCheck}
                                  onChange={() => this.props.changeShop(mode, pay)}
                                  disabled={availableTotalAmount <= 0}
                                >
                                  <p>{pay.orgName}</p>
                                  {
                                    availableTotalAmount <= 0 ? <p>
                                      <span style={{ color: "#FF3030" }}>{
                                        this.renderNoValueTip(mode.code)
                                      }</span>
                                    </p> : (mode.code === $PaymentModeType.CREDIT ?
                                      <p>
                                        <span>可用额度:￥{ availableTotalAmount }</span>
                                        <span>预计支付金额:
                                          <span className="red">
                                            ￥{shopCurrentPayment.expectedAmount && Number(shopCurrentPayment.expectedAmount).toFixed(2)}
                                          </span>
                                        </span>
                                      </p>
                                      :
                                      <p>
                                        <span>账户余额:￥{shopCurrentPayment.availableTotalAmount}</span>
                                        <span>预计抵扣金额:
                                              <span className="red">
                                                ￥{shopCurrentPayment.expectedAmount && Number(shopCurrentPayment.expectedAmount).toFixed(2)}
                                              </span>
                                            </span>
                                      </p>)
                                  }
                                </CheckboxItem>
                              );
                            })
                          }
                        </ShopList>
                      }
                      {
                        (mode.availableTotalAmount > 0 || mode.availableTotalAmountForNoLimitPeriod > 0) && mode.isCheck &&
                        <ShowBtn onClick={() => this.props.showMore(mode)}>
                          {mode.showMore ? "点击收起" : "展开更多"}&nbsp;
                          <i style={{ fontSize: 13 }} className={mode.showMore ? "scmIconfont payment-list-icon scm-icon-jiantou-shang" : "scmIconfont payment-list-icon scm-icon-arrow-down"}/>
                        </ShowBtn>
                      }
                    </div> :
                      <div>
                        <div
                          onClick={() => this.changeSelectStatus(mode, disabled)}
                        >
                          <i
                            className="scmIconfont payment-list-icon scm-icon-zhuanzhang"/>
                          <span>{mode.name}</span>
                        </div>
                        {
                          mode.isCheck &&
                            <OfflineTransferComponentWrap>
                              <OfflineTransferComponent
                                isChooseAccount={isChooseAccount}
                                showAccountMabel={showAccountMabel}
                                transferAmount={offLineTransferAmount}
                                bankName={bankName}
                                accountCode={accountCode}
                                paymentDate={date}
                                changeDate={changeDate}
                                pics={pics}
                                setPics={setPics}
                                changeTransferAmount={changeOffLineTransferAmount}
                                isInput={true}
                              />
                            </OfflineTransferComponentWrap>
                          }
                      </div>
                }
              </CheckboxItem>
            );
          })
        }
      </PaymentList>
    );
  }
}

const PaymentList = styled.div`// styled
  & {
    width: 100%;
    height: auto;
    background: #fff;
    .am-list-item.am-list-item-active {
      background: #fff;
    }
    .am-list-item .am-list-line {
      padding-right: 0;
      border: none!important;
    }
    .ant-radio-group {
      display: block;
    }
    .am-checkbox {
      width: 18px;
      height: 18px;
    }
    .am-checkbox-inner {
      width: 18px;
      height: 18px;
      border-radius: unset;
    }
    .am-checkbox-input {
      width: 30%;
    }
    .payment-list-icon {
      margin-right: 5px;
      position: relative;
      top: 1px;
    }
    .more-text {
      color: #888 !important;
      margin-left: 10px;
    }
    .scm-icon-caiwu {
      color: #FF8627;
    }
    .scm-icon-xinyongqia {
      color: #1890FF;
    }
    .scm-icon-zhuanzhang {
      color: #52C41A;
    }
    .scm-icon-fanli {
      color: #FF3030;
    }
    .scm-icon-jiantou-shang, .scm-icon-arrow-down{
      font-size: 10px;
    }
    .am-list-item {
      position: relative;
      padding-left: 16px;
      :after {
        content: '';
        position: absolute;
        background-color: #D8D8D8 !important;
        display: block;
        z-index: 1;
        top: auto;
        right: auto;
        bottom: 0;
        left: 0;
        width: 100%;
        height: 1px;
        transform-origin: 50% 100%;
        transform: scaleY(0.5);
      }
    }
    .am-list-item .am-list-line .am-list-content {
      white-space: normal;
      font-size: 13px;
      font-family: "SourceHanSansCN-Normal";
      font-weight: 400;
      color: rgba(51, 51, 51, 1);
      padding-top: 0;
      padding-left: 10px;
      > div {
        > div:nth-of-type(1) {
          height: 50px;
          line-height: 54px;
          position: relative;
          padding-left: 14px;
          :after {
            content: '';
            position: absolute;
            //background-color: #D8D8D8 !important;
            display: block;
            z-index: 1;
            top: auto;
            right: auto;
            bottom: 0;
            left: 0;
            width: 100%;
            height: 1px;
            transform-origin: 50% 100%;
            transform: scaleY(0.5);
          }
          > span {
            font-size: 13px;
            font-family: "SourceHanSansCN-Normal";
            font-weight: 400;
            color: rgba(51, 51, 51, 1);
          }
        }
        > .shopinfo {
          height: 32px;
          padding: 10px 0;
          margin-right: 0px;
          font-size: 13px;
          line-height: 13px;
          margin-left: 14px;
          position: relative;
          > .red {
            color: #FF3030;
          }
      }
        >.shopinfo-line{
          :after {
            content: '';
            position: absolute;
            background-color: #D8D8D8 !important;
            display: block;
            z-index: 1;
            right: auto;
            bottom: 0;
            left: 0;
            width: 100%;
            height: 1px;
            transform-origin: 50% 100%;
            transform: scaleY(0.6);
           }
        }
      }
    }
    .am-list-item .am-list-thumb:first-child {
      margin-right: 4px !important;
      position: absolute;
      top: 16px;
    }
    .am-list-item.am-list-item-middle .am-list-line {
      margin-left: 0px;
    }
    /* .am-checkbox-wrapper{
      .am-checkbox-inner{
        border-radius: 0;
        width: 18px;
        height: 18px;
        :after{
          top: 1px;
        }
      }
    } */
    /* .noShow:after {
      display: none !important;
    }
    .noShow .am-list-line:after {
      display: none !important;
    }
    .noShow .am-list-line .am-list-content div div:after {
      display: none !important;
    } */
  }
`;

const ShopList = styled.div`// styled
  & {
    position: relative;
    .noline .am-list-line:after {
      display: none !important;
    }
    .noline .am-list-line .am-list-content {
      padding-top: 13px !important;
      padding-left: 30px;
      > p {
        margin-bottom: 8px;
      }
      > p:nth-of-type(2) {
        margin-right: 8px;
        > span {
          display: inline-block;
          width: auto;
        }
        >span:first-of-type{
          margin-right: 8px;
        }
      }
    }
    .am-list-item .am-list-thumb:first-child {
      top: 9px;
    }
    .noline {
      padding-left: 0px;
      :after {
        display: none !important;
      }
      > .am-list-thumb {
        left: 3px;
      }
    }
    .red {
      color: #FF3030;
    }
  }
`;

const ShowBtn = styled.div`// styled
  & {
    float: right;
    margin-right: 16px;
    height: 24px;
    font-size: 13px;
    line-height: 13px;
    font-family: "SourceHanSansCN-Normal";
    font-weight: 400;
    color: rgba(117, 117, 117, 1);
  }
`;

const OfflineTransferComponentWrap = styled.div`// styled
  & {
    width: 100%;
    overflow: hidden;
    padding-left: 14px;
    .am-list-item.am-input-item.am-list-item-middle:after{
      display: none;
    }
    .am-list-item {
      display: inline-block;
    }
    .am-list-item.am-list-item-middle .am-list-line{
      margin-left: 0;
    }
  }
`;
