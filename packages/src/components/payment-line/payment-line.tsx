import { observer } from "mobx-react";
import React from "react";
import styled from "styled-components";

@observer
export default class PaymentLine extends React.Component<any, any> {
  public changePaymentLine = (item) => {
    this.props.onChange(item);
  }
  public render() {
    const {subMerchantList, goBack} = this.props;
    return (
      <PaymentLineWrapper>
        <ModelWrapper>
          <ModelHeader>
            <i className={"scmIconfont scm-icon-jiantou-you"} onClick={goBack}/>
            选择付款线路
          </ModelHeader>
          <ModelContent>
            {
              subMerchantList.length > 0 &&
              subMerchantList.map((item) => (
                <div
                  key={item.code}
                  className={item.checked ? "item active" : "item"}
                  onClick={() => this.changePaymentLine(item)}
                >
                  {item.name}
                </div>
              ))
            }
          </ModelContent>
        </ModelWrapper>
      </PaymentLineWrapper>
    );
  }
}

const PaymentLineWrapper = styled.div`// styled
  & {
    width: 100%;
    height: ${document.documentElement.clientHeight}px;
    background:rgba(0,0,0,0.5);
    padding: 105px 52px 0px 52px;
    position: fixed;
    top: 0;
    z-index: 1000;
  }
`;
const ModelWrapper = styled.div`// styled
  & {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 80%;
    max-height: 450px;
    background: #fff;
    border-radius: 16px;
    padding: 0 21px 21px 21px;
  }
`;

const ModelHeader = styled.div`// styled
  & {
    width: 100%;
    height: 64px;
    line-height: 64px;
    text-align: center;
    position: relative;
    > .scmIconfont.scm-icon-jiantou-you {
      position: absolute;
      left: 0;
      top: 0;
      -webkit-transform: rotate(180deg);
      -moz-transform: rotate(180deg);
      -o-transform: rotate(180deg);
      -ms-transform: rotate(180deg);
      transform: rotate(180deg);
    }
  }
`;

const ModelContent = styled.div`// styled
  & {
    width: 100%;
    padding: 0 10px;
    max-height: 368px;
    overflow-y: auto;
    .item {
      border-radius: 4px;
      border: 1px solid rgba(221,221,221,1);
      color: #303030;
      font-size: 13px;
      margin-bottom: 10px;
      min-height: 36px;
      line-height: 36px;
      padding-left: 14px;
    }
    .active {
      border: 1px solid rgba(48,125,205,1);
      color: #307DCD;
    }
  }
`;
