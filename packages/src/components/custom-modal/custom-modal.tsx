import * as React from "react";
import { observer } from "mobx-react";
import styled from "styled-components";

@observer
export class CustomModal extends React.Component<any, any> {

  public render() {
    const { header, content, confirmName, confirm, visible, close, isCancle } = this.props;
    return (
      <CustomModalWrapper style={{ display: visible ? "block" : "none" }}>
        <ModalInfo>
          <ModalHeader>{header}</ModalHeader>
          <ModalContent>{content}</ModalContent>
          <ModalFooter>
            {
              isCancle && <span onClick={close} className={"cancle"}>取消</span>
            }
            <span
              className={"confirm"}
              onClick={confirm}
              style={{ width: isCancle ? "calc(100% /2)" : "100%" }}
            >
              {confirmName}
              </span>
          </ModalFooter>
        </ModalInfo>
      </CustomModalWrapper>
    );
  }
}

const CustomModalWrapper = styled.div`// styled
  & {
    width: 100%;
    height: 100%;
    overflow: hidden;
    background: rgba(0, 0, 0, 0.5);
    padding: 0 18px;
    position: fixed;
    top: 0;
    bottom: 0;
    z-index: 999;
  }
`;

const ModalInfo = styled.div`// styled
  & {
    width: 100%;
    max-height: 375px;
    //margin-top: calc((100% - 375px) / 2);
    margin-top: 144px;
    border-radius: 16px;
    background: #fff;
  }
`;

const ModalHeader = styled.div`// styled
  & {
    width: 100%;
    height: 48px;
    background: #F2F2F2;
    text-align: center;
    font-size: 16px;
    font-family: SourceHanSansCN-Regular, SourceHanSansCN;
    font-weight: 400;
    color: rgba(47, 47, 47, 1);
    border-radius: 16px 16px 0px 0px;
    line-height: 48px;
  }
`;

const ModalContent = styled.div`// styled
  & {
    width: 100%;
    max-height: calc(375px - 48px - 50px);
    overflow-y: scroll;
    padding: 25px;
  }
`;

const ModalFooter = styled.div`// styled
  & {
    width: 100%;
    height: 50px;
    border-top: 1px solid #DDDDDD;
    line-height: 50px;
    text-align: center;
    > span {
      display: inline-block;
      font-size: 16px;
      font-family: SourceHanSansCN-Regular, SourceHanSansCN;
      font-weight: 400;
      color: rgba(89, 89, 89, 1);
    }
    .confirm {
      color: #307DCD;
    }
    .cancle {
      border-right: 1px solid #DDDDDD;
      width: calc(100% / 2);
    }
  }
`;
