import * as React from "react";
import { observer } from "mobx-react";
import styled from "styled-components";

@observer
export class NewCustomModal extends React.Component<any, any> {

  public render() {
    const { header, content, confirmName, confirm, visible, close, hideCancel, hideConfirm, style } = this.props;
    return (
      <CustomModalWrapper style={{ display: visible ? "block" : "none" }}>
        <ModalInfo style={style}>
          <ModalHeader>
            {header}
            <i onClick={close} style={{marginLeft: 126, position: "absolute"}} className="scmIconfont scm-icon-guanbi" />
          </ModalHeader>
          <ModalContent>{content}</ModalContent>
          <ModalFooter>
            {
              !hideCancel && <div onClick={close} className={"cancel"}>取消</div>
            }
            {
              !hideConfirm && <div
                className={"confirm"}
                onClick={confirm}
                style={{ width: !hideCancel ? "calc(100% /2)" : "100%" }}
              >
                {confirmName}
              </div>
            }
          </ModalFooter>
        </ModalInfo>
      </CustomModalWrapper>
    );
  }
}

const CustomModalWrapper = styled.div`// styled
  & {
    width: 100%;
    height: 100%;
    overflow: hidden;
    background: rgba(0, 0, 0, 0.5);
    // padding: 0 18px;
    position: fixed;
    top: 0;
    bottom: 0;
    z-index: 999;
  }
`;

const ModalInfo = styled.div`// styled
  & {
    width: 100%;
    position: absolute;
    bottom: 0%;
    border-radius: 8px;
    background: #fff;
    padding-bottom: 26px;
  }
`;

const ModalHeader = styled.div`// styled
  & {
    width: 100%;
    height: 60px;
    text-align: center;
    font-size: 16px;
    font-family: SourceHanSansCN-Regular, SourceHanSansCN;
    font-weight: bold;
    color: #333;
    border-radius: 8px 8px 0px 0px;
    line-height: 60px;
  }
`;

const ModalContent = styled.div`// styled
  & {
    width: 100%;
    max-height: calc(500px - 60px - 60px);
    overflow-y: scroll;
    padding: 0 20px;
  }
`;

const ModalFooter = styled.div`// styled
  & {
    display: flex;
    padding: 24px;
    justify-content: space-between;
    align-items: center;
    width: 100%;
    height: 60px;
    gap: 27px;
    > div {
      flex: 1;
      height: 38px;
      text-align: center;
      line-height: 38px;
      font-size: 16px;
      font-family: SourceHanSansCN-Regular, SourceHanSansCN;
      font-weight: 400;
      border-radius: 20px;
      color: rgba(89, 89, 89, 1);
    }
    .confirm {
      background: #437DF0;
      color: #fff;
    }
    .cancel {
      background: #F2F2F2;
      color: #666666;
    }
  }
`;
