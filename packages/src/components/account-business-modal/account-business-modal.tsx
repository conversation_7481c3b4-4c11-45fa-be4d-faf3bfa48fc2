import { observer } from "mobx-react";
import { autowired } from "@classes/ioc/ioc";
import React from "react";
import styled from "styled-components";
import { PickerView } from "antd-mobile";
import { $AccountInfoMv } from "../../modules/new-account-info/new-account-info-mv";
import { $AccountType, $GiftAccountTabType } from "@classes/const/$account-type";
import { TOTAL_GIFT_LIST, AVAILABLE_GIFT_LIST, REBATE_LIST, GIFT_LIMIT_LIST, GIFT_UPPER_LIMIT_LIST } from "@classes/const/$account-business-type";
const IconSelected = require("../svg/icon_selected.svg");

interface IAccountBusinessModal {
  visible: boolean;
  accountTypeCode?: string;
  giftAccountType?: $GiftAccountTabType;
  businessType?: string[];
  showLimit?: boolean;
  onCancel: () => void;
  onConfirm: (startDate?: Date, endDate?: Date) => void;
}

@observer
export class AccountBusinessModal extends React.Component<IAccountBusinessModal, any> {

  @autowired($AccountInfoMv)
  public $myMv: $AccountInfoMv;

  public constructor(props) {
    super(props);
    this.state = {
      businessType: []
    };
  }

  public componentDidMount(): void {
    if (this.props.businessType) {
      this.setState({
        businessType: this.props.businessType,
      });
    }
  }

  public componentDidUpdate(prevProps: Readonly<IAccountBusinessModal>, prevState: Readonly<any>, snapshot?: any) {
    if (prevProps.giftAccountType !== this.props.giftAccountType) {
      this.setState({
        businessType: [],
      });
    }
  }

  public onChangeBusinessType = (val) => {
    this.setState({ businessType: val })
  }

  public onConfirm = () => {
    const { businessType } = this.state;
    const { onCancel, onConfirm, accountTypeCode } = this.props;
    onCancel();
    if (accountTypeCode === $AccountType.RETURN_FREE_DISTRIBUTION) {
      const result = [];
      businessType.map(v => {
        const type = v.replace("_ED", "");
        if (!result.includes(type)) {
          result.push(type);
        }
      });
      onConfirm(result);
    } else {
      onConfirm(businessType);
    }
  }

  public onChangeGiftBusinessType = (val) => {
    if (this.state.businessType.includes(val)) {
      this.setState({
        businessType: this.state.businessType.filter(v => v !== val),
      });
    } else {
      this.setState({
        businessType: [...this.state.businessType, val],
      });
    }
  }

  public renderGiftItem = (item) => {
    const active = this.state.businessType.includes(item.value);
    return <div key={item.value} className={"item" + (active ? " active" : "")} onClick={() => this.onChangeGiftBusinessType(item.value)}>
      {item.label}
      <IconSelected/>
    </div>;
  }

  public render() {
    const { visible, onCancel, accountTypeCode, showLimit, giftAccountType } = this.props;
    const { businessType } = this.state;
    console.log("businessType", businessType)
    return (
      <Wapper>
        {
          visible && <div className={"model"}>
            <div className={"model-bg"} onClick={() => onCancel()}/>
            <div
              className={"date-content" + (accountTypeCode === $AccountType.RETURN_FREE_DISTRIBUTION ? " gift" : "")}
              onClick={(e) => {e.preventDefault()}}
            >
              <div className={"date-content-top"}>
                <div className="date-tab">
                  <div>业务类型</div>
                </div>
                <i onClick={() => onCancel()} className="scmIconfont scm-icon-guanbi"/>
              </div>
              <div className={"date-content-center"}>
                {
                  accountTypeCode === $AccountType.DEDUCTION_LIMIT &&
                  <PickerView
                    data={REBATE_LIST}
                    value={businessType}
                    cascade={false}
                    onChange={(val) => this.onChangeBusinessType(val)}
                  />
                }
                {
                  accountTypeCode === $AccountType.RETURN_FREE_DISTRIBUTION &&
                  <div className="gift-content">
                    {
                      [$GiftAccountTabType.ALL, $GiftAccountTabType.PZED].includes(giftAccountType) &&
                      <>
                        <div className="title">配赠额度</div>
                        <div className="list">
                          {
                            GIFT_LIMIT_LIST.map(v => this.renderGiftItem(v))
                          }
                        </div>
                      </>
                    }
                    {
                      [$GiftAccountTabType.ALL, $GiftAccountTabType.HFPZ].includes(giftAccountType) &&
                      <>
                        <div className="title">配赠上限</div>
                        <div className="list">
                          {
                            GIFT_UPPER_LIMIT_LIST.map(v => this.renderGiftItem(v))
                          }
                        </div>
                      </>
                    }
                  </div>
                }
              </div>
              <div className="date-confirm" onClick={this.onConfirm}>确定</div>
            </div>
          </div>
        }
      </Wapper>
    );
  }
}


const Wapper = styled.div`// styled

  & {
    .model {
      position: fixed;
      top: 0;
      left: 0;
      bottom: 0;
      right: 0;
      z-index: 999;

      .model-bg {
        position: fixed;
        top: 0;
        left: 0;
        bottom: 0;
        right: 0;
        background-color: rgba(0, 0, 0, 0.4);
      }

      .date-content {
        position: absolute;
        height: 375px;
        bottom: 0;
        left: 0;
        right: 0;
        background: #FFFFFF;
        &.gift{
          height: 400px;
        }

        .date-confirm {
          color: #FFFFFF;
          background: #437DF0;
          border-radius: 22.5px;
          padding: 8px 12px;
          display: flex;
          align-items: center;
          justify-content: center;
          position: absolute;
          bottom: 20px;
          width: 80vw;
          margin-inline-start: 10vw;
        }
      }

      .date-content-top {
        width: 100%;
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 12px 16px;

        .date-tab {
          display: flex;
          justify-content: center;
          width: 100%;
          margin-left: 20px;

          > div {
            margin: 0 10px;
            color: #333333;
            font-size: 15px;
            font-weight: 500;
          }
        }

        > img {
          width: 20px;
          height: 20px;
        }
      }

      .date-content-center {
        padding: 10px 24px;
        .gift-content{
          font-size: 14px;
          line-height: 22px;
          .title{
            font-weight: 500;
            color: #333333;
            margin-bottom: 10px;
          }
          .list{
            text-align: center;
            color: #666666;
            display: flex;
            flex-wrap: wrap;
            margin-bottom: 20px;
            .item{
              width: calc(25% - 10px);
              border-radius: 2px;
              background: #F8F8F8;
              padding: 5px 0;
              margin-bottom: 10px;
              margin-right: 10px;
              position: relative;
              border: 1px solid #F8F8F8;
              &.active{
                border: 1px solid #437DF0;
                svg{
                  display: block;
                }
              }
              svg{
                position: absolute;
                right: 0;
                bottom: 0;
                display: none;
              }
            }
          }
        }
      }

      .date-content-input {
        display: flex;
        align-items: center;

        > span {
          width: 30px;
          text-align: center;
          display: inline-block;
        }

        .onFocusInput-item {
          height: 40px !important;
          line-height: 0px;
          background: #F7FAFF;
          border-radius: 4px;
          border: 1px solid #437DF0;
          padding-left: 15px;
          color: #437DF0;
          width: 150px;

          .am-list-line {
            position: relative;
            display: flex;
            flex: 1;
            align-self: stretch;
            padding-right: 15px;
            overflow: hidden;
          }

          input {
            font-size: 14px;
            appearance: none;
            width: 100%;
            padding: 6px 0;
            border: 0;
            background-color: transparent;
            line-height: 2;
            box-sizing: border-box;
          }
        }

        .am-list-item {
          height: 32px !important;
          line-height: 0px;
          background: #FFFFFF;
          border-radius: 4px;
          border: 1px solid #D8D8D8;
          width: 150px;

          input {
            font-size: 14px;
          }
        }
      }

      .am-picker {
        left: 0;
        width: 100%;
      }
    }
  }

  .am-calendar .header {
    height: 40px;
    line-height: 40px;
    margin-bottom: 10px;
  }

  .am-calendar .header .right {
    display: inline-block;
    width: 56px;
    height: 22px;
    right: 12px;
    top: 14px;
    font-size: 12px;
    font-family: PingFangSC-Regular, PingFang SC;
    font-weight: 400;
    color: #437DF0;
    border: 1px solid #437DF0;
    border-radius: 11px;
    text-align: center;
    line-height: 22px;
  }

  .am-calendar .header .left {
    top: 13px;
    color: #666;
  }

  .am-calendar .content {
    top: 180px;
    border-radius: 20px 20px 0 0;
    height: calc(100% - 190px);
  }

  .am-calendar .date-picker {
    padding-bottom: 0;
  }

  .am-calendar .single-month .month-title {
    font-size: 14px;
    font-family: PingFangSC-Medium, PingFang SC;
    font-weight: 500;
    color: #333333;
  }

  .am-calendar .confirm-panel {
    background-color: #fff;
    border-top: #eee 1px solid;

    .button {
      width: 180px;
      border-radius: 20px;
      background-color: #437DF0;
    }

    .button-disable {
      background-color: #ddd !important;
    }
  }

  .am-calendar .week-panel .cell {
    font-size: 12px;
    font-family: PingFangSC-Medium, PingFang SC;
    font-weight: 500;
    color: #333333;
  }

  .am-calendar .single-month .row .cell .date-wrapper .date-selected, .am-calendar .single-month .row .cell .date-wrapper .date {
    font-size: 12px;
    font-family: PingFangSC-Regular, PingFang SC;
    font-weight: 400;
    color: #606266;
  }

  .am-calendar .single-month .row .cell .info {
    height: 0;
  }

  .am-calendar .confirm-panel .info p {
    font-size: 12px;
    font-family: PingFangSC-Medium, PingFang SC;
    font-weight: 500;
    color: #333333;
  }

  .am-calendar .single-month .row .cell .date-wrapper .date-selected {
    background-color: #E9F0FF;
  }

`;
