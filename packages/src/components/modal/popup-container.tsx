import cls from "classnames";
import * as React from "react";
import "./index.less";
import { PopupHeader } from "./popup-header";

interface IPopupContainerProps {
  className?: string;
  children: React.ReactNode;
  title: React.ReactNode;
  onClose?: (e) => void;
}

export default class PopupContainer extends React.Component<IPopupContainerProps, any> {

  public render() {
    const {
      children,
      title,
      className,
      onClose,
    } = this.props;
    return (<div
      className={cls("popup-container", className)}
    >
      <PopupHeader onClose={onClose}>{title}</PopupHeader>
      {children}
    </div>);
  }
}
