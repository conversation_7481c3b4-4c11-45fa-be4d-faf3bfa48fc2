import cls from "classnames";
import React from "react";
import { useImage } from "../../hooks";
import "./index.less";

interface IPopupHeaderProps {
  className?: string;
  children: React.ReactNode;
  onClose?: (e) => void;
}

export function PopupHeader(props: IPopupHeaderProps) {
  const {
    className,
    children,
    onClose,
  } = props;
  return (<div
    className={cls("popup-header", className)}
  >
    {children}
    <img
      className={"icon-close"}
      onClick={onClose}
      src={useImage("icon-close-circular")}
    />
  </div>);
}
