import React from "react";
import styled from "styled-components";
import { PickerView } from 'antd-mobile';

interface DatePickerProps {
  visible: boolean;
  businessType?: string;
  onCancel: () => void;
  onConfirm: (statusType?: string) => void;
}

const statusList = [
  { label: "全部", value: "" },
  { label: "未结算", value: "N" },
  { label: "已结算", value: "Y" }, 
]

export class AccountStatusModal extends React.Component<DatePickerProps, any> {
  
  public constructor(props) {
    super(props);
    this.state = {
      statusType: ""
    };
  }

  public componentDidMount(): void {}

  public onChangeBusinessType = (val) => {
    this.setState({ statusType: val })
  }

  public onConfirm = () => {
    const { statusType } = this.state;
    const { onCancel, onConfirm } = this.props;
    onCancel();
    onConfirm(statusType);
  }

  public render() {
    const { visible, onCancel } = this.props;
    const { statusType } = this.state;
    return (
      <Wapper>
        {
          visible && <div className={"model"} >
            <div className={"model-bg"} onClick={() => onCancel()} />
            <div className={"date-content"} onClick={(e) => { e.preventDefault() }}>
              <div className={"date-content-top"}>
                <div className="date-tab">
                  <div>状态</div>  
                </div>
                <i onClick={() => onCancel()} className="scmIconfont scm-icon-guanbi" />
              </div>
              <div className={"date-content-center"}>
                <PickerView
                  data={statusList}
                  value={statusType}
                  cascade={false}
                  onChange={(val) => this.onChangeBusinessType(val)}
                />
              </div>
              <div className="date-confirm" onClick={this.onConfirm}>确定</div>
            </div>
          </div>
        }
      </Wapper>
    );
  }
}


const Wapper = styled.div`// styled
  & {
    .model{
      position: fixed;
      top: 0;
      left: 0;
      bottom: 0;
      right: 0;
      z-index: 999;

      .model-bg {
        position: fixed;
        top: 0;
        left: 0;
        bottom: 0;
        right: 0;
        background-color: rgba(0, 0, 0, 0.4);
      }

      .date-content{
        position: absolute;
        height: 375px;
        bottom: 0;
        left: 0;
        right: 0;
        background: #FFFFFF;

        .date-confirm {
          color: #FFFFFF;
          background: #437DF0;
          border-radius: 22.5px;
          padding: 8px 12px;
          display: flex;
          align-items: center;
          justify-content: center;
          position: absolute;
          bottom: 20px;
          width: 80vw;
          margin-inline-start: 10vw;
        }
      }
        .date-content-top {
          width: 100%;
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding:12px 16px;

          .date-tab {
            display: flex;
            justify-content: center;
            width: 100%;
            margin-left: 20px;

            > div {
              margin: 0 10px;
              color: #333333;
              font-size: 15px;
              font-weight: 500;
            }
          }

          > img {
            width: 20px;
            height: 20px;
          }
        }

        .date-content-center{
          padding: 10px 24px;
        }

        .date-content-input{
          display: flex;
          align-items: center;
          >span{
            width: 30px;
            text-align: center;
            display: inline-block;
          }
          .onFocusInput-item {
            height: 40px !important;
            line-height: 0px;
            background: #F7FAFF;
            border-radius: 4px;
            border: 1px solid #437DF0;
            padding-left: 15px;
            color: #437DF0;
            width: 150px;

            .am-list-line {
              position: relative;
              display: flex;
              flex: 1;
              align-self: stretch;
              padding-right: 15px;
              overflow: hidden;
            }

            input{
              font-size: 14px;
              appearance: none;
              width: 100%;
              padding: 6px 0;
              border: 0;
              background-color: transparent;
              line-height: 2;
              box-sizing: border-box;
            }
          }
          .am-list-item {
            height: 32px !important;
            line-height: 0px;
            background: #FFFFFF;
            border-radius: 4px;
            border: 1px solid #D8D8D8;
            width: 150px;
            input{
              font-size: 14px;
            }
          }
        }
        .am-picker{
          left: 0;
          width: 100%;
        }
      }
    }
    
`;
