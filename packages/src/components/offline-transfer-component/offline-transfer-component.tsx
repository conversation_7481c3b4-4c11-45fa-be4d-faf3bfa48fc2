import * as React from "react";
import { observer } from "mobx-react";
import styled from "styled-components";
import { RecognitionEnvironmentComponent } from "../recognition-environment-component/recognition-environment-component";
import { DatePicker, List } from "antd-mobile";
import DateUtils from "../../classes/utils/DateUtils";
import { InputItemComponent } from "../input-item-component/input-item-component";

@observer
export class OfflineTransferComponent extends React.Component<any, any> {
  public render() {
    const { pics, isWxUploadingPicture, isChooseAccount, showAccountMabel, transferAmount, bankName, accountCode, paymentDate, changeDate, setPics, isInput, changeTransferAmount } = this.props;
    return (
      <OfflineTransferComponentPage className="offline-transfer-component">
        <div className="offline-transfer-item acount transferAmount">
          <span>转账金额</span>
          <div className="payment-number">
            {
              isInput ? <InputItemComponent
                type="money"
                value={transferAmount}
                clear={true}
                onChange={changeTransferAmount}
                label={null}
                placeholder={"请输入转账金额"}
              /> : <span>{transferAmount ? "¥" + transferAmount : transferAmount}</span>
            }
          </div>
        </div>
        <div className="offline-transfer-item" onClick={showAccountMabel}>
          <span>收款账户</span>
          <span
            className="payment-account"
          >{isChooseAccount ? "请选择收款账户" : `${bankName} （尾号${accountCode})`}</span>
          <i className={"scmIconfont scm-icon-jiantou-you"}/>
        </div>
        {/*{*/}
        {/*  isChooseAccount ?*/}
        {/*    <div onClick={showAccountMabel}>*/}
        {/*      <span>收款账户</span>*/}
        {/*      <i className={"scmIconfont scm-icon-jiantou-you"}/>*/}
        {/*      <span style={{ float: "right", color: "#2f2f2f" }}>请选择收款账户</span>*/}
        {/*    </div>*/}
        {/*    :*/}
        {/*    <div onClick={showAccountMabel}>*/}
        {/*      <span>收款账户</span>*/}
        {/*      <i className={"scmIconfont scm-icon-jiantou-you"}/>*/}
        {/*      <span style={{ color: "#2f2f2f", float: "right" }}>{bankName}（尾号{accountCode})</span>*/}
        {/*    </div>*/}
        {/*}*/}
        <div className={"offline-transfer-item date"}>
          <List className="date-picker-list">
            <DatePicker
              value={paymentDate}
              onChange={changeDate}
              maxDate={new Date()}
              extra={paymentDate ? DateUtils.toStringFormat(paymentDate, "yyyy-MM-dd HH:mm") : "请选择付款日期"}
            >
              <List.Item arrow="horizontal">
                付款日期</List.Item>
              {/*<span*/}
              {/*style={{ color: "#2f2f2f" }}>{paymentDate ? DateUtils.toStringFormat(paymentDate, "yyyy-MM-dd HH:mm") : "请选择付款日期"}*/}
              {/*<i className={"scmIconfont scm-icon-jiantou-you"}/>*/}
              {/*</span>*/}
            </DatePicker>
          </List>
        </div>
        <PaymentVoucher>
          <RecognitionEnvironmentComponent
            style={{ padding: "0 5px 10px 5px" }}
            pics={pics}
            setPics={setPics}
            limitLength={2}
            picsStyle={{ marginBottom: "10px" }}
            isWxUploadingPicture={isWxUploadingPicture}
          />
          <span style={{ display: "inline-block", width: "104px", whiteSpace: "initial", lineHeight: "initial" }}
                className={"paymentVoucherTip"}>
            建议上传付款凭证
            （如汇款单）
          </span>
        </PaymentVoucher>
      </OfflineTransferComponentPage>
    );
  }
}

const OfflineTransferComponentPage = styled.div`// styled
  & {
    padding-left: 20px;
    > .offline-transfer-item {
      width: 100%;
      height: 40px;
      line-height: 40px;
      border-bottom: 1px solid #D8D8D8;
      position: relative;
      display: flex;
      padding-right: 38px;
      > span:first-of-type{
        padding-right: 5px;
      }
      > span:last-child {
        color: #2F2F2F;
      }
      .payment-account, .payment-number{
        flex: 1;
        display: inline-block;
        text-align: right;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
      > i {
        color: #999999;
        position: absolute;
        top: 50%;
        right: 15px;
        transform: translateY(-50%);
      }
    }
    .acount {
      padding-right: 15px;
    }
    .date {
      padding-right: 0px;
      > div {
        display: inline-block;
        float: right;
      }
      .am-list-item .am-list-line .am-list-arrow {
        margin-left: 1px;
      }
    }
    > div:last-child {
      height: auto;
      border: none;
    }
    .am-image-picker-list {
      padding: 0;
      margin-bottom: 0;
    }
    .am-image-picker-list .am-image-picker-item {
      width: 47px;
      height: 47px;
    }
    input::placeholder {
      font-size: 13px;
    }
    .am-list-item {
      width: 100%;
      min-height: 28px !important;
      height: 28px !important;
      float: right;
    }
    .am-input-control {
      line-height: 28px;
      font-size: 13px !important;
      position: relative;
      top: 5px;
    }
    .am-list-item .am-input-control input {
      font-size: 13px;
      text-align: right;
      padding-right: 20px;
    }
    .am-list-item .am-input-clear {
      width: 14px;
      height: 14px;
      background-size: 14px auto;
      background-position: 2px 2px;
      margin-top: 10px;
      margin-left: 5px;
      margin-right: 0px;
    }
    .am-list-item.am-list-item-middle:after, .offline-transfer-item.date:after{
      visibility: hidden;
    }
    .date-picker-list {
      width: 100%;
      height: 100%;
      .am-list-body::before {
        display: none;
      }
      .am-list-item-middle {
        width: 100%;
        height: 39px !important;
        padding-left: 0;
        padding-right: 15px;
      }
      .am-list-extra {
        font-size: 13px !important;
        font-family: SourceHanSansCN-Normal, SourceHanSansCN;
        font-weight: 400;
        color: #2f2f2f !important;
      }
    }
    > div .am-list-item .am-list-line {
      display: flex;
      height: 100%;
      .am-list-content, .am-list-extra {
        margin-top: 0px;
        height: 100%;
        padding: 0;
        line-height: 40px;
      }
    }
    .date-picker-list .am-list-item .am-list-line .am-list-extra {
      flex: 1;
      text-overflow: ellipsis;
      overflow: hidden;
      white-space: nowrap;
    }
    .am-list-item .am-input-control .fake-input-container .fake-input.focus:after {
      float: right !important;
      top: 28% !important;
      height: 70%;
    }
    .am-list-item .am-input-control .fake-input-container .fake-input-placeholder {
      text-align: right !important;
    }
    .am-list-item .am-input-control .fake-input-container-left .fake-input {
      text-align: right !important;
      font-size: 13px !important;
      font-family: SourceHanSansCN-Normal, SourceHanSansCN;
      font-weight: 400;
      color: rgba(51, 51, 51, 1);
    }
    .am-list-body div:not(:last-child) .am-list-line::after {
      display: none !important;
    }
    .am-list-line::after {
      display: none !important;
    }
    .transferAmount {
      .am-list-item .am-list-line {
        padding-right: 15px !important;
      }
    }
  }
`;

const PaymentVoucher = styled.div`// styled
  & {
    width: 80%;
    height: auto;
    background: #fff;
    position: relative;
    padding-right: 0;
    .am-progress-outer {
      position: absolute;
      top: 56%;
      left: 2%;
      width: 40px;
      border-radius: 5px;
    }

    .am-progress-bar {
      border: 2px solid #307dcd;
      border-radius: 5px;
    }
    > span:last-child {
      position: absolute;
      top: 10px;
      left: 60px;
    }
    .am-image-picker-list .am-image-picker-item .am-image-picker-item-remove {
      top: 0;
      right: 0;
    }
    .am-image-picker-list .am-flexbox .am-flexbox-item {
      width: 48px;
      height: 48px;
      margin-top: 12px;
    }
    .am-flexbox .am-flexbox-item {
      flex: unset;
    }
  }
`;
