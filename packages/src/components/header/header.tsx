import { observer } from "mobx-react";
import React from "react";
import { withRouter } from "react-router";
import styled from "styled-components";

declare let window: any;
declare let require: any;

@withRouter
@observer
export class Footer extends React.Component<any, any> {

  public render() {
    const { title } = this.props;
    return (
      <Header>
        <Title>{title}</Title>
      </Header>
    );
  }
}

const Title = styled.h1`// styled
  & {
    font-size: 18px;
    color:#fff;
  }
`;
const Header = styled.header`// styled
  & {
    background: #ddd;
  }
`;
