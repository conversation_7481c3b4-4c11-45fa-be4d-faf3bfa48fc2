import * as React from "react";
import { observer } from "mobx-react";
import { $CartType } from "../../classes/const/$cart-type";
import styled from "styled-components";
import { Checkbox, List } from "antd-mobile";
import { NoGoods } from "../no-goods/no-goods";

const CheckboxItem = Checkbox.CheckboxItem;

@observer
export class CapitalAccountListMobel extends React.Component<any, any> {

  public render() {
    const { capitalAccountList, isShowMabel } = this.props;
    return (
      <Mabel style={{ display: isShowMabel ? "block" : "none" }}>
        {
          capitalAccountList ? capitalAccountList.length > 0 ?
            capitalAccountList.map((capitalAccount, index) => {
              return (
                <div key={index}>
                  {
                    capitalAccount.isDefault === $CartType.ISDEFAULE ?
                      <div>
                        <List className="capitalAccountDefault">
                          <CheckboxItem
                            onChange={(v) => this.props.setDefaultCheck(v, index)}
                            checked={capitalAccount.isDefault === $CartType.ISDEFAULE ? true : false}
                          >
                            <p>
                              <span>{capitalAccount.bankName}</span>
                              <span>默认</span>
                            </p>
                            <p>
                              {capitalAccount.bankAccountName ? capitalAccount.bankAccountName.length > 6 ? capitalAccount.bankAccountName.slice(0, 6) : capitalAccount.bankAccountName : null} {capitalAccount.accountCode}
                            </p>
                          </CheckboxItem>
                        </List>
                        <img className="border-img"
                             src="https://order.fwh1988.cn:14501/static-img/scm/ico-colorline.png"
                             alt=""/>
                      </div>
                      : <List className="capitalAccount" key={index}>
                        <CheckboxItem
                          onChange={(v) => this.props.setDefaultCheck(v, index)}
                          checked={capitalAccount.isDefault === $CartType.ISDEFAULE ? true : false}
                        >
                          <p>
                            <span>{capitalAccount.bankName}</span>
                          </p>
                          <p>
                            {capitalAccount.bankAccountName ? capitalAccount.bankAccountName.length > 6 ? capitalAccount.bankAccountName.slice(0, 6) : capitalAccount.bankAccountName : null} {capitalAccount.accountCode}
                          </p>
                        </CheckboxItem>
                      </List>
                  }
                </div>
              );
            })
            : <NoGoods title="暂无收款账户"/> : <NoGoods title="暂无收款账户"/>
        }
      </Mabel>
    );
  }
}

const Mabel = styled.div`// styled
  & {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: #F2F2F2;
    z-index: 100;
    overflow-y: auto;
    overflow-x: hidden;
    > div div img {
      margin-top: -22px;
    }
    .am-list .am-list-item.am-checkbox-item .am-list-thumb .am-checkbox {
      height: 98px !important;
    }
    .capitalAccountDefault .am-list-content p:first-child span:last-child {
      display: inline-block;
      width: 28px;
      height: 16px;
      background: #ECF6FF;
      color: #307DCD;
      text-align: center;
      font-size: 10px;
      margin-left: 10px;
    }
    .capitalAccount {
      margin-bottom: 10px;
    }
  }
`;
