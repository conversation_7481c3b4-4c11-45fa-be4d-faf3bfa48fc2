import * as React from "react";
import { observer } from "mobx-react";
import DateUtils from "../../classes/utils/DateUtils";
import styled from "styled-components";
import { autowired } from "@classes/ioc/ioc";
import disabledOprate from "../../components/noWX-disabled-operate";
import { $AppStore } from "@classes/stores/app-store-mv";
import { Checkbox} from "antd-mobile";
const CheckboxItem = Checkbox.CheckboxItem;
@observer
export class SingleExpenseOrderInfo extends React.Component<any, any> {
  @autowired($AppStore)
  public $AppStore: $AppStore;

  public constructor(props) {
    super(props);
    this.state = {
      scanningLogin: localStorage.getItem("scanningLogin") === "Y",
    };
  }

  public paymentFinish = (e, docId, canPartialPay) => {
    e.preventDefault();
    e.stopPropagation();
    this.props.orderButton(docId, canPartialPay);
  }
  public render() {
    const { scanningLogin } = this.state;
    const { order, isShowAmount, isHideAmountList, isHideDocStatus,showCheckItem,checkOne,index } = this.props;
    console.log(order);
    return (
      <Order>
        <div className="order-info">
          <div className="order-title">
            {showCheckItem && 
            <CheckboxItem 
               checked={order.checked}
               disabled={order.disabled}
               onChange={(e) => checkOne(e.target.checked,order.docId,index)}
               onClick={(e)=>  e.stopPropagation()}
             />}
            {!order.disabled ? <span className="order-title-code">编码：{order.code}</span> : 
                              <span className="order-title-message">不满足合并，无相同支付方式 </span>
            }
            {
              !isHideDocStatus &&
              <span className="order-title-status">{order.docStatus}</span>
            }
          </div>
          <div className="order-content">
            <p style={{paddingRight: `${order.isOverdue ? "83px" : "0px"}`}}><span>订货组织：</span><span>{order.orderOrgName}</span></p>
            <p style={{paddingRight: `${order.isOverdue ? "83px" : "0px"}`}}><span>费用单类型：</span><span>{order.feeDocType}</span></p>
            <p><span>最后付款日期：</span><span>{DateUtils.toStringFormat(order.limitPaymentTime, "yyyy-MM-dd HH:mm:ss")}</span></p>
            <p><span>创建时间：</span><span>{DateUtils.toStringFormat(order.createTime, "yyyy-MM-dd HH:mm:ss")}</span></p>
            <p><span>审单时间：</span><span>{DateUtils.toStringFormat(order.confirmTime, "yyyy-MM-dd HH:mm:ss")}</span></p>
            {
              isShowAmount && <p><span>费用金额：</span><span>{order.amount}</span></p>
            }
            {
              order.isOverdue &&
                <div className="img-wrap">
                  <img src="https://order.fwh1988.cn:14501/static-img/scm/icon_overdue.png" alt=""/>
                </div>
            }
          </div>
          {
            !isHideAmountList &&
              <div className="order-amount">
                <p>
                  <span className="name">费用金额：</span>
                  <span className="amount">￥{order.amount}</span>
                  <span className="status">{order.paymentStatus}</span>
                </p>
                <p>
                  <span className="name">待确认金额：<span className="amount">￥{order.unAuditedAmount}</span></span>
                  <span className="name">已确认金额：<span className="amount">￥{order.auditedAmount}</span></span>
                  <span className="name">未支付金额：<span className="amount">￥{order.unPayableAmount}</span></span>
                </p>
              </div>
          }
        </div>
        {
          order.isShowPaymentBtn &&
          <div className="order-operation">
            <span
              onClick={(e) => {
                if (scanningLogin) {
                  disabledOprate();
                } else {
                  this.paymentFinish(e, order.docId, order.canPartialPay);
                }
              }}
            >
              付款
            </span>
          </div>
        }
      </Order>
    );
  }
}

const Order = styled.div`// styled
  & {
    width: 100%;
    background-color: #fff;
    box-sizing: border-box;
    position: relative;
    .am-list-item {
      display: inline-block;
      padding-left: 0;
      min-height: 18px;
      position: relative;
      top: -3px;
    }
    .am-list-item .am-list-thumb:first-child {
      margin-right: 8px;
    }
    .order-info {
      .order-title {
        position: relative;
        padding: 10px 15px;
        >.order-title-code {
          font-size: 14px;
          color: #333;
          font-family: "MicrosoftYaHei";
        }
        .order-title-message{
          font-size: 12px;
          font-family: "SourceHanSansCN-Normal";
          font-weight: 400;
          color: rgba(102, 102, 102, 1);
        }
        >.order-title-status{
          position: absolute;
          top: 10px;
          right: 15px;
          color: #FF3030;
          font-size: 12px;
          font-family: "MicrosoftYaHei";
        }
      }
      .order-content {
        position: relative;
        padding: 10px 15px;
        > p {
          margin-bottom: 10px;
          > span:nth-of-type(1) {
            color: #666666;
            font-size: 12px;
            font-family: "MicrosoftYaHei";
          }
          > span:nth-of-type(2) {
            color: #333333;
            font-size: 12px;
            font-family: "MicrosoftYaHei";
          }
          > p {
            display: inline-block;
            margin-bottom: 0;
            > span:nth-of-type(1) {
              color: #666666;
              font-size: 12px;
              font-family: "MicrosoftYaHei";
            }
            > span:nth-of-type(2) {
              color: #333333;
              font-size: 12px;
              font-family: "MicrosoftYaHei";
            }
          }
          > p:nth-of-type(2) {
            margin-left: 16px;
          }
        }
        > p:last-child {
          margin: 0;
        }
        .img-wrap{
          position: absolute;
          top: 0;
          right: 0;
          width: 83px;
          height: 52px;
          img{
            width: 100%;
            height: 100%;
          }
        }
      }
      .order-amount {
        position: relative;
        padding: 10px 15px;
        > p {
          margin-bottom: 10px;
        }
        .name {
          color: #666666;
          font-size: 12px;
          //margin-right: 5px;
        }
        .amount {
          color: #FF3030;
          font-size: 12px;
        }
        .status {
          position: absolute;
          top: 10px;
          right: 15px;
          color: #FF8627;
          font-size: 12px;
          font-family: "MicrosoftYaHei";
        }
        .payment {
          color: #FF7362;
          background: #FFEBE8;
          padding: 2px 6px;
          border: 1px solid transparent;
          border-radius: 2px;
          font-size: 10px;
        }
        > p:last-child {
          margin: 0;
        }
      }
      > div {
        position: relative;
        :after {
          content: '';
          position: absolute;
          background-color: #D8D8D8 !important;
          display: block;
          z-index: 1;
          top: auto;
          right: auto;
          bottom: 0;
          left: 0;
          width: 100%;
          height: 1.2px;
          transform-origin: 50% 100%;
          transform: scaleY(0.5);
        }
      }
    }
    .order-operation {
      position: relative;
      padding: 10px 0px;
      height: 50px;
      > span {
        float: right;
        color: #307DCD;
        // border: 0.5px solid #307DCD;
        border-radius: 3px;
        width: 20%;
        height: 30px;
        line-height: 20px;
        padding: 5px;
        margin-right: 15px;
        font-size: 12px;
        font-family: "MicrosoftYaHei";
        text-align: center;
        position: relative;
      }
      > span:after {
        content: "  ";
        position: absolute;
        left: 0;
        top: 0;
        z-index: 1;
        width: 200%;
        height: 200%;
        border: 1px solid #307DCD;
        border-radius: 3px;
        -webkit-transform-origin: 0 0;
        transform-origin: 0 0;
        -webkit-transform: scale(.5, .5);
        transform: scale(.5, .5);
      }
      > span:first-child {
        margin-right: 10px;
      }
      .nowPay {
        color: #FF3030;
        font-size: 12px;
        font-family: "MicrosoftYaHei";
        // border: 1px solid #FF3030;
        position: relative;
      }
      .nowPay:after {
        content: "  ";
        position: absolute;
        left: 0;
        top: 0;
        z-index: 1;
        width: 200%;
        height: 200%;
        border: 1px solid #FF3030;
        border-radius: 3px;
        -webkit-transform-origin: 0 0;
        transform-origin: 0 0;
        -webkit-transform: scale(.5, .5);
        transform: scale(.5, .5);
      }
    }
  }
`;
