import { observer } from "mobx-react";
import React from "react";
import styled from "styled-components";
import moment from "moment";
import { Calendar , DatePickerView, DatePicker, InputItem} from 'antd-mobile';

interface DatePickerProps {
  visible: boolean;
  type: "one" | "range";
  defaultValue?: string;
  endDate?: Date;
  beginDate?: Date;
  onCancel:()=>void;
  onConfirm:(startDateTime?: Date, endDateTime?: Date)=>void;
  onSelectHasDisableDate?:(date: Date[])=>void;
}

var now = new Date(); // 当前日期
var nowDayOfWeek = now.getDay(); // 今天本周的第几天
var nowDay = now.getDate(); // 当前日
var nowMonth = now.getMonth(); // 当前月
var nowYear = now.getFullYear(); // 当前年
nowYear += (nowYear <2000) ?1900 :0;
var day = nowDayOfWeek ||7;

@observer
export class DatePickers extends React.Component<DatePickerProps, any> {
  public inputRef = "";
  public constructor(props) {
    super(props);
    this.state = {
      beginDate: props.beginDate,
      endDate: props.endDate,
      inputNumber : true,
    };
  }
  //获取本月天数
  public  getMonthDays = () => {
    var monthStartDate:any= new Date(nowYear, nowMonth,1);
    var monthEndDate:any = new Date(nowYear, nowMonth +1,1);
    var days = (monthEndDate - monthStartDate) / (1000 *60 *60 *24);
    return days;
}
  //底部时间面板改变
  public onValueChange = (val) => {
    const {inputNumber} = this.state;
    if(inputNumber){
      this.setState({beginDate : val})
    }else{
      this.setState({endDate : val})
    }
  };
 //点击范围选择事件
  public thisRange  = (type) => {
    let beginDate: Date;
    let endDate: Date;
    switch (type) {
      case "month":
         beginDate = new Date(nowYear, nowMonth, 1);
         endDate = new Date(nowYear, nowMonth, this.getMonthDays());
         break;
      case "week":
        beginDate = new Date(now.getFullYear(), nowMonth, nowDay + 1 - day);
        endDate = new Date(now.getFullYear(), nowMonth, nowDay + 7 - day);
        break;
      case "date":
        beginDate = new Date();
        endDate = new Date();
    }
    this.setState({beginDate});
    this.setState({ endDate});
    this.inputRef.focus()
  }
  public render() {
    const now = new Date();
    const { visible, type,defaultValue,onSelectHasDisableDate,onCancel,onConfirm} = this.props;
    const {beginDate, endDate, inputNumber} = this.state;
    return (
      <Wapper>
        {visible && <div className={"model"} >
          <div className={"model-bg"} onClick={() => onCancel()} />
          <div className={"date-content"} onClick={(e) => {e.preventDefault()}}>
          <div className={"date-content-top"}>
            <div onClick={() => onCancel()}>取消</div>
            <div onClick={() => onConfirm(beginDate, endDate)}>确认</div>
          </div>
          <div className={"date-content-center"}>
            <div className={"date-content-input"}>
              <InputItem
               ref={(el) => this.inputRef = el}
               value={moment(beginDate).format("YYYY-MM-DD")}
               prefixListCls={inputNumber ? "onFocusInput" : "am-list"}
              //  readOnly={true}
               onFocus={() => {this.setState({inputNumber: true});document.activeElement.blur()}}
               placeholder="请选择开始时间"
               editable
              />
              <span>
                至
              </span>
              <InputItem
              //  readOnly={true}
              prefixListCls={inputNumber ? "am-list"  : "onFocusInput" }
               onFocus={() => {this.setState({inputNumber: false});document.activeElement.blur()}}
               value={moment(endDate).format("YYYY-MM-DD")}
               placeholder="请选择结束时间"
               editable
              />
            </div>
            <div className={"date-content-accurate"}>
              <ShortcutWapper style={{ display: "flex" }}>
                <div>
                  {type === "range" && <span onClick={() => this.thisRange("month")}>
                      本月
                  </span>}
                  {type === "range" && <span onClick={() => this.thisRange("week")}>
                      本周
                  </span>}
                  <span onClick={() => this.thisRange("date")}>
                      今天
                </span>
                </div>
                <span>一次查询不能超过180天</span>
              </ShortcutWapper>
            </div>
          </div>
          <DatePickerView
            onChange={this.onValueChange}
            value={inputNumber ? beginDate : endDate}
            mode="date"
          />
          </div>
        </div>}
      </Wapper>
    );
  }
}


const Wapper = styled.div`// styled
  & {
    .model{
      position: fixed;
      top: 0;
      left: 0;
      bottom: 0;
      right: 0;
      z-index: 999;
      .model-bg{
        position: fixed;
        top: 0;
        left: 0;
        bottom: 0;
        right: 0;
        background-color: rgba(0, 0, 0, 0.4);
      }
      .date-content{
        position: absolute;
        height:375px;
        bottom: 0;
        left: 0;
        right: 0;
        background: #FFFFFF;
        .date-content-top{
          width: 100%;
          height: 40px;
          display: flex;
          justify-content:space-between;
          padding:12px 16px;
          background: #F7F7F7;
          font-family: PingFang-SC-Medium, PingFang-SC;
          font-weight: 500;
          color: #0091FF;
        }
        .date-content-center{
          padding:10px 24px
        }
        .date-content-input{
          display: flex;
          align-items: center;
          >span{
            width: 30px;
            text-align: center;
            display: inline-block;
          }
          .onFocusInput-item{
            height: 32px!important;
            min-height:32px!important;
            line-height: 0px;
            background: #FFFFFF;
            border-radius: 4px;
            border: 1px solid rgb(16, 142, 233);
            padding-left: 15px;
            box-shadow:0 0 3px 0px rgb(16, 142, 233);
            .am-list-line{
              position: relative;
              display: flex;
              flex: 1;
              align-self: stretch;
              padding-right: 15px;
              overflow: hidden;
            }
            input{
              font-size:14px!important;
              color: #000;
              appearance: none;
              width: 100%;
              padding: 2px 0;
              border: 0;
              background-color: transparent;
              line-height: 2;
              box-sizing: border-box;
            }
          }
          .am-list-item{
            height: 32px!important;
            min-height:32px!important;
            line-height: 0px;
            background: #FFFFFF;
            border-radius: 4px;
            border: 1px solid #D8D8D8;
            input{
              font-size:14px!important
            }
          }
        }
        .am-picker{
          position: absolute;
          bottom: 0;
          left: 0;
          width: 100%;
        }
      }
    }
    .am-calendar .header{
      height:40px;
      line-height:40px;
      margin-bottom:10px;
    }
    .am-calendar .header .right{
        display:inline-block;
        width:56px;
        height:22px;
        right:12px;
        top:14px;
        font-size: 12px;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #437DF0;
        border:1px solid #437DF0;
        border-radius:11px;
        text-align:center;
        line-height:22px;
    }
    .am-calendar .header .left {
        top: 13px;
        color:#666;
    }
    .am-calendar .content {
      top:180px;
      border-radius:20px 20px 0 0;
      height:calc(100% - 190px);
    }
    .am-calendar .date-picker{
      padding-bottom:0;
    }
    .am-calendar .single-month .month-title{
      font-size: 14px;
      font-family: PingFangSC-Medium, PingFang SC;
      font-weight: 500;
      color: #333333;
    }
    .am-calendar .confirm-panel{
      background-color:#fff;
      border-top:#eee 1px solid;
      .button{
        width:180px;
        border-radius:20px;
        background-color:#437DF0;
      }
      .button-disable{
        background-color:#ddd !important;
      }
    }
    .am-calendar .week-panel .cell {
      font-size: 12px;
      font-family: PingFangSC-Medium, PingFang SC;
      font-weight: 500;
      color: #333333;
    }
    .am-calendar .single-month .row .cell .date-wrapper .date-selected ,.am-calendar .single-month .row .cell .date-wrapper .date {
      font-size: 12px;
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: 400;
      color: #606266;
    }
    .am-calendar .single-month .row .cell .info {
      height:0;
    }
    .am-calendar .confirm-panel .info p{
      font-size: 12px;
      font-family: PingFangSC-Medium, PingFang SC;
      font-weight: 500;
      color: #333333;
    }
    .am-calendar .single-month .row .cell .date-wrapper .date-selected{
      background-color:#E9F0FF;
    }
    
  }
`;
const ShortcutWapper = styled.div`
  &{
      width:100%;
      height:46px;
      display:flex;
      justify-content: space-between;
      align-items:center;
      border-top:1px solid #eee;
      span{
        display:inline-block;
        font-size: 8px;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #666666;
      }
      div:first-child{
      >span{
        border:1px solid #999;
        margin-right:10px;
        width:36px;
        height:16px;
        text-align:center;
        line-height:16px;
        border-radius:8px;
        &:hover{
          color:#437DF0;
          border:1px #437DF0 solid;
        }
      }
      }

  }
`;