import * as React from "react";
import { observer } from "mobx-react";
import { withoutRouter } from "react-router";
import { ImagePicker, Progress } from "antd-mobile";
import { Toast } from "antd-mobile/es";
import { merge } from "lodash";
import { REQUEST_SERVER } from "../../helpers/ajax-helpers";
import { $PaymentType } from "@classes/const/$payment-type";

@observer
export class ImagePickerUpload extends React.Component<any, any> {

  constructor(props) {
    super(props);
    this.state = {
      percent: 0,
      showProgress: false,
      uploadPictureNumber: 0,
    };
  }

  public componentDidMount(): void {
    const { pics } = this.props;
    this.setState({ uploadPictureNumber: pics.length });
  }

  public onUpload = (files, type, index) => {
    console.log(type,9999);
    if (type === "add") {
      this.setState({ files }, () => this.uploadImage(files[files.length - 1]));
    } else if (type === "remove") {
      this.props.setPics(files);
      const { uploadPictureNumber } = this.state;
      if (uploadPictureNumber > 0) {
        this.setState({ uploadPictureNumber: uploadPictureNumber - 1 });
      }
      if (files.length === 0) {
        document.getElementsByClassName("paymentVoucherTip")[0].style.marginLeft = "0px";
      }
    }
  }

  public uploadImage = (file: any) => {
    const formData = new FormData();
    const { limitLength } = this.props;
    const { uploadPictureNumber } = this.state;
    console.log(file, uploadPictureNumber, limitLength)
    if (uploadPictureNumber >= limitLength) {
      return;
    }
    this.setState({ uploadPictureNumber: uploadPictureNumber + 1 });
    formData.append("file", file.file);
    if (file.file.size / 1024 > 20480) {
      Toast.info("图片大小不能超过20MB", 3);
      return;
    }
    const xhr = new XMLHttpRequest();
    xhr.open("POST", `${REQUEST_SERVER}/k/integration/scm/payvoucher/upload`, true);
    xhr.setRequestHeader("ssoSessionId", localStorage.getItem("token"));
    xhr.onload = () => {
      if (xhr.status === 200) {
        const url = JSON.parse(xhr.responseText).data.data.pic.url;
        // console.log(111111111);
        // console.log(file);
        // console.log(file.file.orientation);
        // file.file.orientation = 1;
        // file.orientation = 1;
        console.log("看看返回的图片数据1",JSON.parse(xhr.responseText));

        // console.log(file.file.orientation);
        // console.log(file.orientation);
        const _file = merge({}, file.file, { url }, {
          thumbUrl: url,
          uid: JSON.parse(xhr.responseText).data.data.pic.uid,
          md5Value:JSON.parse(xhr.responseText).data.data.pic.md5Value ? JSON.parse(xhr.responseText).data.data.pic.md5Value : null
        });
        this.addFile(_file);
      } else {
        this.setState({ uploadPictureNumber: uploadPictureNumber - 1 });
      }
    };
    if (this.props.isExchange) {
      xhr.upload.addEventListener("progress", this.OnExchangeProgRess, false);
    } else {
      xhr.upload.addEventListener("progress", this.OnProgRess, false);
    }
    xhr.send(formData);
  }

  public addFile = (file) => {
    const { pics } = this.props;
    const fileList = pics.slice();
    fileList.push(file);
    this.props.setPics(fileList);
    this.setState({ showProgress: false });
  }

  public OnProgRess = (e) => {
    const { pics, picRule } = this.props;
    if (pics) {
      if (pics.length === 0 && picRule === $PaymentType.PICRULE) {
        // console.log(pics.length);
        document.getElementsByClassName("am-progress-outer")[0].style.left = `2%`;
        document.getElementsByClassName("paymentVoucherTip")[0].style.marginLeft = "48px";
      }
      if (pics.length === 1) {
        // console.log(pics.length);
        document.getElementsByClassName("am-progress-outer")[0].style.left = `23%`;
        // console.log(document.getElementsByClassName("am-progress-outer")[0].style.left);
      }
      if (pics.length === 2) {
        // console.log(pics.length);
        document.getElementsByClassName("am-progress-outer")[0].style.left = `56%`;
        // console.log(document.getElementsByClassName("am-progress-outer")[0].style.left);
      }
      if (pics.length === 3) {
        // console.log(pics.length);
        document.getElementsByClassName("am-progress-outer")[0].style.left = `80%`;
        // console.log(document.getElementsByClassName("am-progress-outer")[0].style.left);
      }
      if (pics.length === 4) {
        // console.log(pics.length);
        document.getElementsByClassName("am-progress-outer")[0].style.left = `8%`;
        document.getElementsByClassName("am-progress-outer")[0].style.top = `68%`;
        // console.log(document.getElementsByClassName("am-progress-outer")[0].style.left);
      }
      if (pics.length === 5) {
        // console.log(pics.length);
        document.getElementsByClassName("am-progress-outer")[0].style.left = `32%`;
        document.getElementsByClassName("am-progress-outer")[0].style.top = `68%`;
        // console.log(document.getElementsByClassName("am-progress-outer")[0].style.left);
      }
      if (pics.length === 6) {
        // console.log(pics.length);
        document.getElementsByClassName("am-progress-outer")[0].style.left = `56%`;
        document.getElementsByClassName("am-progress-outer")[0].style.top = `68%`;
        // console.log(document.getElementsByClassName("am-progress-outer")[0].style.left);
      }
      if (pics.length === 7) {
        // console.log(pics.length);
        document.getElementsByClassName("am-progress-outer")[0].style.left = `80%`;
        document.getElementsByClassName("am-progress-outer")[0].style.top = `68%`;
        //document.getElementsByClassName("am-progress-outer")[0].style.top = `47%`;
        // console.log(document.getElementsByClassName("am-progress-outer")[0].style.left);
      }
      if (pics.length === 8) {
        // console.log(pics.length);
        document.getElementsByClassName("am-progress-outer")[0].style.left = `8%`;
        document.getElementsByClassName("am-progress-outer")[0].style.top = `77%`;
        // console.log(document.getElementsByClassName("am-progress-outer")[0].style.left);
      }
      if (pics.length === 9) {
        // console.log(pics.length);
        document.getElementsByClassName("am-progress-outer")[0].style.left = `32%`;
        document.getElementsByClassName("am-progress-outer")[0].style.top = `77%`;
        // console.log(document.getElementsByClassName("am-progress-outer")[0].style.left);
      }
    }
    const loaded = Math.floor(100 * (e.loaded / e.total));
    this.setState({ percent: loaded, showProgress: true });
  }

  public OnExchangeProgRess = (e) => {
    // console.log(e);
    const { pics, picRule } = this.props;
    if (pics) {
      if (pics.length === 0 && picRule === $PaymentType.PICRULE) {
        // console.log(pics.length);
        document.getElementsByClassName("am-progress-outer")[0].style.left = `15px`;
      }
      if (pics.length === 1) {
        // console.log(pics.length);
        document.getElementsByClassName("am-progress-outer")[0].style.left = `84px`;
        // console.log(document.getElementsByClassName("am-progress-outer")[0].style.left);
      }
      if (pics.length === 2) {
        // console.log(pics.length);
        document.getElementsByClassName("am-progress-outer")[0].style.left = `152px`;
        // console.log(document.getElementsByClassName("am-progress-outer")[0].style.left);
      }
      if (pics.length === 3) {
        // console.log(pics.length);
        document.getElementsByClassName("am-progress-outer")[0].style.left = `223px`;
        // console.log(document.getElementsByClassName("am-progress-outer")[0].style.left);
      }
      if (pics.length === 4) {
        // console.log(pics.length);
        document.getElementsByClassName("am-progress-outer")[0].style.left = `292px`;
        // console.log(document.getElementsByClassName("am-progress-outer")[0].style.left);
      }
    }
    const loaded = Math.floor(100 * (e.loaded / e.total));
    this.setState({ percent: loaded, showProgress: true });
  }

  public render() {
    const { pics, limitLength, picRule } = this.props;
    const { percent, showProgress, files } = this.state;
    return (
      <div>
        <ImagePicker
          files={pics.slice(0, 10)}
          selectable={pics && pics.length < limitLength}
          multiple={picRule === $PaymentType.PICRULE}
          onChange={this.onUpload}
        />
        <Progress percent={percent} style={{ display: showProgress ? "block" : "none" }}/>
      </div>
    );
  }
}
