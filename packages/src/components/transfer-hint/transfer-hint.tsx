import { $TransferHintMv } from "@classes/entity/$transfer-hint-mv";
import { autowired } from "@classes/ioc/ioc";
import { toFixedOptimizing } from "@classes/utils/DateUtils";
import { message } from "antd";
import { Checkbox } from "antd-mobile";
import { toJS } from "mobx";
import { observer } from "mobx-react";
import React, { Component } from "react";
import { NewCustomModal } from "../custom-modal/new-custom-modal";
import { isEmpty, cloneDeep, set, sum } from "lodash";
import styled from "styled-components";
import { $TransferType } from "@classes/const/$transfer-type";

const CheckboxItem = Checkbox.CheckboxItem;

interface ITransferHintProps {
  salesOrderIdList: any[];
  createdTransferList: any[];
  totalWaitPayAmount: number;
  accountIdList: any[];
  divideRebateTransferAmount: number;
  reload: () => void;
}

@observer
export default class TransferHint extends Component<ITransferHintProps, any> {
  @autowired($TransferHintMv)
  public $transferHintMv: $TransferHintMv;

  constructor(props) {
    super(props);
    this.state = {
      isShowTransferModal: false,
      isShowTransferRecordModal: false,
    };
  }

  public showTransferList = () => {
    const { createdTransferList } = this.props;
    if (!isEmpty(createdTransferList)) {
      this.setState({isShowTransferRecordModal: true});
    }
  }

  public onClickTransfer = () => {
    const { totalWaitPayAmount, divideRebateTransferAmount, accountIdList, salesOrderIdList } = this.props;
    if (totalWaitPayAmount <= 0 && divideRebateTransferAmount <= 0) {
      return;
    }
    const params = {
      accountIdList,
      totalWaitPayAmount,
      salesOrderIds: salesOrderIdList,
    };
    this.$transferHintMv.loadTransferInfo(params)
      .then(() => {
        this.setState({
          isShowTransferModal: true,
        });
      });
  }

  public changeTransferOut = (inIndex, outIndex, value) => {
    const { transferList } = this.$transferHintMv;
    const newTransferList = cloneDeep(toJS(transferList));
    set(newTransferList, `[${inIndex}].transferOutList.[${outIndex}].checked`, value);
    this.$transferHintMv.transferList = newTransferList;
  }

  public submitTransfer = () => {
    const { transferList } = this.$transferHintMv;
    const submitTransferList = [];
    transferList.map((transferIn: any) => {
      const { transferInOrgId, transferInAccountId, salesOrderIds, transferOutList } = transferIn;
      const submitTransferOutList = [];
      transferOutList.map((transferOut: any) => {
        const { checked, transferOutOrgId, transferOutAccountId, transferOutAmount } = transferOut;
        if (checked) {
          submitTransferOutList.push({ transferOutOrgId, transferOutAccountId, amount: transferOutAmount });
        }
      });
      if (submitTransferOutList.length > 0) {
        submitTransferList.push({
          transferOutList: submitTransferOutList,
          transferInOrgId, transferInAccountId, salesOrderIds,
        });
      }
    });
    if (submitTransferList.length <= 0) {
      message.warning("请选择转出门店");
      return;
    }
    const params = { transferList: submitTransferList };
    this.setState({ isShowTransferModal: false });
    this.props.reload();
    this.$transferHintMv.submitTransferInfo(params)
      .then((res) => {
        const { result, errorMsg } = res;
        if (result) {
          this.setState({ isShowTransferModal: false });
          message.success("转账成功", .5, () => {
            this.props.reload();
          });
        } else {
          message.error(errorMsg, .5, () => {
            this.props.reload();
          });
        }
      });
  }

  public renderTransferInfo = () => {
    const { transferList } = this.$transferHintMv;
    return <TransferInfo>
      {
        !isEmpty(transferList) && transferList.map((transferIn: any, inIndex) => {
          const { transferInOrgId, transferInOrgName, transferInAccountAmount, transferInAmount, transferOutList } = transferIn;
          const waitTransferAmount = toFixedOptimizing(sum(transferOutList
            .filter((item) => item.checked)
            .map((item) => item.transferOutAmount),
          ));
          const maxTransferAmount = toFixedOptimizing(sum(transferOutList.map((item) => item.transferOutAmount)));
          return <TransferBox key={transferInOrgId}>
            <div className={"org-in-name"}>
              { transferInOrgName }
              {
                maxTransferAmount < transferInAmount &&
                <span className={"max-transfer-tip"}>（最多可转入¥{maxTransferAmount}）</span>
              }
            </div>
            <div className={"org-in-info"}>
              <div>{transferIn.transferType === $TransferType.FZFL ? '分账返利账户' : '余额储值账户'}</div>
            </div>
            <div className={"org-in-info"}>
              <div>
                余额<span className={"amount grey"}>{transferInAccountAmount}</span>，
                需转入<span className={"amount grey"}>{transferInAmount}</span>，
                待转入<span className={"amount"}>{toFixedOptimizing(waitTransferAmount)}</span>
              </div>
            </div>
            <div className={"org-out-info"}>
              {
                !isEmpty(transferOutList) && transferOutList.map((transferOut: any, outIndex) => {
                  const { checked, transferOutOrgId, transferOutOrgName, transferOutAmount} = transferOut;
                  return <CheckboxItem
                    key={`${transferInOrgId}-${transferOutOrgId}`}
                    onChange={(e) => this.changeTransferOut(inIndex, outIndex , e.target.checked)}
                    checked={checked}
                  >
                    <div className={"info"}>
                      <div>转出门店：</div><div className={"counter"}>{transferOutOrgName}</div>
                      <div className={"amount"}>{transferOutAmount}</div>
                    </div>
                  </CheckboxItem>;
                })
              }
            </div>
          </TransferBox>;
        })
      }
    </TransferInfo>;
  }

  public renderTransferRecord = () => {
    const { createdTransferList } = this.props;
    return <TransferRecord>
      {
        !isEmpty(createdTransferList) && createdTransferList.map((item: any, index) => <div key={item.code}>
          <div className={"code"}>{item.code}</div>
          <div><label>账户类型：</label><span>{item.accountTypeName}</span></div>
          <div><label>转账金额：</label><span style={{color: "#ff0000"}}>¥{item.amount}</span></div>
          <div><label>转入门店：</label><span>{item.transferInOrgName}</span></div>
          <div><label>转出门店：</label><span>{item.transferOutOrgName}</span></div>
        </div>)
      }
    </TransferRecord>;
  }

  public render() {
    const { transferList } = this.$transferHintMv;
    const { createdTransferList, totalWaitPayAmount, divideRebateTransferAmount } = this.props;
    const { isShowTransferModal, isShowTransferRecordModal } = this.state;
    const isTransferOutEmpty = transferList.every((transfer) => isEmpty(transfer.transferOutList));
    console.log('divideRebateTransferAmount', divideRebateTransferAmount > 0 || totalWaitPayAmount > 0)
    return (
      <>
        <Wrapper>
          {
            divideRebateTransferAmount > 0 && <div className={"transfer-amount"}>本次订单还可转入分账返利：<span>{divideRebateTransferAmount}</span></div>
          }
          {
            totalWaitPayAmount > 0 && <div className={"transfer-amount"}>本次订单还需余额：<span>{totalWaitPayAmount}</span></div>
          }
          <div className={"tips"}>注：剩余金额可从其他门店转账后金支付；转账后会更新门店余额</div>
          <div>
            <div
              className={`transfer-btn ${divideRebateTransferAmount > 0 || totalWaitPayAmount > 0 ? "" : "disabled"}`}
              onClick={this.onClickTransfer}
            >
              一键转账
            </div>
            <span onClick={this.showTransferList}>
              已生成{createdTransferList.length}个转账单
              {createdTransferList.length > 0 ? ",点击查看" : ""}
            </span>
          </div>
        </Wrapper>
        {
          isShowTransferModal &&
          <NewCustomModal
            header={"转账详情"}
            hideConfirm={isTransferOutEmpty}
            confirm={this.submitTransfer}
            content={this.renderTransferInfo()}
            confirmName={"确认转账"}
            visible={isShowTransferModal}
            close={() => this.setState({isShowTransferModal: false})}
          />
        }
        {
          isShowTransferRecordModal &&
          <NewCustomModal
            style={{top: "45%", padding: "10px 0"}}
            header={"转账单记录"}
            confirm={() => this.setState({isShowTransferRecordModal: false})}
            content={this.renderTransferRecord()}
            hideCancel={true}
            confirmName={"关闭"}
            visible={isShowTransferRecordModal}
          />
        }
      </>
    );
  }
}

const Wrapper = styled.div`// styled
  & {
    margin: 10px;
    padding: 12px;
    background: rgba(255,166,48,0.1);
    border-radius: 4px;
    border: 1px solid #FFA630;
    .transfer-amount {
      font-size: 14px;
      font-weight: 500;
      color: #000000;
      line-height: 21px;
      > span:nth-child(1) {
        font-weight: 400;
        color: #FF3030;
      }
      > span:nth-child(1):before {
        content: "¥";
        font-size: 12px;
      }
    }
    .tips {
      zoom: .85;
      font-size: 12px;
      color: #666666;
    }
    > div:last-child {
      display: flex;
      align-items: flex-end;
      margin-top: 8px;
      .transfer-btn {
        font-size: 12px;
        padding: 2px 12px;
        color: #fff;
        background: #307dcd;
        border-radius: 20px;
        margin-right: 10px;
      }
      .disabled {
        background: #999999;
      }
      > span:last-child {
        font-size: 13px;
        text-decoration: underline;
        text-underline-offset: 3px;
        color: #307dcd;
      }
    }
  }
`;
const TransferInfo = styled.div`// styled
  &{
    width: 100%;
    .amount {
      font-weight: 400;
      color: #FF3030;
    }
    .amount:before {
      content: "¥";
      zoom: .85;
      font-size: 12px;
      margin-left: 5px;
    }
    .tip {
      display: block;
      font-size: 12px;
      color: #666666;
      margin-bottom: 10px;
    }
  }
`;
const TransferBox = styled.div`// styled
  & {
    width: 100%;
    padding: 12px 0;
    border-top: 1px solid rgba(0, 0, 0, .12);
    .org-in-name {
      font-weight: 500;
      color: #333333;
      margin-bottom: 10px;
      .max-transfer-tip {
        margin-left: 10px;
        font-size: 12px;
        // color: #FFA630;
      }
    }
    .org-in-info {
      font-size: 12px;
      margin-bottom: 10px;
      color: #333333;
      . grey {
        color: #333333;
      }
      > div:nth-child(1) {
        color: #333333;
      }
    }
    .org-out-info {
      margin-bottom: 10px;
      .info {
        display: flex;
        font-size: 12px;
        color: #333333;
        .counter {
          white-space: break-spaces;
        }
      }
      .am-list-item {
        padding: 0;
      }
      .am-list-item .am-list-thumb:first-child {
        margin-right: 5px;
      }
      .am-checkbox-inner {
        width: 19px;
        height: 19px;
      }
      .am-checkbox-inner:after {
        top: 2.5px;
        right: 6px;
      }
      .am-checkbox.am-checkbox-checked .am-checkbox-inner {
        border-color: #437DF0;
        background: #437DF0;
      }
    }
  }
`;
const TransferRecord = styled.div`// styled
  & {
    counter-reset: transfer-index;
    > div:before {
      color: #333333;
      font-weight: 500;
      position: absolute;
      transform: translateX(-100%);
      content: counter(transfer-index)"、";
      counter-increment: transfer-index;
    }
    > div {
      margin-left: 20px;
      position: relative;
      .code {
        color: #333333;
        font-size: 14px;
        font-weight: 500;
      }
      > div {
        display: flex;
        margin-bottom: 8px;
        font-size: 14px;
        > label {
          color: #666666;
          flex-shrink: 0;
        }
        > span {
          color: #333333;
        }
      }
    }
  }
`;
