import * as React from "react";
import { observer } from "mobx-react";
import styled from "styled-components";
import { Checkbox, List } from "antd-mobile";
import { $PaymentType } from "../../classes/const/$payment-type";
import { OfflineTransferComponent } from "../offline-transfer-component/offline-transfer-component";
import { ChooseBankCardModal } from "../choose-bank-card-modal/choose-bank-card-modal";
import { $PaymentModeType } from "../../classes/const/$payment-mode-type";
import { ViewRatesModal } from "../view-rates-modal/view-rates-modal";

const CheckboxItem = Checkbox.CheckboxItem;

@observer
export class PaymentListComponent extends React.Component<any, any> {

  public onChange = (mode) => {
    this.props.onChange(mode);
  }

  public renderIcon = (code) => {
    let className = null;
    switch (code) {
      case $PaymentType.BANK_TRANSFER:
        className = "scmIconfont payment-icon-list-item scm-zhuanzhang3";
        break;
      case $PaymentType.ALLINPAY_BANK_CARD:
        className = "scmIconfont payment-icon-list-item scm-yinhangka";
        break;
      case $PaymentType.ALLINPAY_WECHAT:
        className = "scmIconfont payment-icon-list-item scm-Fill";
        break;
      case $PaymentModeType.STOREDVALUE:
        className = "scmIconfont payment-icon-list-item scm-caiwu1";
        break;
      default:
        break;
    }
    return className;
  }

  public renderColor = (code) => {
    let color = null;
    switch (code) {
      case $PaymentType.BANK_TRANSFER:
        color = "#52C41A";
        break;
      case $PaymentType.ALLINPAY_BANK_CARD:
        color = "#108EE9";
        break;
      case $PaymentType.ALLINPAY_WECHAT:
        color = "#52C41A";
        break;
      case $PaymentModeType.STOREDVALUE:
        color = "#FF8627";
        break;
      default:
        break;
    }
    return color;
  }

  public render() {
    const { paymentModeList, showAccountMabel, bankName, accountCode, date, changeDate, pics, setPics, transferAmount, isChooseAccount, showChooseBankCardModal, isShow, goBack, chooseBank, selectedBankName, selectedBankCardType, selectedBankCardNo, bankRedirectUrl, getSelectBankRate, skipToBindCard, showRate, showRateModal, hideRateModal, selectRateList, showPaymentLine, subMerchantList } = this.props;
    // 充值和还款不可以调整金额，只有付款可以
    const paymentLineIndex = subMerchantList && subMerchantList.length > 0 ? subMerchantList.findIndex((line) => line.checked === true) : -1;
    return (
      <PaymentListComponentPage>
        <div>支付方式</div>
        <List>
          {
            paymentModeList && paymentModeList.filter((mode) => mode.code !== $PaymentType.ALLINPAY).map((lis) => {
              console.log(lis, "lis");
              let totalValueText = "";
              if (lis.isCheck && lis.code === $PaymentType.ALLINPAY_BANK_CARD || lis.isCheck && lis.code === $PaymentType.ALLINPAY_WECHAT) {
                // const totalValueServiceCharge = Number(Number(transferAmount * getSelectBankRate(lis.code)).toFixed(2));
                const totalValueServiceCharge = getSelectBankRate(lis.code);
                console.log("totalValueServiceCharge", totalValueServiceCharge);
                totalValueText = `${Number(Number(Number(transferAmount).toFixed(2)) + totalValueServiceCharge).toFixed(2)}${totalValueServiceCharge ? `（含服务费：${totalValueServiceCharge}元）` : "" }`;
                if (totalValueText === "NaN") {
                  totalValueText = "0";
                }
              }
              const disabled = lis.code === $PaymentModeType.STOREDVALUE && (lis.availableTotalAmount < transferAmount || lis.availableTotalAmount === 0);
              return (
                <CheckboxItem
                  checked={lis.isCheck}
                  key={lis.oid}
                  onChange={(e) => this.onChange(lis)}
                  disabled={disabled}
                >
                    <span
                      style={{
                        position: "relative", top: lis.isCheck ? "3px" : 0,
                        display: "inline-block",
                        width: "calc(100% - 62px)",
                      }}
                      onClick={(e) => {
                        if (!disabled) {
                          this.onChange(lis);
                        }
                      }}
                    >
                      <i
                        className={this.renderIcon(lis.code)}
                        style={{ color: this.renderColor(lis.code), position: "relative", top: "-2px" }}
                      />
                      <span style={{ position: "relative", top: "-3px" }}>{lis.name}</span>
                    </span>
                  {
                    lis.isCheck && (lis.code === $PaymentType.ALLINPAY_BANK_CARD || lis.code === $PaymentType.ALLINPAY_WECHAT) &&
                    <div onClick={() => showRateModal(lis)}
                         style={{
                           float: "right",
                           color: "#307DCD",
                           position: "relative",
                           top: "15px",
                           paddingRight: "10px",
                         }}>
                      查看费率
                    </div>
                  }
                  {
                    lis.isCheck && lis.code === $PaymentType.BANK_TRANSFER && <OfflineTransferComponent
                      isChooseAccount={isChooseAccount}
                      showAccountMabel={showAccountMabel}
                      transferAmount={transferAmount}
                      bankName={bankName}
                      accountCode={accountCode}
                      paymentDate={date}
                      changeDate={changeDate}
                      pics={pics}
                      setPics={setPics}
                    />
                  }
                  {
                    lis.isCheck && lis.code === $PaymentType.ALLINPAY_BANK_CARD && <TLBK>
                      {
                        transferAmount ? <span style={{ marginLeft: "20px" }}>
                      预计支付金额：￥
                        <span style={{ color: "#FF3030" }} className={"amount"}>
                          {totalValueText}
                        </span>
                    </span> : <span style={{ marginLeft: "20px" }}>
                      预计支付金额：￥ <span style={{ color: "#FF3030" }}>0</span>
                    </span>
                      }
                      <span style={{ display: "block", border: "none", marginLeft: "20px" }}>
                        <span
                          style={{
                            position: "relative",
                            top: "2px",
                          }}
                        >
                        {selectedBankName}{selectedBankCardType}（{selectedBankCardNo}）
                      </span>
                      <span onClick={showChooseBankCardModal} style={{ float: "right", marginRight: "15px" }}>
                        切换
                        <i className={"scmIconfont payment-icon-list-item scm-icon-jiantou-you"}
                           style={{ color: "#999999", position: "relative", top: "2px" }}/>
                      </span>
                  </span>
                    </TLBK>
                  }
                  {
                    lis.isCheck && lis.code === $PaymentType.ALLINPAY_WECHAT &&
                    <TLBK>
                      {
                        transferAmount ? <span style={{ border: "none", marginLeft: "20px" }}>
                      预计支付金额：￥
                        <span style={{ color: "#FF3030" }} className={"amount"}>
                          {totalValueText}
                        </span>
                    </span> : <span style={{ border: "none", marginLeft: "20px" }}>
                      预计支付金额：￥<span style={{ color: "#FF3030" }}>0</span>
                    </span>
                      }
                    </TLBK>
                  }
                  {
                    lis.code === $PaymentModeType.STOREDVALUE &&
                    <TLBK>
                      {
                        <span style={{ border: "none", marginLeft: "20px" }}>
                      账户余额：￥{Number(Number(Number(lis.availableTotalAmount).toFixed(2)))} {
                          (lis.availableTotalAmount < transferAmount || lis.availableTotalAmount <= 0) && `(余额不足)`
                        }
                          {
                            lis.isCheck && <span style={{ border: "none" }}>
                      预计支付金额：￥
                        <span style={{ color: "#FF3030" }}
                              className={"amount"}>{transferAmount !== "." ? Number(Number(Number(transferAmount).toFixed(2))) : 0}</span>
                    </span>
                          }
                    </span>
                      }
                    </TLBK>
                  }
                  {
                    lis.isCheck && lis.code === $PaymentType.ALLINPAY_WECHAT && subMerchantList.length > 1 &&
                    <div className={"payment-channel"}>
                      <div>付款线路  {paymentLineIndex > -1 ? subMerchantList[paymentLineIndex].name : "请选择付款线路"}</div>
                      <div onClick={showPaymentLine}>切换
                        <i className={"scmIconfont scm-icon-jiantou-you"}/>
                      </div>
                    </div>
                  }
                </CheckboxItem>
              );
            })
          }
        </List>
        <ChooseBankCardModal
          isShow={isShow}
          goBack={goBack}
          chooseBank={chooseBank}
          skipToBindCard={skipToBindCard}
          bankRedirectUrl={bankRedirectUrl}
        />
        <ViewRatesModal
          showModal={showRate}
          hideRateModal={hideRateModal}
          selectRateList={selectRateList}
        />
      </PaymentListComponentPage>
    );
  }
}

const PaymentListComponentPage = styled.div`// styled
  & {
    width: 100%;
    background: #fff;
    .am-checkbox-inner {
      width: 18px;
      height: 18px;
    }
    .am-checkbox {
      width: 18px;
      height: 18px;
      top: 7px;
    }
    .am-list-item {
      vertical-align: unset;
      align-items: unset;
      min-height: 18px;
    }
    .am-list-item.am-list-item-active {
      background: #fff;
    }
    .payment-channel {
      display: flex;
      align-items: center;
      justify-content: space-between;
      height: 30px;
      border-top: 1px solid #D8D8D8;
      padding: 10px 18px 10px 0;
      font-size: 12px;
      > div:nth-child(1) {
        width: 70%;
        white-space: nowrap;
        text-overflow: ellipsis;
        overflow: hidden;
      }
    }
    > div:first-child {
      width: 100%;
      height: 40px;
      position: relative;
      padding: 10px 15px;
      font-size: 13px;
      font-family: SourceHanSansCN-Normal, SourceHanSansCN;
      font-weight: 400;
      color: rgba(48, 48, 48, 1);
      :after {
        content: '';
        position: absolute;
        background-color: #D8D8D8 !important;
        display: block;
        z-index: 1;
        top: auto;
        right: auto;
        bottom: 0;
        left: 0;
        width: 100%;
        height: 1.2px;
        transform-origin: 50% 100%;
        transform: scaleY(0.5);
      }
    }
    .am-checkbox-wrapper {
      vertical-align: -webkit-baseline-middle;
    }
    .payment-icon-list-item {
      margin-right: 5px;
    }
    .am-list-item .am-list-thumb:first-child {
      margin-right: 4px;
    }
    .am-list-content {
      > span:first-child {
        margin-top: 10px;
        display: inline-block;
      }
    }
    .am-list-body::before {
      display: none !important;
    }
    .am-list .am-list-item.am-checkbox-item .am-list-thumb .am-checkbox {
      width: 62px;
    }
    .am-list-item .am-list-line .am-list-content {
      text-overflow: unset;
      white-space: normal;
    }
  }
`;

const TLBK = styled.div`// styled
  & {
    > span {
      display: inline-block;
      width: 100%;
      min-height: 30px;
      border-bottom: 1px solid #D8D8D8;
      line-height: 30px;
    }
    .amount {
      display: inline-block;
      width: calc(100% - 104px);
      white-space: initial;
      // vertical-align: text-top;
      line-height: initial;
    }
  }
`;
