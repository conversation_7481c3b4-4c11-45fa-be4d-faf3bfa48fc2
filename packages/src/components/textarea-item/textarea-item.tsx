import * as React from "react";
import { observer } from "mobx-react";
import { TextareaItem } from "antd-mobile";

@observer
export class TextareaItemComponent extends React.Component<any, any> {

  public onChange = (value) => {
    this.props.onChange(value);
  }

  public render() {
    const { value, placeholder, rows, count } = this.props;
    return (
      <TextareaItem
        value={value}
        placeholder={placeholder}
        rows={rows}
        count={count}
        onChange={this.onChange}
        {...this.props}
      />
    );
  }
}
