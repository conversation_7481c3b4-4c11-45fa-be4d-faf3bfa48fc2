import React from "react";
import { observer } from "mobx-react";
import { Modal } from "antd-mobile";
import styled from "styled-components";

@observer
class RightSwiperModal extends React.Component<any, any> {

  constructor(props) {
    super(props);
    this.state = {
      businessType: this.props.businessType,
      title: this.props.title,
    };
  }

  public onSetBusinessType = (val: string) => {
    const { businessType } = this.state;
    if (businessType.indexOf(val) > -1) {
      businessType.splice(businessType.indexOf(val), 1);
    } else {
      businessType.push(val);
    }
    this.setState({ businessType })
  }

  public onSetTitle = (val: string) => {
    const { title } = this.state;
    if (title.indexOf(val) > -1) {
      title.splice(title.indexOf(val), 1);
    } else {
      title.push(val);
    }
    this.setState({ title })
  }

  public onReset = () => {
    this.setState({ businessType: [], title: [] })
  }

  public onConfirm = () => {
    const { onSearch } = this.props;
    const { businessType, title } = this.state;
    onSearch(businessType, title);
  }

  public render() {
    const { visible, onClose, swiperData } = this.props;
    const { businessType, title } = this.state;

    return (
      <Modal
        className="swiper-modal"
        visible={visible}
        animated={false}
      >
        <SMain onClick={onClose} />
        <SBox>
          {
            swiperData.map((item, index) => {
              return <div className="card" key={index}>
                <div className="title">{item.title}</div>
                <div className="item-list">
                  {
                    item.businessTypeList && item.businessTypeList.map((typeItem, typeIndex) => {
                      return <div className={`item ${businessType.indexOf(typeItem) > -1 && "active"} `}
                        style={{ visibility: typeItem === "" ? "hidden" : "visible" }}
                        key={typeIndex} onClick={() => this.onSetBusinessType(typeItem)}>{typeItem}</div>
                    })
                  }
                  {
                    item.titleList && item.titleList.map((titleItem, titleIndex) => {
                      return <div className={`item ${title.indexOf(titleItem) > -1 && "active"} `}
                        style={{ visibility: titleItem === "" ? "hidden" : "visible" }}
                        key={titleIndex} onClick={() => this.onSetTitle(titleItem)}>{titleItem}</div>
                    })
                  }
                </div>
              </div>
            })
          }
          <div className="footer">
            <div onClick={this.onReset}>重置</div>
            <div onClick={this.onConfirm}>确定</div>
          </div>
        </SBox>
      </Modal>
    )
  }

}

export default RightSwiperModal;

const SMain = styled.div`// styled
  & {
    position: fixed;
    top: 0;
    right: 0;
    left: 0;
    bottom: 0;
    z-index: 100;
  }
`;

const SBox = styled.div`// styled
  & {
    @keyframes bounceInLeft {
      0% {
        transform: translate3d(100%, 0, 0);
      }
      100% {
        transform: translate3d(0, 0, 0);
      }
    }

    position: absolute;
    top: 0;
    display: flex;
    flex-direction: column;
    right: 0;
    left: 90px;
    padding: 0.06rem 0;
    bottom: 0;
    background: #FFF;
    overflow: hidden;
    z-index: 101;
    animation: bounceInLeft 1s infinite;
    animation-iteration-count: 1;

    .card {
      display: flex;
      flex-direction: column;
      margin: 32px 12px 0;

      .title {
        font-size: 14px;
        color: #333;
        text-align: left;
      }

      .item-list {
        display: flex;
        flex-wrap: wrap;
        justify-content: flex-start;

        .item {
          min-width: 30%;
          background: #F7FAFF;
          margin-left: 3%;
          margin-top: 12px;
          height: 34px;
          font-size: 14px;
          line-height: 34px;
          border-radius: 2px;
          color: #666;
          border: 1px solid #F7FAFF;
        }

        .active {
          border: 1px solid #437DF0;
          color: #437DF0;
        }
      }
    }

    .footer {
      position: absolute;
      bottom: 40px;
      display: flex;
      justify-content: space-around;
      width: 100%;
      font-size: 15px;
      font-weight: 500;

      > div:nth-child(1) {
        background: #EBEBEB;
        border-radius: 17.5px;
        width: 120px;
        height: 36px;
        line-height: 36px;
      }

      > div:nth-child(2) {
        background: #437DF0;
        color: #FFF;
        border-radius: 17.5px;
        width: 120px;
        height: 36px;
        line-height: 36px;
      }
    }
  }
`;
