import { $AccountType } from "@classes/const/$account-type";
import { observer } from "mobx-react";
import React from "react";
import { withRouter } from "react-router";
import styled from "styled-components";


@withRouter
@observer
export class AccountFlowItem extends React.Component<any, any> {

  public render() {
    const { item, goAccountFlowDetail, accountTypeCode, bottomTitle } = this.props;
    return (
      <SAccountFlowItem onClick={() => item.IsSpecial ? null : goAccountFlowDetail(item)}>
        <div className="tag">
          <span>{item.businessType}</span>
          {
            item.IsHC === 'Y' && <span>已被红冲</span>
          }
        </div>
        <div className="account">
          <div>{item.accountUse}</div>
          <div style={{ color: item.inOutType === $AccountType.ADD ? "#FF4242" : "#333" }}>
            { item.IsSpecial ? "" : (item.inOutType === $AccountType.ADD ? "+" : "-")}{item.amount}
          </div>
        </div>
        {
          accountTypeCode === $AccountType.DEDUCTION_LIMIT && <div className="status">
            <div style={{ color: item.status === '结算' ? '#437DF0' : '#FF3030' }}>{item.status}</div>
          </div>
        }
        {
          item.relatedUse && <div className="account">
            <div>{item.relatedUse}</div>
            <div style={{ color: item.relatedInoutType === $AccountType.ADD ? "#FF4242" : "#333" }}>{item.relatedInoutType === $AccountType.ADD ? "+" : "-"}{item.relatedAmount}</div>
          </div>
        }
        <div className="date">
          <div>{item.docDate}</div>
          <div className="total">{ bottomTitle ? bottomTitle : (accountTypeCode === $AccountType.DEDUCTION_LIMIT ? '返利额度' : '配赠上限/额度') }: <span>{item.afterAmount}</span></div>
        </div>
      </SAccountFlowItem>
    );
  }
}

const SAccountFlowItem = styled.div`// styled
  & {
    display: flex;
    flex-direction: column;
    padding: 12px;
    border-bottom: 0.5px solid #D8D8D8;

    .tag {
      > span:nth-child(1) {
        background: #F6F6F6;
        color: #333;
        font-size: 12px;
        padding: 4px 8px;
        width: fit-content;
      }

      > span:nth-child(2) {
        color: #FF4242;
        margin-left: 10px;
        font-size: 12px;
        font-weight: 500;
      }
    }

    .status {
      display: flex;
      justify-content: flex-end;
      font-size: 12px;
      margin: 2px 0;
    }

    .account {
      display: flex;
      justify-content: space-between;
      color: #333;
      align-items: center;

      > div:nth-child(1) {
        font-size: 14px;
        font-weight: 500;
      }

      > div:nth-child(2) {
        font-size: 18px;
        font-weight: 500;
      }
    }

    .date {
      display: flex;
      justify-content: space-between;
      align-items: center;
      color: #999;
      font-size: 12px;

      .total {
        > span {
          color: #437DF0;
        }
      }
    }
  }
`;
