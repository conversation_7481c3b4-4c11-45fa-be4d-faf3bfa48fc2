import * as React from "react";
import { observer } from "mobx-react";
import styled from "styled-components";
import { SingleOfflineTransferRecordPictureInfo } from "../../components/single-offline-transfer-record-info/pictureInfo";
import { SinglePaymentRecordInfo } from "../../components/single-payment-record-info";
import { NoGoods } from "../../components/no-goods/no-goods";
import { autowired } from "@classes/ioc/ioc";
import { $OfflineTransferRecordService } from "../../classes/service/$offline-transfer-record-service";
import { $ScrollFreezeTitleBarMV } from "./index-mv";
let viewer = null;
const options = {
  button: false,
  className: "viewer-imgList",
  // inline: true,
  toolbar: {
    oneToOne: false,
    prev: false,
    zoomIn: false,
    zoomOut: false,
    reset: false,
    play: false,
    next: false,
    rotateLeft: "large",
    rotateRight: "large",
    flipHorizontal: false,
    flipVertical: false,
  },
  ready() {
    // 2 methods are available here: "show" and "destroy".
    console.log("show destroy", viewer);
  },
  shown() {
    console.log("9 methods are available here: hide", "view", "prev", "next", "play", "stop", "full", "exit and destroy", viewer);
  },
  viewed() {
    console.log("All methods are available here except", viewer);
    // this.viewer.zoomTo(1).rotateTo(180);
  },
}

@observer
export class ScrollFreezeTitleBar extends React.Component<any, any> {
  @autowired($ScrollFreezeTitleBarMV)
  public $myMv: $ScrollFreezeTitleBarMV;
  constructor(props) {
    super(props);
    this.state = {
    };
  }

  public componentDidMount() {
    this.fetchTransferRecord();
    this.addStrollLister();
  }

  public addStrollLister = () => {
    $("#pageWrap").on("scroll", this.changeHeaderstatus);
  }
  public changeHeaderstatus = () => {
    const { isShowTransferAccount, isShowPaymentAccount } = this.$myMv;
    const arr = [{ isShow: isShowTransferAccount, obj: $("#transfer-list") }, {
      isShow: isShowPaymentAccount,
      obj: $("#pay-list"),
    }];
    this.calcPositionFixedFunc(arr);
  }
  public calcPositionFixedFunc = (arr) => {
    const scrollTop = $(document).scrollTop();
    let indexNumber = 0;
    try {
      arr.map((item, index) => {
        const { isShow, obj } = item;
        if (obj && obj.length) {
          const listHeaderHeight = $(obj).find(".list-header").height() + 1;
          if (isShow && scrollTop + indexNumber * listHeaderHeight >= $(obj).offset().top) {
            $(obj).find(".list-header").css({ position: "fixed", top: indexNumber * listHeaderHeight, zIndex: 1 });
            $(obj).css({ paddingTop: `${listHeaderHeight}px` });
          } else {
            $(obj).find(".list-header").css({ position: "relative", top: 0, zIndex: 0 });
            $(obj).css({ paddingTop: "0px" });
          }
          indexNumber = indexNumber + isShow ? 1 : 0;
        }
      });
    } catch (e) {
      console.log("--------", e);
    }
  }
  public fetchTransferRecord = () => {
    const params = {
      pageIndex: 0,
      pageSize: 10,
      paymentType:"bank_transfer"
    }
    this.$myMv.getOfflineTransferRecordList(params).then((data) => {
      const { todayCount, transferRecordList } = data || {};
      this.$myMv.todayCount = todayCount;
      this.$myMv.transferRecordList = transferRecordList;
      if (transferRecordList.length) {
        const viewerImgList = document.getElementsByClassName("record-picture");
        if (viewerImgList && viewerImgList.length > 0) {
          for (let i = 0; i < viewerImgList.length; i++) {
            const item = new Viewer(document.getElementsByClassName("record-picture")[i], options);
          }
        }
      }
    });
  }
  public changeTransferHeaderList = () => {
    this.$myMv.isShowTransferAccount = !this.$myMv.isShowTransferAccount;
    this.changeHeaderstatus();
  }
  public changePayHeaderList = () => {
    this.$myMv.isShowPaymentAccount = !this.$myMv.isShowPaymentAccount;
    this.changeHeaderstatus();
  }
  public render() {
    const { orderPaymentList, reConfirm, styled, isHaveBankTranfer } = this.props;
    const { isShowTransferAccount, isShowPaymentAccount, todayCount, transferRecordList } = this.$myMv;
    return (
      <OrderPop style={styled}>
        {
          isHaveBankTranfer && transferRecordList && transferRecordList.length > 0 &&
          <TransferList style={{ borderBottom: isShowTransferAccount ? "1px solid #D8D8D8" : "none" }}
                        id="transfer-list">
            <ListHeader
              className="list-header"
              onClick={this.changeTransferHeaderList}
            >
              <span>查看最近的线下转账记录</span>
              <i className={"scmIconfont scm-icon-jiantou-shang " + `${isShowTransferAccount ? "" : "jian-tou-xia"}`}/>
              <span>{todayCount > 0 ? `今天有 ${todayCount} 次 ` : ""}</span>
            </ListHeader>
            <div className="transfer-record-picture-list"
                 id="pictureList"
                 style={{ display: `${isShowTransferAccount ? "block" : "none"}` }}
            >
              {
                transferRecordList.map((item) => {
                  return <SingleOfflineTransferRecordPictureInfo
                    record={item}
                  />
                })
              }
            </div>
          </TransferList>
        }
        {
          orderPaymentList && orderPaymentList.length > 0 &&
          <PayList id="pay-list">
            <ListHeader onClick={this.changePayHeaderList} className="list-header">
              <span>查看订单的付款记录</span>
              <i className={"scmIconfont scm-icon-jiantou-shang " + `${isShowPaymentAccount ? "" : "jian-tou-xia"}`}/>
              <span>{orderPaymentList.length > 0 ? `共 ${orderPaymentList.length} 次` : ""}</span>
            </ListHeader>
            <Records style={{
              display: `${isShowPaymentAccount ? "block" : "none"}`,
            }}>
              {
                orderPaymentList ? orderPaymentList.length > 0 ?
                  orderPaymentList.map((orderPayment, index) => {
                    return (
                      <SinglePaymentRecordInfo
                        key={index}
                        record={orderPayment}
                        reConfirm={reConfirm}
                      />
                    );
                  }) : <NoGoods title="暂无付款记录"/> : null
              }
            </Records>
          </PayList>
        }
      </OrderPop>
    );
  }
}

const OrderPop = styled.div`// styled
  & {
    width: 100%;
    height: 100%;
    background-color: #fff;
    margin-top: 10px;
  }
`;
const PayList = styled.div`// styled
  & {
    width: 100%;
    background: #fff;
    font-size: 14px;
  }
`;
const TransferList = styled.div`// styled
  & {
    width: 100%;
    background: #fff;
    font-size: 14px;

    .transfer-record-picture-list {
      padding: 16px 16px 0;
    }
  }
`;

const ListHeader = styled.div`// styled
  & {
    background-color: #fff;
    width: 100%;
    height: 40px;
    padding: 0px 16px;
    font-size: 14px;
    border-bottom: 1px solid #D8D8D8;

    > span {
      display: inline-block;
      float: left;
      line-height: 40px;
      font-size: 13px;
      color: #2F2F2F;
    }

    > span:last-of-type {
      float: right;
      color: #666666;
    }

    > i {
      float: right;
      display: inline-block;
      width: 12px;
      height: 40px;
      line-height: 40px;
      font-size: 12px;
      margin-left: 10px;
    }

    .jian-tou-xia {
      transform: rotate(180deg)
    }
  }
`;
const Records = styled.div`// styled
  & {
    font-size: 14px;
    color: #2A2A2A;
    width: 100%;
    height: auto;
    background-color: #fff;
    padding: 0 16px 16px;
  }
`;

const Record = styled.div`// styled
  & {
    width: 100%;
    font-size: 12px;
    color: #333;
    border-bottom: 1px solid #D8D8D8;
    border-radius: 3px;
    margin-top: 10px;

    .RecordName {
      color: #666;
      font-size: 12px;
    }

    .RecordValue {
      color: #FF3030;
      font-size: 12px;
    }

    > p {
      margin-bottom: 10px;
    }

    > p:first-child {
      position: relative;

      > span:last-child {
        position: absolute;
        top: 0;
        right: 0;
        color: #999;
      }
    }

    > p:last-of-type {
      display: flex;

      span:first-of-type {
        white-space: nowrap;
      }

      span:nth-of-type(2) {
        flex: 1;
        word-wrap: break-word;
        min-width: 1px;
      }
    }
  }
`;
