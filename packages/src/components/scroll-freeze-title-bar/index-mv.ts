import { autowired, bean } from "@classes/ioc/ioc";
import { action, observable } from "mobx";
import { beanMapper } from "../../helpers/bean-helpers";
import { $OfflineTransferRecordService } from "../../classes/service/$offline-transfer-record-service";
@bean($ScrollFreezeTitleBarMV)
export class $ScrollFreezeTitleBarMV {
  @autowired($OfflineTransferRecordService)
  public $myService: $OfflineTransferRecordService;
  @observable public isShowPaymentAccount: boolean = false;
  @observable public isShowTransferAccount: boolean = false;
  @observable public payHeaderFixed: boolean = false;
  @observable public transferHeaderFixed: boolean = false;
  @observable public todayCount: number = 0;
  @observable public transferRecordList: any[] = [];

  @action
  public getOfflineTransferRecordList(params) {
    return this.$myService.getOfflineTransferRecordList(params);
  }
  @action
  public queryOldData(obj) {
    beanMapper(obj, this);
    console.log(this);
  }

  @action
  public clearMVData() {
    this.isShowPaymentAccount = false;
    this.isShowTransferAccount = false;
    this.payHeaderFixed = false;
    this.transferHeaderFixed = false;
    this.todayCount = 0;
    this.transferRecordList = [];
  }
}
