import { autowired, bean } from "@classes/ioc/ioc";
import { action, observable } from "mobx";
import { $AnnouncementService } from "@classes/service/$announcement-service";
import {$EnterType } from "../../classes/const/$enter-Type"

@bean(AnnouncementModelMv)
export class AnnouncementModelMv {
  @autowired($AnnouncementService)
  public $AnnouncementService: $AnnouncementService;

  @observable public isShowPage: boolean = false;

  @observable public title: string = "";

  @observable public content: string = "";

  @action
  public closeModal=()=>{
      this.isShowPage = false;
  }

  @action 
  public getSessionStorage=( enterType )=>{
      return sessionStorage.getItem( enterType )
  }

  @action
  public setSessionStorage=(enterType)=>{
    sessionStorage.setItem(enterType,"enterType_" + enterType)
  }

  @action 
   public removeSessionStorage=(enterType="ALL")=>{
     if(enterType === "ALL"){
       sessionStorage.removeItem($EnterType.ENTER_MALL);
       sessionStorage.removeItem($EnterType.ENTER_ORDER_SCHEME);

     }else{
      sessionStorage.removeItem(enterType)
     }
      
   }
   
  @action
  public getAnnouncementInfo(params) {
    this.$AnnouncementService.queryAnnouncementInfo(params).then((data) => {
        const {title,content }=data;
    //   console.log("---------------看看公告内容", data);
      if (JSON.stringify(data) !== "{}" && data.title) {
        this.isShowPage = true;
        this.title = title;
        this.content = content;
      }
    });
  }
}
