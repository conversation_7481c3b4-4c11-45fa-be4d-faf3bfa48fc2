import * as React from "react";
import { observer } from "mobx-react";
import * as _ from "lodash";
import styled from "styled-components";
import { AnnouncementModelMv } from "./announcement-model-mv";
import { autowired } from "@classes/ioc/ioc";

@observer
class AnnouncementModel extends React.Component<any, any> {
  @autowired(AnnouncementModelMv)
  public $myMv: AnnouncementModelMv;

  public constructor(props) {
    super(props);
    this.state = {
        isShow:true
    };
  }

  

  componentDidMount() {
    // console.log("---------props",this.props)
    //判断网页临时会话是否存在，若存在则不再弹出公告,若不存在，则建立会话
    const { enterType, isAutoNotice, orderSchemeId,orgIdList } = this.props;
    if(!this.$myMv.getSessionStorage(enterType)){
        this.$myMv.setSessionStorage(enterType)
        this.$myMv.getAnnouncementInfo({ enterType, isAutoNotice, orderSchemeId,orgIdList});
        
    }else{
        this.setState({isShow:false})
    }
  }

  public render() {
    const { title, content,isShowPage } = this.$myMv;
    const {isShow} = this.state;
    return isShow && isShowPage ? (
      <Modal>
        <ModalContent>
          <div className="title">
             <span>{title}</span>         
          </div>
          <div className="contentBox">
          <div className="content" dangerouslySetInnerHTML={{__html:content}}></div>
          </div>
        </ModalContent>
        <ModalFooter>
          <div className="closeModal" onClick={()=>this.$myMv.closeModal()}>×</div>
        </ModalFooter>
      </Modal>
    ) : null;
  }
}
export default AnnouncementModel;

const Modal = styled.div`
  & {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0,0,0,0.5);
    z-index: 100;
    /* overflow-y: auto;
    overflow-x: hidden; */
  }
`;
const ModalContent = styled.div`
  & {
    border-radius:16px;
    width: 90%;
    height:50%;
    background: #fff;
    margin: 30% auto 48px;

    overflow:hidden;
    .title {
        width:100%;
        height:48px; 
        background: #F2F2F2;
        line-height:48px;
        font-size:16px;
        text-align:center;
        font-family: SourceHanSansCN-Regular, SourceHanSansCN;
        font-weight: 400;
        color: #333333;
        
    }
    .contentBox{
      width:100%;
      height:calc(100% - 48px);
      overflow:auto;
        .content{
            width:100%;
            padding:24px;
            >div{
              margin-bottom:24px;
              padding-top:6px;
            }
            >p{
              /* margin-bottom:24px; */
              font-size: 13px;
              font-family: PingFangSC-Regular, PingFang SC;
              font-weight: 400;
              color: #333333;
              line-height: 24px;
            }
          .image-wrap{
            width:100%;
            >img{
              width:100%;
              height:auto;
            }
          }
          .video-wrap {
            width: 100%;

            > video {
              width: 100%;
            }
          }
        }
    }
  }
`;

const ModalFooter = styled.div`
    & {
          width:100%;
          height:48px;
          .closeModal{
            width:48px;
            height:48px;
            border-radius:50%;
            border:1px solid #FFF;
            line-height:42px;
            text-align:center;
            color:#FFF;
            font-size:36px;
            margin:0 auto;
            font-weight:300;
          }
    }

`
