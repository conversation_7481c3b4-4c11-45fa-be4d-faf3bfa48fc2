import { autowired, bean } from "@classes/ioc/ioc";
import { observable, action} from "mobx";
import { $MyInfoService } from "../../classes/service/$my-info-service";

@bean(RechargeRecordMv)
export class RechargeRecordMv {

  @autowired($MyInfoService)
  public $myInfoService: $MyInfoService;

  @observable public rechargeRecordList: any[] = [];
  @action
  public loadRechargeRecord(params) {
    return this.$myInfoService.loadRechargeRecord(params);
  }
  @action
  public setRechargeRecordList(list, pageIndex) {
    this.rechargeRecordList = pageIndex ? this.rechargeRecordList.concat(list) : list;
    return this.rechargeRecordList.length;
  }
}
