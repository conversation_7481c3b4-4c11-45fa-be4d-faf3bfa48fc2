import { autowired } from "@classes/ioc/ioc";
import { observer } from "mobx-react";
import * as React from "react";
import { withRouter } from "react-router";
import styled from "styled-components";
import { Spin } from "antd";
import { ScrollAbilityWrapComponent } from "../../components/scroll-ability/wrap";
import { RechargeRecordMv } from "./recharge-record-mv";
import { LoadingTip } from "../../components/loading-marked-words";
import { NoGoods } from "../../components/no-goods/no-goods";
import { formatAmount } from '../../classes/utils/FormatAmount';

declare let require: any;
@withRouter
@observer
class RechargeRecordWrapper extends React.Component<any, any> {

  @autowired(RechargeRecordMv)
  public $myMv: RechargeRecordMv;

  constructor(props) {
    super(props);
    this.state = {
      finished: true,
      isShow: false,
      isSpinning: true,
      pageIndex: 0,
      pageSize: 20,
      refundOrderOid: "",
    };
  }

  public componentDidMount() {
    document.title = "充值记录";
    this.loadRechargeRecord();
  }

  public componentWillReceiveProps(nextProps: Readonly<any>, nextContext: any): void {
    const { isScrollEnd } = nextProps;
    console.log(isScrollEnd, this.props.isScrollEnd);
    if (isScrollEnd && isScrollEnd !== this.props.isScrollEnd) {
      this.loadData();
    }
  }

  public loadRechargeRecord = () => {
    this.setState({ isSpinning: true })
    const { pageIndex, pageSize } = this.state;
    const params = { pageIndex, pageSize };
    this.$myMv.loadRechargeRecord(params).then((res) => {
      const { rechargeList, itemCount } = res;
      const rechargeListLength = this.$myMv.setRechargeRecordList(rechargeList, pageIndex);
      console.log(rechargeListLength, itemCount);
      this.setState({ isSpinning: false, pageIndex: pageIndex + 1, finished: rechargeListLength >= itemCount });
      const { loadingEnd } = this.props;
      loadingEnd && loadingEnd(rechargeListLength >= itemCount);
    }).catch((err) => {
      this.setState({ isSpinning: false });
    });
  }

  public loadData = () => {
    const { finished, isSpinning } = this.state;
    console.log(finished, isSpinning);
    if (finished || isSpinning) {
      return;
    }
    this.loadRechargeRecord();
  }
  public render() {
    const { finished, isShow, isSpinning } = this.state;
    const { rechargeRecordList } = this.$myMv;
    console.log(this.state);
    return (
      <RechargeRecordPage className="recharge-record-page">
        <Spin spinning={isSpinning}>
          <RechargeRecordContent className="recharge-record-content">
            {
              rechargeRecordList.length > 0 ?
                rechargeRecordList.map((item) => {
                  const { oid, paymentModeName, amount, rechargeTime, docStatusName } = item ;
                  return <div key={oid} className="recharge-record-item">
                    <div>
                      <span className="paymentModeName">{paymentModeName}</span>
                      <span className="amount">{formatAmount(amount)}</span>
                    </div>
                    <div>
                      <span className="rechargeTime">{rechargeTime}</span>
                      <span className="docStatusName">{docStatusName}</span>
                    </div>
                  </div>;
              }) : <NoGoods title="暂无充值记录"/>
            }
            {
              rechargeRecordList && rechargeRecordList.length > 0 &&
							<LoadingTip
								isFinished={finished}
								isLoad={isShow}
							/>
            }
          </RechargeRecordContent>

        </Spin>
      </RechargeRecordPage>
    );
  }
}

const RechargeRecord = ScrollAbilityWrapComponent(RechargeRecordWrapper);
export default RechargeRecord;

const RechargeRecordContent = styled.div`// styled
  & {
    width: 100%;
    height: auto;
    .recharge-record-item{
      width: 100%;
      height: 56px;
      border-bottom: 1px solid #D8D8D8;
      >div:nth-of-type(1) {
         height: 34px;
         line-height: 34px;
         width: 100%;
         padding: 0 16px;
         color: #2F2F2F;
         .paymentModeName {
           float: left;
         }
         .amount{
           float: right;
         }
       }
      >div:nth-of-type(2) {
        height: 12px;
        width: 100%;
        padding: 0 16px;
        color: #595959;
        font-size: 12px;
        line-height: 12px;
        .rechargeTime {
          float: left;
        }
        .docStatusName{
          float: right;
          color: #FF3030;
        }
      }
    }
  }
`;

const RechargeRecordPage = styled.div`// styled
  & {
    width: 100%;
    height: auto;
  }
`;
