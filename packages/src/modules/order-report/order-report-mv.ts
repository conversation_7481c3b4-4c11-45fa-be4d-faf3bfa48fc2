import { autowired, bean } from "@classes/ioc/ioc";
import { action, observable } from "mobx";
import { $OrderReportService } from "../../classes/service/$order-report-service";
import { Toast } from "antd-mobile";
import { $SelectItemStatus } from "../../classes/entity/$select-item-status";
import { $SelectOrderparty } from "../../classes/entity/$select-orderparty";
import { $Statistics } from "../../classes/entity/$statistics";
import { $OrderItem } from "../../classes/entity/$order-item";

@bean($OrderReportMv)
export class $OrderReportMv {
  @autowired($OrderReportService)
  public $orderReportService: $OrderReportService;

  @observable public isSpin: boolean = false;

  @observable public allOrderNum: number;

  @observable public allOweNum: number;

  @observable public allOnTheWayNum: number;

  @observable public itemStatusList: $SelectItemStatus[] = [];

  @observable public orderPartyList: $SelectOrderparty[] = [];

  @observable public orderItemList: $OrderItem[] = [];

  @observable public statistics: $Statistics;

  @observable public page: number = 0;

  @observable public isShowSpin: boolean = false;

  @observable public isOwe: string = "N";

  @observable public isOnTheWay: string = "N";

  @action
  public showSpin() {
    this.isSpin = true;
  }

  @action
  public hideSpin() {
    this.isSpin = false;
  }

  @action
  public isShowSpinTrue() {
    this.isShowSpin = true;
  }

  @action
  public isShowSpinFalse() {
    this.isShowSpin = false;
  }

  @action
  public changePage() {
    this.page++;
  }

  @action
  public clearPage() {
    this.page = 0;
  }

  @action
  public clearItemStatusList() {
    this.itemStatusList = [];
  }

  @action
  public clearOrderPartyList() {
    this.orderPartyList = [];
  }

  @action
  public clearOrderItemList() {
    this.orderItemList = [];
  }

  @action
  public setSelectItemStatus(itemStatus) {
    this.itemStatusList.map((status, index) => {
      if (status.value === itemStatus) {
        status.checked = !status.checked;
        status.showActiveStatus = !status.showActiveStatus;
      }
    });
  }

  @action
  public setSelectOrderParty(orderParty) {
    this.orderPartyList.map((status, index) => {
      if (status.oid === orderParty) {
        status.checked = !status.checked;
        status.showActiveOrderparty = !status.showActiveOrderparty;
      }
    });
  }

  @action
  public orderreportStatistics() {
    this.showSpin();
    this.$orderReportService.orderreportStatistics().then((data) => {
      this.hideSpin();
      this.allOnTheWayNum = data.allOnTheWayNum;
      this.allOrderNum = data.allOrderNum;
      this.allOweNum = data.allOweNum;
    }).catch((err) => {
      console.log(err);
      this.hideSpin();
      Toast.fail(err.response.body.message);
    });
  }

  @action
  public salesorderItemstatuslist() {
    this.showSpin();
    this.$orderReportService.salesorderItemstatuslist().then((data) => {
      this.itemStatusList = data.itemStatusList.map((item) => new $SelectItemStatus(item));
    });
  }

  @action
  public orderpartyList(params) {
    this.$orderReportService.orderpartyList(params).then((data) => {
      this.orderPartyList = data.orderPartyList.map((party) => new $SelectOrderparty(party));
    });
  }

  @action
  public orderreportSearch(params) {
    return new Promise((resolve) => {
      this.$orderReportService.orderreportSearch(params).then((data) => {
        const { orderItemList, statistics, itemCount } = data;
        this.hideSpin();
        this.isShowSpinFalse();
        this.orderItemList = this.orderItemList.concat(orderItemList.map((order) => new $OrderItem(order)));
        this.statistics = new $Statistics(statistics);
        resolve(this.orderItemList.length >= itemCount);
      });
    });
  }
}
