import { observer } from "mobx-react";
import { autowired } from "@classes/ioc/ioc";
import React from "react";
import styled from "styled-components";
import { withRouter } from "react-router";
import { $OrderReportMv } from "./order-report-mv";
import { Spin } from "antd";
import { DatePicker, SearchBar } from "antd-mobile";
import { NoGoods } from "../../components/no-goods/no-goods";
import { GoHome } from "../../components/go-home/go-home";
import { ScrollAbilityWrapComponent } from "../../components/scroll-ability/wrap";
import { LoadingTip } from "../../components/loading-marked-words";

const start = new Date(new Date().getFullYear() + "/1/1");
const end = new Date(Date.now());

@withRouter
@observer
class OrderReportWrap extends React.Component<any, any> {

  @autowired($OrderReportMv)
  public $orderReportMv: $OrderReportMv;

  public constructor(props) {
    super(props);
    this.state = {
      startDate: start,
      endDate: end,
      searchValue: "",
      showOrderPop: false,
      startx: 0,
      starty: 0,
      isFinished: false,
    };
  }

  public componentDidMount() {
    this.$orderReportMv.clearPage();
    this.$orderReportMv.clearItemStatusList();
    this.$orderReportMv.clearOrderItemList();
    this.$orderReportMv.clearOrderPartyList();
    this.$orderReportMv.orderreportStatistics();
    this.$orderReportMv.salesorderItemstatuslist();
    this.$orderReportMv.orderpartyList({ pageIndex: 0, pageSize: 99999999 });
    this.$orderReportMv.showSpin();
    this.orderreportSearch(0);
  }
  public componentWillReceiveProps(nextProps: Readonly<any>, nextContext: any): void {
    const { isScrollEnd } = nextProps;
    console.log(isScrollEnd);
    if (isScrollEnd && isScrollEnd !== this.props.isScrollEnd && !this.state.showOrderPop) {
      this.loadData();
    }
  }
  public changeItemStatus = (val) => {
    this.$orderReportMv.setSelectItemStatus(val);
  }

  public onChangeSearchBar = (val) => {
    console.log(val);
    this.setState({
      searchValue: val,
    });
  }

  // public submitSearchBar = (val) => {
  //   console.log(val);
  //   const params = {
  //     fuzzyQuery: val,
  //     currentPage: 0,
  //     pageSize: 10,
  //   };
  //   this.$orderReportMv.orderreportSearch(params);
  // }

  public changeOrderParty = (oid) => {
    this.$orderReportMv.setSelectOrderParty(oid);
  }

  public orderreportSearch = (page) => { // 点击确定搜索
    const { itemStatusList, orderPartyList, isOwe, isOnTheWay } = this.$orderReportMv;
    const { searchValue, startDate, endDate } = this.state;
    const params = {
      startTime: startDate.getFullYear() + "-" + (startDate.getMonth() + 1) + "-" + startDate.getDate(),
      endTime: endDate.getFullYear() + "-" + (endDate.getMonth() + 1) + "-" + endDate.getDate(),
      orderItemStatusList: itemStatusList.filter((status) => status.checked).map((item) => item.value),
      orgIdList: orderPartyList.filter((party) => party.checked).map((lis) => lis.oid),
      isOwe,
      isOnTheWay,
      fuzzyQuery: searchValue,
      currentPage: page,
      pageSize: 10,
    };
    if (page === 0) {
      this.$orderReportMv.clearOrderItemList();
      this.$orderReportMv.clearPage();
    }
    this.$orderReportMv.orderreportSearch(params).then((boolen) => {
      this.$orderReportMv.changePage();
      this.setState({
        isFinished: boolen,
        showOrderPop: false,
      });
      const { loadingEnd } = this.props;
      loadingEnd && loadingEnd(boolen);
    });
  }

  public OnTheWayNum = () => {
    if (this.$orderReportMv.isOnTheWay === "Y") {
      this.$orderReportMv.isOnTheWay = "N";
    } else {
      this.$orderReportMv.isOnTheWay = "Y";
    }
    this.$orderReportMv.clearOrderItemList();
    this.$orderReportMv.showSpin();
    this.orderreportSearch(0);
  }

  public OweNum = () => {
    if (this.$orderReportMv.isOwe === "Y") {
      this.$orderReportMv.isOwe = "N";
    } else {
      this.$orderReportMv.isOwe = "Y";
    }
    this.$orderReportMv.clearOrderItemList();
    this.$orderReportMv.showSpin();
    this.orderreportSearch(0);
  }

  public renderColor = (itemStatusCode) => {
    let color;
    switch (itemStatusCode) {
      case "WAIT_DELIVER" :
        color = "#FF3030";
        break;
      case "DELIVER_PART" :
        color = "#307DCD";
        break;
      case "RECEIVED_ALL" :
        color = "#52C41A";
        break;
      case "CANCELED" :
        color = "#999999";
        break;
      case "DELIVER_PART_CANCELED" :
        color = "#13C2C2";
        break;
      case "RECEIVED_PART_CANCELED" :
        color = "#9254DE";
        break;
      case "DELIVER_ALL" :
        color = "#FAAD14";
        break;
      default:
        break;
    }
    return color;
  }

  public showOrderPop = () => {
    this.setState({
      showOrderPop: true,
    });
  }

  public loadData = () => {
    const { showOrderPop, isFinished } = this.state;
    if (showOrderPop || isFinished) {
      return;
    }
    const { page } = this.$orderReportMv;
    this.$orderReportMv.isShowSpinTrue();
    this.orderreportSearch(page);
  }

  public canclePop = () => {
    // document.getElementById("shopList").scrollTop = 0;
    // const { itemStatusList, orderPartyList } = this.$orderReportMv;
    this.setState({
      // searchValue: "",
      showOrderPop: false,
    });
    // this.$orderReportMv.clearPage();
    // itemStatusList.map((status) => {
    //   status.checked = false;
    //   status.showActiveStatus = false;
    // });
    // orderPartyList.map((status) => {
    //   status.checked = false;
    //   status.showActiveOrderparty = false;
    // });
  }

  public clearData = () => {
    const { itemStatusList, orderPartyList } = this.$orderReportMv;
    this.setState({
      searchValue: "",
    });
    this.$orderReportMv.clearPage();
    itemStatusList.map((status) => {
      status.checked = false;
      status.showActiveStatus = false;
    });
    orderPartyList.map((status) => {
      status.checked = false;
      status.showActiveOrderparty = false;
    });
    this.orderreportSearch(0);
  }

  public changeDate = () => {
    setTimeout(() => {
      this.orderreportSearch(0);
    }, 0);
  }

  public render() {
    const { isSpin, allOnTheWayNum, allOweNum, allOrderNum, itemStatusList, orderPartyList, statistics, orderItemList, isShowSpin, isOnTheWay, isOwe } = this.$orderReportMv;
    const { startDate, endDate, searchValue, showOrderPop, isFinished } = this.state;
    console.log(showOrderPop);
    return (
      <OrderReportModel className="orderPage" style={{ position: showOrderPop ? "fixed" : "relative" }}
                        theme={{ isHaveData: orderItemList }}>
        <Spin spinning={isSpin}>
          <OrderReportHeader>
            <div>
              <div>
                <p>
                  <i className="scmIconfont scm-icon-qianshui"/>
                </p>
                <p>
                  <p>欠数（件）</p>
                  <p>{allOweNum}</p>
                </p>
              </div>
              <div>
                <p>
                  <i className="scmIconfont scm-icon-fahuo"/>
                </p>
                <p>
                  <p>在途（件）</p>
                  <p>{allOnTheWayNum}</p>
                </p>
              </div>
            </div>
          </OrderReportHeader>
          <OrderSearch>
            <div>
              <div>
                <DatePicker
                  mode="date"
                  title="开始日期"
                  extra="Optional"
                  value={startDate}
                  onChange={startDate => this.setState({ startDate })}
                  onOk={this.changeDate}
                >
              <span>
                {startDate ?
                  `${startDate.getFullYear()}年${(startDate.getMonth() + 1)}月${startDate.getDate()}日`
                  : "开始日期"}
               </span>
                </DatePicker>
                <span>～</span>
                <DatePicker
                  mode="date"
                  title="结束日期"
                  extra="Optional"
                  value={endDate}
                  onChange={endDate => this.setState({ endDate })}
                  onOk={this.changeDate}
                >
              <span>
                {endDate ?
                  `${endDate.getFullYear()}年${(endDate.getMonth() + 1)}月${endDate.getDate()}日`
                  : "结束日期"}
               </span>
                </DatePicker>
                <span className="searchMargin"><i className="scmIconfont scm-icon-xiajiantou"/></span>
              </div>
              <div>
              <span className="searchMargin" onClick={this.showOrderPop}>筛选<i
                className="scmIconfont scm-icon-xiajiantou"/></span>
                <span
                  onClick={this.OweNum}
                  className="searchMargin"
                  style={{ color: isOwe === "Y" ? "#307DCD" : "#757575" }}
                >
              看欠数
            </span>
                <span
                  onClick={this.OnTheWayNum}
                  style={{ color: isOnTheWay === "Y" ? "#307DCD" : "#757575" }}
                >
              看在途
            </span>
              </div>
            </div>
          </OrderSearch>
          <OrderReportTitle>
            <div>
              <span>账单明细</span>
              <div>
              <span>
              <span className="name">订货数：</span>
              <span className="value">{statistics && statistics.allOrderNum}</span>
            </span>
                <span>
              <span className="name">欠货数：</span>
              <span className="value">{statistics && statistics.allOweNum}</span>
            </span>
                <span>
              <span className="name">在途：</span>
              <span className="value" style={{ marginRight: 0 }}>{statistics && statistics.allOnTheWayNum}</span>
            </span>
              </div>
            </div>
          </OrderReportTitle>
          <OrderReportContent>
            {
              orderItemList.length > 0 ? orderItemList.map((item, index) => {
                return (
                  <OrderContent key={index}>
                    <p>
                      <span className="text">订单号：</span>
                      <span className="val">{item.orderNo}</span>
                    </p>
                    <p>
                      <span className="text">下单门店：</span>
                      <span className="val">{item.orgName}</span>
                    </p>
                    <p>
                      <span className="text">订货方案：</span>
                      <span
                        className="val">{item.orderSchemeName.length > 20 ? item.orderSchemeName.slice(0, 20) + "..." : item.orderSchemeName}</span>
                    </p>
                    <p>
                      <span className="text">商&emsp;&emsp;品：</span>
                      <span className="val">
                        <span>{item.skuCode.length + item.skuName.length > 20 ? (item.skuCode + "  " + item.skuName).slice(0, 20) + "..." : item.skuCode + "  " + item.skuName}</span>
                      </span>
                    </p>
                    <p>
                      <span className="text">订货：</span>
                      <span className="val" style={{ paddingRight: "16px" }}>{item.orderNum}</span>
                      <span className="text">欠数：</span>
                      <span className="val" style={{ paddingRight: "16px" }}>{item.oweNum}</span>
                      <span className="text">在途：</span>
                      <span className="val">{item.onTheWayNum}</span>
                      {
                        item.itemStatusName && item.itemStatusName.length > 4 ? <span
                          className="red"
                          style={{ color: this.renderColor(item.itemStatusCode), bottom: "10px" }}>
                        {item.itemStatusName && item.itemStatusName.slice(0, 4)}
                          <br/>
                          {item.itemStatusName && item.itemStatusName.slice(4)}
                      </span> : <span
                          className="red"
                          style={{ color: this.renderColor(item.itemStatusCode) }}>
                        {item.itemStatusName}
                      </span>
                      }
                    </p>
                  </OrderContent>
                );
              }) : <NoGoods title="暂无订单" className="noGoods" height={document.documentElement.clientHeight - 230}/>
            }
            {
              orderItemList.length > 0 ?
                <LoadingTip
                  isFinished={isFinished}
                  isLoad={isShowSpin}
                />
                :
                <div/>
            }
          </OrderReportContent>
          <OrderPop style={{ display: showOrderPop ? "block" : "none" }}>
            <PopLeft onClick={this.canclePop}/>
            <PopRight>
              <SearchBar
                value={searchValue}
                placeholder="订单号，门店，订货方案，商品名"
                onSubmit={() => this.orderreportSearch(0)}
                onChange={this.onChangeSearchBar}
              />
              <SelectStatus>
                <p>
                  快捷筛选
                </p>
                <p>
                  {
                    itemStatusList.map((item, index) => {
                      return (
                        <span
                          key={index}
                          onClick={() => this.changeItemStatus(item.value)}
                          className={item.checked ? "activeStatus" : ""}>
                          {
                            item.text && item.text.length > 4 ? <span style={{ position: "relative", top: "2px" }}>
                              {item.text && item.text.slice(0, 4)}
                              <br/>
                              {item.text && item.text.slice(4)}
                            </span> : <span style={{ lineHeight: "32px" }}>
                              {item.text}
                            </span>
                          }
                          <span style={{ display: item.showActiveStatus ? "block" : "none" }}>
                          <i className="scmIconfont scm-icon-guanbi"/>
                        </span>
                  </span>
                      );
                    })
                  }
                </p>
              </SelectStatus>
              <SelectParty>
                <div>
                  筛选门店
                </div>
                <div id="shopList">
                  {
                    orderPartyList.length > 0 ? orderPartyList.map((item, index) => {
                      return (
                        <p
                          key={index}
                          onClick={() => this.changeOrderParty(item.oid)}
                          className={item.checked ? "activeOrderParty" : ""}
                        >
                          <i className="scmIconfont scm-icon-shop"/>
                          <span>
                            {item.name && item.name.length > 17 ? item.name.slice(0, 17) + "..." : item.name}
                            <span style={{ display: item.showActiveOrderparty ? "block" : "none" }}>
                          <i className="scmIconfont scm-icon-guanbi"/>
                        </span>
                          </span>
                        </p>
                      );
                    }) : <p>暂无门店</p>
                  }
                </div>
              </SelectParty>
              <PopButton>
                <span onClick={this.clearData}>重置</span>
                <span onClick={() => this.orderreportSearch(0)}>确定</span>
              </PopButton>
            </PopRight>
          </OrderPop>
        </Spin>
        <GoHome/>
      </OrderReportModel>
    );
  }
}

const OrderReport = ScrollAbilityWrapComponent(OrderReportWrap);
export default OrderReport;

const OrderReportModel = styled.div`// styled
  & {
    width: 100%;
    height: ${(props) => props.theme.isHaveData && props.theme.isHaveData.length > 0 ? "auto" : document.documentElement.clientHeight + "px"};
    background: rgba(236, 246, 255, 1);
  }
`;

const OrderReportHeader = styled.div`// styled
  & {
    width: 100%;
    height: 137px;
    background: rgba(236, 246, 255, 1);
    position: fixed;
    z-index: 98;
    > div {
      width: calc(100% - 16px - 16px);
      margin: 16px;
      background: url("https://order.fwh1988.cn:14501/static-img/scm/icon-card-bg.png");
      padding: 27px 32px;
      border-radius: 8px;
      box-shadow: 0px 0px 3px 0px rgba(0, 0, 0, 0.05);
      > div {
        display: inline-block;
        width: calc((100% - 16px - 16px) / 2);
        > p {
          display: inline-block;
          margin-bottom: 0;
          > p {
            margin-bottom: 0;
          }
        }
        > p:nth-of-type(1) {
          width: 44px;
          height: 44px;
          line-height: 44px;
          text-align: center;
          border: 1px solid rgba(48, 125, 205, 1);
          border-radius: 22px;
          margin-right: 9px;
          position: relative;
          top: -6px;
          > i {
            color: #307DCD;
            font-size: 28px;
          }
        }
        > p:nth-of-type(2) {
          > p:nth-of-type(1) {
            font-size: 12px;
            font-family: "MicrosoftYaHei";
            color: rgba(102, 102, 102, 1);
          }
          > p:nth-of-type(2) {
            font-size: 20px;
            font-family: "MicrosoftYaHei";
            color: rgba(48, 125, 205, 1);
          }
        }
      }
      > div:nth-of-type(2) {
        float: right;
      }
    }
  }
`;

const OrderReportTitle = styled.div`// styled
  & {
    width: 100%;
    min-height: 40px;
    background: rgba(236, 246, 255, 1);
    margin: 193px 16px 0 16px;
    position: fixed;
    z-index: 98;
    :after {
      content: '';
      position: absolute;
      background-color: #D8D8D8 !important;
      display: block;
      z-index: 1;
      top: auto;
      right: auto;
      bottom: 0;
      left: 0;
      width: calc(100% - 16px - 16px);
      height: 1px;
      transform-origin: 50% 100%;
      transform: scaleY(0.5);
    }
    > div {
      width: calc(100% - 16px - 16px);
      min-height: 40px;
      padding: 10px 16px;
      background: #FFFFFF;
      border-radius: 8px 8px 0px 0px;
      > div {
        display: inline-block;
        width: calc(100% - 52px - 15px);
        text-align: right;
      }
      > span:nth-of-type(1) {
        font-size: 13px;
        font-family: "MicrosoftYaHei";
        color: rgba(51, 51, 51, 1);
        margin-right: 15px;
      }
      .name {
        font-size: 12px;
        font-family: "MicrosoftYaHei";
        color: rgba(51, 51, 51, 1);
      }
      .value {
        font-size: 12px;
        font-family: "MicrosoftYaHei";
        color: #307DCD;
        margin-right: 6px;
      }
    }
  }
`;

const OrderReportContent = styled.div`// styled
  & {
    width: calc(100% - 16px - 16px);
    // height: calc(${document.documentElement.clientHeight - 230}px);
    // overflow-y: scroll;
    background: #FFFFFF;
    margin: 230px 16px 0 16px;
  }
`;

const OrderContent = styled.div`// styled
  & {
    width: calc(100% - 16px);
    min-height: 170px;
    margin-left: 16px;
    padding: 12px 16px 12px 0;
    position: relative;
    :after {
      content: '';
      position: absolute;
      background-color: #D8D8D8 !important;
      display: block;
      z-index: 1;
      top: auto;
      right: auto;
      bottom: 0;
      left: 0;
      width: 100%;
      height: 1px;
      transform-origin: 50% 100%;
      transform: scaleY(0.5);
    }
    > p {
      margin-bottom: 10px;
    }
    .text {
      font-size: 12px;
      font-family: "MicrosoftYaHei";
      color: #757575;
    }
    .val {
      font-size: 12px;
      font-family: "MicrosoftYaHei";
      color: #333333;
    }
    .red {
      font-size: 12px;
      font-family: "MicrosoftYaHei";
      color: rgba(255, 48, 48, 1);
      position: absolute;
      right: 16px;
      bottom: 22px;
    }
  }
`;

const OrderSearch = styled.div`// styled
  & {
    width: 100%;
    min-height: 56px;
    background: rgba(236, 246, 255, 1);
    position: fixed;
    z-index: 98;
    margin: 137px 16px 0 16px;
    > div {
      width: calc(100% - 16px - 16px);
      min-height: 40px;
      background: rgba(255, 255, 255, 1);
      border-radius: 8px;
      padding: 8px 16px;
      .searchMargin {
        margin-right: 10px;
      }
      .scm-icon-xiajiantou {
        position: relative;
        font-size: 8px;
      }
      > div:nth-of-type(1) {
        display: inline-block;
        //margin-right: 6px;
        width: 60%;
        > div {
          display: inline-block;
          > span {
            font-size: 11px;
            font-family: "MicrosoftYaHei";
            color: rgba(117, 117, 117, 1);
          }
        }
      }
      > div:nth-of-type(2) {
        display: inline-block;
        width: 40%;
        text-align: right;
        > span {
          font-size: 11px;
          font-family: "MicrosoftYaHei";
          color: rgba(117, 117, 117, 1);
        }
      }
    }
  }
`;

const OrderPop = styled.div`// styled
  & {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 99;
    .am-search {
      height: 60px;
      background-color: #fff;
      padding: 0;
      position: relative;
      :after {
        content: '';
        position: absolute;
        background-color: #D8D8D8 !important;
        display: block;
        z-index: 1;
        top: auto;
        right: auto;
        bottom: 0;
        left: 0;
        width: 100%;
        height: 1px;
        transform-origin: 50% 100%;
        transform: scaleY(0.5);
      }
    }
    .am-search-input {
      margin: 16px 8px 16px 8px;
      background: #F4F4F4;
      border-radius: 13px;
    }
    //.am-search-cancel {
    //  line-height: 72px;
    //}
    .am-search-input .am-search-synthetic-ph-placeholder {
      font-size: 13px;
      font-family: "SourceHanSansCN-Normal";
      font-weight: 400;
      color: rgba(153, 153, 153, 1);
    }
  }
`;

const PopLeft = styled.div`// styled
  & {
    display: inline-block;
    width: calc(100% - 272px);
    height: 100%;
    background: rgba(0, 0, 0, 0.3);
  }
`;

const PopRight = styled.div`// styled
  & {
    display: inline-block;
    width: 272px;
    height: 100%;
    background: #fff;
    z-index: 99;
    position: fixed;
    .am-search-clear-show {
      top: 6px;
      right: 6px;
    }
  }
`;

const SelectStatus = styled.div`// styled
  & {
    width: 272px;
    height: 172px;
    position: relative;
    padding: 16px 0px 8px 8px;
    :after {
      content: '';
      position: absolute;
      background-color: #D8D8D8 !important;
      display: block;
      z-index: 1;
      top: auto;
      right: auto;
      bottom: 0;
      left: 0;
      width: 100%;
      height: 1px;
      transform-origin: 50% 100%;
      transform: scaleY(0.5);
    }
    > p:first-child {
      font-size: 12px;
      font-family: "SourceHanSansCN-Normal";
      font-weight: 400;
      color: rgba(117, 117, 117, 1);
    }
    > p:last-child {
      > span {
        display: inline-block;
        width: 80px;
        height: 32px;
        // line-height: 32px;
        text-align: center;
        background: rgba(244, 244, 244, 1);
        border-radius: 4px;
        margin-right: 8px;
        font-size: 10px;
        font-family: "SourceHanSansCN-Normal";
        font-weight: 400;
        color: rgba(51, 51, 51, 1);
        margin-bottom: 8px;
        float: left;
      }
      .activeStatus {
        color: #307DCD;
        background: rgba(48, 125, 205, 0.1);
        position: relative;
        > span:last-child {
          display: block;
          width: 0;
          height: 0;
          border-bottom: 20px solid #307DCD;
          border-left: 20px solid transparent;
          position: absolute;
          bottom: 0;
          right: 0;
          border-bottom-right-radius: 0 4px;
          > i {
            font-size: 5px;
            color: #fff;
            position: absolute;
            right: 5px;
            top: 10px;
          }
        }
      }
    }
  }
`;

const SelectParty = styled.div`// styled
  & {
    width: 272px;
    height: auto;
    padding: 16px 8px;
    > div:first-child {
      font-size: 12px;
      font-family: "SourceHanSansCN-Normal";
      font-weight: 400;
      color: rgba(117, 117, 117, 1);
    }
    > div:last-child {
      width: 272px;
      height: 400px;
      overflow-y: scroll;
      -webkit-overflow-scrolling: touch;
      margin-top: 16px;
      padding-bottom: 200px;
      > p {
        width: 256px;
        height: 32px;
        background: rgba(244, 244, 244, 1);
        border-radius: 4px;
        padding: 5px 8px;
        font-size: 12px;
        font-family: "SourceHanSansCN-Normal";
        font-weight: 400;
        color: rgba(51, 51, 51, 1);
        > i {
          margin-right: 8px;
        }
      }
      .activeOrderParty {
        color: #307DCD;
        background: rgba(48, 125, 205, 0.1);
        position: relative;
        > span span {
          display: block;
          width: 0;
          height: 0;
          border-bottom: 20px solid #307DCD;
          border-left: 20px solid transparent;
          position: absolute;
          bottom: 0;
          right: 0;
          border-bottom-right-radius: 0 4px;
          > i {
            font-size: 5px;
            color: #fff;
            position: absolute;
            right: 2px;
            top: 10px;
          }
        }
      }
    }
  }
`;

const PopButton = styled.div`// styled
  & {
    width: 272px;
    height: 48px;
    position: fixed;
    bottom: 0;
    padding: 6px 12px;
    text-align: right;
    border-top: 1px solid #D8D8D8;
    z-index: 100;
    background: #fff;
    > span {
      font-size: 14px;
      font-family: "SourceHanSansCN-Normal";
      font-weight: 400;
      color: rgba(255, 255, 255, 1);
    }
    > span:first-child {
      display: inline-block;
      width: 88px;
      height: 36px;
      background: rgba(82, 196, 26, 1);
      border-radius: 20px 0px 0px 20px;
      padding: 10px 30px;
    }
    > span:last-child {
      display: inline-block;
      width: 88px;
      height: 36px;
      background: rgba(48, 125, 205, 1);
      border-radius: 0px 20px 20px 0px;
      padding: 10px 30px;
    }
  }
`;
