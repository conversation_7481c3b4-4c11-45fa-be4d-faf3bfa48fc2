import { autowired, bean } from "@classes/ioc/ioc";
import { action, computed, observable } from "mobx";
import { $MyInfoService } from "../../classes/service/$my-info-service";
import { $GiftAccountTabType } from "@classes/const/$account-type";
import { min } from "lodash";

@bean($Transfer)
export class $Transfer {
  @autowired($MyInfoService)
  public $myInfoService: $MyInfoService;

  @observable public hfpzInfo: any = {};
  @observable public pzedInfo: any = {};
  @observable public transferInAccountList: any = [];
  @observable public transferOutOrgInfo: any = {};
  @observable public transferInOrgInfo: any = {};

  @observable public isSpin: boolean = false;
  @observable public isShowStoreList: boolean = false;

  @computed
  get estimatedAmount() {
    if (!this.transferInOrgInfo.transferInOrgId) {
      return "";
    }
    return min([
      Number(this.transferInOrgInfo.hfpzAmount || 0) + Number(this.hfpzInfo.amount || 0),
      Number(this.transferInOrgInfo.pzAmount || 0) + Number(this.pzedInfo.amount || 0),
      ]);
  }

  @action
  public showSpin() {
    this.isSpin = true;
  }

  @action
  public hideSpin() {
    this.isSpin = false;
  }

  @action
  public fetchTransfer(params) {
    this.$myInfoService.loadGiftTransferPage(params).then((data) => {
      this.transferInAccountList = data.transferInOrgList;
      const hfpzInfo = data.transferOutAccountList.find((item) => item.accountTypeCode === $GiftAccountTabType.HFPZ);
      this.hfpzInfo = {
        max: hfpzInfo.amount,
        ...hfpzInfo,
        amount: "",
      };
      const pzedInfo = data.transferOutAccountList.find((item) => item.accountTypeCode === $GiftAccountTabType.PZED);
      this.pzedInfo = {
        max: pzedInfo.amount,
        ...pzedInfo,
        amount: "",
      };
      this.transferOutOrgInfo = {
        transferOutOrg: data.transferOutOrg,
        transferOutOrgId: data.transferOutOrgId,
      };
      this.hideSpin();
    });
  }

  @action
  public submitTransfer(params) {
    return this.$myInfoService.toGiftTransfer(params);
  }

  @action.bound
  public changeStore(val) {
    this.transferInOrgInfo = {...val};
    this.isShowStoreList = false;
  }

  @action
  public clear() {
    this.hfpzInfo = {amount: ""};
    this.pzedInfo = {amount: ""};
    this.transferInAccountList = [];
    this.transferOutOrgInfo = {};
    this.transferInOrgInfo = {};
  }
}
