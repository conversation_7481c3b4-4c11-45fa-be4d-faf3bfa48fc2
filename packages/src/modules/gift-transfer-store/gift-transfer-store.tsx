import { autowired } from "@classes/ioc/ioc";
import { observer } from "mobx-react";
import { transaction } from "mobx";
import { withRouter } from "react-router";
import * as React from "react";
import { $Transfer } from "./gift-transfer-store-mv";
import styled from "styled-components";
import { ActivityIndicator, Button, List } from "antd-mobile";
import { Spin } from "antd";
import { Toast } from "antd-mobile/es";
import { $AppStore } from "@classes/stores/app-store-mv";
import { min } from "lodash";
import { GiftTransferStoreList } from "./gift-transfer-store-list";
import { isEmptySimple } from "@classes/utils/CommonUtils";
import { SITE_PATH } from "../app";

const IconTransferIn = require("../../components/svg/icon_transfer_in.svg");
const IconTransferOut = require("../../components/svg/icon_transfer_out.svg");

const Item = List.Item;

@withRouter
@observer
class GiftTransferStore extends React.Component<any, any> {
  @autowired($Transfer)
  public $mv: $Transfer;

  @autowired($AppStore)
  public $AppStore: $AppStore;

  constructor(props) {
    super(props);
    this.state = {
      animating: false,
    };
  }

  public componentDidMount() {
    document.title = "配赠转店";
    this.$mv.clear();
    // 监听 popstate 事件
    window.addEventListener("popstate", this.handleBackButton);
    transaction(() => {
      this.$mv.showSpin();
      const params = {
        orderPartyId: this.props.match.params.orderPartyId,
        keyWord: "",
      };
      this.$mv.fetchTransfer(params);
      this.$mv.hideSpin();
    });
  }

  public componentWillUnmount() {
    window.removeEventListener("popstate", this.handleBackButton);
  }

  public handleBackButton = (event) => {
    if (location.href.indexOf("isShowStoreList") === -1) {
      this.$mv.isShowStoreList = false;
    } else {
      this.$mv.isShowStoreList = true;
    }
  }

  public onSubmit = () => {
    const {pzedInfo, hfpzInfo, transferInOrgInfo} = this.$mv;
    const transferOutList = [];
    if (!transferInOrgInfo.transferInOrgId) {
      Toast.info("请选择转入门店", 3);
      return;
    }
    if (isEmptySimple(pzedInfo.amount) && isEmptySimple(hfpzInfo.amount)) {
      Toast.info("至少输入一个金额", 3);
      return;
    }
    if (!isEmptySimple(pzedInfo.amount)) {
      if (pzedInfo.amount <= 0) {
        Toast.info("配赠额度转出金额须＞0", 3);
        return;
      }
      if (!/^[0-9]+(.[0-9]{0,2})?$/.test(pzedInfo.amount)) {
        Toast.info("配赠额度最多支持2位小数", 3);
        return;
      }
      if (pzedInfo.amount > pzedInfo.max) {
        Toast.info("配赠额度转出金额超过账户额度，请调整后再重新转出", 3);
        return;
      }
      transferOutList.push(pzedInfo);
    }
    if (!isEmptySimple(hfpzInfo.amount)) {
      if (hfpzInfo.amount <= 0) {
        Toast.info("配赠上限转出金额须＞0", 3);
        return;
      }
      if (!/^[0-9]+(.[0-9]{0,2})?$/.test(hfpzInfo.amount)) {
        Toast.info("配赠上限最多支持2位小数", 3);
        return;
      }
      if (hfpzInfo.amount > hfpzInfo.max) {
        Toast.info("配赠上限转出金额超过账户额度，请调整后再重新转出", 3);
        return;
      }
      transferOutList.push(hfpzInfo);
    }
    const params = {
      transferInOrgId: transferInOrgInfo.transferInOrgId,
      transferOutList,
    };
    this.setState({ animating: true }, () => {
      this.$mv.submitTransfer(params).then((data) => {
        if (data.result) {
          this.setState({ animating: false });
          Toast.info("转账成功", 3);
          this.props.history.goBack();
        } else {
          Toast.info(data.errorMsg || "转账失败", 3);
          this.setState({ animating: false });
        }
      }).catch(() => {
        this.setState({ animating: false });
      });
    });
  }

  public changeMax(key) {
    this.$mv[key].amount = this.$mv[key].max;
  }

  public changeAmount(key, value) {
    this.$mv[key].amount = value;
  }

  public openModal() {
    this.props.history.push({ pathname: `/${SITE_PATH}/gift-transfer-store/${this.props.match.params.orderPartyId}?isShowStoreList` });
    this.$mv.isShowStoreList = true;
  }

  public render() {
    const { pzedInfo, hfpzInfo, transferOutOrgInfo, transferInOrgInfo, isSpin, isShowStoreList, estimatedAmount } = this.$mv;
    return (
      <RepaymentWrapper>
        <Spin spinning={isSpin}>
          <Content>
            <div className="title">
              <IconTransferIn/>转入门店
            </div>
            <List>
              <Item arrow="horizontal" className="gray" onClick={() => this.openModal()}>
                {transferInOrgInfo.transferInOrg || "请确认转入门店"}
              </Item>
            </List>
            <div className="tips">转入后，预计门店可用额度为 {estimatedAmount}</div>
          </Content>
          <Content>
            <div className="title out">
              <IconTransferOut/>
              <div>转出门店（{transferOutOrgInfo.transferOutOrg}）</div>
            </div>
            <div className="sub-title">转出配赠额度<span>（当前额度 <span>{pzedInfo.max}</span>）</span></div>
            <BigInput>
              <input type="number" className="big-input" placeholder={`请输入配赠额度`} value={pzedInfo.amount} onChange={(e) => this.changeAmount("pzedInfo", e.target.value)}/>
              <span className="all" onClick={() => this.changeMax("pzedInfo")}>全部</span>
            </BigInput>
            <div className="sub-title">转出配赠上限<span>（当前上限 <span>{hfpzInfo.max}</span>）</span></div>
            <BigInput>
              <input type="number" className="big-input" placeholder={`请输入配赠上限`} value={hfpzInfo.amount} onChange={(e) => this.changeAmount("hfpzInfo", e.target.value)}/>
              <span className="all" onClick={() => this.changeMax("hfpzInfo")}>全部</span>
            </BigInput>
          </Content>
        </Spin>
        <ConfirmButton>
          <Button type="primary" onClick={this.onSubmit}>确认转出</Button>
        </ConfirmButton>
        <ActivityIndicator
          toast={true}
          text="Loading..."
          animating={this.state.animating}
        />
        <Modal style={{ display: isShowStoreList ? "block" : "none" }}>
          <GiftTransferStoreList/>
        </Modal>
      </RepaymentWrapper>
    );
  }
}

export default GiftTransferStore;

const RepaymentWrapper = styled.div`
  &{
    padding: 12px;
    background: #f7f7f7;
    height: 100%;
  .am-list-item .am-list-line .am-list-content{
    font-size:14px;
    color:#666666;
  }
  .gray.am-list-item .am-list-line .am-list-content{
    font-size:14px;
    color:#999999;
  }
  }
`;

const Content = styled.div`
  &{
    background: #fff;
    margin-bottom: 12px;
    padding-bottom: 12px;
    .title{
      font-size:16px;
      height: 40px;
      display: flex;
      align-items: center;
      padding: 0 12px;
      color: #FF3030;
      background: linear-gradient(91deg, #FBEDED 0%, #FFFFFF 125%);
      svg{
        margin-right: 5px;
      }
      &.out{
        background: linear-gradient(90deg, #D0DFFF 0%, #FFFFFF 123%);
        color: #437DF0;
      }
      >div{
        flex: 1;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
      }
    }
    .am-list-line{
      background: #f7f7f7;
      margin: 12px 12px 12px 0;
      padding: 0 12px;
      border-radius: 2px;
    }
    .tips{
      padding-left: 14px;
      font-size: 12px;
      color: #666;
    }
    .sub-title{
      font-size: 16px;
      color: #333;
      font-weight: 500;
      padding: 12px;
      >span{
        font-size: 14px;
        font-weight: normal;
        >span{
          color: #FF3030;
        }
      }
    }
  }
`;

const BigInput = styled.div`
  & {
    position: relative;
    padding: 0 12px;

    .all {
      position: absolute;
      right: 25px;
      top: 8px;
      font-size: 14px;
      color: #437DF0;
    }

    input {
      height: 36px;
      line-height: 36px;
      font-size: 14px;
      border: none;
      width: 100%;
      background: #f7f7f7;
      border-radius: 2px;
      padding: 0 50px 0 12px;
    }
  }
`;

const ConfirmButton = styled.div`
  &{
    padding: 15px;
    left:0;
    width:100%;
    position:fixed;
    bottom: 0px;
    background:#ffffff;
    .am-button{
      height:42px;
      line-height:42px;
      font-size:16px;
    }
    .am-button-primary{
      border-radius:3px;
    }
  }
`;

const Modal = styled.div`
  &{
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: #F2F2F2;
    z-index: 100;
  }
`;
