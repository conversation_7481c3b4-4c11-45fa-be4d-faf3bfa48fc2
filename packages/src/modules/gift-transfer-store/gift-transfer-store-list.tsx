import React from "react";
import styled from "styled-components";
import { observer } from "mobx-react";
import { autowired } from "@classes/ioc/ioc";
import { $Transfer } from "./gift-transfer-store-mv";
import { Checkbox, SearchBar } from "antd-mobile";
import { withRouter } from "react-router";

const CheckboxItem = Checkbox.CheckboxItem;
const IconStore = require("../../components/svg/icon_store.svg");

@withRouter
@observer
export class GiftTransferStoreList extends React.Component<any, any> {
  @autowired($Transfer)
  public $mv: $Transfer;

  constructor(props) {
    super(props);
    this.state = {
      keyword: "",
    };
  }

  public changeKeyword = (value) => {
    this.setState({
      keyword: value,
    });
  }

  public search = () => {
    this.$mv.fetchTransfer({
      orderPartyId: this.props.match.params.orderPartyId.replace("?isShowStoreList", ""),
      keyWord: this.state.keyword,
    });
  }

  public choose = (v) => {
    this.$mv.changeStore(v);
    window.history.back();
  }

  public render() {
    const {transferInAccountList, transferInOrgInfo} = this.$mv;
    const { transferInOrgId } = transferInOrgInfo;
    return <Wrapper>
      <Search>
        <SearchBar placeholder="搜索门店编码、门店名称" onChange={this.changeKeyword} value={this.state.keyword} showCancelButton={false} onSubmit={this.search}/>
        <span onClick={this.search}>搜索</span>
      </Search>
      <List>
        {
          transferInAccountList.map(v => {
            const checked = v.transferInOrgId === transferInOrgId;
            return <div key={v.transferInOrgId} className="item" onClick={() => this.choose(v)}>
              <IconStore/>
              <div className="item-title">
                {v.transferInOrg}
              </div>
              <CheckboxItem checked={checked}/>
            </div>;
          })
        }
      </List>
    </Wrapper>;
  }
}

const Wrapper = styled.div`
  width: 100%;
  height: 100%;
`;

const Search = styled.div`
  display: flex;
  align-items: center;
  background: #fff;
  padding: 8px 12px;
  .am-search{
    flex: 1;
    background: #F6F6F6;
    height: 38px;
    .am-search-input{
      background: #F6F6F6;
      .am-search-synthetic-ph{
        text-align: left;
        padding-left: 0!important;
      }
    }
    .am-search-cancel{
      display: none!important;
    }
  }
  span{
    color: #437DF0;
    padding: 0 10px;
  }
`;

const List = styled.div`
  padding: 12px 12px 0;
  height: calc(100% - 54px);
  overflow-y: auto;
  .item{
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: #fff;
    padding: 4px 12px;
    margin-bottom: 12px;
    >svg{
      margin-right: 5px;
    }
    .item-title{
      flex: 1;
      font-weight: 500;
      color: #333;
      font-size: 16px;
    }
  }
  .am-list-line{
    display: none!important;
  }
  .am-list-thumb{
    margin-right: 0!important;
  }
`;
