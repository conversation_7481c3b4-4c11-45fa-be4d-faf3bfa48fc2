import { autowired, bean } from "@classes/ioc/ioc";
import { action, observable } from "mobx";
import { beanMapper } from "../../helpers/bean-helpers";
import { $BillService } from "../../classes/service/$bill-service";

@bean($NewBillDetailMv)
export class $NewBillDetailMv {
  @autowired($BillService)
  public $BillService: $BillService;

  @observable public billDetailInfo:any = {};
  @observable public isSpin:boolean = false;
  @observable public isFinished: boolean = false;
  @observable public pageIndex:number = 0;
  @observable public pageSize: number = 10;
  @observable public businessType: any = [];
  @observable public title: any = [];
  @observable public condition: string = "ALL";
  // @observable public billList: any = [];
  @observable public billList: any = [];
  @observable public billListTotalCount: number = 0;
  @observable public billListTotalAmount: number = 0;

  @action
  public fetchBillDetail = (params) => {
    return this.$BillService.queryDetailByType(params).then(res => {
      this.billDetailInfo = res;
    }).catch(err => this.isSpin = false)
  }

  @action
  public fetchBillList = (params, isConcat) => {
    return this.$BillService.queryListNew(params).then(res => {
      this.billList = isConcat ? this.billList.concat(res.list) : res.list;
      this.isFinished = this.billList.length >= res.totalCount;
      this.billListTotalCount = res.totalCount;
      this.billListTotalAmount = res.totalAmount;
    }).catch(err => this.isSpin = false)
  }

  @action
  public changePage(pageIndex) {
    this.pageIndex = pageIndex;
  }

  @action
  public clearMvData() {
    this.pageIndex = 0;
    this.condition = "ALL";
    this.businessType = [];
    this.title = [];
    this.billListTotalCount = 0;
    this.billListTotalAmount = 0;
    this.isFinished = false;
  }

}
