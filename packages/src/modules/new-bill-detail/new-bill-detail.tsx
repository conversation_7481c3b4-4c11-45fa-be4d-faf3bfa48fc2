import { autowired } from "@classes/ioc/ioc";
import { Spin } from "antd";
import { observer } from "mobx-react";
import * as React from "react";
import { withRouter } from "react-router";
import { $NewBillDetailMv } from "./new-bill-detail-mv";
import styled from "styled-components";
import { $AppStore } from "../../classes/stores/app-store-mv";
import { Toast } from "antd-mobile/es";
import { $FundType } from "@classes/const/$fund-type";
import RightSwiperModal from "../../components/right-swiper-modal";
import { ScrollAbilityWrapComponent } from "../../components/scroll-ability/wrap";
import { LoadingTip } from "../../components/loading-marked-words";
import { NoGoods } from "../../components/no-goods/no-goods";

const $SearchKeyList = [
  { condition: "ALL", name: "全部" },
  { condition: "ADD", name: "增加" },
  { condition: "REDUCE", name: "减少" },
];

const $PageTitle = {
  [$FundType.FL]: "返利账户",
  [$FundType.YE]: "可用余额",
  [$FundType.paid_not_delivery_amount]: "已付款未发货",
  [$FundType.QK]: "欠款账户",
}

const $CardTitle = {
  [$FundType.FL]: "返利额度",
  [$FundType.YE]: "可用余额",
  [$FundType.paid_not_delivery_amount]: "已付款未发货",
  [$FundType.QK]: "欠款",
}

const $ChangeName = {
  [$FundType.FL]: "本期变动",
  [$FundType.YE]: "本期变动",
  [$FundType.paid_not_delivery_amount]: "本期变动",
  [$FundType.QK]: "本期变动",
}

const $BottomName = {
  [$FundType.FL]: "返利额度",
  [$FundType.YE]: "余额",
  [$FundType.paid_not_delivery_amount]: "余额",
  [$FundType.QK]: "待还金额",
}

const $TitleStartName = {
  [$FundType.FL]: "期初额度",
  [$FundType.YE]: "期初余额",
  [$FundType.paid_not_delivery_amount]: "期初未发货额",
  [$FundType.QK]: "期初待还金额",
}

const $TitleEndName = {
  [$FundType.FL]: "期末额度",
  [$FundType.YE]: "期末余额",
  [$FundType.paid_not_delivery_amount]: "期末未发货额",
  [$FundType.QK]: "期末待还金额",
}

const $TitleAdd = {
  [$FundType.FL]: "本期增加",
  [$FundType.YE]: "本期收入",
  [$FundType.paid_not_delivery_amount]: "本期增加",
  [$FundType.QK]: "本期增加",
}

const $TitleReduce = {
  [$FundType.FL]: "本期减少",
  [$FundType.YE]: "本期支出",
  [$FundType.paid_not_delivery_amount]: "本期减少",
  [$FundType.QK]: "本期减少",
}

const $SwiperData = {
  [$FundType.FL]: [
    {
      title: "筛选",
      businessTypeList: ["销售", "其他调整", "返利转账", "返利订货", "返利订货取消"]
    }
  ],
  [$FundType.YE]: [
    {
      title: "业务类型",
      businessTypeList: ["充值", "转账", "退款", "扣款", "还款", "订单付款", "其他"]
    },
    {
      title: "常用筛选",
      titleList: ["余额充值", "POS分账", "退运费"]
    }
  ],
  [$FundType.paid_not_delivery_amount]: [
    {
      title: "增加",
      businessTypeList: ["订货"]
    },
    {
      title: "减少",
      businessTypeList: ["发货", "费用付款", "其他"]
    }
  ],
  [$FundType.QK]: [
    {
      title: "增加",
      businessTypeList: ["订货付款", "费用付款", "扣款"]
    },
    {
      title: "减少",
      businessTypeList: ["还款", "其他", ""]
    }
  ]
}

@withRouter
@observer
class NewBillDetailWrap extends React.Component<any, any> {

  @autowired($AppStore)
  public $AppStore: $AppStore;

  @autowired($NewBillDetailMv)
  public $myMv: $NewBillDetailMv;

  constructor(props) {
    super(props);
    this.state = ({
      isShowMore: false,
      isShowBusinessModal: false, // 是否显示业务类型弹窗
      orderInfo: null,
    });
  }

  public componentDidMount() {
    const { type } = this.props.match.params;
    document.title = $PageTitle[type];
    const orderInfo = JSON.parse(sessionStorage.getItem("orderInfo"));
    this.setState({ orderInfo });
    this.$myMv.clearMvData();
    this.loadBillDetail();
    this.loadBillList(false);
  }

  public componentWillReceiveProps(nextProps: Readonly<any>, nextContext: any): void {
    const { isScrollEnd } = nextProps;
    const { isShowBusinessModal } = this.state;
    if (isScrollEnd && isScrollEnd !== this.props.isScrollEnd && !isShowBusinessModal ) {
      this.loadMoreBillList();
    }
  }

  public loadMoreBillList = () => {
    const { isFinished, isSpin, pageIndex } = this.$myMv;
    if (!isFinished) {
      if (!isSpin) {
        this.$myMv.pageIndex = pageIndex + 1;
        this.loadBillList(true);
      }
    }
  }

  public loadBillDetail = () => {
    this.$myMv.fetchBillDetail({ oid: this.props.match.params.oid, fundType: this.props.match.params.type });
  }

  public loadBillList = (isConcat) => {
    const { condition, pageIndex, pageSize, title, businessType } = this.$myMv;
    const params = {
      condition,
      oid: this.props.match.params.oid,
      fundType: this.props.match.params.type,
      pageSize,
      pageIndex,
      title,
      businessType
    }
    const { loadingEnd } = this.props;
    loadingEnd && loadingEnd(false);
    this.$myMv.isSpin = true;
    this.$myMv.fetchBillList(params, isConcat).then(() => {
      this.$myMv.isSpin = false;
      loadingEnd && loadingEnd(true);
    })

  }

  public componentWillUnmount() {
    this.$myMv.billDetailInfo = [];
    this.$myMv.billList = [];
    this.$myMv.clearMvData();
  }

  public queryAccountInfo = (activeKey) => {
    this.$myMv.condition = activeKey;
    this.$myMv.isFinished = false;
    this.$myMv.pageIndex = 0;
    this.loadBillList(false);
  }

  public onSearch = (businessType, title) => {
    this.setState({ isShowBusinessModal: false }, () => {
      this.$myMv.businessType = businessType;
      this.$myMv.title = title;
      this.$myMv.isFinished = false;
      this.$myMv.pageIndex = 0;
      this.loadBillList(false);
    });
  }

  public render() {
    const { type } = this.props.match.params;
    const { isSpin, billDetailInfo, condition, businessType, isFinished, title, billListTotalCount, billListTotalAmount, billList } = this.$myMv;
    const { isShowMore, isShowBusinessModal, orderInfo } = this.state;
    const { initAmount, changeAmount, finalAmount, add, reduce } = billDetailInfo;
    return (
      <SNewBillDetail>
        <Spin spinning={isSpin}>
          {
            orderInfo && <STitle>
              <div>{orderInfo.orgName.length > 10 ? orderInfo.orgName.substring(0,16) + "..." : orderInfo.orgName}</div>
              <div>{orderInfo.billMonthlyStart} ~ {orderInfo.billMonthlyEnd}</div>
            </STitle>
          }
          <SCard>
            <div className="title">{$CardTitle[type]}</div>
            <div className="info-top">
              <div>
                <div>{$TitleStartName[type]}</div>
                <div>{initAmount || 0}</div>
              </div>
              <div>
                <div>本期变动</div>
                <div style={{ color: changeAmount > 0 ? "#FF3030" : "#333333" }}>{changeAmount}</div>
              </div>
              <div>
                <div>{$TitleEndName[type]}</div>
                <div>{finalAmount}</div>
              </div>
            </div>
            <div className="middle" onClick={() => this.setState({ isShowMore: !isShowMore })}>
              {
                isShowMore ? <i className="scmIconfont scm-icon-jiantou-shang" /> : <i className="scmIconfont scm-jiantou-xia" />
              }
            </div>
            {
              isShowMore && <div className="info-bottom">
                <div>
                  <div>{$ChangeName[type]}</div>
                  <div style={{ color: changeAmount > 0 ? "#FF3030" : "#333333" }}>{changeAmount}</div>
                </div>
                <div className="symbol">=</div>
                <div>
                  <div>{$TitleAdd[type]}</div>
                  <div>{add}</div>
                </div>
                <div className="symbol">-</div>
                <div>
                  <div>{$TitleReduce[type]}</div>
                  <div>{reduce}</div>
                </div>
              </div>
            }
          </SCard>
          <SSearch>
            <div className="search_key">
            {
                $SearchKeyList.map((item, index) => {
                  return <span key={index} className={condition === item.condition && "active"} onClick={() => this.queryAccountInfo(item.condition)}>
                    {item.name}
                  </span>;
                })
            }
            </div>
            <div className="search_total">
              <div>总 <span>{billListTotalCount}</span> 笔 合计： <span style={{ color: billListTotalAmount > 0 ? "#FF3030" : "#333333" }}>{billListTotalAmount}</span></div>
              <div onClick={() => this.setState({ isShowBusinessModal: true })}>筛选 <i className="scmIconfont scm-icon-xiajiantou" /></div>
            </div>
          </SSearch>
          <SList>
            {
              billList && billList.length > 0 ? billList.map((item, index) => {
                return <div className="bill_item" key={index}>
                  <div>
                    <div><span>{item.businessType}</span> {item.title}</div>
                    <div style={{ color: item.amount > 0 ? "#FF3030" : "#333333" }}>{item.amount}</div>
                  </div>
                  <div>
                    <div>{item.createdOn}</div>
                    <div>
                      {
                        type === $FundType.FL && <div className="flow_status">{item.flowStatus}</div>
                      }
                      <div>{$BottomName[type]}：{item.balanceAmount}</div>
                    </div>
                  </div>
                </div>
              }) : <NoGoods title="暂无数据"/>
            }
            {
              billList && billList.length > 0 ?
                <LoadingTip
                  isFinished={isFinished}
                  isLoad={isSpin}
                /> : null
            }
          </SList>
          <RightSwiperModal
            swiperData={$SwiperData[type]}
            visible={isShowBusinessModal}
            businessType={businessType}
            title={title}
            onSearch={this.onSearch}
            onClose={() => this.setState({ isShowBusinessModal: false })}
          />
        </Spin>
      </SNewBillDetail>)

  }
}

const NewBillDetail = ScrollAbilityWrapComponent(NewBillDetailWrap);

export default NewBillDetail;

const STitle = styled.div`
  & {

    > div:nth-child(1) {
      background: #FFF;
      padding: 8px 12px;
      font-size: 14px;
      color: #333;
    }

    > div:nth-child(2) {
      padding: 8px 12px;
      color: #666;
      font-size: 14px;
    }
  }
`

const SList = styled.div`
  & {
    margin: 0 12px 12px;
    display: flex;
    flex-direction: column;

    .bill_item {
      padding: 12px;
      display: flex;
      flex-direction: column;
      background: #FFF;

      > div:nth-child(1) {
        display: flex;
        justify-content: space-between;

        > div:nth-child(1) {
          font-weight: 500;
          color: #333;
          font-size: 14px;

          > span {
            padding: 4px 8px;
            background: #F8F8F8;
            font-size: 12px;
            color: #333;
          }
        }

        > div:nth-child(2) {
          font-size: 18px;
          color: #333;
        }
      }

      > div:nth-child(2) {
        display: flex;
        justify-content: space-between;
        color: #999;
        font-size: 12px;
        margin-top: 4px;
        align-items: center;

        > div:nth-child(2) {
          display: flex;
          flex-direction: column;

          .flow_status {
            color: #437DF0;
            text-align: right;
            margin-bottom: 4px;
          }
        }
      }
    }
  }
`

const SSearch = styled.div`
  & {
    margin: 12px 12px 0;
    background: #FFF;
    padding-top: 12px;

    .search_key {
      width: 100%;
      height: 40px;
      line-height: 40px;
      display: flex;
      border-bottom: 0.5px solid #E8E8E8;

      > span {
        display: inline-block;
        width: 100%;
        text-align: center;
        color: #666;
        margin: 0 30px;
        font-size: 14px;
      }

      .active {
        border-bottom: 2px solid #307DCD;
        color: #307DCD;
        font-size: 14px;
      }
    }

    .search_total {
      padding: 12px;
      display: flex;
      justify-content: space-between;
      background: #FFF;
      align-items: center;

      > div:nth-child(1) {
        line-height: 18px;
        font-size: 14px;
        color: #666;

        > span {
          font-weight: 500;
          color: #333;
        }
      }

      > div:nth-child(2) {
        color: #333;
        font-size: 14px;
      }
    }
  }
`

const SCard = styled.div`
  & {
    margin: 8px 12px;
    border: 0.5px solid #E8E8E8;
    background: #FFF;

    .title {
      padding: 8px;
      background: #F0F4FC;
      color: #333;
      font-size: 16px;
      font-weight: 500;
    }

    .info-top {
      padding: 12px 12px 4px;
      display: flex;
      justify-content: space-around;

      > div {
        display: flex;
        flex-direction: column;
        align-items: center;

        > div:nth-child(1) {
          font-size: 14px;
          color: #666;
        }

        > div:nth-child(2) {
          margin-top: 4px;
          font-size: 18px;
          font-weight: 500;
          color: #333;
        }
      }
    }

    .middle {
      display: flex;
      justify-content: center;
      align-items: center;
      font-size: 24px;
      color: #999999;
    }

    .info-bottom {
      margin: 0 12px 12px;
      padding: 12px;
      display: flex;
      justify-content: space-around;
      background: #F8F8F8;
      align-items: center;

      > div {
        display: flex;
        flex-direction: column;
        align-items: center;

        > div:nth-child(1) {
          font-size: 14px;
          color: #666;
        }

        > div:nth-child(2) {
          margin-top: 4px;
          font-size: 18px;
          font-weight: 500;
          color: #333;
        }
      }

      .symbol {
        width: 24px;
        height: 24px;
        border: 1px solid #999999;
        border-radius: 50%;
        color: #999999;
      }
    }
  }
`

const SNewBillDetail = styled.div`
  & {
    background: #F7F7F7;
    width: 100%;
    height: 100vh;

    > div {
      width: 100%;
      background:#F7F7F7;
      border-radius:8px;
      margin-bottom: 24px;
      height: 100%;
      overflow: scroll;
    }
  }
`;
