import { autowired } from "@classes/ioc/ioc";
import { List } from "antd-mobile";
import { observer } from "mobx-react";
import React from "react";
import { withRouter } from "react-router";
import styled from "styled-components";
// import "../common.less";
import { NoGoods } from "../../components/no-goods/no-goods";
import { Spin } from "antd";
import { $CreditType } from "../../classes/const/$credit-type";
import { $CreditListMv } from "./credit-list-mv";
import { SITE_PATH } from "../app";
import { $ValidityType } from "@classes/const/$validity-type";

const Item = List.Item;
const Brief = Item.Brief;
declare let window: any;

@withRouter
@observer
class CreditList extends React.Component<any, any> {
  @autowired($CreditListMv)
  public $creditListMv: $CreditListMv;

  public constructor(props) {
    super(props);
  }

  public componentDidMount() {
    this.initPage("All");
  }

  public initPage = (activeKey) => {
    this.$creditListMv.showSpin();
    const params = { creditType: activeKey, validityType: $ValidityType.NEW_CREDIT };
    this.$creditListMv.fetchCreditaccountListLoad(params);
  }

  public goToCreditDetail = (accountId) => {
    this.props.history.push({
      pathname: `/${SITE_PATH}/credit-detail`,
      state: {
        creditId: accountId,
      },
    });
  }

  public render() {
    const { isSpin, creditAccountList } = this.$creditListMv;
    return (
      <Wrapper style={{ overflow: creditAccountList ? creditAccountList.length < 1 ? "hidden" : "auto" : null }}>
        <Spin spinning={isSpin}>
          <CreditAccountInfo>
            <CreditAccountInfoContents>
              {
                creditAccountList.length > 0 ? creditAccountList.map((credit, index) => {
                  return (
                    <CreditAccountInfoContent onClick={() => this.goToCreditDetail(credit.accountId)} key={index}>
                      <p>
                        {credit.code}（{credit.ruleType}）
                      </p>
                      <div>
                        <p>
                          <span className="range">￥{Number(credit.creditAmount).toFixed(2)}</span>
                          <br/>
                          <span>信用额度</span>
                        </p>
                        <p>
                          <span className="value">￥{Number(credit.surplusToReturnAmount).toFixed(2)}</span>
                          <br/>
                          <span>欠款金额</span>
                        </p>
                        <p>
                          <span className="value"
                                style={{ color: credit.creditStatus === $CreditType.USEING ? "#7ED321" : credit.creditStatus === $CreditType.OVERDUE ? "#FF3030" : "#307DCD" }}
                          >
                            {credit.creditStatusDesc}
                          </span>
                          <br/>
                          <span>还款日：{credit.repaymentTime}</span>
                        </p>
                        <p>
                          <b class="right">
                            <i class="right-arrow1"></i>
                            <i class="right-arrow2"></i>
                          </b>
                        </p>
                      </div>
                      <p>
                        有效期：{credit.creditAmountExpiryDateFrom}至{credit.creditAmountExpiryDateTo}
                      </p>
                    </CreditAccountInfoContent>
                  );
                }) : <NoGoods title="暂无可用的信用~" height={document.documentElement.clientHeight / 2}/>
              }
            </CreditAccountInfoContents>
          </CreditAccountInfo>
        </Spin>
      </Wrapper>
    );
  }
}

export default CreditList;

const Wrapper = styled.div`// styled
  & {
    width: 100%;
    height: calc(${document.documentElement.clientHeight}px);
    background: #ECF6FF;
    padding: 15px;
    overflow:auto;
    -webkit-overflow-scrolling: touch;
  }
`;

const CreditAccountInfo = styled.div`// styled
  & {
    width: 100%;
    height: auto;
    border: 1px solid transparent;
    border-radius: 8px;
  }
`;

const CreditAccountInfoContents = styled.div`// styled
  & {
    width: 100%;
    height: auto;
  }
`;

const CreditAccountInfoContent = styled.div`// styled
  & {
    width: 100%;
    height: 110px;
    padding: 8px 12px;
    border: 0.5px solid #D8D8D8;
    border-radius: 4px;
    margin-bottom: 15px;
    background: #fff;
    > p {
      margin-bottom: 5px;
      color: #999999;
      font-family: MicrosoftYaHei;
      font-size: 12px;
    }
    > div {
      > p {
        display: inline-block;
        margin-bottom: 5px;
        > span {
          font-family: MicrosoftYaHei;
          font-size: 12px;
          color: #999999;
        }
        > .range {
          font-family: MicrosoftYaHei;
          font-size: 13px;
          color: #333333;
        }
        > .value {
          font-family: MicrosoftYaHei;
          font-size: 13px;
          color: #FF3030;
        }
      }
      > p:nth-of-type(1) {
        margin-left: 4%;
      }
      > p:nth-of-type(2) {
        margin-left: 14%;
      }
      > p:nth-of-type(3) {
        margin-left: 10%;
      }
      > p:nth-of-type(4) {
        width: 15px;
        height: 15px;
        position: relative;
        float: right;
        .right {
          width: 15px;
          height: 15px;
          position: absolute;
          left: 0;
          top: 10px;
        }
        .right-arrow1, .right-arrow2 {
          width: 0;
          height: 0;
          display: block;
          position: absolute;
          left: 0;
          top: 0;
          border-top: 10px transparent dashed;
          border-right: 10px transparent dashed;
          border-bottom: 10px transparent dashed;
          border-left: 10px white solid;
          overflow: hidden;
        }
        .right-arrow1 {
          left: 1px; /*重要*/
          border-left: 10px #999999 solid;
        }
        .right-arrow2 {
          border-left: 10px white solid;
        }
      }
    }
  }
`;
