import { autowired, bean } from "@classes/ioc/ioc";
import { action, observable } from "mobx";
import { $ComponentService } from "../../classes/service/$component-service";
import { $CreditAccount } from "../../classes/entity/$credit-account";

@bean($CreditListMv)
export class $CreditListMv {
  @autowired($ComponentService)
  public $componentService: $ComponentService;

  @observable public isSpin: boolean = false;

  @observable public automaticType: string;

  @observable public effectiveCreditAccount: number;

  @observable public creditAccountList: any[] = [];

  @action
  public showSpin() {
    this.isSpin = true;
  }

  @action
  public hideSpin() {
    this.isSpin = false;
  }

  @action
  public fetchCreditaccountListLoad(params) {
    this.$componentService.queryCreditaccountListLoad(params).then((data) => {
      this.automaticType = data.automaticType;
      this.effectiveCreditAccount = data.effectiveCreditAccount;
      this.creditAccountList = data.creditAccountList.map((credit) => new $CreditAccount(credit));
      this.hideSpin();
    }).catch(() => {
      this.hideSpin();
    });
  }
}
