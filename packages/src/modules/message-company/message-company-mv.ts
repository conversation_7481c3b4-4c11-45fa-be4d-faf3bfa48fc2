import { autowired, bean } from "@classes/ioc/ioc";
import { action, observable } from "mobx";
import { $AnnouncementList } from "../../classes/entity/$announcement-list";
import { $MyInfoService } from "../../classes/service/$my-info-service";
import { beanMapper } from '../../helpers/bean-helpers';

@bean($MessageCompanyMv)
export class $MessageCompanyMv {

  @autowired($MyInfoService)
  public $myInfoService: $MyInfoService;

  @observable public announcementList: $AnnouncementList[] = [];

  @observable public isSpin: boolean = false;

  @observable public pageIndex: number = 0;
  @observable public pageSize: number = 0;
  @observable public scrollHeight: number = 10;

  @observable public isShow: boolean = false;

  @observable public finished: boolean = false;

  @action
  public showSpin() {
    this.isSpin = true;
  }

  @action
  public hideSpin() {
    this.isSpin = false;
  }

  @action
  public clearPageIndex() {
    this.pageIndex = 0;
  }

  @action
  public showMore() {
    this.isShow = true;
  }

  @action
  public hideMore() {
    this.isShow = false;
  }

  @action
  public showFinished() {
    this.finished = true;
  }

  @action
  public hideFinished() {
    this.finished = false;
  }

  @action
  public changePageIndex() {
    this.pageIndex = this.pageIndex + 1;
  }

  @action
  public fetchAnnouncementList(params) {
    return this.$myInfoService.queryAnnouncementList(params).then((data) => {
      this.hideMore();
      const { announcementList, total } = data;
      if (announcementList) {
        this.announcementList = this.announcementList.concat(announcementList.map((announcement) => new $AnnouncementList(announcement)));
        if (this.announcementList.length >= total) {
          this.showFinished();
        }
      }
    });
  }
  @action
  public queryOldData(obj) {
    beanMapper(obj, this);
  }
  @action
  public clearMVData() {
    this.announcementList = [];
    this.isSpin = false;

    this.pageIndex = 0;
    this.pageSize = 10;
    this.scrollHeight = 0;

    this.isShow = false;

    this.finished = false;
  }
}
