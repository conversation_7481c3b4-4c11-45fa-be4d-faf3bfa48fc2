import { autowired } from "@classes/ioc/ioc";
import { Spin } from "antd";
import { List } from "antd-mobile";
import { observer } from "mobx-react";
import React from "react";
import styled from "styled-components";
import { SITE_PATH } from "../app";
import { $MessageCompanyMv } from "./message-company-mv";
import { NoGoods } from "../../components/no-goods/no-goods";
import DateUtils from "../../classes/utils/DateUtils";
import { withRouter } from "react-router";
import { ScrollAbilityWrapComponent } from "../../components/scroll-ability/wrap";
import { LoadingTip } from "../../components/loading-marked-words";
import { $AppStore, AppStoreKey } from "../../classes/stores/app-store-mv";

declare let window: any;
const Item = List.Item;

@withRouter
@observer
class MessageCompanyWrap extends React.Component<any, any> {
  @autowired($AppStore)
  public $AppStore: $AppStore;
  @autowired($MessageCompanyMv)
  public $myMv: $MessageCompanyMv;

  constructor(props) {
    super(props);
    this.state = {
    };
  }
  // 离开记录滚动高度
  public saveMV = () => {
    this.$myMv.scrollHeight = $(".scroll-ability-wrap").scrollTop();
    this.$AppStore.savePageMv(AppStoreKey.MESSAGECOMPANY, this.$myMv);
  }
  public componentWillUnmount(): void {
    this.saveMV();
  }
  public componentDidMount() {
    document.title = "公司公告列表";
    setTimeout(() => {
      const oldData = this.$AppStore.getPageMv(AppStoreKey.MESSAGECOMPANY)
      if (oldData) {
        this.$myMv.queryOldData(JSON.parse(oldData));
        this.$AppStore.clearPageMv(AppStoreKey.MESSAGECOMPANY);
        $(".scroll-ability-wrap").scrollTop(this.$myMv.scrollHeight);
      } else {
        this.$myMv.clearMVData();
        this.initPage();
      }
    }, 50);
  }
  public initPage = () => {
    this.$myMv.showSpin();
    this.loadAnnouncementList();
  }
  public componentWillReceiveProps(nextProps: Readonly<any>, nextContext: any): void {
    const { isScrollEnd } = nextProps;
    console.log(isScrollEnd);
    if (isScrollEnd && isScrollEnd !== this.props.isScrollEnd) {
      this.loadData();
    }
  }

  public loadAnnouncementList = () => {
    const { pageIndex, pageSize } = this.$myMv;
    const params = { pageIndex, pageSize };
    this.$myMv.fetchAnnouncementList(params).then(() => {
      this.$myMv.hideSpin();
      const { finished } = this.$myMv;
      const { loadingEnd } = this.props;
      loadingEnd && loadingEnd(finished);
      this.$myMv.changePageIndex();
    });
  }

  public loadData = () => {
    const { finished } = this.$myMv;
    if (finished) {
      return;
    }
    this.$myMv.showMore();
    this.loadAnnouncementList();
  }

  public goToDetail = (id) => {
    this.props.history.push({ pathname: `/${SITE_PATH}/message-company-detail/${id}` });
  }

  public renderMessageCompany = (announcementList) => {
    return (
      announcementList.length > 0 ? announcementList.map((announcement, index) => {
        return (
          <div key={index}>
            <List>
              <Item arrow={"horizontal"} onClick={() => this.goToDetail(announcement.id)}>
                <div><i className="scmIconfont scm-icon-rule"/></div>
                <div>
                  <p>{announcement.title ? announcement.title.length > 18 ? announcement.title.slice(0, 18) + "..." : announcement.title : null}</p>
                  <p>{DateUtils.toStringFormat(announcement.announcementTime, "yyyy-MM-dd")}</p>
                </div>
              </Item>
            </List>
            <Spancing/>
          </div>
        );
      }) : <NoGoods title="暂无公告" height={document.documentElement.clientHeight / 2}/>
    );
  }

  public render() {
    const { announcementList, isSpin, isShow, finished } = this.$myMv;
    return (
      <MessageCompanyPage>
        <Spin spinning={isSpin}>
          <div className="messageContents">
            {
              announcementList ? this.renderMessageCompany(announcementList) : null
            }
          </div>
          {
            announcementList && announcementList.length > 0 ?
              <LoadingTip
                isFinished={finished}
                isLoad={isShow}
              /> : null
          }
        </Spin>
      </MessageCompanyPage>
    );
  }
}

const MessageCompany = ScrollAbilityWrapComponent(MessageCompanyWrap);
export default MessageCompany;

const MessageCompanyPage = styled.div`// styled
  & {
    .am-list .am-list-body::before {
      height: 0;
    }
    .am-list .am-list-body::after {
      height: 0;
    }
    .am-list-item.am-list-item-middle .am-list-line {
      position: relative;
    }
    .am-list-item .am-list-line .am-list-content {
      > div {
        display: inline-block;
      }
    }
    .scm-icon-rule {
      color: #307DCD;
    }
    .am-list-content {
      height: 64px;
    }
    .am-list-content div:nth-of-type(1) {
      width: 16px;
      position: absolute;
      top: 8px;
      left: 17px;
    }
    .am-list-content div:nth-of-type(2) {
      position: absolute;
      top: 10px;
      left: 38px;
      > p {
        margin-bottom: 7px;
      }
      > p:nth-of-type(1) {
        font-size: 14px;
        color: #333333;
      }
      > p:nth-of-type(2) {
        font-size: 12px;
        color: #999999;
      }
    }
    .am-list-content div:nth-of-type(3) {
      position: absolute;
      top: 27px;
      right: 38px;
      width: 10px;
      height: 10px;
      border-radius: 10px;
      background: #FF3030;
    }
  }
`;

const Spancing = styled.div`// styled
  & {
    width: 100%;
    height: 10px;
    background: #F2F2F2;
  }
`;
