html, body {
  font-family: <PERSON><PERSON><PERSON><PERSON><PERSON>, sans-serif;
  height: 100%;
  div, span, ul, li, a {
    box-sizing: border-box;
  }
  #react-root {
    height: 100%;
    > div {
      height: 100%;
    }
  }
}

img {
  border: none;
}

ul {
  margin: 0;
  padding: 0;
}

.iScroll {
  overflow: hidden;
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
}

.bottom_btn {
  margin-top: 20px;
}

.f_right {
  float: right;
}

.controller {
  overflow: hidden;
  color: #aaa;
  .f_right {
    height: 44px;
    line-height: 44px;
    a {
      display: inline-block;
      width: 76px;
      text-align: center;
    }
    .edit {
      background: url("../components/assets/edit.png") no-repeat left center;
      background-size: 16px;
    }
    .del {
      background: url("../components/assets/del.png") no-repeat left center;
      background-size: 16px;
    }
  }
}

.checkbox {
  float: left;
  width: 140px;
}

.address_showInput {
  width: 100%;
}

.customerLink {
  position: absolute;
  top: 0;
  right: 0;
  width: 24%;
  height: 88px;
  border: 1px solid #ddd;
  border-top: none;
  border-right: none;
  background: url("../components/assets/customerLink.png") no-repeat center 20px;
  p {
    text-align: center;
    margin-top: 60px;
    color: #aaa;
  }
}

.ant-spin.ant-spin-spinning.ant-spin-show-text {
  color: #307dcd;
}

.ant-spin-dot i {
  background: #307dcd;
}

.am-list-line::after {
  height: 0px !important;
  border-bottom: none;
}

.am-list-body {
  border: none !important;
}

.am-modal-mask {
  background-color: transparent !important;
}

.am-modal-wrap {
  background: rgba(0, 0, 0, 0.5);
}

.swiper-modal {
  .am-modal-content {
    background-color: transparent !important;
  }
}

//.am-list-body::after {
//  content: '';
//  position: absolute;
//  background-color: #ddd !important;
//  display: block;
//  z-index: 1;
//  top: auto;
//  right: auto;
//  bottom: 0;
//  left: 0;
//  width: 100%;
//  height: 1PX;
//  transform-origin: 50% 100%;
//  transform: scaleY(0.5);
//}

.am-list-line {
  border: none !important;
}
@media (min-resolution: 2dppx) {
  html:not([data-scale]) .am-list-item:not(:last-child) .am-list-line {
    border: none !important;
  }
}
@media (min-resolution: 2dppx) {
  html:not([data-scale]) .am-list-body::after {
    display: none !important;
  }
}
.am-list-body::after {
  display: none !important;
}
.am-button-primary::before {
  display: none !important;
}
.am-button {
  border: none !important;
}
