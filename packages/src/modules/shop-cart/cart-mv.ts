import { autowired, bean } from "@classes/ioc/ioc";
import { Modal, Toast } from "antd-mobile";
import { findIndex, remove, sum } from "lodash";
import { action, computed, observable } from "mobx";
import { $CartType } from "../../classes/const/$cart-type";
import { $Page } from "../../classes/entity/$pagination";
import { $Product } from "../../classes/entity/$product";
import { $ComponentService } from "../../classes/service/$component-service";
import { $ProductService } from "../../classes/service/$product-service";
import { $OrderPriceType } from "@classes/const/$order-price-type";
import { $AccountType } from "@classes/const/$account-type";
import { $ExceedQuotaStatus } from "@classes/const/$exceed-quota-status";


const alert = Modal.alert;

@bean($CartMv)
export class $CartMv {
  @autowired($ProductService)
  public $productService: $ProductService;

  @autowired($ComponentService)
  public $componentService: $ComponentService;

  @observable public products: $Product[] = [];

  @observable public productsParams = [];

  @observable public page: $Page = new $Page();

  // 自身额外的管理状态
  @observable public loaded: boolean = false;

  @observable public keyword: string;

  @observable public productMessage: string;

  @observable public isSpin: boolean = false;

  @observable public isAgency: boolean = false; // 是否代办

  @observable public orderPriceViewPermission: string;

  @observable public retailPriceViewPermission: string;

  @observable public orderSchemeType: string;

  @observable public shopSchemeInfo: any;

  @observable public shopcartTransferInfo: any;

  @observable public isShowTransferModal: boolean = false;

  @observable public isShowDiscount: boolean = true;

  @observable public discountInfo: any;

  @observable public totalDiscountAmount: number = 0;

  @observable public splitTotalAmount: number = 0;

  @observable public expectedDiscountAmount: number = 0;

  @observable public totalDeductionAmount: number = 0;

  @observable public totalQuotaAmount: number = 0; // 总额度（配赠或返利）
  @observable public totalPzAbleAmount: number = 0; // 总配赠额度
  @observable public totalHFPZAbleAmount: number = 0; // 总配赠上限

  @observable public accountTypeCode: string = ''; // 账户类型
  @observable public openMoreStore: boolean = false; // 转账-显示更多门店

  @observable public selectedAmount: number = 0; // 用户已选商品金额

  @observable public itemList: any[] = []; // 额度明细

  @observable public quotaAmount: number = 0; // 可用额度（配赠或返利）

  @observable public transferAccountId: string = ''; // 转账账户id

  @observable public hasToken: boolean = false; // 是否有权限

  @action
  public showSpin() {
    this.isSpin = true;
  }

  @action
  public hideSpin() {
    this.isSpin = false;
  }

  @action
  public showTransferModal() {
    this.isShowTransferModal = true;
  }

  @action
  public hideTransferModal() {
    this.isShowTransferModal = false;
  }

  @action
  public openDiscount() {
    this.isShowDiscount = true;
  }

  @action
  public closeDiscount() {
    this.isShowDiscount = false;
  }

  @action
  public setIsAgency() {
    this.isAgency = true;
  }

  @action
  public removeById(id) {
    remove(this.products, (product: $Product) => product.productSkuId === id);
  }

  @action
  public clearProducts() {
    this.products = [];
  }

  @action
  public shopcartBatchDelete(params) {
    return this.$productService.shopcartBatchDelete(params).then((data) => {
      if (data.result) {
        this.clearProducts();
        this.closeDiscount();
        this.resetTotalDiscountAmount();
        this.resetTotalDeductionAmount();
        this.resetSplitTotalAmount();
        this.resetExpectedDiscountAmount();
        this.resetTotalDeductionAmount();
        sessionStorage.setItem("isSetShopCartData", "false");
      }
    });
  }

  @action
  public setShopCartInfoFromOrder(data) {
    const { shopCartList, message } = data;
    this.products = shopCartList.map((product) => new $Product(product));
    console.log(this.products);
    this.productMessage = message;
    this.orderPriceViewPermission = data.orderPriceViewPermission;
    this.retailPriceViewPermission = data.retailPriceViewPermission;
  }

  @action
  public showMoreStore = () => {
    this.openMoreStore = true;
  }

  @action
  public fetchCarts() {
    return this.$productService.queryCarts().then((data) => {
      this.hideSpin();
      if (!data.errorCode) {
        const { shopCartList, message } = data;
        this.products = shopCartList.map((product) => new $Product(product));
        if (this.accountTypeCode !== "") {
          this.selectedAmount = shopCartList.reduce((total, product) => {
            if (product.checked) {
              let price = product?.product?.deductionAmount || 0;
              total += product.quantity * price;
            }
            return total;
          }, 0);
          if (this.selectedAmount === 0) {
            this.closeDiscount();
            this.resetTotalDiscountAmount();
            this.resetTotalDeductionAmount();
            this.resetSplitTotalAmount();
            this.resetExpectedDiscountAmount();
            this.resetTotalDeductionAmount();
          }
        }
        this.productMessage = message;
        this.orderPriceViewPermission = data.orderPriceViewPermission;
        this.retailPriceViewPermission = data.retailPriceViewPermission;
        this.orderSchemeType = data.orderSchemeType;
      }
      return data;
    }).catch((err) => {
      this.hideSpin();
      Toast.fail(err.response.body.message);
    });
  }

  @action
  public fetchItemSplit(params) {
    this.$productService.fetchShopcartItemSplit(params)
      .then((data) => {
        console.log('data', data)
        this.hideSpin();
        if (!data.errorCode) {
          this.discountInfo = data;
          const { shopCartItemList, shopCartTotalAmount, totalDeductionAmount } = data;
          let totalAmount = 0
          let expectedAmount = 0
          if (this.isShowDiscount) {
            shopCartItemList.map((item) => {
              totalAmount += item.discountOrderPrice * item.quantity
              if (item.orderPriceType === $OrderPriceType.DISCOUNT) {
                expectedAmount += item.discountOrderPrice * item.quantity;
              }
            })
          }
          this.splitTotalAmount = shopCartTotalAmount;
          this.expectedDiscountAmount = expectedAmount;
          this.totalDiscountAmount = totalAmount;
          this.totalDeductionAmount = totalDeductionAmount;
        }
      }).catch((err) => {
        this.hideSpin();
        Toast.fail(err.response.body.message);
      });
  }

  @action
  public resetExpectedDiscountAmount() {
    this.expectedDiscountAmount = 0;
  }

  @action
  public resetTotalDeductionAmount() {
    this.totalDeductionAmount = 0;
  }

  @action
  public resetTotalDiscountAmount() {
    this.totalDiscountAmount = 0;
  }

  @action
  public resetTotalDeductionAmount() {
    this.totalDeductionAmount = 0;
  }

  @action
  public resetSplitTotalAmount() {
    this.splitTotalAmount = 0;
  }

  @action
  public fetchShopScheme() {
    return this.$productService.queryShopScheme()
      .then((data) => {
        this.hideSpin();
        if (!data.errorCode) {
          this.shopSchemeInfo = data;
        }
      }).catch((err) => {
        this.hideSpin();
        Toast.fail(err.response.body.message);
      });
  }

  @action
  public loadTransferInfo(params) {
    return this.$productService.fetchTransferInfo(params)
      .then((data) => {
        this.hideSpin();
        if (!data.errorCode) {
          this.shopcartTransferInfo = data;
        }
      }).catch((err) => {
        this.hideSpin();
        Toast.fail(err.response.body.message);
      });
  }

  @action
  public loadNewTransferInfo(params) {
    return this.$productService.fetchNewTransferInfo(params)
     .then((data) => {
        this.hideSpin();
        if (!data.errorCode) {
          this.shopcartTransferInfo = data;
        }
      }).catch((err) => {
        this.hideSpin();
        Toast.fail(err.response.body.message);
      });
  }

  @action
  public loadPzTransferInfo(params) {
    return this.$productService.fetchPzTransferInfo(params)
      .then((data) => {
        this.hideSpin();
        if (!data.errorCode) {
          if (data.transferOutList) {
            data.transferOutList.map(item => {
              item.checked = true;
            });
          }
          this.shopcartTransferInfo = data;
        }
      }).catch((err) => {
        this.hideSpin();
        Toast.fail(err.response.body.message);
      });
  }

  @action
  public fetchShopSchemeTips() {
    return this.$componentService.shopSchemeTipsLoad().then((res) => {
      this.hideSpin();
      const { accountTypeCode, hasToken, totalAbleAmount, accountId, availableAmount, itemList } = res;
      if (accountTypeCode === $AccountType.DEDUCTION_LIMIT || accountTypeCode === $AccountType.RETURN_FREE_DISTRIBUTION) {
        this.accountTypeCode = accountTypeCode;
        this.totalQuotaAmount = totalAbleAmount;
        this.itemList = itemList;
        this.quotaAmount = availableAmount;
        this.transferAccountId = accountId;
        this.hasToken = hasToken;
        if (accountTypeCode === $AccountType.RETURN_FREE_DISTRIBUTION) {
          this.totalHFPZAbleAmount = res.totalHFPZAbleAmount;
          this.totalPzAbleAmount = res.totalPzAbleAmount;
        }
      }
    })
  }

  @action
  public fetchShopcartproductnum() {
    return this.$productService.queryShopcartproductnum().then((data) => {
      this.hideSpin();
      // console.log(data);
      const { shopCartProductNum } = data;
      if (shopCartProductNum) {
        this.products = shopCartProductNum.map((product) => new $Product(product));
        this.selectedAmount = shopCartProductNum.reduce((total, product) => {
          if (product.checked) {
            let price = product?.deductionAmount || 0;
            total += product.quantity * price;
          }
          return total;
        }, 0);
      }
      return data;
    });
  }

  @computed
  get selectedRows() {
    return this.products
      .filter((product) => product.checked);
  }

  @computed
  get totalCount() {
    return sum(this.products
      .filter((product) => product.checked)
      .map((product) => product.quantity));
  }

  @computed
  get getProductsParams() {
    this.productsParams = [];
    this.products.filter((product) => product.checked)
      .map((product) => {
        const { itemType, quantity, productSkuId } = product;
        switch (product.itemType) {
          case $CartType.ACTIVITSUITTYPE:
            const { code, productList } = product.activitySuit;
            productList.map((item) => {
              this.productsParams.push({
                activitySuitCode: code,
                activitySuitQuantity: quantity,
                itemType,
                productSkuId: item.productSkuId,
                quantity: item.quantity,
                imageUrl: product.activitySuit.imageUrl || "https://order.fwh1988.cn:14501/static-img/scm/ico-nopic.png"
              });
            });
            break;
          case $CartType.ITEMTYPE:
          case $CartType.VIRTUALSUIT:
          default:
            this.productsParams.push({
              activitySuitCode: "",
              activitySuitQuantity: "",
              itemType,
              productSkuId,
              quantity,
              imageUrl: product.itemType === $CartType.VIRTUALSUIT ? product.virtualSuit.imageUrl : product.product.imageUrl
            })
            break;
        }
        console.log(product);
      });
    sessionStorage.setItem("productsParams", JSON.stringify(this.productsParams))
    return this.productsParams;
  }

  @computed
  get totalCategories() {
    return this.products.filter((product) => product.checked).length;
  }

  @computed
  get totalAmount() {
    return sum(this.products
      .filter((product) => product.checked)
      .map((product) => {
        if (product.itemType === $CartType.ITEMTYPE) {
          if (product.product) {
            return product.quantity * product.product.orderPrice;
          }
        } else if (product.itemType === $CartType.VIRTUALSUIT) {
          return product.virtualSuit.amount * product.quantity;
        } else {
          return product.activitySuit.amount * product.quantity;
        }
      }));
  }

  @computed
  get totalMemberAmount() {
    return sum(this.products
      .filter((product) => product.checked)
      .map((product) => {
        if (product.itemType === $CartType.ITEMTYPE) {
          if (product.product) {
            return product.quantity * product.product.retailPrice;
          }
        } else if (product.itemType === $CartType.VIRTUALSUIT) {
          return product.virtualSuit.memberAmount * product.quantity;
        } else {
          return product.activitySuit.memberAmount * product.quantity;
        }
      }));
  }

  @computed
  get totalRetailAmount() {
    return sum(this.products
      .filter((product) => product.checked)
      .map((product) => {
        if (product.itemType === $CartType.ITEMTYPE) {
          if (product.product) {
            return product.quantity * product.product.retailPrice;
          }
        } else if (product.itemType === $CartType.VIRTUALSUIT) {
          return product.virtualSuit.retailAmount * product.quantity;
        } else {
          // 计算的是活动套装明细的初始化数量 * 零售价 * 购物车该套装的数量
          let price = 0;
          product.activitySuit.productList.map((item) => {
            price += item.retailPrice * item.quantity;
          });
          return price;
        }
      }));
  }

  @computed
  get allSelected() {
    return this.products.filter((product) => product.isActive !== $CartType.ISACTIVE_KEY).length === this.products.filter((product) => product.checked).length;
  }

  @action
  public setProducts(products: $Product[]) {
    this.products = products;
  }

  @action
  public minusQuantityByProduct(product) {
    const index = findIndex(this.products, { productSkuId: product.productSkuId });
    if (index > -1) {
      // this.products[index].quantity--;
      if (this.products[index].quantity === 0) {
        const removedProduct = this.products[index];
        if (this.products[index].productSkuId) {
          this.removeById(this.products[index].productSkuId);
        }
        /*this.$ProductMv.removeById(this.products[index].productSkuId);*/
        return removedProduct;
      }
      if (product.itemType === $CartType.ACTIVITSUITTYPE) {
        const { productList } = this.products[index].activitySuit;
        const newProductList = [];
        productList.length > 0 && productList.map((item) => {
          item.quantity = item.baseQuantity * this.products[index].quantity;
          newProductList.push(item);
        });
        this.products[index].activitySuit.productList = newProductList;
      }
    }
    return this.getProductById(product);
  }

  @action
  public addQuantityByProduct(product: $Product, count?) {
    const index = findIndex(this.products, { productSkuId: product.productSkuId });
    let newProduct = null;
    if (index > -1) {
      if (count !== undefined) {
        this.products[index].quantity = count;
        this.products[index].skuType = product.skuType;
        if (product.itemType === $CartType.ACTIVITSUITTYPE) {
          const { productList } = this.products[index].activitySuit;
          const newProductList = [];
          productList.length > 0 && productList.map((item) => {
            item.quantity = item.baseQuantity * count;
            newProductList.push(item);
          });
          this.products[index].activitySuit.productList = newProductList;
        }
      } else {
        this.products[index].quantity++;
        this.products[index].skuType = product.skuType;
      }
    } else {
      if (count !== undefined) {
        newProduct = new $Product({ ...product, quantity: count, checked: true, skuType: product.skuType });
        this.products.push(newProduct);
      } else {
        newProduct = new $Product({ ...product, quantity: 1, checked: true, skuType: product.skuType });
        this.products.push(newProduct);
      }
    }
    return newProduct ? newProduct : this.getProductById(product);
  }

  @action
  public getProductQuantity(product: $Product) {
    const index = findIndex(this.products, { productSkuId: product.productSkuId });
    if (index > -1) {
      return this.products[index].quantity;
    } else {
      return 0;
    }
  }

  @action
  public getProductById(product) {
    const index = findIndex(this.products, { productSkuId: product.productSkuId });
    if (index > -1) {
      return this.products[index];
    } else {
      return null;
    }
  }

  @action
  public selectAll(checked: boolean) {
    this.products.forEach((product) => {
      if (product.isActive !== $CartType.ISACTIVE_KEY) {
        product.checked = checked;
      }
    });
    if (!checked) {
      this.totalDeductionAmount = 0;
    }
  }

  @action
  public fetchProductPage(params) {
    return this.$productService.chooseProductPage(params);
  }
}
