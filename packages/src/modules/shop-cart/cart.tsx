import { autowired } from "@classes/ioc/ioc";
import { message, Spin } from "antd";
import { Button, Checkbox, Modal, Toast } from "antd-mobile";
import { observer } from "mobx-react";
import React from "react";
import { withRouter } from "react-router";
import styled from "styled-components";
import { $ActiveType } from "../../classes/const/$active-type";
import { $CartType } from "../../classes/const/$cart-type";
import { $ProductService } from "../../classes/service/$product-service";
import { Footer } from "../../components/footer/footer";
import { Table } from "../../components/table/normal-table";
import { SITE_PATH } from "../app";
import { $CartMv } from "./cart-mv";
import { AppStoreKey, $AppStore } from "../../classes/stores/app-store-mv";
import { $ReturnedSalesMv } from "../returned-sales/$returned-sales-mv";
import { $OrderPriceType } from "@classes/const/$order-price-type";
import { $ExceedQuotaStatus } from "@classes/const/$exceed-quota-status";
import { NewCustomModal } from "../../components/custom-modal/new-custom-modal";
import { cloneDeep, isEmpty, set, sum } from "lodash";
import { $AccountType } from "@classes/const/$account-type";
import { toFixedOptimizing } from "@classes/utils/DateUtils";
import { toJS } from "mobx";
import { $TransferHintMv } from "@classes/entity/$transfer-hint-mv";
import { $TransferHintService } from "@classes/service/$transfer-hint-service";
import { autoFormatNumber } from "@classes/utils/FormatAmount";
declare let window: any;
const CheckboxItem = Checkbox.CheckboxItem;
const alert = Modal.alert;

const IconFork = require("../../components/svg/icon_fork.svg");
const IconCheck = require("../../components/svg/icon_check.svg");

@withRouter
@observer
class ShopCart extends React.Component<any, any> {

  @autowired($AppStore)
  public $AppStore: $AppStore;

  @autowired($CartMv)
  public $CartMv: $CartMv;

  @autowired($ProductService)
  public $productService: $ProductService;

  @autowired($ReturnedSalesMv)
  public $ReturnedSalesMv: $ReturnedSalesMv;

  @autowired($TransferHintMv)
  public $TransferHintMv: $TransferHintMv;

  @autowired($TransferHintService)
  public $TransferHintService: $TransferHintService;

  public constructor(props) {
    super(props);
    this.state = {
      isShowBtn: false,
      showDiscountModal: false, // 显示折扣详情
    };
  }
  public saveMV = () => {
    const scrollHeight = $(".page-wrap").scrollTop();
    const params = {
      scrollHeight,
    }
    this.$AppStore.savePageMv(AppStoreKey.SHOPCART, params);
  }
  public componentWillUnmount(): void {
    this.saveMV();
  }
  public componentDidMount() {
    document.title = "购物车";
    // const params = { source: "shopCart" };
    console.log(this.props.location.state);
    this.$CartMv.fetchShopSchemeTips();
    if (this.props.location.state && this.props.location.state.shopCartInfoFromOrder && sessionStorage.getItem("isSetShopCartData") === "true") { // 从复制订单和编辑订单过来的
      this.$CartMv.setShopCartInfoFromOrder(this.props.location.state.shopCartInfoFromOrder);
      this.$CartMv.fetchShopScheme().then(() => {
         // 计算优惠额度
         const { openDiscountBtn } = this.$CartMv.shopSchemeInfo;
         if (openDiscountBtn) {
           if (this.$CartMv.getProductsParams.length === 0) {
             this.$CartMv.resetSplitTotalAmount();
             this.$CartMv.resetTotalDiscountAmount();
             this.$CartMv.resetExpectedDiscountAmount();
           } else {
            const params = {
              openDiscountCheck: this.$CartMv.isShowDiscount,
              oldSalesOrderId: sessionStorage.getItem("editOrderId") === "null" ? null : sessionStorage.getItem("editOrderId"),
              shopCartItemList: this.$CartMv.getProductsParams,
              shopCartTotalAmount: this.$CartMv.totalAmount ? Number(this.$CartMv.totalAmount).toFixed(2) : 0,
            };
            this.$CartMv.fetchItemSplit(params);
           }
         } else {
            this.$CartMv.isShowDiscount = false;
            if (this.$CartMv.getProductsParams.length > 0) {
              const params = {
                openDiscountCheck: false,
                oldSalesOrderId: sessionStorage.getItem("editOrderId") === "null" ? null : sessionStorage.getItem("editOrderId"),
                shopCartItemList: this.$CartMv.getProductsParams,
                shopCartTotalAmount: this.$CartMv.totalAmount ? Number(this.$CartMv.totalAmount).toFixed(2) : 0,
              };
              this.$CartMv.fetchItemSplit(params);
            }
         }
      })
      this.setState({
        isShowBtn: true,
      });
    } else {
      this.$AppStore.queryShopOverdue("", () => {
        this.$CartMv.showSpin();
        this.$CartMv.fetchShopScheme().then(() => {
          this.$CartMv.fetchCarts().then((res) => {

            // 从缓存中获取老数据
            let oldData = this.$AppStore.getPageMv(AppStoreKey.SHOPCART)
            if (oldData) {
              oldData = JSON.parse(oldData);
              const {
                scrollHeight,
              } = oldData;
              $(".page-wrap").scrollTop(scrollHeight);
              this.$AppStore.clearPageMv(AppStoreKey.SHOPCART);
            }
            this.setState({
              isShowBtn: true,
            });
            if (res.errorCode === $CartType.NOSCHEME) {
              this.$CartMv.hideSpin();
              this.$CartMv.setIsAgency();
              alert("暂未选择订货方案，是否继续进入订货方案开始订货", "", [
                {
                  text: "取消", onPress: () => {
                    // window.location.href = document.referrer;
                    history.go(-1);
                  },
                },
                {
                  text: "继续", onPress: () => {
                    // console.log(909090);
                    this.$AppStore.clearPageMv(AppStoreKey.ORDERSCHEMELIST);
                    this.$ReturnedSalesMv.businesssControlCheck({ orgId: res.orderPartyId, businessTypeControl: 'SalesOrderControl' }).then(data => {
                      if (data.errorCode == "-1") {
                        history.go(-1);
                        Toast.info(data.errorMessage)

                      } else {
                        this.props.history.push({
                          pathname: `/${SITE_PATH}/select-scheme`,
                          state: { orderPartyId: `${res.orderPartyId}` },
                        });
                      }
                    })

                  },
                },
              ]);
            }
            // 计算优惠额度
            const { openDiscountBtn } = this.$CartMv.shopSchemeInfo;
            if (openDiscountBtn) {
              if (this.$CartMv.getProductsParams.length === 0) {
                this.$CartMv.resetSplitTotalAmount();
                this.$CartMv.resetTotalDiscountAmount();
                this.$CartMv.resetExpectedDiscountAmount();
              } else {
                const params = {
                  openDiscountCheck: this.$CartMv.isShowDiscount,
                  oldSalesOrderId: sessionStorage.getItem("editOrderId") === "null" ? null : sessionStorage.getItem("editOrderId"),
                  shopCartItemList: this.$CartMv.getProductsParams,
                  shopCartTotalAmount: this.$CartMv.totalAmount ? Number(this.$CartMv.totalAmount).toFixed(2) : 0,
                };
                this.$CartMv.fetchItemSplit(params);
              }
            } else {
              this.$CartMv.isShowDiscount = false;
              if (this.$CartMv.getProductsParams.length > 0) {
                const params = {
                  openDiscountCheck: false,
                  oldSalesOrderId: sessionStorage.getItem("editOrderId") === "null" ? null : sessionStorage.getItem("editOrderId"),
                  shopCartItemList: this.$CartMv.getProductsParams,
                  shopCartTotalAmount: this.$CartMv.totalAmount ? Number(this.$CartMv.totalAmount).toFixed(2) : 0,
                };
                this.$CartMv.fetchItemSplit(params);
              }
            }
          });
        })
      }, true);
    }
  }

  public selectAll = (all: boolean) => {
    this.$CartMv.selectAll(all);
    const { allSelected } = this.$CartMv;
    let checked;
    if (allSelected) {
      checked = "Y";
    } else {
      checked = "N";
    }
    const params = {
      checked,
      operation: checked === "Y" ? "add" : "reduce",
    };
    this.$productService.batchCheckOrUnCheck(params)
      .then((res) => {
        console.log('xxx', this.$CartMv.allSelected)
        if (this.$CartMv.getProductsParams.length > 0) {
          this.onCheckedDiscount(this.$CartMv.isShowDiscount);
        } else {
          this.$CartMv.totalDeductionAmount = 0;
        }
      })
  }

  public onCheckedDiscount = (checked) => {
    if (checked) {
      this.$CartMv.openDiscount();
    } else {
      this.$CartMv.closeDiscount();
      this.$CartMv.resetSplitTotalAmount();
      this.$CartMv.resetTotalDiscountAmount();
      this.$CartMv.resetExpectedDiscountAmount();
    }

    const productsParams = this.$CartMv.getProductsParams;
    if (productsParams.length === 0) {
      this.$CartMv.resetSplitTotalAmount();
      this.$CartMv.resetTotalDiscountAmount();
      this.$CartMv.resetTotalDeductionAmount();
      this.$CartMv.resetExpectedDiscountAmount();
    }
    if (productsParams.length > 0) {
      const params = {
        openDiscountCheck: checked,
        oldSalesOrderId: sessionStorage.getItem("editOrderId") === "null" ? null : sessionStorage.getItem("editOrderId"),
        shopCartItemList: productsParams,
        shopCartTotalAmount: this.$CartMv.totalAmount ? Number(this.$CartMv.totalAmount).toFixed(2) : 0,
      };
      this.$CartMv.fetchItemSplit(params);
    }
  }

  public gotoSubmitOrder = () => {
    if (this.$CartMv.totalCount === 0) {
      Toast.fail("购物车是空的");
      return;
    }
    const { discountInfo, getProductsParams, totalAmount, splitTotalAmount } = this.$CartMv;
    let products = getProductsParams
    if (discountInfo && discountInfo.shopCartItemList.length > 0) {
      products = discountInfo.shopCartItemList
      sessionStorage.setItem("productsParams", JSON.stringify(discountInfo.shopCartItemList))
    }
    const params = {
      openDiscountCheck: this.$CartMv.isShowDiscount,
      oldSalesOrderId: sessionStorage.getItem("editOrderId") === "null" ? null : sessionStorage.getItem("editOrderId"),
      shopCartItemList: products,
      shopCartTotalAmount: splitTotalAmount ? Number(splitTotalAmount).toFixed(2) : (totalAmount ? Number(totalAmount).toFixed(2) : 0),
    };
    this.$CartMv.fetchCarts().then((res) => {
      this.$CartMv.fetchProductPage(params).then((data) => {
        if (data.errorCode === $CartType.NO_USE_ORDER_PRICE) { // 没有使用优惠订货价
          this.setState({ showDiscountModal: true })
          alert(<div style={{lineHeight: "24px"}}>存在商品未使用"优惠订货价"，是否继续提交？<br/>
            <span style={{ color: "#666", fontSize: "16px" }}>可通过"额度明细"查看详情</span></div>, "", [
            {
              text: "取消", onPress: () => {
              },
            },
            {
              text: "确认提交", onPress: () => {
                if (data.nextPage === $CartType.NEXTPAGE) {// 去促销
                  this.$AppStore.clearPageMv(AppStoreKey.PROMOTIONMATCH);
                  this.props.history.push({
                    pathname: `/${SITE_PATH}/promotion/match`, state: {
                      orderId: sessionStorage.getItem("editOrderId"),
                      shopCartTotalAmount: this.$CartMv.totalAmount ? Number(this.$CartMv.totalAmount).toFixed(2) : 0,
                      backSource: this.props.location.state ? this.props.location.state.backSource : "single",
                      hasPromotion: data.hasPromotion,
                    },
                  });
                } else {
                  this.props.history.push({
                    pathname: `/${SITE_PATH}/submit/order`, state: {
                      orderId: sessionStorage.getItem("editOrderId"),
                      shopCartTotalAmount: this.$CartMv.totalAmount ? Number(this.$CartMv.totalAmount).toFixed(2) : 0,
                      backSource: this.props.location.state ? this.props.location.state.backSource : "single",
                      prePageSource: "cart",
                      hasPromotion: data.hasPromotion,
                    },
                  });
                }
              },
            }
          ]);
        } else if (data.errorCode === $CartType.ERRORCODEDZ) { // 店长下单
          alert(`${data.errorMessage}`, "", [
            {
              text: "取消", onPress: () => {
              },
            },
          ]);
        } else if (data.errorCode === $CartType.ERRORCODEJXS) { // 经销商下单
          alert(`${data.errorMessage}`, "", [
            {
              text: "取消", onPress: () => {
              },
            },
            {
              text: "去还款", onPress: () => {
                this.$AppStore.clearPageMv(AppStoreKey.ACCOUNTINFOLIST);
                this.props.history.push({
                  pathname: `/${SITE_PATH}/account-info-list/${data.creditAccountId}/XYZH`,
                });
              },
            },
          ]);
        } else if (data.errorCode === $CartType.ERRORCODEXE) { // 限额
          alert(`${data.errorMessage}`, "", [
            {
              text: "确认", onPress: () => {
              },
            },
          ]);
        } else if (data.errorCode === $CartType.ONEMINORDERLIMIT) { // 最小限额
          alert(`${data.errorMessage}`, "", [
            {
              text: "确认", onPress: () => {
              },
            },
          ]);
        } else if (data.errorCode === $CartType.TIMELIMIT) { // 下单次数限制
          alert(`${data.errorMessage}`, "", [
            {
              text: "确认", onPress: () => {

              },
            },
          ]);
        } else if (data.errorCode === $CartType.COUNTLIMIT) { // 最大购买数量限制
          alert(`${data.errorMessage}`, "", [
            {
              text: "确认", onPress: () => {

              },
            },
          ]);
        } else if (data.errorCode === $CartType.GROUPING) { // 商品分组不符合限制
          alert(`${data.errorMessage}`, "", [
            {
              text: "确认", onPress: () => {

              },
            },
          ]);
        } else if (data.errorCode === $CartType.NOORDER) { // 逾期不可订货
          alert(`${data.errorMessage}`, "", [
            {
              text: "取消", onPress: () => {
              },
            },
            {
              text: "去还款", onPress: () => {
                this.$AppStore.clearPageMv(AppStoreKey.ACCOUNTINFOLIST);
                this.props.history.push({
                  pathname: `/${SITE_PATH}/account-info-list/898989/XYZH`,
                });
              },
            },
          ]);
        } else if (data.errorCode === $CartType.NOSEND) { // 费用单预期
          alert(`${data.errorMessage}`, "", [
            {
              text: "取消", onPress: () => {
              },
            },
            {
              text: "去付款", onPress: () => {
                this.props.history.push({ pathname: `/${SITE_PATH}/expense-node-list/Payment/null` });
              },
            },
          ]);
        } else if (data.nextPage === $CartType.NEXTPAGE) {// 去促销
          this.$AppStore.clearPageMv(AppStoreKey.PROMOTIONMATCH);
          this.props.history.push({
            pathname: `/${SITE_PATH}/promotion/match`, state: {
              orderId: sessionStorage.getItem("editOrderId"),
              shopCartTotalAmount: this.$CartMv.totalAmount ? Number(this.$CartMv.totalAmount).toFixed(2) : 0,
              backSource: this.props.location.state ? this.props.location.state.backSource : "single",
              hasPromotion: data.hasPromotion,
            },
          });
        } else if (data.errorCode === $CartType.ORG_ORDER_ISORDEMUST_ERROR || data.errorCode === $CartType.BALANCE_ERROR
          || data.errorCode === $CartType.NO_BALANCE_ACCOUNT
        ) {
          alert(`${data.errorMessage}`, "", [
            {
              text: "确认", onPress: () => {

              },
            },
          ]);
        } else if (data.errorCode === $CartType.NO_USE_DEDUCTION) {
          alert(`${data.errorMessage}`, "", [
            {
              text: "确认", onPress: () => {
                window.location.reload();
              },
            },
          ]);
        } else {
          this.props.history.push({
            pathname: `/${SITE_PATH}/submit/order`, state: {
              orderId: sessionStorage.getItem("editOrderId"),
              shopCartTotalAmount: this.$CartMv.totalAmount ? Number(this.$CartMv.totalAmount).toFixed(2) : 0,
              backSource: this.props.location.state ? this.props.location.state.backSource : "single",
              prePageSource: "cart",
              hasPromotion: data.hasPromotion,
            },
          });
        }
      });
    });
  }

  public onShowTransferModal = (canTransferAmount) => {
    const { shopSchemeInfo, transferAccountId, accountTypeCode, totalDeductionAmount } = this.$CartMv;
    const params: any = {
      accountId: transferAccountId,
      transferInOrgId: shopSchemeInfo.orderPartyId,
    };
    if (accountTypeCode === $AccountType.RETURN_FREE_DISTRIBUTION) {
      this.$CartMv.openMoreStore = false;
      params.amount = totalDeductionAmount;
      this.$CartMv.loadPzTransferInfo(params)
        .then(() => {
          this.setState({ showTransferModal: true });
        });
    } else {
      params.amount = canTransferAmount;
      this.$CartMv.loadNewTransferInfo(params)
        .then(() => {
          this.setState({ showTransferModal: true });
        });
    }
  }

  public submitPzTransfer = () => {
    const { shopcartTransferInfo } = this.$CartMv;
    const { transferOutList, transferInOrgId } = shopcartTransferInfo;
    const params: any = {
      transferInOrgId,
      transferOutList: transferOutList.filter((item) => item.checked),
    };
    if (params.transferOutList.length === 0) {
      message.warning("请选择转出门店");
      return;
    }
    this.$TransferHintService.pzTransferSubmit(params)
      .then((res) => {
        const { result, errorMsg } = res;
        if (result) {
          this.setState({ showTransferModal: false });
          message.success("转账成功", 2, () => {
            window.location.reload();
          });
        } else {
          alert(`${errorMsg}`, "", [
            {
              text: "确认", onPress: () => {
                window.location.reload();
              },
            },
          ]);
        }
      });
  }

  public submitTransfer = () => {
    if (this.$CartMv.accountTypeCode === $AccountType.RETURN_FREE_DISTRIBUTION) {
      this.submitPzTransfer();
      return;
    }
    const { shopcartTransferInfo } = this.$CartMv;
    const { transferList } = shopcartTransferInfo;
    let params: any;
    transferList.map((transferIn: any) => {
      const { transferInOrgId, transferInAccountId, transferOutList } = transferIn;
      const checkedTransferAmount: number = toFixedOptimizing(sum(transferOutList
        .filter((item) => item.checked)
        .map((item) => item.transferOutAmount),
      ));
      const submitTransferOutList = [];
      transferOutList.map((transferOut: any) => {
        const { transferOutOrgId, transferOutAccountId, transferOutAmount, checked } = transferOut;
        if (checked) {
          submitTransferOutList.push({ transferOutOrgId, transferOutAccountId, amount: transferOutAmount });
        }
      });
      if (submitTransferOutList.length > 0) {
        params = {
          transferOutList: submitTransferOutList,
          transferInOrgId,
          transferInAmount: checkedTransferAmount,
          transferInAccountId,
        }
      }
    });
    if (!params) {
      message.warning("请选择转出门店");
      return;
    }

    this.$TransferHintMv.manualSubmitTransferInfo(params)
      .then((res) => {
        const { result, errorMsg } = res;
        this.setState({ showTransferModal: false });
        if (result) {
          message.success("转账成功", 2, () => {
            window.location.reload();
          });
        } else {
          alert(`${errorMsg}`, "", [
            {
              text: "确认", onPress: () => {
                window.location.reload();
              },
            },
          ]);
        }
      });
  }

  public onChangeTransferOut = (inIndex, outIndex, value) => {
    const { shopcartTransferInfo } = this.$CartMv;
    const { transferList } = shopcartTransferInfo;
    const newTransferList = cloneDeep(toJS(transferList));
    set(newTransferList, `[${inIndex}].transferOutList.[${outIndex}].checked`, value);
    this.$CartMv.shopcartTransferInfo.transferList = newTransferList;
  }

  public onChangePzTransferOut = (outIndex, value) => {
    const { shopcartTransferInfo } = this.$CartMv;
    const { transferOutList } = shopcartTransferInfo;
    const newTransferOutList = cloneDeep(toJS(transferOutList));
    set(newTransferOutList, `[${outIndex}].checked`, value);
    this.$CartMv.shopcartTransferInfo.transferOutList = newTransferOutList;
    if (value) {
      this.$CartMv.shopcartTransferInfo.pzTransferInDiff += newTransferOutList[outIndex].pzTransferOutAmount;
      this.$CartMv.shopcartTransferInfo.hfpzTransferInDiff += newTransferOutList[outIndex].hfpzTransferOutAmount;
    } else {
      this.$CartMv.shopcartTransferInfo.pzTransferInDiff -= newTransferOutList[outIndex].pzTransferOutAmount;
      this.$CartMv.shopcartTransferInfo.hfpzTransferInDiff -= newTransferOutList[outIndex].hfpzTransferOutAmount;
    }
  }

  public renderTransferInfo = () => {
    const { shopcartTransferInfo, accountTypeCode, openMoreStore } = this.$CartMv;
    if (accountTypeCode === $AccountType.RETURN_FREE_DISTRIBUTION) {
      const { transferOutList, transferInOrgName, pzTransferInAmount, hfpzTransferInAmount, pzTransferInDiff, hfpzTransferInDiff } = shopcartTransferInfo;
      return <TransferInfo>
        <TransferBox>
          <div className="store-name">{transferInOrgName}</div>
          <div className={"pz-in-info"}>
            <div className="pz-left">
              本单还需转入：
            </div>
            <div className="pz-right">
              {
                pzTransferInDiff < 0 ? <div className="error">还差{autoFormatNumber(Math.abs(pzTransferInDiff))}额度 <IconFork/></div> : <div>共{pzTransferInAmount}额度 <IconCheck/></div>
              }
              {
                hfpzTransferInDiff < 0 ? <div className="error">还差{autoFormatNumber(Math.abs(hfpzTransferInDiff))}上限 <IconFork/></div> : <div>共{hfpzTransferInAmount}上限 <IconCheck/></div>
              }
            </div>
          </div>
          <div className="out-title">转出门店</div>
          <div className={"org-out-info pz"}>
            {
              !isEmpty(transferOutList) ? (openMoreStore ? transferOutList : transferOutList.slice(0, 5)).map((transferOut: any, outIndex) => {
                const { transferOutAccountId, orgName, checked, hfpzTransferOutAmount, hfpz_accountBalance, pzTransferOutAmount, pz_accountBalance } = transferOut;
                return <CheckboxItem
                  key={transferOutAccountId}
                  onChange={(e) => this.onChangePzTransferOut(outIndex, e.target.checked)}
                  checked={checked}
                  className={`${checked ? "transfer-out-checked" : "transfer-out-unchecked"}`}
                >
                  <div className={"info"}>
                    <div className={"counter"}>{orgName}</div>
                    <div className={"pz-amount"}>
                      <div>{pzTransferOutAmount}/{pz_accountBalance}额度</div>
                      <div>{hfpzTransferOutAmount}/{hfpz_accountBalance}上限</div>
                    </div>
                  </div>
                </CheckboxItem>;
              }) : <div className="empty-counter">无可转账门店</div>
            }
          </div>
          {
            !isEmpty(transferOutList) && transferOutList.length > 5 && !openMoreStore ? <div className="more-store" onClick={this.$CartMv.showMoreStore}>
              选择更多门店 <i className="scmIconfont scm-icon-arrow-down"/>
            </div> : null
          }
        </TransferBox>
      </TransferInfo>;
    } else {
      const { transferList, totalWaitPayAmount } = shopcartTransferInfo;
      return <TransferInfo>
        {
          !isEmpty(transferList) && transferList.map((transferIn: any, inIndex) => {
            const {
              transferInOrgId, transferInOrgName, transferInAccountType, transferInAccountAmount, transferInAmount,
              transferOutList } = transferIn;
            let waitTransferAmount: number = 0; // 待转入金额
            if (transferInAccountType === $AccountType.DEDUCTION_LIMIT || transferInAccountType === $AccountType.RETURN_FREE_DISTRIBUTION) {
              waitTransferAmount = toFixedOptimizing(sum(transferOutList
                .map((item) => item.transferOutAmount),
              ));
            }
            let checkedTransferAmount: number = 0; // 已选择金额
            if (transferInAccountType === $AccountType.DEDUCTION_LIMIT || transferInAccountType === $AccountType.RETURN_FREE_DISTRIBUTION) {
              checkedTransferAmount = toFixedOptimizing(sum(transferOutList
                .filter((item) => item.checked)
                .map((item) => item.transferOutAmount),
              ));
            }
            const maxTransferAmount = toFixedOptimizing(sum(transferOutList.map((item) => item.transferOutAmount)));
            return <TransferBox key={transferInOrgId}>
              <div className={"org-in-name"}>
                <div className="org-name">{transferInOrgName} <span>（当前额度{transferInAccountAmount}）</span></div>
                <div className={"org-in-info"}>
                  {`本店额度不足，本单支付还需转入`}
                  <span className="red">{toFixedOptimizing(totalWaitPayAmount)}</span>
                  {`额度。已选`}
                  <span className="red">{toFixedOptimizing(checkedTransferAmount)}</span>
                  {`额度。`}
                  {
                    maxTransferAmount < transferInAmount &&
                    <>
                      {`最多可转入`}
                      <span className={"red"}>{toFixedOptimizing(maxTransferAmount)}</span>
                      {`额度。`}
                    </>
                  }
                </div>
              </div>
              <div className={"org-out-info"}>
                {
                  !isEmpty(transferOutList) ? transferOutList.map((transferOut: any, outIndex) => {
                    const { transferOutAccountId, transferOutOrgName, transferOutAmount, checked} = transferOut;
                    return <CheckboxItem
                      key={transferOutAccountId}
                      onChange={(e) => this.onChangeTransferOut(inIndex, outIndex, e.target.checked)}
                      checked={checked}
                      className={`${checked ? "transfer-out-checked" : "transfer-out-unchecked"}`}
                    >
                      <div className={"info"}>
                        <div className={"counter"}>{transferOutOrgName}</div>
                        <div className={"amount"}>转出<span className="red">{transferOutAmount}</span>额度</div>
                      </div>
                    </CheckboxItem>
                  }) : <div className="empty-counter">无可转账门店</div>
                }
              </div>
            </TransferBox>;
          })
        }
      </TransferInfo>;
    }
  }

  public render() {
    const { products, page, totalRetailAmount, totalCount, totalAmount, totalMemberAmount, allSelected, selectedAmount, hasToken,
      isSpin, isAgency, orderPriceViewPermission, retailPriceViewPermission, orderSchemeType, shopSchemeInfo, isShowDiscount,
      discountInfo, splitTotalAmount, totalDeductionAmount, accountTypeCode, totalQuotaAmount, quotaAmount, shopcartTransferInfo } = this.$CartMv;
    let shopCartItemList = []
    if (discountInfo) {
      shopCartItemList = discountInfo.shopCartItemList;
    }
    const { showDiscountModal, showTransferModal } = this.state;
    const needTransferAmount = selectedAmount - quotaAmount > 0 ? toFixedOptimizing(selectedAmount - quotaAmount) : 0;
    const isPz = accountTypeCode === $AccountType.RETURN_FREE_DISTRIBUTION;
    const hideConfirmBtn = (!isPz && shopcartTransferInfo?.transferList[0].transferOutList.length <= 0) || false;
    return (
      <Wrapper className="page-wrap">
        <Spin spinning={isSpin}>
          <TableWrapper>
            <Table
              checkable={true}
              isCart={true}
              data={products}
              page={page}
              suppressSearchMenuBar={true}
              pricePermission={orderPriceViewPermission}
              retailPriceViewPermission={retailPriceViewPermission}
              orderSchemeType={orderSchemeType}
              shopSchemeInfo={shopSchemeInfo}
              totalAmount={totalAmount}
              isShowDiscount={isShowDiscount}
            />
          </TableWrapper>
        </Spin>
        {
          (totalDeductionAmount > 0 || totalAmount > 0 || splitTotalAmount > 0) && accountTypeCode !== "" && <STips>
            <div className="content">
              当前门店可用额度{quotaAmount}，总可用额度{totalQuotaAmount}
              {
                needTransferAmount !== 0 && <div>本单支付还差{needTransferAmount}</div>
              }
            </div>
            {
                needTransferAmount !== 0 && hasToken && <div className="btn" onClick={() => this.onShowTransferModal(needTransferAmount)}>去转账</div>
            }
          </STips>
        }
        <ToPay>
          <span className="select-button" onClick={() => this.selectAll(!allSelected)}>
            <CheckboxItem checked={allSelected}
              // onChange={(e) => this.selectAll(e.target.checked)}
            />
            <span style={{ position: "relative", top: "3px" }}>全选</span>
          </span>
          {/*已选中 {totalCategories} 种 {totalCount} 件*/}
          <span className="price">
            {
              totalDeductionAmount > 0 && <p>订货额度抵扣合计：<span style={{ color: "#FF3636" }}>¥{totalDeductionAmount}</span></p>
            }
            {
              (orderPriceViewPermission === $CartType.ORDERPRICEVIEWPERMISSION && totalDeductionAmount === 0) ?
                <p>合计：
                  <span style={{ color: "#FF3030", fontSize: "18px" }}>
                    {isShowDiscount ? Number(splitTotalAmount).toFixed(2) : Number(totalAmount).toFixed(2) }
                  </span>
                </p> : null
            }
            {
              retailPriceViewPermission === $CartType.RETAILPRICEVIEWPERMISSION ?
                <p>
                  零售价合计：
                  <span style={{ color: "#FF3030" }}>{totalRetailAmount ? Number(totalRetailAmount).toFixed(2) : 0.00}</span>
                  不含运费
                  {
                    isShowDiscount && shopSchemeInfo && shopSchemeInfo.openDiscountBtn && orderPriceViewPermission === $CartType.ORDERPRICEVIEWPERMISSION &&
                    <span style={{ color: 'rgba(65, 123, 238, 1)' }} onClick={() => this.setState({ showDiscountModal: true })}>额度明细</span>
                  }
                </p> : null
            }
          </span>
          {
            this.state.isShowBtn && <Button
              loading={isSpin}
              className="pay"
              onClick={this.gotoSubmitOrder}
            >
              去下单
            </Button>
          }
        </ToPay>
        <Footer
          isAgency={isAgency}
          activeKey={$ActiveType.CAR_KEY}
          count={totalCount}
          leaveCurrentPage={this.saveMV}
        />
        {
          showDiscountModal && <Modal
            popup
            visible={this.state.showDiscountModal}
            onClose={() => this.setState({showDiscountModal: false})}
            animationType="slide-up"
          >
            <DiscountModal>
              <div className="title">额度明细 <img
                onClick={() => this.setState({ showDiscountModal: false })}
                src="https://cdnweixin.haoduoke.cn/images/fwh/close-icon.png" /></div>
              {
                shopCartItemList.length > 0 && shopCartItemList.map((item) => {
                  return <div className="cart-item-goods" key={item.productSkuId}>
                    <img
                    src={item.imageUrl  ? item.imageUrl : "https://order.fwh1988.cn:14501/static-img/scm/ico-nopic.png"}
                    alt=""/>
                    <div className="discount-info">
                      <div className="discount-name">{item.name}</div>
                      {
                        item.orderPriceType === $OrderPriceType.DISCOUNT ? <div className="discount-detail">
                          <span className="discount-tag">优惠</span>
                          <span className="discount-amount">¥<span style={{ fontSize: 15 }}>{item.discountOrderPrice}</span></span>
                        </div> :  <div className="discount-detail">
                          <span className="default-tag">常规</span>
                          <span className="discount-amount">¥<span style={{ fontSize: 15 }}>{item.orderPrice}</span></span>
                        </div>
                      }
                    </div>
                    <div className="discount-quantity">×{item.quantity}</div>
                  </div>
                })
              }
            </DiscountModal>
          </Modal>
        }
        {
          showTransferModal && <NewCustomModal
            header={"转账详情"}
            hideConfirm={hideConfirmBtn}
            confirm={this.submitTransfer}
            content={this.renderTransferInfo()}
            confirmName={"确认转账"}
            visible={showTransferModal}
            close={() => this.setState({ showTransferModal: false })}
          />
        }
      </Wrapper>
    );
  }
}

export default ShopCart;

const TransferBox = styled.div`// styled
  & {
    width: 100%;

    .org-in-name {
      color: #333333;
      margin-bottom: 10px;
      padding: 12px;
      border: 1px solid #E8E8E8;
      border-radius: 4px;

      .org-name {
        font-size: 16px;
        font-weight: 500;

        > span {
          font-size: 14px;
          color: #333333;
          font-weight: 400;
        }
      }

      .max-transfer-tip {
        margin-left: 10px;
        font-size: 12px;
        color: #FFA630;
      }
    }
    .store-name, .out-title{
      font-size: 16px;
      font-weight: 500;
      color: #333;
      margin-bottom: 10px;
    }
    .pz-in-info{
      display: flex;
      border-radius: 4px;
      background: #F8F8F8;
      justify-content: space-between;
      align-items: center;
      padding: 10px;
      margin-bottom: 20px;
      .pz-left{
        font-weight: 500;
        color: #333;
      }
      .pz-right{
        >div{
          line-height: 20px;
          display: flex;
          align-items: center;
          color: #437DF0;
          justify-content: flex-end;
          &.error{
            color: #FF3030;
          }
          svg{
            margin-left: 5px;
          }
        }
      }
    }
    .org-in-info {
      font-size: 14px;
      color: #333333;
      .red {
        color: #FF3030;
      }
    }
    .org-out-info {
      margin-bottom: 10px;

      &.pz{
        max-height: 180px;
        overflow-y: auto;
      }

      .am-list-item{
        border-radius: 4px;
        padding: 0px 8px !important;
        margin-top: 4px !important;
        .am-list-line{
          padding-right: 0;
        }
      }

      .empty-counter {
        text-align: center;
        font-size: 14px;
        color: #666666;
      }

      .transfer-out-checked {
        background: #E8F0FF;
      }

      .transfer-out-unchecked {
        background: #F8F8F8;
      }

      .info {
        display: flex;
        font-size: 14px;
        color: #333333;
        justify-content: space-between;
        margin-left: 5px;
        align-items: center;
        .counter {
          display: -webkit-box;
          -webkit-box-orient: vertical;
          -webkit-line-clamp: 2;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: break-spaces;
          font-weight: 500;
        }
        .amount {
          > span {
            color: #FF3030;
          }
        }
        .pz-amount{
          text-align: right;
        }
      }
      .am-list-item {
        padding: 0;
      }
      .am-list-item .am-list-thumb:first-child {
        margin-right: 5px;
      }
      .am-checkbox-inner {
        width: 19px;
        height: 19px;
      }
      .am-checkbox-inner:after {
        top: 2.5px;
        right: 6px;
      }
      .am-checkbox.am-checkbox-checked .am-checkbox-inner {
        border-color: #437DF0;
        background: #437DF0;
      }
    }
    .more-store{
      text-align: center;
      color: #666;
    }
  }
`;

const TransferInfo = styled.div`// styled
  &{
    width: 100%;
    .amount {
      // color: #FF3030;
    }
    .amount:before {
      // content: "¥";
      zoom: .85;
      font-size: 12px;
      margin-left: 5px;
    }
    .tip {
      display: block;
      font-size: 12px;
      color: #666666;
      margin-bottom: 10px;
    }
  }
`;

const STips = styled.div`
  & {
    position: fixed;
    bottom: 100px;
    width: 100%;
    background: #FBF1E8;
    display: flex;
    justify-content: space-between;
    padding: 5px;
    align-items: center;

    .content {
      color: #333333;
      font-size: 13px;
      flex: 1;
      >div{
        color: #FA6400;
      }
    }

    .btn {
      background: #FF3030;
      color: #FFF;
      font-size: 14px;
      border-radius: 2px;
      padding: 4px 12px;
    }
  }
`

const Wrapper = styled.div`// styled
  & {
    display: flex;
    flex-direction: column;
    height: 100%;
    overflow: scroll;
  }
`;

const DiscountModal = styled.div`
  & {
    display: flex;
    flex-direction: column;
    margin-bottom: 30px;
    max-height: 400px;

    .title {
      color: #333333;
      margin-top: 20px;
      margin-bottom: 20px;
      font-weight: 500;

      > img {
        position: absolute;
        width: 15px;
        height: 15px;
        margin-left: 34%;
      }
    }

    .cart-item-goods {
      display: flex;
      align-items: center;
      margin: 10px 20px;

      >img {
        width: 52px;
        height: 52px;
      }

      .discount-info {
        margin-left: 20px;
        line-height: 16px;

        .discount-name {
          color: rgba(51, 51, 51, 1);
          font-weight: 500;
          font-size: 14px;
          text-align: left;
        }

        .discount-detail {
          display: flex;
          margin-top: 5px;
          align-items: center;
        }

        .discount-tag {
          border: 1px solid rgba(255, 54, 54, 1);
          border-radius: 10px;
          background-color: rgba(255, 66, 49, 0.10);
          padding: 1px 6px;
          font-size: 11px;
          color: rgba(255, 54, 54, 1);
        }

        .default-tag {
          border: 1px solid rgba(255, 166, 48, 1);
          border-radius: 10px;
          background-color: rgba(255, 66, 49, 0.10);
          padding: 1px 6px;
          font-size: 11px;
          color: rgba(255, 166, 48, 1);
        }

        .discount-amount {
          color: rgba(255, 54, 54, 1);
          font-size: 10px;
          margin-left: 5px;
        }

        .original-amount {
          color: rgba(153, 153, 153, 1);
          font-size: 10px;
          margin-left: 5px;
        }
      }

      .discount-quantity {
        margin-left: auto;
        color: #666666;
        font-size: 11px;
      }
    }
  }
`

const TableWrapper = styled.div`// styled
  & {
    display: inline-block;
    flex: 1;
    width: 100%;
    border: 0;
    .am-list .am-list-body {
      border: 0;
    }
    .am-checkbox-inner:after {
      position: absolute;
      top: 2.5px;
      right: 7px;
    }
  }
`;

const ToPay = styled.div`// styled
  & {
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: fixed;
    width: 100%;
    bottom: 50px;
    height: 50px;
    background: #FFFFFF;
    // border-top: 1px solid #D8D8D8;
    z-index: 99;
    padding: 5px 12px 5px 5px;
    .select-button{
      line-height: 36px;
      display: flex;
    }
    > span {
      display: inline-block;
      .am-list-item {
        align-items: baseline;
      }

      .am-list-item .am-list-line {
        padding-right: 0;
      }
      .am-list-item .am-list-thumb:first-child {
        margin-right: 5px;
      }
      .am-checkbox {
        width: 18px !important;
        height: 18px !important;
      }
    }
    .pay {
      width: 88px;
      text-align: center;
      height: 36px;
      line-height: 36px;
      border-radius: 20px;
      color: #ffffff;
      background: #307DCD;
      font-family: "MicrosoftYaHei";
      margin-left: 10px;
      font-size: 14px;
    }
    > .price {
      display: inline-block;
      width: 170px;
      height: 40px;
      position: relative;
      > p {
        margin-bottom: 0px;
        text-align: right;
      }
      > p:nth-of-type(1) {
        font-size: 12px;
        font-family: "PingFangSC-Regular";
        color: rgba(51, 51, 51, 1);
      }
      > p:nth-of-type(2), p:nth-of-type(3) {
        font-size: 10px;
        font-family: "PingFangSC-Regular";
        color: rgba(117, 117, 117, 1);
      }
    }
  }
`;
