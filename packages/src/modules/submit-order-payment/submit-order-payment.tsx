import { autowired } from "@classes/ioc/ioc";
import { Spin } from "antd";
import { ActivityIndicator, Button, Checkbox, List, Modal, Toast } from "antd-mobile";
import { toJS } from "mobx";
import { observer } from "mobx-react";
import * as React from "react";
import { withRouter } from "react-router";
import TransferHint from "../../components/transfer-hint/transfer-hint"
import PaymentLine from "../../components/payment-line/payment-line";
import styled from "styled-components";
import DateUtils, { toFixedOptimizing } from "../../classes/utils/DateUtils";
import { SITE_PATH } from "../app";
import { $SubmitOrderMv } from "../submit-order/submit-order-mv";
import { $SubmitOrderPaymentMv } from "./submit-order-payment-mv";
import "./submit-order-payment.less";
import { ExcessIndication } from "../../components/excess-indication/excess-indication";
import { CapitalAccountListMobel } from "../../components/capital-account-list-mobel/capital-account-list-mobel";
import { $PaymentModeType } from "../../classes/const/$payment-mode-type";
import { WxUploadImgMv } from "../../components/wx-upload-img/wx-upload-img-mv";
import { ScrollFreezeTitleBar } from "../../components/scroll-freeze-title-bar";
import { SinglePaymentModeList } from "../../components/single-payment-mode-list/single-payment-mode-list";
import { $PaymentType } from "../../classes/const/$payment-type";
import { ChooseBankCardMv } from "../../components/choose-bank-card-modal/choose-bank-card-mv";
import { $SinglePaymentModeList } from "../../classes/entity/$single-payment-mode-list";
import { OrderPaymentAccountShow } from "../../components/order-payment-account-show/order-payment-account-show";
import { getUrlParam } from "../../classes/utils/UrlUtils";
import { find, indexOf, isEmpty, sum } from "lodash";
import { $OrderType } from "../../classes/const/$order-type";
import { $AppStore, AppStoreKey } from "../../classes/stores/app-store-mv";
import { $ScrollFreezeTitleBarMV } from "../../components/scroll-freeze-title-bar/index-mv";
import { $TransferType } from "@classes/const/$transfer-type";
import { $ValidityType, ValidityTypeName } from "@classes/const/$validity-type";

const Item = List.Item;
const Brief = Item.Brief;
const CheckboxItem = Checkbox.CheckboxItem;
const alert = Modal.alert;

declare let WeixinJSBridge: any;
declare let window: any;

@withRouter
@observer
class SubmitOrderPayment extends React.Component<any, any> {
  @autowired($AppStore)
  public $AppStore: $AppStore;

  @autowired($SubmitOrderPaymentMv)
  public $myMv: $SubmitOrderPaymentMv;

  @autowired($ScrollFreezeTitleBarMV)
  public $ScrollFreezeTitleBarMV: $ScrollFreezeTitleBarMV;

  @autowired($SubmitOrderMv)
  public $SubmitOrderMv: $SubmitOrderMv;

  @autowired(WxUploadImgMv)
  public wxUploadImgMv: WxUploadImgMv;

  @autowired(ChooseBankCardMv)
  public chooseBankCardMv: ChooseBankCardMv;

  constructor(props) {
    super(props);
    this.state = {
      isShowMabel: false,
      balance: false,
      pay: 0,
      record: "",
      orderId: "",
      confirmAmount: 0,
      isShowAccount: false,
      percent: 0,
      showProgress: false,
      date: null,
      feeDocumentId: null,
      overTop: false,
      animating: false,
      selectedPaymentList: [],
      selectCapitalAccountInfo: { // 线下转账选择银行卡
        bankName: "",
        accountCode: "",
        bankAccountName: "",
        capitalAccountId: "",
      },
      showQuestionHelp: false,
      activekey: "",
      isChooseAccount: true, // 是否展示收款账户（通联）
      tpRate: 0, // 服务费 （通联支付）
      isShowChooseBankCardModal: false, // 展示选择银行卡modal(通联)
      selectedBankName: null, // 选择银行卡（通联）
      selectedBankCardType: null, // 选择银行卡（通联
      selectedBankCardNo: null, // 选择银行卡（通联)
      selectedBankId: null, // 选择银行卡（通联）
      transferAmount: 0, // 线下转账
      tpTransferAmount: 0, //  调整金额（通联）
      showAmountModal: false, // 展示调整金额modal（通联）
      isAdjust: false,
      needBind: false,
      canPartialPay: false,
      bindUrl: null,
      bankInfo: {
        bankCardRate: 0,
      },
      isChangeCard: false,
      showRate: false,
      selectRateList: null,
      isShowPaymentLine: false,   // 是否展示付款线路
      subMerchantList: [],  // 可选付款线路
      originSubMerchantList: [],  // 用于恢复初始状态
      isShowTransferHint: false,  // 是否展示转账
      totalWaitPayAmount: 0,  // 转账相关参数
      accountIdList: [],  // 转账相关参数
      divideRebateTransferAmount: 0, // 分账返利转账金额
      disabledDivideRebate: false, // 禁用分账返利
      disabledTransfer: false, // 禁用转账
      isShowCreditModal: false, // 显示信用支付模式弹窗
      validityType: "", // 默认新模式信用（无限期信用）：N，常规信用：Y
      isShowMoreValidityType: true, // 是否显示信用支付方式
      isCreditLimit: false, // 是否限制信用支付方式
      disabledCredit: false, // 禁用信用支付
      isShowCreditTips: false, // 是否显示信用支付提示
    };
  }

  public saveData = (cb?) => {
    const { paymentModeList, pics } = this.$myMv
    const { imgList } = this.wxUploadImgMv
    const { bankInfo, date, isChooseAccount, selectCapitalAccountInfo, transferAmount } = this.state;
    const singlePaymentStorage = {
      bankInfo: JSON.stringify(bankInfo),
      paymentModeList: JSON.stringify(paymentModeList),
      isChooseAccount,
      date: date ? date.toString() : "",
      scrollHeight: $("#pageWrap").scrollTop(),
      scrollFreezeTitleBarMV: JSON.stringify(this.$ScrollFreezeTitleBarMV),
      imgList: JSON.stringify(imgList),
      selectCapitalAccountInfo: JSON.stringify(selectCapitalAccountInfo),
      transferAmount,
      pics: JSON.stringify(pics),
    };
    this.$AppStore.savePageMv(AppStoreKey.SINGLEPAYMENTSTORAGE, singlePaymentStorage);
    cb && cb();
  }

  public componentWillUnmount(): void {
    this.saveData();
  }

  public pageWindowSkip = (url) => {
    // 跳转到其他页面需要保存数据
    this.saveData();
    setTimeout(() => {
      window.location.href = url;
    }, 50);
  }

  public componentDidMount() {
    document.title = Number(getUrlParam("feeDocumentId")) > 0 ? "费用单单笔支付" : "单笔支付";
    this.$myMv.hideSpin();
    const { record, orderId } = this.props.match.params;
    this.setState({
      record,
      orderId,
    });
    this.loadPaymentInfo();
    this.pageGoBack();
    this.setState({
      canPartialPay: getUrlParam("canPartialPay") === "true",
      feeDocumentId: Number(getUrlParam("feeDocumentId")),
    });
  }

  public pageGoBack = () => {
    window.addEventListener("popstate", (res) => {
      // 触发函数
      this.setState({
        isShowMabel: false,
      });
    });
  }

  public checkTongLianPaymentStatus = () => {
    const { orderId } = this.props.match.params;
    const params = {
      docType: getUrlParam("feeDocumentId") ? $OrderType.FEEDOCUMENT : $OrderType.ORDERPAYMENT,
      salesOrderIdList: [orderId],
      feeDocumentIdList: getUrlParam("feeDocumentId") ? [getUrlParam("feeDocumentId")] : null,
    };
    this.$myMv.checkTongLianPaymentStatus(params).then((data) => {
      this.$myMv.hideSpin();
      const { errorCode, errorMsg, docInfo } = data;
      if (errorCode && errorCode !== "0") {
        Toast.fail(errorMsg);
      } else if (docInfo && docInfo.oid) {
        const { oid, code, totalAmount } = docInfo
        alert(`提示`, `你有${totalAmount.toFixed(2)}元正在付款`, [
          {
            text: "不付款了", onPress: () => {
              this.cancelTongLianPay(oid);
            },
          },
          {
            text: "去付款", onPress: () => {
              this.checkContinuePay(oid);
            },
          },
        ]);
      } else {
        // 继续主流程操作
      }
    }).catch((err) => {
      this.$myMv.hideSpin();
    });
  }

  public checkContinuePay = (oid) => {
    this.$myMv.showSpin();
    const { orderId } = this.props.match.params;
    const params = {
      docType: $OrderType.ORDERPAYMENT,
      oid,
    }
    this.$myMv.checkContinuePay(params).then((data) => {
      this.$myMv.hideSpin();
      const { errorCode, errorMessage, redirectUrl } = data;
      if (errorCode && errorCode !== "0") {
        Toast.fail(errorMessage);
      } else if (redirectUrl) {
        localStorage.setItem("paymentMessage", JSON.stringify({
          feeDocumentId: this.state.feeDocumentId,
          canPartialPay: this.state.canPartialPay,
          fromWhere: "single-order-payment",
          paymentModeList: this.$myMv.paymentModeList,
          orderId,
          backSource: getUrlParam("backSource"),
          transferAmount: this.state.transferAmount,
        }));
        this.pageWindowSkip(`${redirectUrl}&redirectUrl=${document.location.protocol}//${document.domain}:${window.location.port}/${SITE_PATH}/payment-result-show/allinpay`);
      } else {
      }
    }).catch(() => {
      this.$myMv.hideSpin();
    });
  }

  public cancelTongLianPay = (oid) => {
    const params = {
      docType: $OrderType.ORDERPAYMENT,
      oid,
    }
    this.$myMv.showSpin();
    this.$myMv.cancelTongLianPay(params).then((data) => {
      this.$myMv.hideSpin();
      const { errorCode, errorMessage } = data;
      if (errorCode && errorCode !== "0") {
        alert(`提示`, "显示接口里的错误信息", [
          {
            text: "我知道了", onPress: () => {
              window.location.reload();
            },
          },
        ]);
      } else {
        window.location.reload();
      }
    }).catch(() => {
      this.$myMv.hideSpin();
    });
  }

  public loadPaymentInfo = (callback?) => {
    const { orderId } = this.props.match.params;
    this.$myMv.showSpin();
    const oldData = this.$AppStore.getPageMv(AppStoreKey.SINGLEPAYMENTSTORAGE);
    const singlePaymentStorage = oldData && JSON.parse(oldData);
    this.$AppStore.clearPageMv(AppStoreKey.SINGLEPAYMENTSTORAGE);
    let params = null;
    if (getUrlParam("payRange") === "All" || getUrlParam("payRange") === "Freight") {
      params = {
        orderId,
        payRange: getUrlParam("payRange"),
        orderType: getUrlParam("feeDocumentId") ? $OrderType.FEEDOCUMENT : $OrderType.SALESORDER,
      };
    } else {
      params = {
        orderId,
        orderType: getUrlParam("feeDocumentId") ? $OrderType.FEEDOCUMENT : $OrderType.SALESORDER,
      };
    }
    this.$myMv.loadTransferRecordList({ salesOrderIdList: [orderId] });
    this.$myMv.fetchPaymentInfo(params).then((data) => {
      this.$myMv.orderPaymentList = data.orderPaymentList;
      this.$myMv.paymentInfo = data.orderTotalAmountInfo;
      this.$myMv.surplusAmount = data.orderTotalAmountInfo && data.orderTotalAmountInfo.unPayableAmount;
      this.$myMv.orderPartyId = data.orderPartyId;
      this.$myMv.paymentModeList = data.paymentModeList.map((pay) => new $SinglePaymentModeList(pay));
      this.$myMv.freightPayInfo = data.freightPayInfo;

      if (getUrlParam("payRange") === "All") {
        const { orderSchemePayLimit } = data.orderTotalAmountInfo;
        this.setState({ isCreditLimit: orderSchemePayLimit, disabledCredit: orderSchemePayLimit });
        if (orderSchemePayLimit) {
          this.setState({ isShowCreditTips: true });
        }
      }

      if ((this.$myMv.paymentModeList && this.$myMv.paymentModeList.length === 0) || (this.$myMv.paymentModeList && this.$myMv.paymentModeList.length === 1 && this.$myMv.paymentModeList.filter((mode) => mode.code === $PaymentModeType.ALLINPAY).length === 1)) {
        Toast.info("无可用的支付方式，请联系管理员");
      }
      if (this.$myMv.paymentModeList.filter((mode) => mode.code === $PaymentType.BANK_TRANSFER).length > 0) {
        this.loadCapitalAccount();
      }

      if (this.$myMv.paymentModeList.filter((mode) => mode.code === $PaymentModeType.CREDIT).length > 0) {
        const creditPaymentInfo = this.$myMv.paymentModeList.filter((mode) => mode.code === $PaymentModeType.CREDIT)[0];
        if (creditPaymentInfo.hasCredit && !creditPaymentInfo.hasNewModeCredit) {
          this.setState({ validityType: $ValidityType.REGULAR_CREDIT, isShowMoreValidityType: false });
        } else if (creditPaymentInfo.hasNewModeCredit && !creditPaymentInfo.hasCredit) {
          this.setState({ validityType: $ValidityType.NEW_CREDIT, isShowMoreValidityType: false });
        } else {
          this.setState({ validityType: $ValidityType.NEW_CREDIT });
        }
      }

      // 调起通联银行卡接口
      if (this.$myMv.paymentModeList.filter((mode) => mode.code === $PaymentType.ALLINPAY).length > 0) { // 只有通联支付有才调用通联银行卡接口
        // 存在通联微信支付，获取付款线路
        const allInPayPayment = this.$myMv.paymentModeList.filter((mode) => mode.code === $PaymentType.ALLINPAY)[0];
        const { paymentProductList } = allInPayPayment;
        if (paymentProductList.length > 0) {
          paymentProductList.map((item) => {
            if (item.paymentCode === $PaymentModeType.ALLINPAY_WECHAT) {
              this.setState({
                originSubMerchantList: item.subMerchantList,
                subMerchantList: this.handleSubMerchantList(item.subMerchantList)},
              );
            }
          });
        }
        this.chooseBankCardMv.loadPaymentTpBankList().then((data1) => {
          this.$myMv.hideSpin();
          if (data1.bankList && data1.bankList.length > 0) {
            data1.bankList.map((lis) => {
              if (lis.bankCardTypeCode === $PaymentModeType.CREDITBANKCARTCODE) { // 信用卡
                lis.bankCardRate = $PaymentModeType.CREDITCARTRATE;
              } else { // 储值卡
                lis.bankCardRate = $PaymentModeType.STOREDVALUECARDRATE;
              }
            });
          }
          // const { bankList } = data1;
          // const paymentModeList = JSON.parse(sessionStorage.getItem("singlePaymentModeList"));
          if (singlePaymentStorage) {
            // const bankInfo = JSON.parse(sessionStorage.getItem("singlePaymentState"));
            const bankInfo = JSON.parse(singlePaymentStorage.bankInfo);
            if (bankInfo) {
              this.setState({
                selectedBankName: bankInfo.bankName,
                selectedBankCardType: bankInfo.bankCardTypeName,
                selectedBankCardNo: bankInfo.bankCardNo,
                selectedBankId: bankInfo.id,
                bankInfo,
                isDisabled: false,
              });
            } else { // 这个入口应该进不来，后期没有问题可以删掉
              // this.setState({
              //   selectedBankName: (bankList[0] && bankList[0].bankName) || null,
              //   selectedBankCardType: (bankList[0] && bankList[0].bankCardTypeName) || null,
              //   selectedBankCardNo: (bankList[0] && bankList[0].bankCardNo) || null,
              //   selectedBankId: (bankList[0] && bankList[0].id) || null,
              //   bankInfo: bankList[0],
              //   isDisabled: false,
              // });
            }
          }
        }).catch((err) => {
          this.$myMv.hideSpin();
          if (err.response) {
            Toast.fail(err.response.body.message);
          }
        });
      }
      // 调整线下转账优先级
      this.$myMv.paymentModeList.map((mode, index) => { // 线下转账在最后面优先级最低
        mode.priority = index;
      });
      // 拆分通联支付内部支付方式
      if (this.$myMv.paymentModeList.filter((mode) => mode.code === $PaymentType.ALLINPAY).length > 0) {
        this.$myMv.paymentModeList.filter((mode) => mode.code === $PaymentType.ALLINPAY)[0].paymentProductList.map((product) => {
          product.code = product.paymentCode;
          product.name = product.paymentName;
          product.isTPPymt = $PaymentType.ISTPYMT;
          product.expectedAmount = 0;
          product.priority = this.$myMv.paymentModeList.filter((mode) => mode.code === $PaymentType.ALLINPAY)[0].priority; // 通联父子优先级相同
          this.$myMv.paymentModeList.push(new $SinglePaymentModeList(product));
        });
        // this.paymentModeList = remove(this.paymentModeList, (m) => m.code !== $PaymentType.ALLINPAY);
      }
      // this.$myMv.refundDocumentsList = data.refundDocumentsList;

      // 记录之前的支付
      // const paramsList = sessionStorage.getItem("singlePaymentModeList");
      if (singlePaymentStorage) { // 防止有支付方式停用重定向数据和新的支付方式数据要取交集
        const oldPaymentModeList = JSON.parse(singlePaymentStorage.paymentModeList);
        oldPaymentModeList.map((mode) => {
          if (find(this.$myMv.paymentModeList, { code: mode.code })) {
            this.$myMv.paymentModeList.map((lis, index) => {
              if (lis.code === mode.code && mode.isCheck
                && (lis.code === $PaymentType.ALLINPAY_BANK_CARD || lis.code === $PaymentType.ALLINPAY_WECHAT || lis.code === $PaymentType.ALLINPAY)) { // 记录的支付方式中选中的重新赋值
                // mode.isCheck = false;
                // this.thridPartyModeChangeNext(mode);
                this.$myMv.paymentModeList[index].isCheck = true;
                if (oldPaymentModeList.filter((l) => l.code === $PaymentType.ALLINPAY)[0].expectedPaymentAmount < this.$myMv.surplusAmount) {
                  this.setState({
                    isAdjust: true,
                  }, () => {
                    this.$myMv.paymentModeList.filter((m) => m.code === $PaymentType.ALLINPAY)[0].isCheck = true;
                    this.$myMv.paymentModeList.filter((m) => m.code === $PaymentType.ALLINPAY)[0].expectedPaymentAmount = oldPaymentModeList.filter((l) => l.code === $PaymentType.ALLINPAY)[0].expectedPaymentAmount;
                    this.$myMv.paymentModeList.filter((m) => m.code === $PaymentType.ALLINPAY)[0].availableTotalAmount = oldPaymentModeList.filter((l) => l.code === $PaymentType.ALLINPAY)[0].availableTotalAmount;
                  });
                }
                if (lis.code === mode.code && mode.isCheck && lis.code === $PaymentType.ALLINPAY_WECHAT) {
                  this.setState({
                    subMerchantList: mode.subMerchantList,
                  });
                }
              } else if (lis.code === mode.code
                && lis.code !== $PaymentType.ALLINPAY_BANK_CARD
                && lis.code !== $PaymentType.ALLINPAY_WECHAT
                && lis.code !== $PaymentType.ALLINPAY
                && lis.code !== $PaymentType.BANK_TRANSFER) {
                this.$myMv.paymentModeList[index].isCheck = this.$myMv.paymentModeList[index].availableTotalAmount > 0 ? mode.isCheck : false;
              } else if (lis.code === mode.code && lis.code === $PaymentType.BANK_TRANSFER) {
                this.$myMv.paymentModeList[index] = mode;
              } else {
              }
            });
          }
        });
        // 计算各个选中状态的支付方式的支付金额
        if (this.$myMv.paymentModeList[0] && this.$myMv.paymentModeList[0].isCheck) {
          this.priorityAmountCalculate(0, this.$myMv.paymentModeList[0], this.$myMv.paymentModeList[0].isTPPymt);
        }
        if (this.$myMv.paymentModeList[1] && this.$myMv.paymentModeList[1].isCheck) {
          this.priorityAmountCalculate(1, this.$myMv.paymentModeList[1], this.$myMv.paymentModeList[1].isTPPymt);
        }
        if (this.$myMv.paymentModeList[2] && this.$myMv.paymentModeList[2].isCheck) {
          this.priorityAmountCalculate(2, this.$myMv.paymentModeList[2], this.$myMv.paymentModeList[2].isTPPymt);
        }
        if (this.$myMv.paymentModeList[3] && this.$myMv.paymentModeList[3].isCheck) {
          this.priorityAmountCalculate(3, this.$myMv.paymentModeList[3], this.$myMv.paymentModeList[3].isTPPymt);
        }
        // 剩余金额
        let sum = 0;
        this.$myMv.paymentModeList.map((lis) => {
          if (lis.code === $PaymentType.ALLINPAY) {
            this.$myMv.mostTpAmount = this.$myMv.paymentInfo.unPayableAmount - sum;
          }
          sum += Number(lis.expectedPaymentAmount);
        });
        this.$myMv.surplusAmount = this.$myMv.paymentInfo.unPayableAmount - sum;
        // 禁用通联支付
        if (this.$myMv.paymentModeList.filter((lis) => lis.code === $PaymentType.ALLINPAY_BANK_CARD).length > 0
          && this.$myMv.paymentModeList.filter((lis) => lis.code === $PaymentType.ALLINPAY)[0].expectedPaymentAmount === 0
          && this.$myMv.mostTpAmount === 0) {
          this.$myMv.paymentModeList.filter((lis) => lis.code === $PaymentType.ALLINPAY_BANK_CARD)[0].isCheck = false;
          this.$myMv.paymentModeList.filter((lis) => lis.code === $PaymentType.ALLINPAY_BANK_CARD)[0].isDisabled = true;
        }
        if (this.$myMv.paymentModeList.filter((lis) => lis.code === $PaymentType.ALLINPAY_WECHAT).length > 0
          && this.$myMv.paymentModeList.filter((lis) => lis.code === $PaymentType.ALLINPAY)[0].expectedPaymentAmount === 0
          && this.$myMv.mostTpAmount === 0) {
          this.$myMv.paymentModeList.filter((lis) => lis.code === $PaymentType.ALLINPAY_WECHAT)[0].isCheck = false;
          this.$myMv.paymentModeList.filter((lis) => lis.code === $PaymentType.ALLINPAY_WECHAT)[0].isDisabled = true;
        }
        // 计算通联支付费率
        const bankInfo = singlePaymentStorage ? JSON.parse(singlePaymentStorage.bankInfo) : null;
        // const bankInfo = JSON.parse(sessionStorage.getItem("singlePaymentState"));
        if (bankInfo) {
          this.setState({
            selectedBankName: bankInfo.bankName,
            selectedBankCardType: bankInfo.bankCardTypeName,
            selectedBankCardNo: bankInfo.bankCardNo,
            selectedBankId: bankInfo.id,
            bankInfo,
            isDisabled: false,
          }, () => {
            if (this.$myMv.paymentModeList.filter((lis) => lis.code === $PaymentType.ALLINPAY_BANK_CARD).length > 0
              && this.$myMv.paymentModeList.filter((lis) => lis.code === $PaymentType.ALLINPAY_BANK_CARD)[0].isCheck) { // 通联银行卡
              if (this.state.bankInfo.bankCardRate === $PaymentModeType.STOREDVALUECARDRATE
                && this.$myMv.paymentModeList.filter((lis) => lis.code === $PaymentType.ALLINPAY_BANK_CARD)[0].rateList.filter((lis) => lis.rateCode === $PaymentModeType.STOREDVALUECARDRATE).length > 0) { // 储蓄卡
                // todo
                const rateBookList = this.$myMv.paymentModeList.filter((lis) => lis.code === $PaymentType.ALLINPAY_BANK_CARD)[0].rateList.filter((lis) => lis.rateCode === $PaymentModeType.STOREDVALUECARDRATE)[0].rateBookList;
                const expectedPaymentAmount = this.$myMv.paymentModeList.filter((lis) => lis.code === $PaymentType.ALLINPAY)[0].expectedPaymentAmount;
                if (rateBookList.length > 0) {
                  rateBookList.map((item) => {
                    if (item.endValue === null || item.endValue === "" || item.endValue === undefined) {
                      if (item.beginValue <= expectedPaymentAmount) {
                        this.setState({
                          tpRate: toFixedOptimizing(expectedPaymentAmount * item.rate, 100),
                        });
                      }
                    } else {
                      if ((item.beginValue <= expectedPaymentAmount) && (expectedPaymentAmount < item.endValue)) {
                        this.setState({
                          tpRate: toFixedOptimizing(expectedPaymentAmount * item.rate, 100),
                        });
                      } else if (expectedPaymentAmount >= item.endValue) {
                        this.setState({
                          tpRate: toFixedOptimizing(expectedPaymentAmount * item.rate, 100),
                        });
                      }
                    }
                  });
                }
              } else { // 信用卡
                // todo
                const rateBookList = this.$myMv.paymentModeList.filter((lis) => lis.code === $PaymentType.ALLINPAY_BANK_CARD)[0].rateList.filter((lis) => lis.rateCode === $PaymentModeType.CREDITCARTRATE)[0].rateBookList;
                const expectedPaymentAmount = this.$myMv.paymentModeList.filter((lis) => lis.code === $PaymentType.ALLINPAY)[0].expectedPaymentAmount;
                if (rateBookList.length > 0) {
                  rateBookList.map((item) => {
                    if (item.endValue === null || item.endValue === "" || item.endValue === undefined) {
                      if (item.beginValue <= expectedPaymentAmount) {
                        this.setState({
                          tpRate: toFixedOptimizing(expectedPaymentAmount * item.rate, 100),
                        });
                      }
                    } else {
                      if ((item.beginValue <= expectedPaymentAmount) && (expectedPaymentAmount < item.endValue)) {
                        this.setState({
                          tpRate: toFixedOptimizing(expectedPaymentAmount * item.rate, 100),
                        });
                      } else if (expectedPaymentAmount >= item.endValue) {
                        this.setState({
                          tpRate: toFixedOptimizing(expectedPaymentAmount * item.rate, 100),
                        });
                      }
                    }
                  });
                }
              }
            }
          });
        }
        if (this.$myMv.paymentModeList.filter((lis) => lis.code === $PaymentType.ALLINPAY_WECHAT).length > 0 && this.$myMv.paymentModeList.filter((lis) => lis.code === $PaymentType.ALLINPAY_WECHAT)[0].isCheck) { // 通联微信
          if (this.$myMv.paymentModeList.filter((lis) => lis.code === $PaymentType.ALLINPAY_BANK_CARD)[0].rateList.length > 0) {
            // todo
            const rateBookList = this.$myMv.paymentModeList.filter((lis) => lis.code === $PaymentType.ALLINPAY_WECHAT)[0].rateList[0].rateBookList;
            const expectedPaymentAmount = this.$myMv.paymentModeList.filter((lis) => lis.code === $PaymentType.ALLINPAY)[0].expectedPaymentAmount;
            if (rateBookList.length > 0) {
              rateBookList.map((item) => {
                if (item.endValue === null || item.endValue === "" || item.endValue === undefined) {
                  if (item.beginValue <= expectedPaymentAmount) {
                    this.setState({
                      tpRate: toFixedOptimizing(expectedPaymentAmount * item.rate, 100),
                    });
                  }
                } else {
                  if ((item.beginValue <= expectedPaymentAmount) && (expectedPaymentAmount < item.endValue)) {
                    this.setState({
                      tpRate: toFixedOptimizing(expectedPaymentAmount * item.rate, 100),
                    });
                  } else if (expectedPaymentAmount >= item.endValue) {
                    this.setState({
                      tpRate: toFixedOptimizing(expectedPaymentAmount * item.rate, 100),
                    });
                  }
                }
              });
            }
          }
        }
        // 记录线下转账金额
        if (this.$myMv.paymentModeList.filter((lis) => lis.code === $PaymentType.BANK_TRANSFER).length > 0 && this.$myMv.paymentModeList.filter((lis) => lis.code === $PaymentType.BANK_TRANSFER)[0].isCheck) { // 银行卡
          this.setState({
            transferAmount: getUrlParam("transferAmount"),
          });
        }
      } else {
        // 不用记录之前状态的（新单的）支付方式状态
        if (this.$myMv.paymentModeList.filter((lis) => lis.code === $PaymentType.ALLINPAY_BANK_CARD).length > 0 && this.$myMv.paymentModeList.filter((lis) => lis.code === $PaymentType.ALLINPAY)[0].expectedPaymentAmount === 0 && this.$myMv.surplusAmount === 0) {
          this.$myMv.paymentModeList.filter((lis) => lis.code === $PaymentType.ALLINPAY_BANK_CARD)[0].isDisabled = true;
        }
        if (this.$myMv.paymentModeList.filter((lis) => lis.code === $PaymentType.ALLINPAY_WECHAT).length > 0 && this.$myMv.paymentModeList.filter((lis) => lis.code === $PaymentType.ALLINPAY)[0].expectedPaymentAmount === 0 && this.$myMv.surplusAmount === 0) {
          this.$myMv.paymentModeList.filter((lis) => lis.code === $PaymentType.ALLINPAY_WECHAT)[0].isDisabled = true;
        }
      }
      this.checkTongLianPaymentStatus();
      // 还原原始状态
      const comeBack = getUrlParam("comeBack");
      if (singlePaymentStorage && comeBack !== "true") {
        const { scrollHeight, scrollFreezeTitleBarMV, imgList, pics, selectCapitalAccountInfo, date, isChooseAccount, transferAmount } = singlePaymentStorage;
        this.$ScrollFreezeTitleBarMV.queryOldData(JSON.parse(scrollFreezeTitleBarMV));
        this.wxUploadImgMv.recordOldImgList(JSON.parse(imgList), JSON.parse(pics) ? JSON.parse(pics).length : 0);
        this.$myMv.pics = JSON.parse(pics);
        this.setState({
          selectCapitalAccountInfo: JSON.parse(selectCapitalAccountInfo),
          isChooseAccount,
          transferAmount,
          date: date ? new Date(date) : null,
        })
        setTimeout(() => {
          $("#pageWrap").scrollTop(scrollHeight);
        }, 50);
      }
      if (callback) {
        callback();
      }
    }).catch((err) => {
      this.$myMv.hideSpin();
      if (err.response) {
        Toast.fail(err.response.body.message);
      }
    });
  }

  public handleSubMerchantList = (subMerchantList) => {
    if (subMerchantList.length > 0) {
      subMerchantList.map((item) => {
        item.checked = item.defaulted === "Y";
      });
    }
    return subMerchantList;
  }

  public showAccountMabel = () => {
    this.props.history.push({
      pathname: window.location.pathname,
    });
    this.setState({
      isShowMabel: true,
    });
  }

  public loadCapitalAccount = () => {
    const { paymentModeList } = this.$myMv;
    let paymentModeId;
    if (paymentModeList) {
      paymentModeList.map((paymentMode) => {
        if (paymentMode.code === "bank_transfer") {
          paymentModeId = paymentMode.oid;
        }
      });
    }
    const params = { paymentModeId, orderPartyId: this.$myMv.orderPartyId };
    this.$myMv.fetchCapitalAccount(params).then((data) => {
      const { capitalAccountList } = data;
      if (capitalAccountList && capitalAccountList.length === 1) {
        this.setState(
          {
            isShowMabel: false,
            selectCapitalAccountInfo: {
              bankName: capitalAccountList[0].bankName,
              accountCode: capitalAccountList[0].accountCode,
              bankAccountName: capitalAccountList[0].bankAccountName,
              capitalAccountId: capitalAccountList[0].capitalAccountId,
            },
            isChooseAccount: false,
          });
      }
    });
  }

  public setDefaultCheck = (v, index) => {
    const { capitalAccountList, paymentInfo } = this.$myMv;
    capitalAccountList.forEach((item) => {
      item.isDefault = "N";
    });
    capitalAccountList[index].isDefault = "Y";
    /*取消收款账户默认值*/
    this.props.history.goBack();
    this.setState(
      {
        isShowMabel: false,
        isChooseAccount: false,
        selectCapitalAccountInfo: {
          bankName: capitalAccountList[index].bankName,
          accountCode: capitalAccountList[index].accountCode,
          bankAccountName: capitalAccountList[index].bankAccountName,
          capitalAccountId: capitalAccountList[index].capitalAccountId,
        },
      });
  }

  public reConfirm = (paymentModeId, paymentTime) => {
    const { orderId } = this.props.match.params;
    this.props.history.push({
      pathname: `/${SITE_PATH}/resubmit/order/${orderId}/${paymentModeId}/${this.$myMv.paymentInfo.unPayableAmount}`,
      state: {
        paymentTime,
      },
    });
  }

  public reload = () => {
    // const { orderId } = this.props.match.params;
    // window.location.href = `/${SITE_PATH}/submit/order-payment/12345/${orderId}/1234`;
    window.location.reload();
  }

  public hideQuestionHelp = () => {
    this.setState({
      overTop: false,
      activekey: "",
    });
  }

  public setPics = (files) => {
    this.$myMv.setPics(files);
  }

  public paymentCheck = () => {
    const { orderId } = this.props.match.params;
    const { paymentInfo, pics, paymentModeList, surplusAmount } = this.$myMv;
    const { unPayableAmount } = paymentInfo;
    const { transferAmount, isAdjust, canPartialPay, feeDocumentId } = this.state;
    if (surplusAmount > 0 && canPartialPay && feeDocumentId > 0) {
      Toast.info("该类型的费用单不支持部分付款！", 3);
      return;
    }
    if (paymentModeList.filter((mode) => mode.isCheck).length === 0) {
      Toast.info("请选择支付方式", 3);
      return;
    }

    // 校验 线下转账
    if (paymentModeList.filter((mode) => mode.code === $PaymentType.BANK_TRANSFER).length > 0 && paymentModeList.filter((mode) => mode.code === $PaymentType.BANK_TRANSFER)[0].isCheck) {
      if (transferAmount === null || transferAmount === undefined || transferAmount === "") {
        Toast.info("请输入线下转账金额", 3);
        return;
      }
      if (transferAmount <= 0) {
        Toast.info("请输入正数的数字", 3);
        return;
      }
      if (String(transferAmount).indexOf(".") > -1) {
        if (!/^[0-9]+(.[0-9]{0,2})?$/.test(transferAmount)) {
          Toast.info("支付金额最多有两位小数");
          return;
        }
      }
      if (this.state.selectCapitalAccountInfo.accountCode === "") {
        Toast.info("请选择收款账户", 3);
        return;
      }
      if (this.state.date === null) {
        Toast.info("请选择正确的付款日期，财务将根据提交的日期核对银行流水", 5);
        return;
      }
      if (!pics.length && this.wxUploadImgMv.isWx === false) {
        Toast.info("请上传付款凭证", 5);
        return;
      }
      if (this.wxUploadImgMv.imgList && this.wxUploadImgMv.imgList.length === 0 && this.wxUploadImgMv.isWx === true) {
        Toast.info("请上传付款凭证", 5);
        return;
      }
      let selectedAmount = 0;
      if (this.$myMv.paymentModeList.filter((lis) => lis.isCheck).length > 0) {
        this.$myMv.paymentModeList.filter((lis) => lis.isCheck).map((lis1) => {
          if (lis1.expectedPaymentAmount) {
            selectedAmount += Number(lis1.expectedPaymentAmount);
          }
        });
      }
      if (transferAmount) {
        selectedAmount += Number(transferAmount);
      }
      if (selectedAmount > unPayableAmount) {
        this.setState({
          overTop: true,
        });
        // const param = { salesOrderIds: [orderId] };
        // this.$SubmitOrderMv.queryBatchLoadShops(param).then(() => {
        //
        // });
        return;
      }
    }
    this.confirmPayNewType();
  }

  public confirmPayNewType = () => {
    const { orderId } = this.props.match.params;
    const { paymentInfo, pics } = this.$myMv;
    // const { unPayableAmount } = paymentInfo;
    const { selectCapitalAccountInfo, activekey, overTop, transferAmount, selectedBankId, selectedBankCardNo,
       canPartialPay, feeDocumentId, subMerchantList, validityType } = this.state;
    // const { orderOrgList } = this.$SubmitOrderMv;
    // if (!activekey && transferAmount > unPayableAmount && orderOrgList.length > 1) { // 超额未选择退款门店
    //   Toast.info("请选择退款门店");
    //   return;
    // }
    if (overTop) {
      this.hideQuestionHelp();
    }
    // sessionStorage.setItem("singlePaymentModeList", JSON.stringify(this.$myMv.paymentModeList));
    // 提交
    const paymentItemList = [];
    if (this.$myMv.paymentModeList.filter((mode) => mode.code === $PaymentType.BANK_TRANSFER).length > 0 && this.$myMv.paymentModeList.filter((mode) => mode.code === $PaymentType.BANK_TRANSFER)[0].isCheck && transferAmount > 0) { // 线下转账是勾选的
      const offLinePaymentItem = {
        paymentModeId: this.$myMv.paymentModeList.filter((mode) => mode.code === $PaymentType.BANK_TRANSFER)[0].oid,
        sellerAccountId: selectCapitalAccountInfo.capitalAccountId,
        amount: transferAmount,
        paymentTime: DateUtils.toStringFormat(this.state.date, "yyyy-MM-dd HH:mm"),
        voucherImages: pics && pics.length === 0 ? toJS(this.wxUploadImgMv.imgList) : toJS(pics),
        paymentModeCode: this.$myMv.paymentModeList.filter((mode) => mode.code === $PaymentType.BANK_TRANSFER)[0].code,
        paymentModeName: this.$myMv.paymentModeList.filter((mode) => mode.code === $PaymentType.BANK_TRANSFER)[0].name,
      };
      paymentItemList.push(offLinePaymentItem);
    }
    if (this.$myMv.paymentModeList.filter((mode) => mode.code === $PaymentModeType.REBATEDEDUCTION).length > 0 && this.$myMv.paymentModeList.filter((mode) => mode.code === $PaymentModeType.REBATEDEDUCTION)[0].isCheck && this.$myMv.paymentModeList.filter((mode) => mode.code === $PaymentModeType.REBATEDEDUCTION)[0].expectedPaymentAmount > 0) { // 返利是勾选的
      const amount = this.$myMv.paymentModeList.filter((mode) => mode.code === $PaymentModeType.REBATEDEDUCTION)[0].expectedPaymentAmount
      const rebatePaymentItem = {
        paymentModeId: this.$myMv.paymentModeList.filter((mode) => mode.code === $PaymentModeType.REBATEDEDUCTION)[0].oid,
        paymentAccountId: this.$myMv.paymentModeList.filter((mode) => mode.code === $PaymentModeType.REBATEDEDUCTION)[0].accountId,
        amount: Number(Number(amount).toFixed(2)),
        paymentModeCode: this.$myMv.paymentModeList.filter((mode) => mode.code === $PaymentModeType.REBATEDEDUCTION)[0].code,
        paymentModeName: this.$myMv.paymentModeList.filter((mode) => mode.code === $PaymentModeType.REBATEDEDUCTION)[0].name,
      };
      paymentItemList.push(rebatePaymentItem);
    }
    if (this.$myMv.paymentModeList.filter((mode) => mode.code === $PaymentModeType.DIVIDE_REBATE).length > 0
      && this.$myMv.paymentModeList.filter((mode) => mode.code === $PaymentModeType.DIVIDE_REBATE)[0].isCheck
      && this.$myMv.paymentModeList.filter((mode) => mode.code === $PaymentModeType.DIVIDE_REBATE)[0].availableOrderAmount > 0 ) {
        const amount = this.$myMv.paymentModeList.filter((mode) => mode.code === $PaymentModeType.DIVIDE_REBATE)[0].availableOrderAmount;
        const divideRebatePaymentItem = {
          paymentModeId: this.$myMv.paymentModeList.filter((mode) => mode.code === $PaymentModeType.DIVIDE_REBATE)[0].oid,
          paymentAccountId: this.$myMv.paymentModeList.filter((mode) => mode.code === $PaymentModeType.DIVIDE_REBATE)[0].accountId,
          amount: Number(Number(amount).toFixed(2)),
          paymentModeCode: this.$myMv.paymentModeList.filter((mode) => mode.code === $PaymentModeType.DIVIDE_REBATE)[0].code,
          paymentModeName: this.$myMv.paymentModeList.filter((mode) => mode.code === $PaymentModeType.DIVIDE_REBATE)[0].name,
        };
        paymentItemList.push(divideRebatePaymentItem);
    }

    if (this.$myMv.paymentModeList.filter((mode) => mode.code === $PaymentModeType.STOREDVALUE).length > 0
      && this.$myMv.paymentModeList.filter((mode) => mode.code === $PaymentModeType.STOREDVALUE)[0].isCheck
      && this.$myMv.paymentModeList.filter((mode) => mode.code === $PaymentModeType.STOREDVALUE)[0].expectedPaymentAmount > 0) { // 余额是勾选的
      const amount = this.$myMv.paymentModeList.filter((mode) => mode.code === $PaymentModeType.STOREDVALUE)[0].expectedPaymentAmount;
      const balancePaymentItem = {
        paymentModeId: this.$myMv.paymentModeList.filter((mode) => mode.code === $PaymentModeType.STOREDVALUE)[0].oid,
        paymentAccountId: this.$myMv.paymentModeList.filter((mode) => mode.code === $PaymentModeType.STOREDVALUE)[0].accountId,
        amount: Number(Number(amount).toFixed(2)),
        paymentModeCode: this.$myMv.paymentModeList.filter((mode) => mode.code === $PaymentModeType.STOREDVALUE)[0].code,
        paymentModeName: this.$myMv.paymentModeList.filter((mode) => mode.code === $PaymentModeType.STOREDVALUE)[0].name,
      };
      paymentItemList.push(balancePaymentItem);
    }

    if (this.$myMv.paymentModeList.filter((mode) => mode.code === $PaymentModeType.CREDIT).length > 0 && this.$myMv.paymentModeList.filter((mode) => mode.code === $PaymentModeType.CREDIT)[0].isCheck && this.$myMv.paymentModeList.filter((mode) => mode.code === $PaymentModeType.CREDIT)[0].expectedPaymentAmount > 0) { // 信用是勾选的
      const amount = this.$myMv.paymentModeList.filter((mode) => mode.code === $PaymentModeType.CREDIT)[0].expectedPaymentAmount;
      let payFreightAmount = 0;
      if (this.$myMv.freightPayInfo) {
        payFreightAmount = this.$myMv.freightPayInfo.freightNotPayAmount >= this.$myMv.paymentModeList.filter((mode) => mode.code === $PaymentModeType.CREDIT)[0].availableFreightAmount ? this.$myMv.paymentModeList.filter((mode) => mode.code === $PaymentModeType.CREDIT)[0].availableFreightAmount : this.$myMv.freightPayInfo.freightNotPayAmount;
      }
      const creditPaymentItem = {
        paymentModeId: this.$myMv.paymentModeList.filter((mode) => mode.code === $PaymentModeType.CREDIT)[0].oid,
        paymentAccountId: this.$myMv.paymentModeList.filter((mode) => mode.code === $PaymentModeType.CREDIT)[0].accountId,
        amount: Number(Number(amount).toFixed(2)),
        paymentModeCode: this.$myMv.paymentModeList.filter((mode) => mode.code === $PaymentModeType.CREDIT)[0].code,
        paymentModeName: this.$myMv.paymentModeList.filter((mode) => mode.code === $PaymentModeType.CREDIT)[0].name,
        orderAmount: this.$myMv.paymentModeList.filter((mode) => mode.code === $PaymentModeType.CREDIT)[0].orderAmount,
        freightAmount: this.$myMv.paymentModeList.filter((mode) => mode.code === $PaymentModeType.CREDIT)[0].freightAmount,
        validityType,
      };
      paymentItemList.push(creditPaymentItem);
    }
    if (this.$myMv.paymentModeList.filter((mode) => mode.code === $PaymentType.ALLINPAY_BANK_CARD).length > 0 && this.$myMv.paymentModeList.filter((mode) => mode.code === $PaymentType.ALLINPAY_BANK_CARD)[0].isCheck && this.$myMv.paymentModeList.filter((mode) => mode.code === $PaymentType.ALLINPAY)[0].expectedPaymentAmount > 0) { // 通联银行卡是勾选的
      let serviceFee = null;
      if (this.state.bankInfo.bankCardRate === $PaymentModeType.STOREDVALUECARDRATE && this.$myMv.paymentModeList.filter((lis) => lis.code === $PaymentType.ALLINPAY_BANK_CARD)[0].rateList.filter((lis) => lis.rateCode === $PaymentModeType.STOREDVALUECARDRATE).length > 0) { // 储蓄卡

        const rateBookList = this.$myMv.paymentModeList.filter((lis) => lis.code === $PaymentType.ALLINPAY_BANK_CARD)[0].rateList.filter((lis) => lis.rateCode === $PaymentModeType.STOREDVALUECARDRATE)[0].rateBookList;
        const expectedPaymentAmount = this.$myMv.paymentModeList.filter((lis) => lis.code === $PaymentType.ALLINPAY)[0].expectedPaymentAmount;
        if (rateBookList.length > 0) {
          rateBookList.map((item) => {
            if (item.endValue === null || item.endValue === "" || item.endValue === undefined) {
              if (item.beginValue <= expectedPaymentAmount) {
                serviceFee = toFixedOptimizing(expectedPaymentAmount * item.rate, 100);
              }
            } else {
              if ((item.beginValue <= expectedPaymentAmount) && (expectedPaymentAmount < item.endValue)) {
                serviceFee = toFixedOptimizing(expectedPaymentAmount * item.rate, 100);
              } else if (expectedPaymentAmount >= item.endValue) {
                serviceFee = toFixedOptimizing(expectedPaymentAmount * item.rate, 100);
              }
            }
          });
        }

        // serviceFee = Number(Number(this.$myMv.paymentModeList.filter((lis) => lis.code === $PaymentType.ALLINPAY)[0].expectedPaymentAmount * this.$myMv.paymentModeList.filter((lis) => lis.code === $PaymentType.ALLINPAY_BANK_CARD)[0].rateList.filter((lis) => lis.rateCode === $PaymentModeType.STOREDVALUECARDRATE)[0].rate).toFixed(2));
      } else {

        const rateBookList = this.$myMv.paymentModeList.filter((lis) => lis.code === $PaymentType.ALLINPAY_BANK_CARD)[0].rateList.filter((lis) => lis.rateCode === $PaymentModeType.CREDITCARTRATE)[0].rateBookList;
        const expectedPaymentAmount = this.$myMv.paymentModeList.filter((lis) => lis.code === $PaymentType.ALLINPAY)[0].expectedPaymentAmount;
        if (rateBookList.length > 0) {
          rateBookList.map((item) => {
            if (item.endValue === null || item.endValue === "" || item.endValue === undefined) {
              if (item.beginValue <= expectedPaymentAmount) {
                serviceFee = toFixedOptimizing(expectedPaymentAmount * item.rate, 100);
              }
            } else {
              if ((item.beginValue <= expectedPaymentAmount) && (expectedPaymentAmount < item.endValue)) {
                serviceFee = toFixedOptimizing(expectedPaymentAmount * item.rate, 100);
              } else if (expectedPaymentAmount >= item.endValue) {
                serviceFee = toFixedOptimizing(expectedPaymentAmount * item.rate, 100);
              }
            }
          });
        }

        // serviceFee = Number(Number(this.$myMv.paymentModeList.filter((lis) => lis.code === $PaymentType.ALLINPAY)[0].expectedPaymentAmount * this.$myMv.paymentModeList.filter((lis) => lis.code === $PaymentType.ALLINPAY_BANK_CARD)[0].rateList.filter((lis) => lis.rateCode === $PaymentModeType.CREDITCARTRATE)[0].rate).toFixed(2));
      }

      const amount = this.$myMv.paymentModeList.filter((mode) => mode.code === $PaymentType.ALLINPAY)[0].expectedPaymentAmount;
      const allInPayBankCardItem = {
        paymentModeId: this.$myMv.paymentModeList.filter((mode) => mode.code === $PaymentType.ALLINPAY_BANK_CARD)[0].oid,
        paymentModeCode: this.$myMv.paymentModeList.filter((mode) => mode.code === $PaymentType.ALLINPAY_BANK_CARD)[0].code,
        paymentModeName: this.$myMv.paymentModeList.filter((mode) => mode.code === $PaymentType.ALLINPAY_BANK_CARD)[0].name,
        amount: Number(Number(amount).toFixed(2)),
        bankCardId: selectedBankId,
        serviceFee,
      };
      paymentItemList.push(allInPayBankCardItem);
    }
    if (this.$myMv.paymentModeList.filter((mode) => mode.code === $PaymentType.ALLINPAY_WECHAT).length > 0 && this.$myMv.paymentModeList.filter((mode) => mode.code === $PaymentType.ALLINPAY_WECHAT)[0].isCheck && this.$myMv.paymentModeList.filter((mode) => mode.code === $PaymentType.ALLINPAY)[0].expectedPaymentAmount > 0) { // 通联微信是勾选的
      let serviceFee = null;
      if (this.$myMv.paymentModeList.filter((mode) => mode.code === $PaymentType.ALLINPAY_WECHAT)[0].rateList.length > 0) {

        const rateBookList = this.$myMv.paymentModeList.filter((lis) => lis.code === $PaymentType.ALLINPAY_WECHAT)[0].rateList[0].rateBookList;
        const expectedPaymentAmount = this.$myMv.paymentModeList.filter((lis) => lis.code === $PaymentType.ALLINPAY)[0].expectedPaymentAmount;
        if (rateBookList.length > 0) {
          rateBookList.map((item) => {
            if (item.endValue === null || item.endValue === "" || item.endValue === undefined) {
              if (item.beginValue <= expectedPaymentAmount) {
                serviceFee = toFixedOptimizing(expectedPaymentAmount * item.rate, 100);
              }
            } else {
              if ((item.beginValue <= expectedPaymentAmount) && (expectedPaymentAmount < item.endValue)) {
                serviceFee = toFixedOptimizing(expectedPaymentAmount * item.rate, 100);
              } else if (expectedPaymentAmount >= item.endValue) {
                serviceFee = toFixedOptimizing(expectedPaymentAmount * item.rate, 100);
              }
            }
          });
        }

        // serviceFee = Number(Number(this.$myMv.paymentModeList.filter((mode) => mode.code === $PaymentType.ALLINPAY)[0].expectedPaymentAmount * this.$myMv.paymentModeList.filter((mode) => mode.code === $PaymentType.ALLINPAY_WECHAT)[0].rateList[0].rate).toFixed(2));
      }
      const amount = this.$myMv.paymentModeList.filter((mode) => mode.code === $PaymentType.ALLINPAY)[0].expectedPaymentAmount;
      const paymentLineIndex = subMerchantList && subMerchantList.length > 0 ? subMerchantList.findIndex((line) => line.checked === true) : -1;
      const subMerchantCode = paymentLineIndex > -1 ? subMerchantList[paymentLineIndex].code : "";
      const allInWeiXinItem = {
        paymentModeId: this.$myMv.paymentModeList.filter((mode) => mode.code === $PaymentType.ALLINPAY_WECHAT)[0].oid,
        paymentModeCode: this.$myMv.paymentModeList.filter((mode) => mode.code === $PaymentType.ALLINPAY_WECHAT)[0].code,
        paymentModeName: this.$myMv.paymentModeList.filter((mode) => mode.code === $PaymentType.ALLINPAY_WECHAT)[0].name,
        amount: Number(Number(amount).toFixed(2)),
        bankCardId: null,
        subMerchantCode,
        serviceFee,
      };
      paymentItemList.push(allInWeiXinItem);
      this.setState({
        animating: true,
      }, () => {
        this.chooseBankCardMv.paygatewayGetchanneluser().then((res) => {
          this.setState({
            needBind: res.needBind,
            bindUrl: res.bindUrl,
          }, () => {
            if (this.state.needBind) {
              this.pageWindowSkip(`${this.state.bindUrl}&redirectUrl=${encodeURIComponent(`${document.location.protocol}//${document.domain}:${window.location.port}/${SITE_PATH}/submit/order-payment/34657/${this.props.match.params.orderId}/notPay?feeDocumentId=${feeDocumentId ? feeDocumentId : ""}&canPartialPay=${canPartialPay}&backSource=${getUrlParam("backSource")}&comeBack=true`)}`);
            } else {
              const params = {
                paymentItemList,
                relatedDocId: orderId,
                relatedDocType: getUrlParam("feeDocumentId") ? $OrderType.FEEDOCUMENT : $OrderType.SALESORDER,
                payRange: getUrlParam("payRange"),
              };
              this.$myMv.saveOrder(params).then((data) => {
                const { result, errorCode, errorMessage, redirectUrl } = data;
                if (errorCode === "40210" && errorMessage) {
                  alert(errorMessage, "", [
                    {
                      text: "关闭", onPress: () => {
                        window.location.reload();
                      },
                    }]);
                  return;
                }
                if (result) {
                  if (redirectUrl) { // 通联支付
                    localStorage.setItem("paymentMessage", JSON.stringify({
                      fromWhere: "single-order-payment",
                      feeDocumentId: this.state.feeDocumentId,
                      canPartialPay: this.state.canPartialPay,
                      paymentModeList: this.$myMv.paymentModeList,
                      orderId,
                      backSource: getUrlParam("backSource"),
                      transferAmount: this.state.transferAmount,
                    }));
                    this.pageWindowSkip(`${data.redirectUrl}&redirectUrl=${document.location.protocol}//${document.domain}:${window.location.port}/${SITE_PATH}/payment-result-show/allinpay`);
                  } else {
                    if (this.$myMv.paymentModeList.filter((mode) => mode.code === $PaymentType.BANK_TRANSFER).length > 0
                      && this.$myMv.paymentModeList.filter((mode) => mode.code === $PaymentType.BANK_TRANSFER)[0].isCheck
                      && transferAmount > 0) { // 选择线下转账
                      localStorage.setItem("paymentMessage", JSON.stringify({
                        fromWhere: "single-order-payment",
                        feeDocumentId: this.state.feeDocumentId,
                        canPartialPay: this.state.canPartialPay,
                        orderId,
                        paymentModeList: this.$myMv.paymentModeList,
                        backSource: getUrlParam("backSource"),
                        transferAmount: this.state.transferAmount,
                      }));
                      this.pageWindowSkip(`/${SITE_PATH}/payment-result-show/bank_transfer`);
                      // this.setState({
                      //   animating: false,
                      // });
                    } else { // 系统内除线下转账
                      localStorage.setItem("paymentMessage", JSON.stringify({
                        fromWhere: "single-order-payment",
                        feeDocumentId: this.state.feeDocumentId,
                        canPartialPay: this.state.canPartialPay,
                        orderId,
                        paymentModeList: this.$myMv.paymentModeList,
                        backSource: getUrlParam("backSource"),
                        transferAmount: this.state.transferAmount,
                      }));
                      this.pageWindowSkip(`/${SITE_PATH}/payment-result-show/internal-payment`);
                      // this.setState({
                      //   animating: false,
                      // });
                    }
                  }
                }
              }).catch(() => {
                this.setState({
                  animating: false,
                });
              });
            }
          });
        }).catch(() => {
          this.setState({
            animating: false,
          });
        });
      });
    } else {
      const params = {
        paymentItemList,
        relatedDocId: orderId,
        relatedDocType: getUrlParam("feeDocumentId") ? $OrderType.FEEDOCUMENT : $OrderType.SALESORDER,
        payRange: getUrlParam("payRange"),
      };
      this.setState({
        animating: true,
      }, () => {
        this.$myMv.saveOrder(params).then((data) => {
          const { result, errorCode, errorMessage, redirectUrl } = data;
          if (errorCode === "40210" && errorMessage) {
            alert(errorMessage, "", [
              {
                text: "关闭", onPress: () => {
                  // this.reload();
                  window.location.reload();
                },
              }]);
            return;
          }
          if (result) {
            if (redirectUrl) { // 通联支付
              localStorage.setItem("paymentMessage", JSON.stringify({
                fromWhere: "single-order-payment",
                feeDocumentId: this.state.feeDocumentId,
                canPartialPay: this.state.canPartialPay,
                paymentModeList: this.$myMv.paymentModeList,
                orderId,
                backSource: getUrlParam("backSource"),
                transferAmount: this.state.transferAmount,
              }));
              this.pageWindowSkip(`${data.redirectUrl}&redirectUrl=${document.location.protocol}//${document.domain}:${window.location.port}/${SITE_PATH}/payment-result-show/allinpay`);
            } else {
              if (this.$myMv.paymentModeList.filter((mode) => mode.code === $PaymentType.BANK_TRANSFER).length > 0 && this.$myMv.paymentModeList.filter((mode) => mode.code === $PaymentType.BANK_TRANSFER)[0].isCheck && transferAmount > 0) { // 选择线下转账
                localStorage.setItem("paymentMessage", JSON.stringify({
                  fromWhere: "single-order-payment",
                  feeDocumentId: this.state.feeDocumentId,
                  canPartialPay: this.state.canPartialPay,
                  orderId,
                  paymentModeList: this.$myMv.paymentModeList,
                  backSource: getUrlParam("backSource"),
                  transferAmount: this.state.transferAmount,
                }));
                this.pageWindowSkip(`/${SITE_PATH}/payment-result-show/bank_transfer`);
              } else { // 系统内除线下转账
                localStorage.setItem("paymentMessage", JSON.stringify({
                  fromWhere: "single-order-payment",
                  feeDocumentId: this.state.feeDocumentId,
                  canPartialPay: this.state.canPartialPay,
                  orderId,
                  paymentModeList: this.$myMv.paymentModeList,
                  backSource: getUrlParam("backSource"),
                  transferAmount: this.state.transferAmount,
                }));
                this.pageWindowSkip(`/${SITE_PATH}/payment-result-show/internal-payment`);
              }
            }
          }
        }).catch(() => {
          this.setState({
            animating: false,
          });
        });
      });
    }
  }

  public changeOrg = (code) => {
    this.setState({
      activekey: code,
    });
  }

  public changeInternalPaymentMethod = (payment, isNeedChange = true) => {// 内部支付方式
    if (isNeedChange) {
      payment.isCheck = !payment.isCheck;
    }

    this.priorityAmountCalculate(payment.priority, payment, payment.isTPPymt);
    // 剩余金额
    let sum = 0;
    this.$myMv.paymentModeList.map((lis) => {
      if (lis.code === $PaymentType.ALLINPAY) {
        this.$myMv.mostTpAmount = this.$myMv.paymentInfo.unPayableAmount - sum;
      }
      if (lis.code === $PaymentModeType.ALLINPAY_BANK_CARD || lis.code === $PaymentModeType.ALLINPAY_WECHAT) {
        lis.expectedPaymentAmount = 0;
      }
      if (lis.isCheck && lis.code === $PaymentModeType.DIVIDE_REBATE) {
        sum += Number(lis.availableOrderAmount)
      } else {
        sum += Number(lis.expectedPaymentAmount);
      }

      if (lis.code === $PaymentModeType.DIVIDE_REBATE) {
        this.setState({ disabledDivideRebate: lis.isCheck })
      } else if (lis.code === $PaymentModeType.BANKTRANSFER) {
        this.setState({ disabledTransfer: lis.isCheck })
      }
    });
    this.$myMv.surplusAmount = this.$myMv.paymentInfo.unPayableAmount - sum < 0 ? 0 : toFixedOptimizing(this.$myMv.paymentInfo.unPayableAmount - sum);
    if (this.$myMv.paymentModeList.filter((lis) => lis.isTPPymt === $PaymentType.ISTPYMT).length > 0) { // 如果通联支付选中计算服务费
      if (this.$myMv.paymentModeList.filter((lis) => lis.code === $PaymentType.ALLINPAY_BANK_CARD).length > 0
        && this.$myMv.paymentModeList.filter((lis) => lis.code === $PaymentType.ALLINPAY_BANK_CARD)[0].isCheck) { // 通联银行卡选中
        if (this.state.bankInfo.bankCardRate === $PaymentModeType.STOREDVALUECARDRATE
          && this.$myMv.paymentModeList.filter((lis) => lis.code === $PaymentType.ALLINPAY_BANK_CARD)[0].rateList.filter((lis) => lis.rateCode === $PaymentModeType.STOREDVALUECARDRATE).length > 0) { // 储蓄卡
          // todo
          const rateBookList = this.$myMv.paymentModeList.filter((lis) => lis.code === $PaymentType.ALLINPAY_BANK_CARD)[0].rateList.filter((lis) => lis.rateCode === $PaymentModeType.STOREDVALUECARDRATE)[0].rateBookList;
          const expectedPaymentAmount = this.$myMv.paymentModeList.filter((lis) => lis.code === $PaymentType.ALLINPAY)[0].expectedPaymentAmount;
          if (rateBookList.length > 0) {
            rateBookList.map((item) => {
              if (item.endValue === null || item.endValue === "" || item.endValue === undefined) {
                if (item.beginValue <= expectedPaymentAmount) {
                  this.setState({
                    tpRate: toFixedOptimizing(expectedPaymentAmount * item.rate, 100),
                  });
                }
              } else {
                if ((item.beginValue <= expectedPaymentAmount) && (expectedPaymentAmount < item.endValue)) {
                  this.setState({
                    tpRate: toFixedOptimizing(expectedPaymentAmount * item.rate, 100),
                  });
                } else if (expectedPaymentAmount >= item.endValue) {
                  this.setState({
                    tpRate: toFixedOptimizing(expectedPaymentAmount * item.rate, 100),
                  });
                }
              }
            });
          }
        } else { // 信用卡
          // todo
          const rateBookList = this.$myMv.paymentModeList.filter((lis) => lis.code === $PaymentType.ALLINPAY_BANK_CARD)[0].rateList.filter((lis) => lis.rateCode === $PaymentModeType.CREDITCARTRATE)[0].rateBookList;
          const expectedPaymentAmount = this.$myMv.paymentModeList.filter((lis) => lis.code === $PaymentType.ALLINPAY)[0].expectedPaymentAmount;
          if (rateBookList.length > 0) {
            rateBookList.map((item) => {
              if (item.endValue === null || item.endValue === "" || item.endValue === undefined) {
                if (item.beginValue <= expectedPaymentAmount) {
                  this.setState({
                    tpRate: toFixedOptimizing(expectedPaymentAmount * item.rate, 100),
                  });
                }
              } else {
                if ((item.beginValue <= expectedPaymentAmount) && (expectedPaymentAmount < item.endValue)) {
                  this.setState({
                    tpRate: toFixedOptimizing(expectedPaymentAmount * item.rate, 100),
                  });
                } else if (expectedPaymentAmount >= item.endValue) {
                  this.setState({
                    tpRate: toFixedOptimizing(expectedPaymentAmount * item.rate, 100),
                  });
                }
              }
            });
          }
        }
      }
      if (this.$myMv.paymentModeList.filter((lis) => lis.code === $PaymentType.ALLINPAY_WECHAT).length > 0
        && this.$myMv.paymentModeList.filter((lis) => lis.code === $PaymentType.ALLINPAY_WECHAT)[0].isCheck) { // 通联微信选中
        if (this.$myMv.paymentModeList.filter((lis) => lis.code === $PaymentType.ALLINPAY_WECHAT).length > 0
          && this.$myMv.paymentModeList.filter((lis) => lis.code === $PaymentType.ALLINPAY_WECHAT)[0].rateList.length > 0) {
          // todo
          const rateBookList = this.$myMv.paymentModeList.filter((lis) => lis.code === $PaymentType.ALLINPAY_WECHAT)[0].rateList[0].rateBookList;
          const expectedPaymentAmount = this.$myMv.paymentModeList.filter((lis) => lis.code === $PaymentType.ALLINPAY)[0].expectedPaymentAmount;
          if (rateBookList.length > 0) {
            rateBookList.map((item) => {
              if (item.endValue === null || item.endValue === "" || item.endValue === undefined) {
                if (item.beginValue <= expectedPaymentAmount) {
                  this.setState({
                    tpRate: toFixedOptimizing(expectedPaymentAmount * item.rate, 100),
                  });
                }
              } else {
                if ((item.beginValue <= expectedPaymentAmount) && (expectedPaymentAmount < item.endValue)) {
                  this.setState({
                    tpRate: toFixedOptimizing(expectedPaymentAmount * item.rate, 100),
                  });
                } else if (expectedPaymentAmount >= item.endValue) {
                  this.setState({
                    tpRate: toFixedOptimizing(expectedPaymentAmount * item.rate, 100),
                  });
                }
              }
            });
          }
        }
      }
    }
    if (this.$myMv.paymentModeList.filter((lis) => lis.code === $PaymentType.ALLINPAY).length > 0) { // 如果通联支付的未支付金额为0且剩余金额为0，则通联支付不可点击
      const arr = this.$myMv.paymentModeList.slice(this.$myMv.paymentModeList.filter((lis) => lis.code === $PaymentType.ALLINPAY)[0].priority);
      const isDisabled = arr.every((lis) => lis.expectedPaymentAmount === 0);
      if (this.$myMv.paymentModeList.filter((lis) => lis.code === $PaymentType.ALLINPAY)[0].expectedPaymentAmount === 0
        && this.$myMv.mostTpAmount === 0 && isDisabled) { // 通联支付后面优先级未选中
        if (this.$myMv.paymentModeList.filter((lis) => lis.code === $PaymentType.ALLINPAY_BANK_CARD).length > 0) {
          if (this.$myMv.paymentModeList.filter((lis) => lis.code === $PaymentType.ALLINPAY_BANK_CARD)[0].isCheck) {
            // 通联支付支付外部支付方式选中状态取消
            this.$myMv.paymentModeList.filter((lis) => lis.code === $PaymentType.ALLINPAY)[0].isCheck = false;
            this.$myMv.paymentModeList.filter((lis) => lis.code === $PaymentType.ALLINPAY_BANK_CARD)[0].isCheck = false;
            this.$myMv.paymentModeList.filter((lis) => lis.code === $PaymentType.ALLINPAY_BANK_CARD)[0].isDisabled = true;
            this.setState({
              tpRate: 0,
            });
          } else {
            this.$myMv.paymentModeList.filter((lis) => lis.code === $PaymentType.ALLINPAY_BANK_CARD)[0].isDisabled = true;
          }
        }
        if (this.$myMv.paymentModeList.filter((lis) => lis.code === $PaymentType.ALLINPAY_WECHAT).length > 0) {
          if (this.$myMv.paymentModeList.filter((lis) => lis.code === $PaymentType.ALLINPAY_WECHAT)[0].isCheck) {
            // 通联支付支付外部支付方式选中状态取消
            this.$myMv.paymentModeList.filter((lis) => lis.code === $PaymentType.ALLINPAY)[0].isCheck = false;
            this.$myMv.paymentModeList.filter((lis) => lis.code === $PaymentType.ALLINPAY_WECHAT)[0].isCheck = false;
            this.$myMv.paymentModeList.filter((lis) => lis.code === $PaymentType.ALLINPAY_WECHAT)[0].isDisabled = true;
            this.setState({
              tpRate: 0,
            });
          } else {
            this.$myMv.paymentModeList.filter((lis) => lis.code === $PaymentType.ALLINPAY_WECHAT)[0].isDisabled = true;
          }
        }
      } else {
        if (this.$myMv.paymentModeList.filter((lis) => lis.code === $PaymentType.ALLINPAY_BANK_CARD).length > 0) {
          this.$myMv.paymentModeList.filter((lis) => lis.code === $PaymentType.ALLINPAY_BANK_CARD)[0].isDisabled = false;
        }
        if (this.$myMv.paymentModeList.filter((lis) => lis.code === $PaymentType.ALLINPAY_WECHAT).length > 0) {
          this.$myMv.paymentModeList.filter((lis) => lis.code === $PaymentType.ALLINPAY_WECHAT)[0].isDisabled = false;
        }
      }
    }

    const { isCreditLimit } = this.state;
    if (isCreditLimit) {
      // 当选中非信用支付方式时，启用信用支付
      if (payment.code !== $PaymentModeType.CREDIT && payment.isCheck) {
        this.setState({ disabledCredit: false });
      }

      // 当仅选中信用支付方式时，禁用并取消选中
      const creditPayment = this.$myMv.paymentModeList.find(
        mode => mode.code === $PaymentModeType.CREDIT && mode.isCheck
      );
      const checkedPayments = this.$myMv.paymentModeList.filter(mode => mode.isCheck);
      if (creditPayment && checkedPayments.length === 1)  {
        this.setState({ disabledCredit: true });
        creditPayment.isCheck = false;
        this.$myMv.surplusAmount = this.$myMv.paymentInfo.unPayableAmount;
      } else if (checkedPayments.length === 0) {
        this.setState({ disabledCredit: true });
      }
    }

    if (this.$myMv.paymentModeList.filter((mode) => mode.code === $PaymentType.BANK_TRANSFER).length > 0
      && this.$myMv.paymentModeList.filter((mode) => mode.code === $PaymentType.BANK_TRANSFER)[0].isCheck === false) {
      this.setState({
        transferAmount: 0,
      });
    }
    this.checkIsShowTransferHint();
  }

  public getCheckedTransferInfo = (checkedPayment) => {
    const { paymentInfo: { unPayableAmount }, freightPayInfo, orderPartyId, paymentModeList } = this.$myMv;
    let freight = 0
    if (freightPayInfo) {
      const { freightNotPayAmount, isCreateFreightFeeDocument } = freightPayInfo
      freight = isCreateFreightFeeDocument === 'Y' ? freightNotPayAmount : 0;
    }
    let isShowTransferMode = false;
    let transferShopList = [];
    let divideRebateTransferAmount = 0;
    let totalWaitPayAmount = 0;
    if (checkedPayment.length === 1) {
      if([$PaymentModeType.STOREDVALUE, $PaymentModeType.DIVIDE_REBATE].indexOf(checkedPayment[0].code) > -1) {
        isShowTransferMode = true;
        // 获取选中支付的信息
        const checkedPaymentModeList = paymentModeList.filter((item) => item.code === checkedPayment[0].code);
        const checkedPaymentModeInfo: any = checkedPaymentModeList[0];
        let transferType = '';
        let availableAmount = 0;
        let amount = 0;
        if (checkedPaymentModeInfo.code === $PaymentModeType.DIVIDE_REBATE) {
          transferType = $TransferType.FZFL;
          availableAmount = checkedPaymentModeInfo.availableOrderAmount;
          //totalWaitPayAmount =  toFixedOptimizing(unPayableAmount - checkedPaymentModeInfo.availableOrderAmount);
          divideRebateTransferAmount = this.getSmallNum(unPayableAmount, checkedPaymentModeInfo.transferAmount); // 分账可转账金额
          amount = toFixedOptimizing(unPayableAmount - checkedPaymentModeInfo.availableOrderAmount - freight);
        } else if (checkedPaymentModeInfo.code === $PaymentModeType.STOREDVALUE) {
          transferType = $TransferType.YECZ;
          availableAmount = checkedPaymentModeInfo.availableTotalAmount;
          totalWaitPayAmount = toFixedOptimizing(unPayableAmount - checkedPaymentModeInfo.expectedPaymentAmount);
          amount = toFixedOptimizing(unPayableAmount - checkedPaymentModeInfo.expectedPaymentAmount);
        }
        transferShopList.push({
          transferInOrgId: orderPartyId,
          availableAmount: availableAmount,
          amount,
          transferType,
        })

        if (checkedPaymentModeInfo.code === $PaymentModeType.DIVIDE_REBATE && divideRebateTransferAmount === 0) {
          isShowTransferMode = false;
        }
      }
    } else if (checkedPayment.length === 2) {
      let showTransferDivede = false;
      let showTransferStore = false;
      let remainingAmount = unPayableAmount;
      let isCheckedStore = false;
      const checkedDivideRebateList = checkedPayment.filter((item) => item.code === $PaymentModeType.DIVIDE_REBATE)
      const checkedDivideRebateInfo: any = checkedDivideRebateList.length === 1 ? checkedDivideRebateList[0] : []
      if (!isEmpty(checkedDivideRebateInfo)) {
        remainingAmount = (unPayableAmount - checkedDivideRebateInfo.availableOrderAmount).toFixed(2);
        divideRebateTransferAmount = unPayableAmount < checkedDivideRebateInfo.transferAmount ? unPayableAmount : checkedDivideRebateInfo.transferAmount;
        if (checkedDivideRebateInfo.transferAmount > 0) {
          showTransferDivede = true
          transferShopList.push({
            transferInOrgId: orderPartyId,
            availableAmount: (checkedDivideRebateInfo.availableOrderAmount).toFixed(2),
            amount: (checkedDivideRebateInfo.transferAmount).toFixed(2),
            transferType: $TransferType.FZFL,
          })
        }
      }

      const checkedStoredValueList = checkedPayment.filter((item) => item.code === $PaymentModeType.STOREDVALUE)
      const checkedStoredValueInfo: any = checkedDivideRebateList.length === 1 ? checkedStoredValueList[0] : []
      if (!isEmpty(checkedStoredValueInfo)) {
        isCheckedStore = true;
        const storedValueAvailableAmount = this.getSmallNum(checkedStoredValueInfo.availableTotalAmount, remainingAmount);
        remainingAmount = remainingAmount - storedValueAvailableAmount <= 0 ? 0 : remainingAmount - storedValueAvailableAmount;
        paymentModeList.map((item) => {
          if (item.code === $PaymentModeType.STOREDVALUE) {
            item.expectedPaymentAmount = storedValueAvailableAmount;
          }
        })
        if (remainingAmount > 0) {
          showTransferStore = true
          transferShopList.push({
            transferInOrgId: orderPartyId,
            availableAmount: (remainingAmount).toFixed(2),
            amount: storedValueAvailableAmount,
            transferType: $TransferType.YECZ,
          })
        }
      }
      isShowTransferMode = showTransferDivede || showTransferStore;
      totalWaitPayAmount = isCheckedStore ? (remainingAmount).toFixed(2) : 0;
    } else if (checkedPayment.length > 2) {
      const checkedPaymentList = checkedPayment.filter((item) => item.code === $PaymentModeType.DIVIDE_REBATE)
      if (checkedPaymentList.length > 0) {
        isShowTransferMode = true;
        let remainingAmount = unPayableAmount;
        const checkedDivideRebateList = paymentModeList.filter((item) => item.code === $PaymentModeType.DIVIDE_REBATE)
        const checkedDivideRebateInfo: any = checkedDivideRebateList.length === 1 ? checkedDivideRebateList[0] : []
        if (!isEmpty(checkedDivideRebateInfo)) {
          remainingAmount = (unPayableAmount - checkedDivideRebateInfo.availableOrderAmount).toFixed(2);
          divideRebateTransferAmount = unPayableAmount < checkedDivideRebateInfo.transferAmount ? unPayableAmount : checkedDivideRebateInfo.transferAmount;
          if (checkedDivideRebateInfo.transferAmount > 0) {
            transferShopList.push({
              transferInOrgId: orderPartyId,
              availableAmount: (checkedDivideRebateInfo.availableOrderAmount).toFixed(2),
              amount: (checkedDivideRebateInfo.transferAmount).toFixed(2),
              transferType: $TransferType.FZFL,
            })
          }
        }
      }
    }
    return {
      isShowTransferMode,
      totalWaitPayAmount,
      transferShopList,
      divideRebateTransferAmount,
    }
  }

  public checkIsShowTransferHint = () => {
    const checkedPayment = this.$myMv.paymentModeList.filter((item) => toJS(item).isCheck);
    const checkedTransferInfo = this.getCheckedTransferInfo(checkedPayment);
    const { isShowTransferMode, totalWaitPayAmount, transferShopList, divideRebateTransferAmount } = checkedTransferInfo;
    this.setState({
      isShowTransferHint: isShowTransferMode,
      totalWaitPayAmount,
      accountIdList: transferShopList,
      divideRebateTransferAmount,
    });
  }

  public thridPartyModeChangeNext = (mode) => { // 通联支付
    try {
      const { originSubMerchantList } = this.state;
      if (!this.state.isChangeCard) { // 未进行银行卡切换
        mode.isCheck = !mode.isCheck;
      }

      if (this.$myMv.paymentModeList.filter((lis) => lis.code === $PaymentType.ALLINPAY).length > 0) {
        this.$myMv.paymentModeList.filter((lis) => lis.code === $PaymentType.ALLINPAY)[0].isCheck = !this.$myMv.paymentModeList.filter((lis) => lis.code === $PaymentType.ALLINPAY)[0].isCheck;
        // 重置通联支付金额
        this.$myMv.paymentModeList.filter((lis) => lis.code === $PaymentType.ALLINPAY)[0].expectedPaymentAmount = 0;
        this.$myMv.paymentModeList.filter((lis) => lis.code === $PaymentType.ALLINPAY)[0].availableTotalAmount = this.$myMv.paymentInfo.unPayableAmount;
      }

      if ((this.$myMv.paymentModeList.filter((lis) => lis.code === $PaymentType.ALLINPAY_BANK_CARD).length > 0 && this.$myMv.paymentModeList.filter((lis) => lis.code === $PaymentType.ALLINPAY_BANK_CARD)[0].isCheck) || (this.$myMv.paymentModeList.filter((lis) => lis.code === $PaymentType.ALLINPAY_WECHAT).length > 0 && this.$myMv.paymentModeList.filter((lis) => lis.code === $PaymentType.ALLINPAY_WECHAT)[0].isCheck)) {
  // 通联内部支付方式（微信，银行卡）选中时，外部支付方式（通联）也选中
        this.$myMv.paymentModeList.filter((lis) => lis.code === $PaymentType.ALLINPAY)[0].isCheck = true;

      }

      if (mode.code === $PaymentType.ALLINPAY_WECHAT) { // 通联微信
        if (this.$myMv.paymentModeList.filter((lis) => lis.code === $PaymentType.ALLINPAY_BANK_CARD).length > 0 && this.$myMv.paymentModeList.filter((lis) => lis.code === $PaymentType.ALLINPAY_BANK_CARD)[0].isCheck) { // 通联银行卡选中则取消
          this.$myMv.paymentModeList.filter((lis) => lis.code === $PaymentType.ALLINPAY_BANK_CARD)[0].isCheck = false;
        }
        // 通联微信计算
        this.$myMv.paymentModeList.filter((lis) => lis.code === $PaymentType.ALLINPAY_WECHAT)[0].availableTotalAmount = this.$myMv.mostTpAmount;
        setTimeout(() => {
          this.priorityAmountCalculate(this.$myMv.paymentModeList.filter((lis) => lis.code === $PaymentType.ALLINPAY)[0].priority, this.$myMv.paymentModeList.filter((lis) => lis.code === $PaymentType.ALLINPAY)[0], mode.isTPPymt);
          if (mode.isCheck) { // 通联选中 服务费加金额
            if (this.$myMv.paymentModeList.filter((lis) => lis.code === $PaymentType.ALLINPAY_WECHAT)[0].rateList.length > 0) {
              // todo
              const rateBookList = this.$myMv.paymentModeList.filter((lis) => lis.code === $PaymentType.ALLINPAY_WECHAT)[0].rateList[0].rateBookList;
              const expectedPaymentAmount = this.$myMv.paymentModeList.filter((lis) => lis.code === $PaymentType.ALLINPAY)[0].expectedPaymentAmount;
              if (rateBookList.length > 0) {
                rateBookList.map((item) => {
                  if (item.endValue === null || item.endValue === "" || item.endValue === undefined) {
                    if (item.beginValue <= expectedPaymentAmount) {
                      this.setState({
                        tpRate: toFixedOptimizing(expectedPaymentAmount * item.rate, 100),
                      });
                    }
                  } else {
                    if ((item.beginValue <= expectedPaymentAmount) && (expectedPaymentAmount < item.endValue)) {
                      this.setState({
                        tpRate: toFixedOptimizing(expectedPaymentAmount * item.rate, 100),
                      });
                    } else if (expectedPaymentAmount >= item.endValue) {
                      this.setState({
                        tpRate: toFixedOptimizing(expectedPaymentAmount * item.rate, 100),
                      });
                    }
                  }
                });
              }
              // this.setState({
              //   tpRate: Number(Number(this.$myMv.paymentModeList.filter((lis) => lis.code === $PaymentType.ALLINPAY)[0].expectedPaymentAmount * this.$myMv.paymentModeList.filter((lis) => lis.code === $PaymentType.ALLINPAY_WECHAT)[0].rateList[0].rate).toFixed(2)),
              // });
            }
          } else { // 通联未选中 服务费加金额
            this.setState({
              tpRate: 0,
            });
            this.$myMv.paymentModeList.filter((lis) => lis.code === $PaymentType.ALLINPAY)[0].expectedPaymentAmount = 0;
          }
          // 剩余金额
          let sum = 0;
          this.$myMv.paymentModeList.map((lis) => {
            if (lis.code === $PaymentType.ALLINPAY) {
              this.$myMv.mostTpAmount = this.$myMv.paymentInfo.unPayableAmount - sum;
            }
            sum += Number(lis.expectedPaymentAmount);
          });
          this.$myMv.surplusAmount = this.$myMv.paymentInfo.unPayableAmount - sum;
        }, 0);
      }
      if (mode.code === $PaymentType.ALLINPAY_BANK_CARD) { // 通联银行卡
        if (this.$myMv.paymentModeList.filter((lis) => lis.code === $PaymentType.ALLINPAY_WECHAT).length > 0 && this.$myMv.paymentModeList.filter((lis) => lis.code === $PaymentType.ALLINPAY_WECHAT)[0].isCheck) { // 通联微信选中则取消
          this.$myMv.paymentModeList.filter((lis) => lis.code === $PaymentType.ALLINPAY_WECHAT)[0].isCheck = false;
        }
        // 通联银行卡计算
        this.$myMv.paymentModeList.filter((lis) => lis.code === $PaymentType.ALLINPAY_BANK_CARD)[0].availableTotalAmount = this.$myMv.mostTpAmount;
        setTimeout(() => {
          this.priorityAmountCalculate(this.$myMv.paymentModeList.filter((lis) => lis.code === $PaymentType.ALLINPAY)[0].priority, this.$myMv.paymentModeList.filter((lis) => lis.code === $PaymentType.ALLINPAY)[0], mode.isTPPymt);
          if (mode.isCheck) { // 通联选中 服务费加金额
            if (this.state.bankInfo.bankCardRate === $PaymentModeType.STOREDVALUECARDRATE && this.$myMv.paymentModeList.filter((lis) => lis.code === $PaymentType.ALLINPAY_BANK_CARD)[0].rateList.filter((lis1) => lis1.rateCode === $PaymentModeType.STOREDVALUECARDRATE).length > 0) { // 储蓄卡
              const rateBookList = this.$myMv.paymentModeList.filter((lis) => lis.code === $PaymentType.ALLINPAY_BANK_CARD)[0].rateList.filter((lis) => lis.rateCode === $PaymentModeType.STOREDVALUECARDRATE)[0].rateBookList;
              const expectedPaymentAmount = this.$myMv.paymentModeList.filter((lis) => lis.code === $PaymentType.ALLINPAY)[0].expectedPaymentAmount;
              if (rateBookList.length > 0) {
                rateBookList.map((item) => {
                  if (item.endValue === null || item.endValue === "" || item.endValue === undefined) {
                    if (item.beginValue <= expectedPaymentAmount) {
                      this.setState({
                        tpRate: toFixedOptimizing(expectedPaymentAmount * item.rate, 100),
                      });
                    }
                  } else {
                    if ((item.beginValue <= expectedPaymentAmount) && (expectedPaymentAmount < item.endValue)) {
                      this.setState({
                        tpRate: toFixedOptimizing(expectedPaymentAmount * item.rate, 100),
                      });
                    } else if (expectedPaymentAmount >= item.endValue) {
                      this.setState({
                        tpRate: toFixedOptimizing(expectedPaymentAmount * item.rate, 100),
                      });
                    }
                  }
                });
              }
              // this.setState({
              //   tpRate: Number(Number(this.$myMv.paymentModeList.filter((lis) => lis.code === $PaymentType.ALLINPAY)[0].expectedPaymentAmount * this.$myMv.paymentModeList.filter((lis) => lis.code === $PaymentType.ALLINPAY_BANK_CARD)[0].rateList.filter((lis) => lis.rateCode === $PaymentModeType.STOREDVALUECARDRATE)[0].rate).toFixed(2)),
              // });
            } else { // 信用卡
              const rateBookList = this.$myMv.paymentModeList.filter((lis) => lis.code === $PaymentType.ALLINPAY_BANK_CARD)[0].rateList.filter((lis) => lis.rateCode === $PaymentModeType.CREDITCARTRATE)[0].rateBookList;
              const expectedPaymentAmount = this.$myMv.paymentModeList.filter((lis) => lis.code === $PaymentType.ALLINPAY)[0].expectedPaymentAmount;
              if (rateBookList.length > 0) {
                rateBookList.map((item) => {
                  if (item.endValue === null || item.endValue === "" || item.endValue === undefined) {
                    if (item.beginValue <= expectedPaymentAmount) {
                      this.setState({
                        tpRate: toFixedOptimizing(expectedPaymentAmount * item.rate, 100),
                      });
                    }
                  } else {
                    if ((item.beginValue <= expectedPaymentAmount) && (expectedPaymentAmount < item.endValue)) {
                      this.setState({
                        tpRate: toFixedOptimizing(expectedPaymentAmount * item.rate, 100),
                      });
                    } else if (expectedPaymentAmount >= item.endValue) {
                      this.setState({
                        tpRate: toFixedOptimizing(expectedPaymentAmount * item.rate, 100),
                      });
                    }
                  }
                });
              }
              // this.setState({
              //   tpRate: Number(Number(this.$myMv.paymentModeList.filter((lis) => lis.code === $PaymentType.ALLINPAY)[0].expectedPaymentAmount * this.$myMv.paymentModeList.filter((lis) => lis.code === $PaymentType.ALLINPAY_BANK_CARD)[0].rateList.filter((lis) => lis.rateCode === $PaymentModeType.CREDITCARTRATE)[0].rate).toFixed(2)),
              // });
            }
          } else { // 通联未选中 服务费加金额
            this.setState({
              tpRate: 0,
            });
            this.$myMv.paymentModeList.filter((lis) => lis.code === $PaymentType.ALLINPAY)[0].expectedPaymentAmount = 0;
            // 剩余金额
            let sum1 = 0;
            this.$myMv.paymentModeList.map((lis) => {
              if (lis.code === $PaymentType.ALLINPAY) {
                this.$myMv.mostTpAmount = this.$myMv.paymentInfo.unPayableAmount - sum1;
              }
              sum1 += Number(lis.expectedPaymentAmount);
            });
            this.$myMv.surplusAmount = this.$myMv.paymentInfo.unPayableAmount - sum1;
            this.$myMv.paymentModeList.filter((lis) => lis.code === $PaymentType.ALLINPAY)[0].availableTotalAmount = this.$myMv.surplusAmount;
          }
          // 剩余金额
          let sum = 0;
          this.$myMv.paymentModeList.map((lis) => {
            if (lis.code === $PaymentType.ALLINPAY) {
              this.$myMv.mostTpAmount = this.$myMv.paymentInfo.unPayableAmount - sum;
            }
            sum += Number(lis.expectedPaymentAmount);
          });
          this.$myMv.surplusAmount = this.$myMv.paymentInfo.unPayableAmount - sum;
        }, 0);
      }
      this.handleSubMerchantList(originSubMerchantList);
      this.checkIsShowTransferHint();
    } catch(e) {
      // console.log('thridPartyModeChangeNextError', e)
    }
  }

  public onChangeTP = (mode) => { // 通联支付
    if (mode.code === $PaymentType.ALLINPAY_BANK_CARD && !mode.isCheck) {
      this.setState({
        isShowChooseBankCardModal: true,
      });
    } else {
      this.setState({
        isChangeCard: false,
      }, () => {
        this.thridPartyModeChangeNext(mode);
      });
    }
  }

  public getSmallNum = (a, b) => {
    const res = a < b ? a : b;
    return res ? res : 0;
  }

  // 根据每次支付方式的点击及优先级调整各个支付方式金额
  public priorityAmountCalculate = (priority, mode, isTPPymt) => { // 当各个支付方式选中时
    const { freightPayInfo } = this.$myMv;
    const currentAmount = sum(
      this.$myMv.paymentModeList
        .filter((item) => indexOf([$PaymentModeType.ALLINPAY_WECHAT, $PaymentModeType.ALLINPAY_BANK_CARD], item.code) < 0)
        .filter((item) => item.isCheck)
        .filter((item) => item.priority < priority)
        .map((item) => item.expectedPaymentAmount));
    let remainAmount = this.$myMv.paymentInfo.unPayableAmount - currentAmount;
    this.$myMv.paymentModeList.map((item) => {
      if (item.isCheck && item.code === $PaymentModeType.DIVIDE_REBATE) {
        item.orderAmount = this.getSmallNum(item.availableOrderAmount, remainAmount);
        remainAmount = Number((remainAmount - item.orderAmount).toFixed(2));
      }
    })
    this.$myMv.paymentModeList
      .filter((item) => item.priority >= priority)
      .map((item) => {
        switch (item.code) {
          case $PaymentModeType.BANKTRANSFER:
            // 线下转账
            // console.log("线下转账", item);
            break;
          case $PaymentModeType.ALLINPAY:
            // case $PaymentModeType.ALLINPAY_WECHAT:
            // case $PaymentModeType.ALLINPAY_BANK_CARD:
            // 通联支付
            if (item.isCheck) {
              item.availableTotalAmount = this.state.isAdjust ? item.availableTotalAmount : remainAmount;
              item.expectedPaymentAmount = this.getSmallNum(remainAmount, item.availableTotalAmount);
              remainAmount = remainAmount - item.expectedPaymentAmount;
            } else {
              item.expectedPaymentAmount = 0;
            }
            break;
          case $PaymentModeType.CREDIT:
            if (item.isCheck && remainAmount > 0) {
              if (freightPayInfo && freightPayInfo.freightNotPayAmount >= 0) { // 有运费待支付
                const availableFreightAmount = this.state.validityType === $ValidityType.NEW_CREDIT ? item.availableFreightAmountForNoLimitPeriod : item.availableFreightAmount;
                const availableTotalAmount = this.state.validityType === $ValidityType.NEW_CREDIT ? item.availableTotalAmountForNoLimitPeriod : item.availableTotalAmount;
                const paymenFreightAmount = Number((this.$myMv.paymentInfo.unPayableAmount - remainAmount).toFixed(2));
                // 剩余未支付的运费
                let remainFreightAmount = Number((freightPayInfo.freightNotPayAmount - paymenFreightAmount).toFixed(2)) > 0 ? Number((freightPayInfo.freightNotPayAmount - paymenFreightAmount).toFixed(2)) : 0;
                // 此次订单使用信用支付实际支付的运费金额
                item.freightAmount = this.getSmallNum(remainFreightAmount, availableFreightAmount);
                remainFreightAmount = Number((remainFreightAmount - item.freightAmount).toFixed(2));
                remainAmount = Number((remainAmount - item.freightAmount).toFixed(2));
                // 订单能够使用信用额度支付的金额
                const orderAvailableAmount = Number((availableTotalAmount - item.freightAmount).toFixed(2));
                // 订单剩余支付金额
                const orderRemainAmount = Number((remainAmount - remainFreightAmount).toFixed(2));
                // 此次订单使用信用支付实际支付的订单金额
                item.orderAmount = this.getSmallNum(orderAvailableAmount, orderRemainAmount);
                // 此次订单使用信用支付实际支付的所有金额
                item.expectedPaymentAmount = Number((item.orderAmount + item.freightAmount).toFixed(2));
                remainAmount = Number((remainAmount - item.orderAmount).toFixed(2));
              } else {
                const availableTotalAmount = this.state.validityType === $ValidityType.NEW_CREDIT ? item.availableTotalAmountForNoLimitPeriod : item.availableTotalAmount;
                item.expectedPaymentAmount = this.getSmallNum(remainAmount, availableTotalAmount);
                remainAmount = Number((remainAmount - item.expectedPaymentAmount).toFixed(2));
              }
            } else {
              item.expectedPaymentAmount = 0;
            }
            break;
          case $PaymentModeType.STOREDVALUE:
          case $PaymentModeType.REBATEDEDUCTION:
            // 内部支付方式
            if (item.isCheck && remainAmount > 0) {
              item.expectedPaymentAmount = this.getSmallNum(remainAmount, item.availableTotalAmount);
              remainAmount = remainAmount - item.expectedPaymentAmount;
            } else {
              item.expectedPaymentAmount = 0;
            }
            break;
          default:
            // 未知类型
            // console.log("未知类型-通联支付子类型", item);
            break;
        }
      });
  }

  public showChooseBankCardModal = () => {
    this.setState({
      isShowChooseBankCardModal: true,
      isChangeCard: true,
    });
  }

  public goBack = () => {
    this.setState({
      isShowChooseBankCardModal: false,
    });
    if (!this.state.isChangeCard) {
      if (this.$myMv.paymentModeList.filter((mode) => mode.code === $PaymentType.ALLINPAY_BANK_CARD).length > 0) {
        this.$myMv.paymentModeList.filter((mode) => mode.code === $PaymentType.ALLINPAY_BANK_CARD)[0].isCheck = false;
        this.$myMv.paymentModeList.filter((mode) => mode.code === $PaymentType.ALLINPAY)[0].isCheck = false;
        if (this.$myMv.paymentModeList.filter((mode) => mode.code === $PaymentType.ALLINPAY_WECHAT).length > 0) {
          if (this.$myMv.paymentModeList.filter((mode) => mode.code === $PaymentType.ALLINPAY_WECHAT)[0].isCheck === false) {
            this.$myMv.paymentModeList.filter((mode) => mode.code === $PaymentType.ALLINPAY)[0].expectedPaymentAmount = 0;
          }
        } else {
          this.$myMv.paymentModeList.filter((mode) => mode.code === $PaymentType.ALLINPAY)[0].expectedPaymentAmount = 0;
        }
        this.setState({
          tpRate: 0,
        }, () => {
          // 剩余金额
          let sum = 0;
          this.$myMv.paymentModeList.map((lis) => {
            if (lis.code === $PaymentType.ALLINPAY) {
              this.$myMv.mostTpAmount = this.$myMv.paymentInfo.unPayableAmount - sum;
            }
            sum += Number(lis.expectedPaymentAmount);
          });
          this.$myMv.surplusAmount = this.$myMv.paymentInfo.unPayableAmount - sum;
        });
      }
    }
  }

  public chooseBank = (bank) => {
    this.setState({
      isShowChooseBankCardModal: false,
      selectedBankName: bank.bankName,
      selectedBankCardType: bank.bankCardTypeName,
      selectedBankCardNo: bank.bankCardNo,
      selectedBankId: bank.id,
      bankInfo: bank,
    }, () => {
      if (this.state.selectedBankId) {
        bank.isCheck = true;
        this.chooseBankCardMv.bankList.filter((lis) => lis.id !== bank.id).map((card) => {
          card.isCheck = false;
        });
        const mode = this.$myMv.paymentModeList.filter((lis) => lis.code === $PaymentType.ALLINPAY_BANK_CARD)[0];
        // if (!mode.isCheck) {
        this.thridPartyModeChangeNext(mode);
        // }
      }
    });
  }

  public adjustAmount = () => { // 调整金额弹窗
    this.setState({
      showAmountModal: true,
      tpTransferAmount: this.$myMv.paymentModeList.filter((mode) => mode.code === $PaymentType.ALLINPAY).length > 0 && this.$myMv.paymentModeList.filter((mode) => mode.code === $PaymentType.ALLINPAY)[0].expectedPaymentAmount,
    }, () => {
      // 剩余金额
      let sum = 0;
      this.$myMv.paymentModeList.map((lis) => {
        if (lis.code === $PaymentType.ALLINPAY) {
          this.$myMv.mostTpAmount = this.$myMv.paymentInfo.unPayableAmount - sum;
        }
        sum += Number(lis.expectedPaymentAmount);
      });
      this.$myMv.surplusAmount = this.$myMv.paymentInfo.unPayableAmount - sum + this.state.tpTransferAmount;
    });
  }

  public changeAdjustAmount = (value) => { // 改变调整金额
    const targetPaymentBank = this.$myMv.paymentModeList.filter((mode) => mode.code === $PaymentType.ALLINPAY_BANK_CARD)
    const targetPaymentWechat = this.$myMv.paymentModeList.filter((mode) => mode.code === $PaymentType.ALLINPAY_WECHAT)
    if (targetPaymentBank.length > 0 && targetPaymentBank[0].isCheck) {
      this.$myMv.paymentModeList.filter((mode) => mode.code === $PaymentType.ALLINPAY_BANK_CARD)[0].availableTotalAmount = value;
    }
    if (targetPaymentWechat.length > 0 && targetPaymentWechat[0].isCheck) {
      this.$myMv.paymentModeList.filter((mode) => mode.code === $PaymentType.ALLINPAY_WECHAT)[0].availableTotalAmount = value;
    }
    this.setState({
      tpTransferAmount: value,
      isAdjust: true,
    });
  }

  public confirmAdjustAmount = () => { // 确认调整金额
    const { isAdjust } = this.state;
    if (isAdjust) {
      this.$myMv.paymentModeList.filter((pay) => pay.code === $PaymentType.ALLINPAY)[0].expectedPaymentAmount = 0;
      // 剩余金额
      let sum = 0;
      this.$myMv.paymentModeList.map((lis) => {
        if (lis.code === $PaymentType.ALLINPAY) {
          this.$myMv.mostTpAmount = this.$myMv.paymentInfo.unPayableAmount - sum;
        }
        sum += Number(lis.expectedPaymentAmount);
      });
      this.$myMv.surplusAmount = this.$myMv.paymentInfo.unPayableAmount - sum;
      if (this.state.tpTransferAmount > this.$myMv.mostTpAmount) {
        Toast.info(`本次最多支付${this.$myMv.mostTpAmount ? Number(this.$myMv.mostTpAmount).toFixed(2) : 0}元`);
        return;
      }
      if (this.state.tpTransferAmount <= 0) {
        Toast.info("请输入正数的数字");
        return;
      }
      if (!/^[0-9]+(.[0-9]{0,2})?$/.test(String(this.state.tpTransferAmount))) {
        Toast.info("金额最多有两位小数");
        return;
      }
      this.$myMv.paymentModeList.filter((pay) => pay.code === $PaymentType.ALLINPAY)[0].expectedPaymentAmount = Number(this.state.tpTransferAmount);
      this.$myMv.paymentModeList.filter((pay) => pay.code === $PaymentType.ALLINPAY)[0].availableTotalAmount = Number(this.state.tpTransferAmount);
      this.$myMv.paymentModeList.filter((pay) => pay.code === $PaymentType.ALLINPAY)[0].isCheck = true;
      // 剩余金额
      let sum2 = 0;
      this.$myMv.paymentModeList.map((lis) => {
        if (lis.code === $PaymentType.ALLINPAY) {
          this.$myMv.mostTpAmount = this.$myMv.paymentInfo.unPayableAmount - sum2;
        }
        sum2 += Number(lis.expectedPaymentAmount);
      });
      this.$myMv.surplusAmount = this.$myMv.paymentInfo.unPayableAmount - sum2;
      if (this.$myMv.paymentModeList.filter((lis) => lis.code === $PaymentType.ALLINPAY_BANK_CARD).length > 0 && this.$myMv.paymentModeList.filter((lis) => lis.code === $PaymentType.ALLINPAY_BANK_CARD)[0].isCheck) { // 通联银行卡
        if (this.state.bankInfo.bankCardRate === $PaymentModeType.STOREDVALUECARDRATE && this.$myMv.paymentModeList.filter((lis) => lis.code === $PaymentType.ALLINPAY_BANK_CARD)[0].rateList.filter((lis) => lis.rateCode === $PaymentModeType.STOREDVALUECARDRATE).length > 0) { // 储蓄卡
          // todo
          const rateBookList = this.$myMv.paymentModeList.filter((lis) => lis.code === $PaymentType.ALLINPAY_BANK_CARD)[0].rateList.filter((lis) => lis.rateCode === $PaymentModeType.STOREDVALUECARDRATE)[0].rateBookList;
          const expectedPaymentAmount = this.$myMv.paymentModeList.filter((lis) => lis.code === $PaymentType.ALLINPAY)[0].expectedPaymentAmount;
          if (rateBookList.length > 0) {
            rateBookList.map((item) => {
              if (item.endValue === null || item.endValue === "" || item.endValue === undefined) {
                if (item.beginValue <= expectedPaymentAmount) {
                  this.setState({
                    tpRate: toFixedOptimizing(expectedPaymentAmount * item.rate, 100),
                  });
                }
              } else {
                if ((item.beginValue <= expectedPaymentAmount) && (expectedPaymentAmount < item.endValue)) {
                  this.setState({
                    tpRate: toFixedOptimizing(expectedPaymentAmount * item.rate, 100),
                  });
                } else if (expectedPaymentAmount >= item.endValue) {
                  this.setState({
                    tpRate: toFixedOptimizing(expectedPaymentAmount * item.rate, 100),
                  });
                }
              }
            });
          }
          // this.setState({
          //   tpRate: Number(Number(this.$myMv.paymentModeList.filter((lis) => lis.code === $PaymentType.ALLINPAY)[0].expectedPaymentAmount * this.$myMv.paymentModeList.filter((lis) => lis.code === $PaymentType.ALLINPAY_BANK_CARD)[0].rateList.filter((lis) => lis.rateCode === $PaymentModeType.STOREDVALUECARDRATE)[0].rate).toFixed(2)),
          // });
        } else { // 信用卡
          const rateBookList = this.$myMv.paymentModeList.filter((lis) => lis.code === $PaymentType.ALLINPAY_BANK_CARD)[0].rateList.filter((lis) => lis.rateCode === $PaymentModeType.CREDITCARTRATE)[0].rateBookList;
          const expectedPaymentAmount = this.$myMv.paymentModeList.filter((lis) => lis.code === $PaymentType.ALLINPAY)[0].expectedPaymentAmount;
          if (rateBookList.length > 0) {
            rateBookList.map((item) => {
              if (item.endValue === null || item.endValue === "" || item.endValue === undefined) {
                if (item.beginValue <= expectedPaymentAmount) {
                  this.setState({
                    tpRate: toFixedOptimizing(expectedPaymentAmount * item.rate, 100),
                  });
                }
              } else {
                if ((item.beginValue <= expectedPaymentAmount) && (expectedPaymentAmount < item.endValue)) {
                  this.setState({
                    tpRate: toFixedOptimizing(expectedPaymentAmount * item.rate, 100),
                  });
                } else if (expectedPaymentAmount >= item.endValue) {
                  this.setState({
                    tpRate: toFixedOptimizing(expectedPaymentAmount * item.rate, 100),
                  });
                }
              }
            });
          }
          // this.setState({
          //   tpRate: Number(Number(this.$myMv.paymentModeList.filter((lis) => lis.code === $PaymentType.ALLINPAY)[0].expectedPaymentAmount * this.$myMv.paymentModeList.filter((lis) => lis.code === $PaymentType.ALLINPAY_BANK_CARD)[0].rateList.filter((lis) => lis.rateCode === $PaymentModeType.CREDITCARTRATE)[0].rate).toFixed(2)),
          // });
        }
      }
      if (this.$myMv.paymentModeList.filter((lis) => lis.code === $PaymentType.ALLINPAY_WECHAT).length > 0
        && this.$myMv.paymentModeList.filter((lis) => lis.code === $PaymentType.ALLINPAY_WECHAT)[0].isCheck) { // 通联微信
        if (this.$myMv.paymentModeList.filter((lis) => lis.code === $PaymentType.ALLINPAY_WECHAT)[0].rateList.length > 0) {
          // todo
          const rateBookList = this.$myMv.paymentModeList.filter((lis) => lis.code === $PaymentType.ALLINPAY_WECHAT)[0].rateList[0].rateBookList;
          const expectedPaymentAmount = this.$myMv.paymentModeList.filter((lis) => lis.code === $PaymentType.ALLINPAY)[0].expectedPaymentAmount;
          if (rateBookList.length > 0) {
            rateBookList.map((item) => {
              if (item.endValue === null || item.endValue === "" || item.endValue === undefined) {
                if (item.beginValue <= expectedPaymentAmount) {
                  this.setState({
                    tpRate: toFixedOptimizing(expectedPaymentAmount * item.rate, 100),
                  });
                }
              } else {
                if ((item.beginValue <= expectedPaymentAmount) && (expectedPaymentAmount < item.endValue)) {
                  this.setState({
                    tpRate: toFixedOptimizing(expectedPaymentAmount * item.rate, 100),
                  });
                } else if (expectedPaymentAmount >= item.endValue) {
                  this.setState({
                    tpRate: toFixedOptimizing(expectedPaymentAmount * item.rate, 100),
                  });
                }
              }
            });
          }
          // this.setState({
          //   tpRate: Number(Number(this.$myMv.paymentModeList.filter((lis) => lis.code === $PaymentType.ALLINPAY)[0].expectedPaymentAmount * this.$myMv.paymentModeList.filter((lis) => lis.code === $PaymentType.ALLINPAY_WECHAT)[0].rateList[0].rate).toFixed(2)),
          // });
        }
      }

      const arr = this.$myMv.paymentModeList.slice(this.$myMv.paymentModeList.filter((lis) => lis.code === $PaymentType.ALLINPAY)[0].priority + 1).filter(((lis1) => lis1.code !== $PaymentModeType.ALLINPAY_BANK_CARD)).filter((lis2) => lis2.code !== $PaymentModeType.ALLINPAY_WECHAT);
      let isCheck = false;
      arr.map((lis3) => {// 如果通联后面的有一个选中
        if (lis3.isCheck) {
          isCheck = true;
        }
      });
      if (isCheck) {
        this.priorityAmountCalculate(this.$myMv.paymentModeList.filter((lis) => lis.code === $PaymentType.ALLINPAY)[0].priority, this.$myMv.paymentModeList.filter((lis) => lis.code === $PaymentType.ALLINPAY)[0], null);
        // 剩余金额
        let sum1 = 0;
        this.$myMv.paymentModeList.map((lis) => {
          if (lis.code === $PaymentType.ALLINPAY) {
            this.$myMv.mostTpAmount = this.$myMv.paymentInfo.unPayableAmount - sum1;
          }
          if (lis.code === $PaymentType.ALLINPAY_BANK_CARD) {
            lis.expectedPaymentAmount = 0;
          }
          if (lis.code === $PaymentType.ALLINPAY_WECHAT) {
            lis.expectedPaymentAmount = 0;
          }
          sum1 += Number(lis.expectedPaymentAmount);
        });
        this.$myMv.surplusAmount = this.$myMv.paymentInfo.unPayableAmount - sum1;
      }
      this.setState({
        showAmountModal: false,
        isAdjust: false,
      });
    } else {
      // 剩余金额
      let sum = 0;
      this.$myMv.paymentModeList.map((lis) => {
        if (lis.code === $PaymentType.ALLINPAY) {
          this.$myMv.mostTpAmount = this.$myMv.paymentInfo.unPayableAmount - sum;
        }
        sum += Number(lis.expectedPaymentAmount);
      });
      this.$myMv.surplusAmount = this.$myMv.paymentInfo.unPayableAmount - sum;
      this.setState({
        showAmountModal: false,
      });
    }
  }

  public hideAmountModal = () => {
    this.setState({
      showAmountModal: false,
    });
    this.$myMv.paymentModeList.filter((pay) => pay.code === $PaymentType.ALLINPAY)[0].expectedPaymentAmount = this.$myMv.surplusAmount;
    // 剩余金额
    let sum = 0;
    this.$myMv.paymentModeList.map((lis) => {
      if (lis.code === $PaymentType.ALLINPAY) {
        this.$myMv.mostTpAmount = this.$myMv.paymentInfo.unPayableAmount - sum;
      }
      sum += Number(lis.expectedPaymentAmount);
    });
    this.$myMv.surplusAmount = this.$myMv.paymentInfo.unPayableAmount - sum;
  }

  public changeTransferAmount = (value) => { // 线下转账
    this.setState({
      transferAmount: value,
    });
  }

  public renderUnPayableAmount = (unPayableAmount) => {
    let amount = 0;
    if (this.$myMv.paymentModeList.filter((mode) => mode.code === $PaymentType.ALLINPAY_BANK_CARD).length > 0
      && this.$myMv.paymentModeList.filter((mode) => mode.code === $PaymentType.ALLINPAY_BANK_CARD)[0].isCheck) { // 如果通联银行卡选中
      if (this.state.bankInfo.bankCardRate === $PaymentModeType.STOREDVALUECARDRATE
        && this.$myMv.paymentModeList.filter((lis) => lis.code === $PaymentType.ALLINPAY_BANK_CARD)[0].rateList.filter((lis1) => lis1.rateCode === $PaymentModeType.STOREDVALUECARDRATE).length > 0) { // 储蓄卡

        const rateBookList = this.$myMv.paymentModeList.filter((lis) => lis.code === $PaymentType.ALLINPAY_BANK_CARD)[0].rateList.filter((lis) => lis.rateCode === $PaymentModeType.STOREDVALUECARDRATE)[0].rateBookList;
        const expectedPaymentAmount = this.$myMv.paymentModeList.filter((lis) => lis.code === $PaymentType.ALLINPAY)[0].expectedPaymentAmount;
        if (rateBookList.length > 0) {
          rateBookList.map((item) => {
            if (item.endValue === null || item.endValue === "" || item.endValue === undefined) {
              if (item.beginValue <= expectedPaymentAmount) {
                amount = Number(unPayableAmount) + toFixedOptimizing(expectedPaymentAmount * item.rate, 100);
              }
            } else {
              if ((item.beginValue <= expectedPaymentAmount) && (expectedPaymentAmount < item.endValue)) {
                amount = Number(unPayableAmount) + toFixedOptimizing(expectedPaymentAmount * item.rate, 100);
              } else if (expectedPaymentAmount >= item.endValue) {
                amount = Number(unPayableAmount) + toFixedOptimizing(expectedPaymentAmount * item.rate, 100);
              }
            }
          });
        }

        // amount = Number(unPayableAmount) + Number(Number(this.$myMv.paymentModeList.filter((mode) => mode.code === $PaymentType.ALLINPAY)[0].expectedPaymentAmount * this.$myMv.paymentModeList.filter((lis) => lis.code === $PaymentType.ALLINPAY_BANK_CARD)[0].rateList.filter((lis) => lis.rateCode === $PaymentModeType.STOREDVALUECARDRATE)[0].rate).toFixed(2));
      } else { // 信用卡
        const rateBookList = this.$myMv.paymentModeList.filter((lis) => lis.code === $PaymentType.ALLINPAY_BANK_CARD)[0].rateList.filter((lis) => lis.rateCode === $PaymentModeType.CREDITCARTRATE)[0].rateBookList;
        const expectedPaymentAmount = this.$myMv.paymentModeList.filter((lis) => lis.code === $PaymentType.ALLINPAY)[0].expectedPaymentAmount;
        if (rateBookList.length > 0) {
          rateBookList.map((item) => {
            if (item.endValue === null || item.endValue === "" || item.endValue === undefined) {
              if (item.beginValue <= expectedPaymentAmount) {
                amount = Number(unPayableAmount) + toFixedOptimizing(expectedPaymentAmount * item.rate, 100);
              }
            } else {
              if ((item.beginValue <= expectedPaymentAmount) && (expectedPaymentAmount < item.endValue)) {
                amount = Number(unPayableAmount) + toFixedOptimizing(expectedPaymentAmount * item.rate, 100);
              } else if (expectedPaymentAmount >= item.endValue) {
                amount = Number(unPayableAmount) + toFixedOptimizing(expectedPaymentAmount * item.rate, 100);
              }
            }
          });
        }

        // amount = Number(unPayableAmount) + Number(Number(this.$myMv.paymentModeList.filter((mode) => mode.code === $PaymentType.ALLINPAY)[0].expectedPaymentAmount * this.$myMv.paymentModeList.filter((lis) => lis.code === $PaymentType.ALLINPAY_BANK_CARD)[0].rateList.filter((lis) => lis.rateCode === $PaymentModeType.CREDITCARTRATE)[0].rate).toFixed(2));
      }
    } else if (this.$myMv.paymentModeList.filter((mode) => mode.code === $PaymentType.ALLINPAY_WECHAT).length > 0
      && this.$myMv.paymentModeList.filter((mode) => mode.code === $PaymentType.ALLINPAY_WECHAT)[0].isCheck) { // 如果通联微信选中
      if (this.$myMv.paymentModeList.filter((mode) => mode.code === $PaymentType.ALLINPAY_WECHAT)[0].rateList.length > 0) {
        const rateBookList = this.$myMv.paymentModeList.filter((lis) => lis.code === $PaymentType.ALLINPAY_WECHAT)[0].rateList[0].rateBookList;
        const expectedPaymentAmount = this.$myMv.paymentModeList.filter((lis) => lis.code === $PaymentType.ALLINPAY)[0].expectedPaymentAmount;
        if (rateBookList.length > 0) {
          rateBookList.map((item) => {
            if (item.endValue === null || item.endValue === "" || item.endValue === undefined) {
              if (item.beginValue <= expectedPaymentAmount) {
                amount = Number(unPayableAmount) + toFixedOptimizing(expectedPaymentAmount * item.rate, 100);
              }
            } else {
              if ((item.beginValue <= expectedPaymentAmount) && (expectedPaymentAmount < item.endValue)) {
                amount = Number(unPayableAmount) + toFixedOptimizing(expectedPaymentAmount * item.rate, 100);
              } else if (expectedPaymentAmount >= item.endValue) {
                amount = Number(unPayableAmount) + toFixedOptimizing(expectedPaymentAmount * item.rate, 100);
              }
            }
          });
        }

        // amount = Number(unPayableAmount) + Number(Number(this.$myMv.paymentModeList.filter((mode) => mode.code === $PaymentType.ALLINPAY)[0].expectedPaymentAmount * this.$myMv.paymentModeList.filter((mode) => mode.code === $PaymentType.ALLINPAY_WECHAT)[0].rateList[0].rate).toFixed(2));
      }
    } else {
      amount = unPayableAmount;
    }
    return Number(amount).toFixed(2);
  }

  public renderPayableAmount = (payableAmount) => {
    let amount = 0;
    if (this.$myMv.paymentModeList.filter((mode) => mode.code === $PaymentType.ALLINPAY_BANK_CARD).length > 0 && this.$myMv.paymentModeList.filter((mode) => mode.code === $PaymentType.ALLINPAY_BANK_CARD)[0].isCheck) { // 如果通联银行卡选中
      if (this.state.bankInfo.bankCardRate === $PaymentModeType.STOREDVALUECARDRATE && this.$myMv.paymentModeList.filter((lis) => lis.code === $PaymentType.ALLINPAY_BANK_CARD)[0].rateList.filter((lis1) => lis1.rateCode === $PaymentModeType.STOREDVALUECARDRATE).length > 0) { // 储蓄卡
        const rateBookList = this.$myMv.paymentModeList.filter((lis) => lis.code === $PaymentType.ALLINPAY_BANK_CARD)[0].rateList.filter((lis) => lis.rateCode === $PaymentModeType.STOREDVALUECARDRATE)[0].rateBookList;
        const expectedPaymentAmount = this.$myMv.paymentModeList.filter((lis) => lis.code === $PaymentType.ALLINPAY)[0].expectedPaymentAmount;
        if (rateBookList.length > 0) {
          rateBookList.map((item) => {
            if (item.endValue === null || item.endValue === "" || item.endValue === undefined) {
              if (item.beginValue <= expectedPaymentAmount) {
                amount = Number(payableAmount) + toFixedOptimizing(expectedPaymentAmount * item.rate, 100);
              }
            } else {
              if ((item.beginValue <= expectedPaymentAmount) && (expectedPaymentAmount < item.endValue)) {
                amount = Number(payableAmount) + toFixedOptimizing(expectedPaymentAmount * item.rate, 100);
              } else if (expectedPaymentAmount >= item.endValue) {
                amount = Number(payableAmount) + toFixedOptimizing(expectedPaymentAmount * item.rate, 100);
              }
            }
          });
        }

        // amount = Number(payableAmount) + Number(Number(this.$myMv.paymentModeList.filter((mode) => mode.code === $PaymentType.ALLINPAY)[0].expectedPaymentAmount * this.$myMv.paymentModeList.filter((lis) => lis.code === $PaymentType.ALLINPAY_BANK_CARD)[0].rateList.filter((lis) => lis.rateCode === $PaymentModeType.STOREDVALUECARDRATE)[0].rate).toFixed(2));
      } else { // 信用卡
        const rateBookList = this.$myMv.paymentModeList.filter((lis) => lis.code === $PaymentType.ALLINPAY_BANK_CARD)[0].rateList.filter((lis) => lis.rateCode === $PaymentModeType.CREDITCARTRATE)[0].rateBookList;
        const expectedPaymentAmount = this.$myMv.paymentModeList.filter((lis) => lis.code === $PaymentType.ALLINPAY)[0].expectedPaymentAmount;
        if (rateBookList.length > 0) {
          rateBookList.map((item) => {
            if (item.endValue === null || item.endValue === "" || item.endValue === undefined) {
              if (item.beginValue <= expectedPaymentAmount) {
                amount = Number(payableAmount) + toFixedOptimizing(expectedPaymentAmount * item.rate, 100);
              }
            } else {
              if ((item.beginValue <= expectedPaymentAmount) && (expectedPaymentAmount < item.endValue)) {
                amount = Number(payableAmount) + toFixedOptimizing(expectedPaymentAmount * item.rate, 100);
              } else if (expectedPaymentAmount >= item.endValue) {
                amount = Number(payableAmount) + toFixedOptimizing(expectedPaymentAmount * item.rate, 100);
              }
            }
          });
        }

        // amount = Number(payableAmount) + Number(Number(this.$myMv.paymentModeList.filter((mode) => mode.code === $PaymentType.ALLINPAY)[0].expectedPaymentAmount * this.$myMv.paymentModeList.filter((lis) => lis.code === $PaymentType.ALLINPAY_BANK_CARD)[0].rateList.filter((lis) => lis.rateCode === $PaymentModeType.CREDITCARTRATE)[0].rate).toFixed(2));
      }
    } else if (this.$myMv.paymentModeList.filter((mode) => mode.code === $PaymentType.ALLINPAY_WECHAT).length > 0 && this.$myMv.paymentModeList.filter((mode) => mode.code === $PaymentType.ALLINPAY_WECHAT)[0].isCheck) { // 如果通联微信选中
      if (this.$myMv.paymentModeList.filter((mode) => mode.code === $PaymentType.ALLINPAY_WECHAT)[0].rateList.length > 0) {

        const rateBookList = this.$myMv.paymentModeList.filter((lis) => lis.code === $PaymentType.ALLINPAY_WECHAT)[0].rateList[0].rateBookList;
        const expectedPaymentAmount = this.$myMv.paymentModeList.filter((lis) => lis.code === $PaymentType.ALLINPAY)[0].expectedPaymentAmount;
        if (rateBookList.length > 0) {
          rateBookList.map((item) => {
            if (item.endValue === null || item.endValue === "" || item.endValue === undefined) {
              if (item.beginValue <= expectedPaymentAmount) {
                amount = Number(payableAmount) + toFixedOptimizing(expectedPaymentAmount * item.rate, 100);
              }
            } else {
              if ((item.beginValue <= expectedPaymentAmount) && (expectedPaymentAmount < item.endValue)) {
                amount = Number(payableAmount) + toFixedOptimizing(expectedPaymentAmount * item.rate, 100);
              } else if (expectedPaymentAmount >= item.endValue) {
                amount = Number(payableAmount) + toFixedOptimizing(expectedPaymentAmount * item.rate, 100);
              }
            }
          });
        }

        // amount = Number(payableAmount) + Number(Number(this.$myMv.paymentModeList.filter((mode) => mode.code === $PaymentType.ALLINPAY)[0].expectedPaymentAmount * this.$myMv.paymentModeList.filter((mode) => mode.code === $PaymentType.ALLINPAY_WECHAT)[0].rateList[0].rate).toFixed(2));
      }
    } else {
      amount = payableAmount;
    }
    return Number(amount).toFixed(2);
  }

  public showRateModal = (list) => {
    const { bankInfo } = this.state; // bankCardRate STORED_VALUE_CARD_RATE CREDIT_CARD_RATE
    if (list.paymentCode === $PaymentType.ALLINPAY_WECHAT) { // 微信
      this.setState({
        showRate: true,
        selectRateList: list && list.rateList && list.rateList[0] && list.rateList[0].rateBookList && list.rateList[0],
      }, () => {
        $("html").css("overflow", "hidden");
        $("body").css("overflow", "hidden");
      });
    } else { // 银行卡
      const data = list.rateList && list.rateList.filter((item) => item.rateCode === bankInfo.bankCardRate);
      this.setState({
        showRate: true,
        selectRateList: data && data[0],
      }, () => {
        $("html").css("overflow", "hidden");
        $("body").css("overflow", "hidden");
      });
    }
  }

  public hideRateModal = () => {
    this.setState({
      showRate: false,
    }, () => {
      $("html").css("overflow", "scroll");
      $("body").css("overflow", "scroll");
    });
  }

  public showPaymentLine = () => {
    this.setState({isShowPaymentLine: true});
  }

  public changePaymentLine = (line) => {
    const {subMerchantList} = this.state;
    if (subMerchantList.length > 0) {
      subMerchantList.map((item) => {
        item.checked = false;
        if (item.code === line.code) {
          item.checked = true;
        }
      });
    }
    this.setState({
      subMerchantList,
      isShowPaymentLine: false,
    });
  }

  public transferCallback = () => {
    this.loadPaymentInfo(() => {
      const { paymentModeList } = this.$myMv;
      const storedValuedMode = paymentModeList.find((mode) => mode.code === $PaymentModeType.STOREDVALUE);
      if (storedValuedMode) {
        this.changeInternalPaymentMethod(storedValuedMode);
      }
    });
  }

  public showCreditModal = (isShowCreditModal) => {
    this.setState({ isShowCreditModal });
  }

  public onChangeValidity = (validityType) => {
    this.setState({ validityType, isShowCreditModal: false }, () => {
      const { paymentModeList } = this.$myMv;
      const creditMode = paymentModeList.find((mode) => mode.code === $PaymentModeType.CREDIT);
      this.changeInternalPaymentMethod(creditMode, false);
    });
  }

  public showCreditTips = (isShowCreditTips) => {
    this.setState({ isShowCreditTips });
  }

  public render() {
    const { match } = this.props;
    const {
      isShowMabel, record, confirmAmount, showQuestionHelp, overTop, activekey, isChooseAccount, disabledCredit,
      selectCapitalAccountInfo, transferAmount, isShow, selectedBankName, selectedBankCardType,
      isShowMoreValidityType, selectedBankCardNo, tpTransferAmount, showAmountModal, isShowChooseBankCardModal,
      isShowCreditModal, isShowCreditTips, feeDocumentId, canPartialPay, showRate, selectRateList, isShowPaymentLine,
      subMerchantList, validityType, isShowTransferHint, totalWaitPayAmount, accountIdList, divideRebateTransferAmount,
      disabledDivideRebate, disabledTransfer
    } = this.state;
    const { isRole, orderOrgList } = this.$SubmitOrderMv;
    const { paymentInfo, pics, capitalAccountList, isSpin, questionHelp, orderPaymentList, paymentModeList, surplusAmount, mostTpAmount, freightPayInfo, createdTransferList } = this.$myMv;
    const { payableAmount, auditedAmount, unAuditedAmount, unPayableAmount } = paymentInfo || {};
    const paymentAmount = Number(unPayableAmount - surplusAmount + this.state.tpRate + Number(transferAmount)).toFixed(2);
    const paymentLineIndex = subMerchantList && subMerchantList.length > 0 ? subMerchantList.findIndex((line) => line.checked === true) : -1;
    const selectedSubMerchant = paymentLineIndex > -1 && subMerchantList[paymentLineIndex].name;

    return (
      <PaymentPage id="pageWrap" style={{ position: overTop ? "fixed" : "relative" }}>
        <Spin spinning={isSpin}>
          <OrderPaymentAccountShow
            payableAmount={this.renderPayableAmount(payableAmount)}
            auditedAmount={auditedAmount}
            unAuditedAmount={unAuditedAmount}
            unPayableAmount={this.renderUnPayableAmount(unPayableAmount)}
          />
          {
            ((isShowTransferHint && (totalWaitPayAmount > 0 || divideRebateTransferAmount > 0)) || (createdTransferList.length > 0)) &&
            <TransferHint
              salesOrderIdList={[match.params.orderId]}
              createdTransferList={createdTransferList}
              totalWaitPayAmount={totalWaitPayAmount}
              divideRebateTransferAmount={divideRebateTransferAmount}
              accountIdList={accountIdList}
              reload={this.transferCallback}
            />
          }
          {
            record !== "record" && <SinglePaymentModeList
              paymentModeList={paymentModeList}
              isChooseAccount={isChooseAccount}
              showAccountMabel={this.showAccountMabel}
              transferAmount={transferAmount}
              bankName={selectCapitalAccountInfo.bankName}
              accountCode={selectCapitalAccountInfo.accountCode}
              paymentDate={this.state.date}
              changeDate={(val) => this.setState({ date: val })}
              pics={pics}
              setPics={this.setPics}
              disabledDivideRebate={disabledDivideRebate}
              disabledTransfer={disabledTransfer}
              changeInternalPaymentMethod={this.changeInternalPaymentMethod}
              onChangeTP={this.onChangeTP}
              tpTransferAmount={tpTransferAmount && String(tpTransferAmount).indexOf(".") > -1 && String(tpTransferAmount).split(".")[1].length > 2 ? Number(Number(tpTransferAmount).toFixed(2)) : tpTransferAmount}
              isShow={isShowChooseBankCardModal}
              goBack={this.goBack}
              chooseBank={this.chooseBank}
              singlePaymentModeList={this.$myMv.paymentModeList}
              showChooseBankCardModal={this.showChooseBankCardModal}
              selectedBankName={selectedBankName}
              selectedBankCardType={selectedBankCardType}
              selectedBankCardNo={selectedBankCardNo}
              adjustAmount={this.adjustAmount}
              changeAdjustAmount={this.changeAdjustAmount}
              showAmountModal={showAmountModal}
              hideAmountModal={this.hideAmountModal}
              changeTransferAmount={this.changeTransferAmount}
              confirmAdjustAmount={this.confirmAdjustAmount}
              skipToBindCard={this.saveData}
              surplusAmount={surplusAmount}
              mostAmount={mostTpAmount ? Number(mostTpAmount).toFixed(2) : 0}
              bankRedirectUrl={encodeURIComponent(`${document.location.protocol}//${document.domain}:${window.location.port}/${SITE_PATH}/submit/order-payment/34657/${this.props.match.params.orderId}/notPay?feeDocumentId=${feeDocumentId ? feeDocumentId : ""}&canPartialPay=${canPartialPay}&backSource=${getUrlParam("backSource")}&comeBack=true`)}
              tpRate={this.state.tpRate}
              freightPayInfo={freightPayInfo}
              showPaymentLine={this.showPaymentLine}
              showRate={showRate}
              showRateModal={this.showRateModal}
              hideRateModal={this.hideRateModal}
              selectRateList={selectRateList}
              selectedSubMerchant={selectedSubMerchant}
              subMerchantList={subMerchantList}
              showCreditModal={this.showCreditModal}
              validityTypeName={ValidityTypeName[validityType]}
              isShowMoreValidityType={isShowMoreValidityType}
              disabledCredit={disabledCredit}
              showCreditTips={this.showCreditTips}
            />
          }
          <ScrollFreezeTitleBar
            orderPaymentList={orderPaymentList}
            isHaveBankTranfer={(paymentModeList.filter((mode) => mode.code === $PaymentModeType.BANKTRANSFER).length > 0) && (paymentModeList.filter((mode) => mode.code === $PaymentModeType.ALLINPAY).length <= 0)}
            styled={{ marginBottom: "62px" }}
          />
        </Spin>
        <ConfirmButton>
          {
            record === "record" ?
              isRole && match.params.showPaymentBtn !== "notPay" ? unPayableAmount !== 0 ?
                match.params.showPaymentBtn === "true" ?
                  <Button type="primary" onClick={this.reload}>
                    继续付款
                  </Button> : null : null : null :
              <Button
                type="primary"
                disabled={!(Number(paymentAmount) > 0)}
                onClick={this.paymentCheck}
              >
                确认支付
                ￥{paymentAmount}
              </Button>
          }
        </ConfirmButton>
        <CapitalAccountListMobel
          isShowMabel={isShowMabel}
          capitalAccountList={capitalAccountList}
          setDefaultCheck={this.setDefaultCheck}
        />
        <Modal
          visible={isShowCreditModal}
          transparent={true}
          maskClosable={true}
          popup
          animationType="slide-up"
          onClose={() => this.showCreditModal(false)}
          title="信用支付模式"
        >
          <List style={{ paddingBottom: 30 }}>
            <CheckboxItem checked={validityType === $ValidityType.NEW_CREDIT} onClick={() => this.onChangeValidity($ValidityType.NEW_CREDIT)}>
              新模式信用
            </CheckboxItem>
            <CheckboxItem checked={validityType === $ValidityType.REGULAR_CREDIT} onClick={() => this.onChangeValidity($ValidityType.REGULAR_CREDIT)}>
              常规信用
            </CheckboxItem>
          </List>
        </Modal>
        {/* <Modal
          visible={showQuestionHelp}
          transparent={true}
          maskClosable={false}
          onClose={this.hideQuestionHelp}
          title="信用额度"
          footer={[{
            text: "我知道了",
            onPress: this.hideQuestionHelp
          }]}
        >
          <div style={{ height: "auto", overflow: "scroll", textAlign: "left" }}>
            {
              questionHelp.orderSchemeAmount ? <div>
                <span>{`￥${Number(questionHelp.orderSchemeAmount).toFixed(2)} 订货方案专享(不可使用)`}</span><br/>
              </div> : null
            }
            {
              questionHelp.salesOrderAmount ?
                <div><span>{`￥${Number(questionHelp.salesOrderAmount).toFixed(2)} 订单专享(不可使用)`}</span><br/></div> : null
            }
            <span>{questionHelp.usableAmount ? `本次可用额度为：￥${Number(questionHelp.usableAmount).toFixed(2)}` : `本次可用额度为：￥0.00`}</span>
          </div>
        </Modal> */}
        <ExcessIndication
          overtop={overTop}
          orderOrgList={orderOrgList}
          activekey={activekey}
          changeOrg={this.changeOrg}
          hideQuestionHelp={this.hideQuestionHelp}
          batchSave={this.confirmPayNewType}
        />
        <ActivityIndicator
          toast={true}
          text="Loading..."
          animating={this.state.animating}
        />
        {
          isShowPaymentLine &&
          <PaymentLine
            subMerchantList={subMerchantList}
            onChange={this.changePaymentLine}
            goBack={() => this.setState({isShowPaymentLine: false})}
          />
        }
        <Modal
          visible={isShowCreditTips}
          transparent={true}
          maskClosable={false}
          onClose={() => this.showCreditTips(false)}
          title="提示"
          footer={[{
            text: "我知道了",
            onPress: () => this.showCreditTips(false)
          }]}
        >
          <div style={{ height: "auto", overflow: "scroll", textAlign: "left" }}>
            您支付的订单中含有“欠货补订”订单不支持信用支付，请选择其他支付方式。
          </div>
        </Modal>
      </PaymentPage>
    );
  }

}

export default SubmitOrderPayment;

const PaymentPage = styled.div`// styled
  & {
    width: 100%;
    background: #F2F2F2;
    height: ${document.documentElement.clientHeight}px;
    overflow-y: scroll;
    .gray-bg {
      display: block;
      height: 10px;
      background: #f2f2f2;
    }

    .hide {
      display: none;
    }

    .bank .am-list-body {
      border: 0;
    }

    .bank .am-list-body .choose .am-list-line .am-list-content {
      padding-top: 20px;
      color: #333;
      font-size: 14px;
    }

    .padding-bottom {
      padding-bottom: 10px;
    }

    .ant-radio-wrapper {
      height: 23px;
      color: #333;
    }

    .am-list-line::after {
      height: 1px !important;
    }

    .am-list-body::before {
      height: 1px !important;
    }

    @media (min-resolution: 2dppx) {
      html:not([data-scale]) .am-list-body::after {
        display: none !important;
      }

      .am-list-body::after {
        display: none !important;
      }
    }
    .am-list-item.am-list-item-middle .am-list-line {
      border: none !important;
    }
    @media (min-resolution: 2dppx) {
      html:not([data-scale]) .am-list-item:not(:last-child) .am-list-line {
        border: none !important;
      }
    }
    @media (min-resolution: 2dppx) {
      html:not([data-scale]) .am-list-body::after {
        display: none !important;
      }

      .am-list-body::after {
        display: none !important;
      }
    }
    .date {
      position: relative;
      :after {
        content: '';
        position: absolute;
        background-color: #D8D8D8 !important;
        display: block;
        z-index: 1;
        top: auto;
        right: auto;
        bottom: 0;
        left: 0;
        width: 100%;
        height: 1.2px;
        transform-origin: 50% 100%;
        transform: scaleY(0.5);
      }
    }
  }
`;

const ConfirmButton = styled.div`// styled
  & {
    padding: 10px 15px;
    background: #fff;
    position: fixed;
    bottom: 0;
    width: 100%;
    z-index: 10;
    .am-button-primary.am-button-disabled {
      background: #d9d9d9 !important;
    }

    .am-button-primary {
      border-radius: 3px;
      background: #307dcd;
      height: 42px;
      line-height: 42px;
      font-size: 16px;
      font-family: "PingFangSC-Regular";
    }
  }
`;
