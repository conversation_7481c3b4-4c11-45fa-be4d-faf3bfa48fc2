import { autowired, bean } from "@classes/ioc/ioc";
import { $TransferHintService } from "@classes/service/$transfer-hint-service";
import { action, observable } from "mobx";
import { $OrderService } from "../../classes/service/$order-service";
import { $ComponentService } from "../../classes/service/$component-service";
import { $File } from "../../classes/entity/$file";
import { $Product } from "../../classes/entity/$product";
import { $QuestionHelp } from "../../classes/entity/$question-help";
import { Toast } from "antd-mobile";
import { $SinglePaymentModeList } from "../../classes/entity/$single-payment-mode-list";
import { $PaymentType } from "../../classes/const/$payment-type";
import { remove } from "lodash";

@bean($SubmitOrderPaymentMv)
export class $SubmitOrderPaymentMv {
  @autowired($OrderService)
  public $orderService: $OrderService;
  @autowired($ComponentService)
  public $ComponentService: $ComponentService;
  @autowired($TransferHintService)
  public $transferHintService: $TransferHintService;

  @observable public paymentInfo: any;

  @observable public pics: $File[] = [];

  @observable public paymentModeId: number;

  @observable public capitalAccountList: any[] = [];

  @observable public amountPayable: number = 0;

  @observable public isSpin: boolean = false;

  @observable public orderPartyId: number;

  @observable public questionHelp: $QuestionHelp;

  @observable public paymentModeList: $SinglePaymentModeList[] = [];

  @observable public orderPaymentList: any[] = [];

  @observable public refundDocumentsList: any[] = [];

  @observable public surplusAmount: number = 0;  // 剩余待支付金额

  @observable public mostTpAmount: number = 0;  // 通联支付最多支付金额

  @observable public freightPayInfo: any;  // 运费详情

  @observable public createdTransferList: any[] = []; // 已生成转账单

  @action
  public showSpin() {
    this.isSpin = true;
  }

  @action
  public hideSpin() {
    this.isSpin = false;
  }

  @action
  public fetchPaymentInfo(params) {
    return this.$orderService.queryPaymentInfo(params);
  }

  @action
  public setPics(pics: any[]) {
    this.pics = pics;
  }

  @action
  public fetchCapitalAccount(params) {
    return this.$orderService.queryCapitalAccount(params).then((data) => {
      const { capitalAccountList } = data;
      this.capitalAccountList = capitalAccountList.map((capitalAccount) => new $Product(capitalAccount));
      return data;
    });
  }

  @action
  public saveOrder(params) {
    return this.$ComponentService.saveOrderPay(params);
  }

  @action
  public setIsShow(index) {
    this.paymentInfo.paymentModeList.map((v) => {
      v.isShow = false;
    });
    this.paymentInfo.paymentModeList[index].isShow = true;
  }

  @action
  public fetchQuestionHelp(params) {
    this.$orderService.queryQuestionHelp(params).then((data) => {
      this.questionHelp = data;
    }).catch((err) => {
      console.log(err);
    });
  }
  @action
  public checkTongLianPaymentStatus(params) {
    return this.$ComponentService.checkTongLianPaymentStatus(params);
  }
  @action
  public checkContinuePay(params) {
    return this.$ComponentService.checkContinuePay(params);
  }
  @action
  public cancelTongLianPay(params) {
    return this.$ComponentService.cancelTongLianPay(params);
  }
  @action
  public loadTransferRecordList = (params) => {
    return this.$transferHintService.transferRecordList(params).then((res) => {
      this.createdTransferList = res.transferList;
    });
  }
}
