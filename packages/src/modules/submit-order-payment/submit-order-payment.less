.pay-model-wrap {
  width: 100%;
  margin-top: 10px;
  background: rgba(255, 255, 255, 1);

  .pay-model-list {
    color: rgba(51, 51, 51, 1);
    height: auto;
    width: 100%;
  }

  .cut-off-rule {
    width: 100%;
    height: 1px;
    background-color: rgba(216, 216, 216, 1);
  }

  .pay-model-bill-detail {
    height: 125px;
  }
}

.pay-model-list {
  .am-list-header {
    height: 40px;
    padding: 0 16px;
    font-size: 13px;
    line-height: 40px;
  }

  .am-list-body {
    width: 100%;
    height: auto;
  }
}

.pay-model-item {
  font-size: 12px;
  width: 100%;
  height: 52px;
  overflow: hidden;

  i {
    font-size: 14px;
    padding-right: 8px;
  }
  .scm-icon-caiwu {
    color: #FF8627;
  }
  .scm-icon-xinyongqia {
    color: #1890FF;
  }
  .scm-icon-zhuanzhang {
    color: #52C41A;
  }
  .scm-icon-fanli {
    color: #FF3030;
  }
  .am-list-thumb {
    width: 18px !important;
    height: 18px !important;
    margin-right: 9px !important;
    .am-checkbox-wrapper {
      .am-checkbox {
        .am-checkbox-inner {
          width: 18px;
          height: 18px;
          left: 16px;
          top: 14px;
        }
      }
    }
  }

  .am-list-line {
    overflow: hidden;
    border-bottom: none !important;

    .am-list-content {
      font-size: 12px;
      font-weight: 400;
      color: rgba(51, 51, 51, 1);
      padding-top: 10px;
      padding-bottom: 0px;

      .am-list-brief {
        line-height: 11px;
        margin-top: 3px;
        padding-left: 22px;
      }
    }
  }

  .am-list-line::after {
    width: 0 !important;

  }
}

.pay-model-offLine {
  height: 50px;

  .am-list-line {
    overflow: hidden;

    .am-list-content {
      padding-top: 0px;
      margin-bottom: 2px;
    }
  }
}

.rebate-info-warp,.balance-info-warp, .credit-info-warp, .offLine-info-warp {
  span {
    color: rgba(51, 51, 51, 1);
    font-size: 11px;
  }

  span:first-of-type {
    margin-right: 25px;
  }

  .default-payment-acount{
    color: #FF3030;
    margin: 0;
  }
}

.no-rebate,.no-balance, .no-credit {
  font-size: 11px;
  color: rgba(255, 48, 48, 1);
}
