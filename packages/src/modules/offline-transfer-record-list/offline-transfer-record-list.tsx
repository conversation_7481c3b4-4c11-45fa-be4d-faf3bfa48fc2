import { autowired } from "@classes/ioc/ioc";
import { Spin } from "antd";
import { Modal } from "antd-mobile";
import { observer } from "mobx-react";
import * as React from "react";
import { withRouter } from "react-router";
import styled from "styled-components";
import { NoGoods } from "../../components/no-goods/no-goods";
import { SITE_PATH } from "../app";
import { $OfflineTransferRecordListMv } from "./offline-transfer-record-list-mv";
import { GoHome } from "../../components/go-home/go-home";
import { LoadingTip } from "../../components/loading-marked-words";
import { SingleOfflineTransferRecordInfo } from "../../components/single-offline-transfer-record-info";
import { ScrollAbilityWrapComponent } from "../../components/scroll-ability/wrap";
import { gaEvent } from "../../classes/website-statistics/website-statistics";
import { $AppStore, AppStoreKey } from "../../classes/stores/app-store-mv";

const alert = Modal.alert;
declare let window: any;

@withRouter
@observer
class OfflineTransferRecordListWrap extends React.Component<any, any> {

  @autowired($OfflineTransferRecordListMv)
  public $offlineTransferRecordListMv: $OfflineTransferRecordListMv;

  @autowired($AppStore)
  public $appStore: $AppStore;

  constructor(props) {
    super(props);
    this.state = ({
      isFinished: false,
      isLoading: false,
      key: "ALL",
      pageIndex: 0,
      pageSize: 10,
    });
  }

  public componentDidMount() {
    document.title = "转账记录列表";
    gaEvent("转账记录列表");
    let offlineTransferRecordStore = this.$appStore.getPageMv(AppStoreKey.OFFLINETRANSFERRECORDLIST);
    offlineTransferRecordStore = offlineTransferRecordStore && JSON.parse(offlineTransferRecordStore);
    if (offlineTransferRecordStore && offlineTransferRecordStore.nextPage === AppStoreKey.OFFLINETRANSFERRECORDLIST) {
      this.$offlineTransferRecordListMv.offlineTransferRecordListInfo = offlineTransferRecordStore.offlineTransferRecordListInfo && JSON.parse(offlineTransferRecordStore.offlineTransferRecordListInfo);
      this.setState({
        pageIndex: Number(offlineTransferRecordStore.pageIndex) + 1,
        key: offlineTransferRecordStore.key,
      }, () => {
        setTimeout(() => {
          document.getElementsByClassName("scroll-ability-wrap")[0].scrollTo(0, Math.abs(Number(offlineTransferRecordStore.scrollTop)));
        }, 100);
      });
    } else {
      this.$offlineTransferRecordListMv.offlineTransferRecordListInfo = [];
      this.getOfflineTransferRecordList("ALL");
    }
  }

  public componentWillReceiveProps(nextProps: Readonly<any>, nextContext: any): void {
    const { isScrollEnd } = nextProps;
    console.log(isScrollEnd);
    if (isScrollEnd && isScrollEnd !== this.props.isScrollEnd) {
      this.loadData();
    }
  }

  public loadData = () => {
    const { isFinished } = this.state;
    if (!isFinished) {
      const { key } = this.state;
      this.getOfflineTransferRecordList(key);
    }
  }

  public getOfflineTransferRecordList = (type) => {
    const { pageIndex, pageSize } = this.state;
    if (pageIndex === 0) {
      this.$offlineTransferRecordListMv.clearOfflineTransferRecordList();
    }
    const params = {
      docStatus: type,
      pageIndex,
      pageSize,
    };
    this.setState({ isLoading: true });
    const { loadingEnd } = this.props;
    this.$offlineTransferRecordListMv.getOfflineTransferRecordList(params).then((res) => {
      // console.log(res);
      const { transferRecordList, itemCount } = res;
      if (transferRecordList.length > 0) {
        this.$offlineTransferRecordListMv.changeOfflineTransferRecordList(transferRecordList).then((length) => {
          this.setState({
            isFinished: length >= itemCount,
            pageIndex: pageIndex + 1,
          });
          loadingEnd && loadingEnd(length >= itemCount);
        });
      }
    }).catch((error) => {
      console.log("error", error);
    }).then(() => {
      this.setState({ isLoading: false });
    });
  }

  public goToOrderDetail = (oid, docType) => {
    console.log("进入线下转账详情");
    const offlineTransferRecordStore = {
      offlineTransferRecordListInfo: JSON.stringify(this.$offlineTransferRecordListMv.offlineTransferRecordListInfo),
      pageIndex: this.state.pageIndex,
      key: this.state.key,
      scrollTop: $(".offlineTransferRecord").offset().top,
      nextPage: AppStoreKey.OFFLINETRANSFERRECORDLIST,
    };
    this.$appStore.savePageMv(AppStoreKey.OFFLINETRANSFERRECORDLIST, offlineTransferRecordStore);
    this.props.history.push({
      pathname: `/${SITE_PATH}/offline-transfer-record-detail/${oid}/${docType}`,
    });
  }

  public withdraw = (oid, docType) => {
    const params = { oid, docType };
    this.$offlineTransferRecordListMv.withdraw(params).then((res) => {
      console.log(res);
      if (res.errorCode === "0") {
        console.log("撤销成功");
        this.setState({ pageIndex: 0 }, () => {
          const { key } = this.state;
          this.getOfflineTransferRecordList(key);
        });
      }
    });
  }

  public changeOfflineTransferStatus = (type) => {
    document.getElementsByClassName("offlineTransferRecord")[0].style.position = "fixed";
    setTimeout(() => {
      document.getElementsByClassName("offlineTransferRecord")[0].style.position = "relative";

    }, 10);
    this.setState({ pageIndex: 0, key: type }, () => {
      this.getOfflineTransferRecordList(type);
    });
  }

  public render() {
    const { offlineTransferRecordListInfo, offlineTransferRecordStatus } = this.$offlineTransferRecordListMv;
    const { isLoading, isFinished, key } = this.state;
    return (
      <OrderPage className="offlineTransferRecord">
        <SearchTypeBar>
          {
            offlineTransferRecordStatus.map((status, index) => {
              return (
                <div key={index} onClick={() => {
                  this.changeOfflineTransferStatus(status.value);
                }}>
                  <span className={status.value === key ? "active" : null}>
                    {status.name}
                  </span>
                </div>
              );
            })
          }
        </SearchTypeBar>
        <Spin spinning={isLoading}>
          <RecordLists>
            {
              offlineTransferRecordListInfo.length > 0 ?
                offlineTransferRecordListInfo.map((record, index) => {
                  return <div key={index}>
                    <SingleOfflineTransferRecordInfo
                      record={record}
                      goToOrderDetail={this.goToOrderDetail}
                      withdraw={this.withdraw}
                    />
                    <MarginBottom/>
                  </div>;
                })
                : <NoGoods title="暂无订单" height={document.documentElement.clientHeight - 50}/>
            }
            {
              offlineTransferRecordListInfo.length > 0 ?
                <LoadingTip
                  isFinished={isFinished}
                  isLoad={isLoading}
                /> : null
            }
          </RecordLists>
        </Spin>
        <GoHome/>
      </OrderPage>
    );
  }
}

const OfflineTransferRecordList = ScrollAbilityWrapComponent(OfflineTransferRecordListWrap);
export default OfflineTransferRecordList;

const OrderPage = styled.div`// styled
  & {
    width: 100%;
    height: auto;
    min-height: calc(${document.documentElement.clientHeight}px);
    background-color: #f5f5f5;
    overflow: hidden;
    color: #2a2a2a;
    > .orderPage span {
      text-align: center;
    }
  }
`;

const MarginBottom = styled.div`// styled
  & {
    width: 100%;
    height: 10px;
    background: #f5f5f5;
  }
`;
const SearchTypeBar = styled.div`// styled
  & {
    width: 100%;
    height: 40px;
    line-height: 20px;
    background-color: #fff;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    position: fixed;
    color: #8a8a8a;
    z-index: 99;
    padding: 10px 16px;
    box-sizing: border-box;
    > div {
      height: 30px;
      width: auto;
      > span {
        display: inline-block;
        height: 30px;
        font-size: 14px;
        color: #666;
      }
      > .active {
        color: #307DCD;
        font-size: 14px;
        border-bottom: 2px solid #307DCD;
      }
    }
  }
`;

const RecordLists = styled.div`// styled
  & {
    width: 100%;
    padding-top: 50px;
  }
`;
