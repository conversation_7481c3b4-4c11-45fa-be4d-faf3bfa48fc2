import { autowired, bean } from "@classes/ioc/ioc";
import { action, observable } from "mobx";
import { $OfflineTransferRecordService } from "../../classes/service/$offline-transfer-record-service";
// import { $SelectOrderparty } from "../../classes/entity/$select-orderparty";

@bean($OfflineTransferRecordListMv)
export class $OfflineTransferRecordListMv {
  @autowired($OfflineTransferRecordService)
  public $offlineTransferRecordService: $OfflineTransferRecordService;

  @observable public offlineTransferRecordStatus = [
    {
      activeKey: "ALL",
      name: "全部",
      value: "ALL",
    },
    {
      activeKey: "S",
      name: "待审核",
      value: "S",
    },
    {
      activeKey: "A",
      name: "已确认收款",
      value: "A",
    },
    {
      activeKey: "C",
      name: "已作废",
      value: "C",
    },
  ];

  @observable public offlineTransferRecordListInfo = [];
  //
  // @action
  // public setOrderDetail(obj) {
  //   this.orderDetail = obj;
  // }
  @action
  public getOfflineTransferRecordList(params) {
    return this.$offlineTransferRecordService.getOfflineTransferRecordList(params);
  }
  @action
  public withdraw(params) {
    return this.$offlineTransferRecordService.withdraw(params);
  }
  @action
  public changeOfflineTransferRecordList(list) {
    return new Promise((resolve) => {
      this.offlineTransferRecordListInfo = this.offlineTransferRecordListInfo.concat(list);
      resolve(this.offlineTransferRecordListInfo.length);
    });
  }
  @action
  public clearOfflineTransferRecordList() {
    this.offlineTransferRecordListInfo = [];
  }
  @action
  public setOfflineTransferRecordList(list) {
    this.offlineTransferRecordListInfo = list;
  }
}
