import {autowired, bean} from "@classes/ioc/ioc";
import {action, observable, extendObservable} from "mobx";
import {$MyInfoService} from "../../classes/service/$my-info-service";

@bean($CouponDetailMv)
export class $CouponDetailMv {
  @autowired($MyInfoService)
  public $myInfoService: $MyInfoService;

  @observable public paymentModeList: any[];
  @observable public paymentModeId: number;
  @observable public balanceAmount: number;
  @observable public couponDetail: object = {};
  @observable public isSpin: boolean = false;
  @observable public newCouponDetail: any[] = [];
  @action
  public showSpin() {
    this.isSpin = true;
  }

  @action
  public hideSpin() {
    this.isSpin = false;
  }
  @action
  public fetchRepayment(params) {
    this.$myInfoService.loadRechargePage(params).then((data) => {
      this.paymentModeId = data.paymentModeList[0].oid;
      data.paymentModeList.map((v, index) => {
        if (v.code !== "wechat") {
          this.balanceAmount = v.balanceAmount;
        }
        extendObservable(v, {
          isShow: index === 0 ? true : false,
        });
      });
      this.paymentModeList = data.paymentModeList;
    });
  }

  @action
  public submitRepay(params) {
    return this.$myInfoService.toRecharge(params);
  }

  @action
  public setIsShow(index) {
    this.paymentModeList.map((v) => {
      v.isShow = false;
    });
    this.paymentModeList[index].isShow = true;
  }

  @action
  public queryCouponDetail(params) {
    this.$myInfoService.queryCouponDetail(params).then((data) => {
      this.couponDetail = data;
      this.newCouponDetail = JSON.stringify(this.couponDetail.content).replace('"', "").replace('"', "").split("\\n");
      this.hideSpin();
    });
  }
}
