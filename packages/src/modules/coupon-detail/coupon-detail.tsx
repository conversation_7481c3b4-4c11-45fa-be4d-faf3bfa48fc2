import { autowired } from "@classes/ioc/ioc";
import { observer } from "mobx-react";
import { withRouter } from "react-router";
import * as React from "react";
import { $CouponDetailMv } from "./coupon-detail-mv";
import styled from "styled-components";
import { Button, Checkbox, List } from "antd-mobile";
import { SITE_PATH } from "../app";
import { Spin } from "antd";
import { $AppStore, AppStoreKey } from "../../classes/stores/app-store-mv";

const Item = List.Item;
const CheckboxItem = Checkbox.CheckboxItem;

@withRouter
@observer
class CouponDetail extends React.Component<any, any> {
  @autowired($AppStore)
  public $AppStore: $AppStore;
  @autowired($CouponDetailMv)
  public $mv: $CouponDetailMv;

  constructor(props) {
    super(props);
    this.state = {};
  }

  public componentDidMount() {
    document.title = "优惠卷详情";
    this.loadCouponDetail();
  }

  public loadCouponDetail = () => {
    const { couponId } = this.props.history.location.state;
    this.$mv.showSpin();
    this.$mv.queryCouponDetail({ couponId });
  }

  public onChange = (val) => {
  }

  public goToShopList = () => {
    this.$AppStore.queryShopOverdue("", () => {
      this.$AppStore.clearPageMv(AppStoreKey.SHOPLIST);
      this.props.history.push({ pathname: `/${SITE_PATH}/shop/list` });
    });
  }

  public render() {
    const { couponDetail, isSpin, newCouponDetail } = this.$mv;
    /*const newCouponDetail = couponDetail ? couponDetail.content ? couponDetail.content.split("↵") : [] : [];*/
    return (
      <CouponDetailWrapper>
        <Spin spinning={isSpin}>
          <div className="coupon-info">
            <div className="info-tag">{couponDetail ? couponDetail.tag : null}</div>
            <div className="info-header">
              <p>{couponDetail ? couponDetail.promotionTagLabel : null}</p>
              <p>{couponDetail ? couponDetail.condition : null}</p>
            </div>
            <div className="info-detail">
              <ul>
                {
                  newCouponDetail.map((newCoupon, index) => {
                    return (
                      <li key={index}>{newCoupon}</li>
                    );
                  })
                }
              </ul>
            </div>
          </div>
        </Spin>
        <ConfirmButton>
          <Button type="primary" onClick={this.goToShopList}>立即使用</Button>
        </ConfirmButton>
      </CouponDetailWrapper>
    );
  }
}

export default CouponDetail;

const CouponDetailWrapper = styled.div`
  &{
    width:100%;
    min-height:${document.documentElement.clientHeight}px;
    background: #ECF6FF;
    padding:15px 15px 0;
    overflow: auto;
    .coupon-info{
      height:${document.documentElement.clientHeight - 45 - 42}px;
      width:100%;
      background:url("https://order.fwh1988.cn:14501/static-img/scm/ico-voucher-bg-b.png");
      background-size:cover;
      overflow:hidden;
      position:relative;
      .info-tag{
        position:absolute;
        right:0;
        top:16px;
        background: #FF7362;
        border-radius: 10px 0 0 10px;
        width:54px;
        height:20px;
        color:white;
        text-align:center;
      }
      .info-header{
        color:#307DCD;
        text-align:center;
        margin-top:10%;
        height:20%;
        p{
          margin:0;
          font-size:12px;
          &:first-child{
            font-size:30px;
          }
        }
      }
      .info-detail{
        height:400px;
      }
      ul{
        margin-top:5%;
        padding-left:45px;
        padding-right:15px;
        color:#666666;
        font-size:14px;
        height:400px;
        overflow:auto;
        padding-bottom: 60px;
        li{
          margin-bottom:10px;
          list-style:none;
          position:relative;
          &:before{
            position:absolute;
            left:-17px;
            top:7px;
            content:"";
            display:block;
            width:5px;
            height:5px;
            border-radius:50%;
            background:#307DCD;
          }
        }
      }
    }
  }
`;

const ConfirmButton = styled.div`
  &{
    padding: 0 15px;
    left:0;
    width:100%;
    position:fixed;
    bottom: 15px;
  }
`;

const CouponNav = styled.div`// styled
  & {
    height: 40px;
    line-height: 40px;
    background: white;
    width: 100%;
    position: fixed;
    top: 0;
    left: 0;
    z-index: 10;
    display: flex;
    div {
      color: #666666;
      font-size: 14px;
      text-align: center;
      flex: 1;
      span {
        padding: 8px;
        border-bottom: 2px solid white;
        &.active {
          border-bottom: 2px solid #307DCD;
          color: #307DCD;
        }
      }
    }
  }
`;
