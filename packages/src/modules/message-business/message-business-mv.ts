import { autowired, bean } from "@classes/ioc/ioc";
import { action, observable } from "mobx";
import { $MyInfoService } from "../../classes/service/$my-info-service";
import { $OrderPartyService } from "../../classes/service/$order-party-service";
import { $MessageBusiness } from "../../classes/entity/$message-business";
import { beanMapper } from "../../helpers/bean-helpers";

@bean($MessageBusinessMv)
export class $MessageBusinessMv {

  @autowired($MyInfoService)
  public $myInfoService: $MyInfoService;

  @autowired($OrderPartyService)
  public $orderPartyService: $OrderPartyService;

  @observable public messageList: $MessageBusiness[] = [];

  @observable public unReadCount: number;

  @observable public isSpin: boolean = false;

  @observable public pageIndex: number = 0;
  @observable public pageSize: number = 10;
  @observable public scrollHeight: number = 0;

  @observable public isShow: boolean = false;

  @observable public finished: boolean = false;

  @observable public dataLength: number;

  @action
  public showSpin() {
    this.isSpin = true;
  }

  @action
  public hideSpin() {
    this.isSpin = false;
  }

  @action
  public showMore() {
    this.isShow = true;
  }

  @action
  public clearPageIndex() {
    this.pageIndex = 0;
  }

  @action
  public hideMore() {
    this.isShow = false;
  }

  @action
  public showFinished() {
    this.finished = true;
  }

  @action
  public hideFinished() {
    this.finished = false;
  }

  @action
  public changePageIndex() {
    this.pageIndex = this.pageIndex + 1;
  }

  @action
  public fetchMessageBusinessList(params) {
    return this.$myInfoService.queryMessageBusinessList(params);
  }

  @action
  public messageRead(params) {
    return this.$myInfoService.messageRead(params);
  }
  @action
  public saveShopAndScheme(params) {
    return this.$orderPartyService.saveShopAndScheme(params);
  }
  @action
  public queryOldData(obj) {
    beanMapper(obj, this);
  }
  @action
  public clearMVData() {
   this.messageList = [];

   this.unReadCount = 0;

   this.isSpin = false;

   this.pageIndex = 0;
   this.pageSize = 10;
   this.scrollHeight = 0;

   this.isShow = false;

   this.finished = false;

   this.dataLength = 0;
  }
}
