import { autowired } from "@classes/ioc/ioc";
import { Spin } from "antd";
import { List, Toast } from "antd-mobile";
import { observer } from "mobx-react";
import React from "react";
import styled from "styled-components";
import { $MessageBusinessMv } from "./message-business-mv";
import { $MessageType } from "../../classes/const/$message-type";
import { SITE_PATH } from "../app";
import { NoGoods } from "../../components/no-goods/no-goods";
import { withRouter } from "react-router";
import { ScrollAbilityWrapComponent } from "../../components/scroll-ability/wrap";
import { LoadingTip } from "../../components/loading-marked-words";
import { $AppStore, AppStoreKey } from "../../classes/stores/app-store-mv";
import { $MessageBusiness } from "../../classes/entity/$message-business";
import { $CartType } from "@classes/const/$cart-type";
import { $OrderType } from "@classes/const/$order-type";

declare let window: any;
const Item = List.Item;

@withRouter
@observer
class MessageBusinessWrap extends React.Component<any, any> {
  @autowired($AppStore)
  public $AppStore: $AppStore;
  @autowired($MessageBusinessMv)
  public $myMv: $MessageBusinessMv;

  constructor(props) {
    super(props);
    this.state = {
    };
  }
  // 离开记录滚动高度
  public saveMV = () => {
    this.$myMv.scrollHeight = $(".scroll-ability-wrap").scrollTop();
    this.$AppStore.savePageMv(AppStoreKey.MESSAGELIST, this.$myMv);
  }
  public componentWillUnmount(): void {
    this.saveMV();
  }
  public componentDidMount() {
    document.title = "消息列表";
    this.$myMv.clearMVData();
    this.initPage();
  }
  public initPage = () => {
    this.loadMessageBusinessList();
  }
  public componentWillReceiveProps(nextProps: Readonly<any>, nextContext: any): void {
    const { isScrollEnd } = nextProps;
    console.log(isScrollEnd);
    if (isScrollEnd && isScrollEnd !== this.props.isScrollEnd) {
      this.loadData();
    }
  }
  public loadMessageBusinessList = () => {
    const { pageSize, pageIndex } = this.$myMv;
    const params = { pageIndex, pageSize };
    const { loadingEnd } = this.props;
    this.$myMv.showSpin();
    this.$myMv.fetchMessageBusinessList(params).then((data) => {
      this.$myMv.hideMore();
      this.$myMv.hideSpin();
      const { messageList, unReadCount, total } = data;
      const oldData = this.$AppStore.getPageMv(AppStoreKey.MESSAGELIST)
      if (oldData) {
        this.$myMv.queryOldData(JSON.parse(oldData));
        this.$AppStore.clearPageMv(AppStoreKey.MESSAGELIST);
        $(".scroll-ability-wrap").scrollTop(this.$myMv.scrollHeight)
        if (messageList) {
          this.$myMv.unReadCount = unReadCount;
        }
      } else {
        if (messageList) {
          this.$myMv.unReadCount = unReadCount;
          this.$myMv.dataLength = messageList.length;
          this.$myMv.messageList = this.$myMv.messageList.concat(messageList.map((lis) => new $MessageBusiness(lis)));
        }
        if (this.$myMv.messageList.length >= total) {
          this.$myMv.showFinished();
        }
        this.$myMv.pageIndex = pageIndex + 1;
        loadingEnd && loadingEnd();
      }
    });
  }

  public loadData = () => {
    const { finished } = this.$myMv
    if (finished) {
      return;
    }
    this.$myMv.showMore();
    this.loadMessageBusinessList();
  }

  public goToDetail = (linkTo, orderId, accountType, accountId, orderSchemeName, msgId, orderPartyId, docId, docType, accountFlowId) => {
    const linkToList = [$MessageType.ORDERDETAIL, $MessageType.ACCOUNTDETAIL, $MessageType.ORDERDELIVERYLIST, $MessageType.ORDERPAYMENTLIST, $MessageType.OFFLINETRANSFERRECORDDETAIL, $MessageType.ORDERPAYMENTDETAIL, $MessageType.ACCOUNTFLOWDETAIL, $MessageType.REFUNDORDERDETAIL, $MessageType.RECEIVEORDERDETAIL, $MessageType.FEEDOCUMENTLIST, $MessageType.ACCOUNTINFOLIST];
    if (linkToList.indexOf(linkTo) >= 0) {
      this.$myMv.messageRead({ msgId }).then((data) => {
        if (data.result) {
          switch (linkTo) {
            case $MessageType.ORDERDETAIL:
              this.props.history.push({
                pathname: `/${SITE_PATH}/shop/order-detail/${orderId}/${orderSchemeName}`,
              });
              break;
            case $MessageType.FEEDOCUMENTLIST:
              this.props.history.push({
                pathname: `/${SITE_PATH}/expense-node-list/Payment/${orderPartyId}`,
              });
              break;
            case $MessageType.ACCOUNTDETAIL:
              if (orderPartyId) {
                this.$myMv.saveShopAndScheme({ orderPartyId }).then((res) => {
                  const message = res.errorMessage;
                  if (message) {
                    Toast.info(message);
                  } else {
                    this.$AppStore.clearPageMv(AppStoreKey.ACCOUNTINFO);
                    this.props.history.push({ pathname: `/${SITE_PATH}/account-info/${accountId}/${accountType}`});
                  }
                });
              }
              break;
            case $MessageType.ACCOUNTINFOLIST:
              if (orderPartyId) {
                this.$myMv.saveShopAndScheme({ orderPartyId }).then((res) => {
                  const message = res.errorMessage;
                  if (message) {
                    Toast.info(message);
                  } else {
                    this.$AppStore.clearPageMv(AppStoreKey.ACCOUNTINFOLIST);
                    this.props.history.push({ pathname: `/${SITE_PATH}/account-info-list/${accountId}/${accountType}`});
                  }
                });
              }
              break;
            case $MessageType.ACCOUNTFLOWDETAIL:
              if (orderPartyId) {
                this.$myMv.saveShopAndScheme({ orderPartyId }).then((res) => {
                  const message = res.errorMessage;
                  if (message) {
                    Toast.info(message);
                  } else {
                    this.props.history.push({ pathname: `/${SITE_PATH}/account-info-detail/${accountFlowId}`});
                  }
                });
              }
              break;
            case $MessageType.ORDERDELIVERYLIST:
              this.props.history.push({ pathname: `/${SITE_PATH}/delivery-order/${orderId}`});
              break;
            case $MessageType.ORDERPAYMENTLIST:
              this.saveMV();
              this.$AppStore.clearPageMv(AppStoreKey.SINGLEPAYMENTSTORAGE);
              setTimeout(() => {
                window.location.href = `/${SITE_PATH}/submit/order-payment/record/${orderId}/false`;
              });
              break;
            case $MessageType.OFFLINETRANSFERRECORDDETAIL:
              this.props.history.push({ pathname: `/${SITE_PATH}/offline-transfer-record-detail/${docId}/${docType}`});
              break;
            case $MessageType.ORDERPAYMENTDETAIL:
              // this.props.history.push({ pathname: `/${SITE_PATH}/payment-information-list/${orderId}?orderType=${$OrderType.SALESORDER}`});
              window.location.href = `/${SITE_PATH}/payment-information-list/${orderId}?orderType=${$OrderType.SALESORDER}`;
              break;
            case $MessageType.REFUNDORDERDETAIL:
              this.props.history.push({ pathname: `/${SITE_PATH}/returned-detail/${orderId}`});
              break;
            case $MessageType.RECEIVEORDERDETAIL:
              this.props.history.push({ pathname: `/${SITE_PATH}/receive-order-detail/${orderId}`});
              break;
            default:
              break;
          }
        }
      });
    }
  }

  public renderMessageContent = (messageList) => {
    console.log("新的代码提交", messageList);
    return (
      messageList.length > 0 ? messageList.map((msg, index) => {
        return (
          <MessageContent key={index}
                          onClick={() => this.goToDetail(msg.linkTo, msg.orderId,  msg.accountType, msg.accountId, msg.orderSchemeName, msg.id, msg.orderPartyId, msg.docId, msg.docType, msg.accountFlowId)}>
            <MessageContentHeader>
              <div className="message-content-header-left">
                <span className={msg.isRead ? "" : "active"}/>
                <span>{msg.title}</span>
              </div>
              <span>{msg.receiveTime}</span>
            </MessageContentHeader>
            <MessageContentInfo>
              <div className="text">{msg.subTitle}</div>
              <i className="scmIconfont scm-icon-jiantou-you"/>
            </MessageContentInfo>
          </MessageContent>
        );
      }) : <NoGoods title="暂无消息" height={document.documentElement.clientHeight / 2}/>
    );
  }

  public messageRead = () => {
    this.$myMv.messageRead({ msgId: null }).then((data) => {
      if (data.result) {
        Toast.info("主人，消息已全部读取完成", 3);
        window.location.reload();
      }
    });
  }

  public render() {
    const { messageList, dataLength, isSpin, unReadCount, isShow, finished } = this.$myMv;
    return (
      <MessageBusinessPage>
        {
          messageList && messageList.length > 0 &&
            <MessageHeader>
              <span>未读消息</span>
              <span>{unReadCount}</span>
              <span onClick={this.messageRead}>一键已读</span>
            </MessageHeader>
        }
        <Spin spinning={isSpin}>
          <MessageContents className="messageContents">
            {
              messageList ? this.renderMessageContent(messageList) : null
            }
            {
              messageList && messageList.length > 0 ?
                <LoadingTip
                  isFinished={finished}
                  isLoad={isShow}
                />
                :
                null
            }
          </MessageContents>
        </Spin>
      </MessageBusinessPage>
    );
  }
}

const MessageBusiness = ScrollAbilityWrapComponent(MessageBusinessWrap);
export default MessageBusiness;

const MessageBusinessPage = styled.div`// styled
  & {
    width: 100%;
    min-height: calc(${document.documentElement.clientHeight}px);
    height: auto;
    background: #ECF6FF;
    .am-list .am-list-body::before {
      height: 0;
    }
    .am-list .am-list-body::after {
      height: 0;
    }
    .am-list-item .am-list-line {
      position: relative;
      padding-right: 0;
    }
    .am-list-content {
      height: 40px;
    }
    .am-list-item {
      padding-left: 30px;
    }
    .am-list-item .am-list-line .am-list-content {
      width: 80%;
      height: 40px;
      font-size: 12px;
      color: #999999;
      white-space: normal;
    }
  }
`;

const MessageHeader = styled.div`// styled
  & {
    width: 100%;
    height: 40px;
    position: fixed;
    //margin-bottom: 50px;
    background: #fff;
    border-bottom: 1px solid #d8d8d8;
    z-index: 99;
    > span:nth-of-type(1) {
      font-size: 14px;
      color: #666666;
      margin-right: 5px;
      position: absolute;
      top: 11px;
      left: 16px;
    }
    > span:nth-of-type(2) {
      font-size: 14px;
      color: #307DCD;
      position: absolute;
      top: 11px;
      left: 78px;
    }
    > span:nth-of-type(3) {
      display: inline-block;
      width: 72px;
      height: 30px;
      line-height: 30px;
      box-sizing: border-box;
      border: 1px solid #307DCD;
      border-radius: 3px;
      color: #307dcd;
      font-family: PingFangSC-Regular;
      text-align: center;
      position: absolute;
      top: 5px;
      right: 16px;
    }
  }
`;

const MessageContents = styled.div`// styled
  & {
    width: 100%;
    height: auto;
    padding: 10px 15px;
    margin-top: 40px;
  }
`;

const MessageContent = styled.div`// styled
  & {
    width: 100%;
    height: 110px;
    padding: 10px 15px;
    margin-bottom: 10px;
    background: #FFFFFF;
    border-radius: 5px;
  }
`;
const MessageContentInfo = styled.div`// styled
  & {
    width: 100%;
    height: 40px;
    background: #FFFFFF;
    border-radius: 5px;
    overflow: hidden;
    position: relative;
    color: rgb(153, 153, 153);
    > .text {
      height: 100%;
      width: 100%;
      padding: 4px 23px 0px 30px;
      font-size: 12px;
      white-space: normal;
      line-height: 18px;
    }
    > i {
      position: absolute;
      top: 50%;
      right: 0;
      transform: translateY(-50%);
      width: 15px;
      height: 15px;
    }
  }
`;

const MessageContentHeader = styled.div`// styled
  & {
    width: 100%;
    height: 38px;
    line-height: 38px;
    padding: 0px 10px;
    border-bottom: 1px solid #d8d8d8;
    display: flex;
    .message-content-header-left {
      flex: 1;
      text-overflow: ellipsis;
      overflow: hidden;
      white-space: nowrap;
      > span:nth-of-type(1) {
        display: inline-block;
        width: 10px;
        height: 10px;
        border-radius: 10px;
        background: #D8D8D8;
        margin-right: 10px;
      }
      > span.active {
        background: #FF3030;
      }
      > span:nth-of-type(2) {
        font-size: 14px;
        color: #333333;
      }
    }
    > span:nth-of-type(1) {
      font-size: 12px;
      color: #999999;
      float: right;
      //width: 115px;
    }
  }
`;
