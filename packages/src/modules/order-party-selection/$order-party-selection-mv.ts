import { autowired, bean } from "@classes/ioc/ioc";
import { action, observable } from "mobx";
import { $OrderParty } from "../../classes/entity/client/client";
import { $OrderPartyService } from "../../classes/service/$order-party-service";
import { $OrderSchemeList } from "../../classes/entity/orderSchemeList";
import { beanMapper } from "../../helpers/bean-helpers";
import { $AppStore, AppStoreKey } from "../../classes/stores/app-store-mv";
import { Modal} from 'antd-mobile';
import { SITE_PATH } from "../app";
const alert = Modal.alert;

@bean($OrderPartySelectionMV)
export class $OrderPartySelectionMV {
  @autowired($OrderPartyService)
  public $orderPartyService: $OrderPartyService;
  @autowired($AppStore)
  public $AppStore: $AppStore;

  @observable public orderParties: $OrderParty[] = [];

  @observable public orderSchemeList: $OrderSchemeList[] = [];

  @observable public currentId: string;

  @observable public currentName: string;

  @observable public loading: boolean = true;

  @observable public showModal: boolean = false;

  @observable public isSpin: boolean = false;

  @observable public buttonList: any = [];

  @observable public orderPartyId: number;
  @observable public activeKey: string = "";
  @observable public orderPartyName: string = "";
  @observable public isShow: boolean = false;
  @observable public isFinished: boolean = false;
  @observable public isLoad: boolean = false;
  @observable public pageIndex: number = 0;
  @observable public pageSize: number = 10;
  @observable public scrollHeight: number = 0;
  @observable public isShowAnnouncement: boolean = false;
  @observable public remainQuotaList: any = [];

  @action
  public showSpin() {
    this.isSpin = true;
  }

  @action
  public hideSpin() {
    this.isSpin = false;
  }

  @action
  public isWeiXin() {
    //window.navigator.userAgent属性包含了浏览器类型、版本、操作系统类型、浏览器引擎类型等信息，这个属性可以用来判断浏览器类型
    var ua = window.navigator.userAgent.toLowerCase();
    //通过正则表达式匹配ua中是否含有MicroMessenger字符串
    if (ua.match(/MicroMessenger/i) == 'micromessenger') {
      return true;
    } else {
      return false;
    }
  }

  @action
  public setLocalStorage(path, value) {
    localStorage.setItem(path, value);
  }

  @action
  public fetchOrderParties() {
    return this.$orderPartyService.queryOrderParties().then((data) => {
      const { orderPartyList } = data;
      this.orderParties = orderPartyList.map((orderParty) => new $OrderParty(orderParty));
      return data;
    });
  }

  @action
  public selectOrderParty(orderPartyId: string) {
    this.currentId = orderPartyId;
    return this.$orderPartyService.saveOrderClients({ orderPartyId });
  }

  @action
  public fetchCurrentOrderParty() {
    return this.$orderPartyService.queryCurrentOrderParty().then((data) => {
      const { orderPartyId, name } = data;
      this.currentId = orderPartyId;
      this.currentName = name;
      return data;
    });
  }

  @action
  public openModal() {
    this.showModal = true;
  }

  @action
  public closeModal() {
    this.showModal = false;
  }

  @action
  public load() {
    this.loading = true;
  }

  @action
  public loaded() {
    this.loading = false;
  }

  @action
  public setCurrentId(currentId: string) {
    this.currentId = currentId;
  }

  @action
  public fetchAgencyList() {
    return new Promise((resolve) => {
      this.$orderPartyService.queryAgencyList().then((data) => {
        const { buttonList } = data;
        this.hideSpin();
        // this.buttonList = buttonList;
        const buttonListParms = [];
        if (buttonList) {
          buttonList.map((item) => {
            buttonListParms.push(item.type);
            if(item.type === "BILLINFO" && Number(item.quantity) > 0 ){
              alert('账单提醒', '你有账单未确认，点击“查看”确认（每月11日前自动生成上月账单，请在当月20日内确认，逾期将视为对账单无异议)', [
                { text: '跳过', onPress: () => console.log('cancel') },
                { text: '查看', onPress: () => {
                  if (item.isNewBillList) {
                    this.$AppStore.clearPageMv(AppStoreKey.BILLLIST);
                    window.location.href = `/${SITE_PATH}/bill-list?activeKey=wait`;
                  } else {
                    this.$AppStore.clearPageMv(AppStoreKey.BILLLIST);
                    window.location.href = `/${SITE_PATH}/old-bill-list?activeKey=wait`;
                  }
                } },
              ])
            }
          });
        }
        this.$orderPartyService.queryAgencyListIsNew({ buttonCodeList: buttonListParms }).then((res) => {
          const { buttonInfoList } = res;
          const newButtonList = [];
          if (buttonList) {
            buttonList.map((item) => {
              buttonInfoList.some((a) => {
                if (item.type === a.buttonCode) {
                  item.isNew = a.isNew;
                  newButtonList.push(item);
                  return item.type === a.type;
                }
              });
            });
          }
          this.buttonList = newButtonList;
          resolve();
        });
      });
    });
  }

  @action
  public fetchOrderPartyList(params) {
    const { pageIndex } = params;
    return this.$orderPartyService.queryOrderPartyList(params).then((data) => {
      this.hideSpin();
      const { orderPartyList, itemCount } = data;
      this.orderParties = pageIndex ? this.orderParties.concat(orderPartyList.map((orderParty) => new $OrderParty(orderParty))) : orderPartyList.map((orderParty) => new $OrderParty(orderParty));
      this.isShow = true;
      this.isFinished = this.orderParties.length >= itemCount;
      this.isLoad = false;
      this.pageIndex = pageIndex + 1;
      this.isShowAnnouncement = true ;

    }).catch(() => {
      this.isLoad = false;
      this.isShowAnnouncement = true ;
      this.hideSpin();
    });
  }

  @action
  public fetchRemainQuotaList(params) {
    return this.$orderPartyService.queryRemainQuotaList(params).then((data) => {
      console.log('6666', data);
      const { orderSchemeQuota } = data;
      console.log('orderSchemeQuota', orderSchemeQuota)
      this.remainQuotaList = orderSchemeQuota;
      return data;
    }).then(() => this.hideSpin());
  }

  @action
  public fetchQueryOrderSchemeList(params) {
    return this.$orderPartyService.queryOrderSchemeList(params).then((data) => {
      const { orderSchemeList, orderPartyName, orderPartyId } = data;
      this.orderPartyName = orderPartyName;
      this.orderPartyId = orderPartyId;
      this.orderSchemeList = orderSchemeList.map((orderScheme) => new $OrderSchemeList(orderScheme));
      return data;
    });
  }

  @action
  public saveShopAndScheme(params) {
    return this.$orderPartyService.saveShopAndScheme(params);
  }

  @action
  public recordinsert(params) {
    return this.$orderPartyService.recordinsert(params);
  }
  @action
  public queryOldData(obj) {
    beanMapper(obj, this);
    console.log(this);
  }
  @action
  public clearMVData() {
   this.orderParties = [];

   this.orderSchemeList = [];

   this.currentId = "";

   this.currentName = "";

   this.loading = true;

   this.showModal = false;

   this.AgencyList = { orderPartyId: 0, orderPartyName: "", buttonList: [] };

   this.isSpin = false;

   this.buttonList = [];

   this.orderPartyId = 0;
   this.activeKey = "";
   this.orderPartyName = "";
   this.isShow = false;
   this.isFinished = false;
   this.isLoad = false;
   this.pageIndex = 0;
   this.pageSize = 10;
   this.scrollHeight = 0;
   this.isShowAnnouncement = false ;

  }
}
