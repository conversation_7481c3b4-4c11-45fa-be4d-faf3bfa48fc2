import { Spin } from "antd";
import { observer } from "mobx-react";
import * as React from "react";
import { with<PERSON>out<PERSON> } from "react-router";
import styled from "styled-components";
import { ScrollAbilityModuleComponent } from "../../components/scroll-ability/module";
import { LoadingTip } from "../../components/loading-marked-words";
declare let require: any;

@withRouter
@observer
class ShopListWrap extends React.Component<any, any> {
  constructor(props) {
    super(props);
    this.state = {
    };
  }

  public render() {
    const { orderParties, isFinished, isLoad, goToOrder, goToMy } = this.props;
    return (
      <ProBoxWrap>
        <div>
          {
              orderParties.map((item, index) => {
                return (
                  <Shop key={index}
                    // onClick={() => this.loadAgencyList(item)}
                  >
                    <i
                      className="scmIconfont scm-icon-shop"/>
                    <span
                      style={{ lineHeight: item.name ? item.name.length > 14 ? "18px" : "34px" : "34px" }}>
                                {item.name ? item.name.length > 19 ? item.name.slice(0, 20) + "..." : item.name : null}
                              </span>
                    <span onClick={() => goToOrder(item.oid)}>开始订货</span>
                    <span onClick={() => goToMy(item.oid)}>门店账户</span>
                    {/*<span style={{ width: "100%", height: "1px", position: "absolute", bottom: 20, left: 0 }}>*/}
                    {/*<svg*/}
                    {/*xmlns='http://www.w3.org/2000/svg'*/}
                    {/*width='100%' height='1px'>*/}
                    {/*<line x1='0'*/}
                    {/*y1='100%'*/}
                    {/*x2='100%'*/}
                    {/*y2='100%'*/}
                    {/*stroke='#dcdcdc'*/}
                    {/*stroke-width='1'/></svg>*/}
                    {/*</span>*/}
                  </Shop>
                );
              })
          }
        </div>
        <LoadingTip
          isFinished={isFinished}
          isLoad={isLoad}
        />
      </ProBoxWrap>
    );
  }
}

const shopList = ScrollAbilityModuleComponent(ShopListWrap);

export default shopList;
const ProBoxWrap = styled.div`// styled
  & {
    position: relative;
    padding: 0px 15px 0 15px;
  }
`;
const Shop = styled.div`// styled
  & {
    font-family: "MicrosoftYaHei";
    font-size: 12px;
    color: #333;
    display: inline-block;
    width: 100%;
    height: 45px;
    line-height: 45px;
    text-align: left;
    // border-bottom: 1px solid #d8d8d8;
    position: relative;

    :after {
      content: '';
      position: absolute;
      background-color: #D8D8D8 !important;
      display: block;
      z-index: 1;
      top: auto;
      right: auto;
      bottom: 0;
      left: 0;
      width: 100%;
      height: 1.2px;
      transform-origin: 50% 100%;
      transform: scaleY(0.5);
    }

    > i {
      color: #D2D2D2;
    }

    > span:nth-of-type(1) {
      display: inline-block;
      width: 168px;
      height: 34px;
      position: absolute;
      top: 6px;
      font-size: 12px;
      color: #333333;
      margin-left: 5px;
    }

    > span:nth-of-type(2), span:nth-of-type(3) {
      float: right;
      display: inline-block;
      font-family: "SinhalaSangamMN";
      font-size: 11px;
      color: #1890FF;
      position: relative;
      width: 56px;
      height: 22px;
      text-align: center;

      :after {
        content: "  ";
        position: absolute;
        left: 0;
        top: 12px;
        z-index: 1;
        width: 200%;
        height: 200%;
        border: 1px solid #1890FF;
        border-radius: 4px;
        padding: 3px 6px;
        -webkit-transform-origin: 0 0;
        transform-origin: 0 0;
        -webkit-transform: scale(.5, .5);
        transform: scale(.5, .5);
      }
    }

    > span:nth-of-type(3) {
      margin-right: 8px;
    }
  }
`;
