import { autowired } from "@classes/ioc/ioc";
import { Spin } from "antd";
import { observer } from "mobx-react";
import React from "react";
import { withRouter } from "react-router";
import styled from "styled-components";
import { $ComponentService } from "../../classes/service/$component-service";
import { NoGoods } from "../../components/no-goods/no-goods";
import { post } from "../../helpers/ajax-helpers";
import { SITE_PATH } from "../app";
import { $CartMv } from "../shop-cart/cart-mv";
import { $OrderPartySelectionMV } from "./$order-party-selection-mv";
import { $SelectButtonType } from "../../classes/const/$select-button-type";
import ShopList from "./shop-list";
import { gaEvent } from "../../classes/website-statistics/website-statistics";
import { $AppStore, AppStoreKey } from "../../classes/stores/app-store-mv";
import { $ReturnedSalesMv } from "../returned-sales/$returned-sales-mv";
import { Toast } from "antd-mobile";
import AnnouncementModel from "../../components/announcement-model/announcement-model"
import { $EnterType } from "../../classes/const/$enter-Type"

declare let window: any;

@withRouter
@observer
class OrderPartySelection extends React.Component<any, any> {
  @autowired($AppStore)
  public $AppStore: $AppStore;
  @autowired($OrderPartySelectionMV)
  public $myMv: $OrderPartySelectionMV;

  @autowired($ComponentService)
  public $componentService: $ComponentService;

  @autowired($CartMv)
  public $CartMv: $CartMv;
  @autowired($ReturnedSalesMv)
  public $ReturnedSalesMv: $ReturnedSalesMv;

  public constructor(props) {
    super(props);
    this.state = {
    };
  }
  public saveMV = () => {
    this.$myMv.scrollHeight = $(".module-scroll-ability-wrap").scrollTop();
    this.$AppStore.savePageMv(AppStoreKey.ORDERPARTYSELECTION, this.$myMv);
  }
  public componentWillUnmount(): void {
    this.saveMV();
  }
  public pageWindowSkip = (url) => {
    this.saveMV()
    setTimeout(() => {
      window.location.href = url;
    }, 50);
  }
  public componentDidMount() {
    document.title = "工作台";
    this.$myMv.clearMVData();
    this.initPage();
  }
  public initPage = () => {
    const ssoSessionId = this.props.history.location.search && this.props.history.location.search.split("=")[1];
    console.log(ssoSessionId);
    if (ssoSessionId) {
      localStorage.setItem("token", ssoSessionId);
    }
    if (this.$myMv.isWeiXin()) {
      this.$componentService.getOpenId().then((data1) => {
        this.$myMv.setLocalStorage("openId", data1.openId);
      });
    }
    if (localStorage.getItem("token")) {
      post("/integration/scm/ordermall/login/permission/query").then((res) => {
        if (res.permission === "refuse") {
          this.pageWindowSkip(`/${SITE_PATH}/no-authority`);
        } else {
          gaEvent("工作台");
          this.$myMv.showSpin();
          this.loadOrderPartyListAndAgencyList();
          this.$myMv.fetchAgencyList();
        }
      });
    }
  }
  public loadData(isFinished) {
    console.log(isFinished);
    if (!isFinished) {
      this.loadOrderPartyListAndAgencyList();
    }
  }

  public loadOrderPartyListAndAgencyList() {
    let oldData = this.$AppStore.getPageMv(AppStoreKey.ORDERPARTYSELECTION)
    if (oldData) {
      oldData  = JSON.parse(oldData);
      this.$myMv.pageIndex = oldData.pageIndex;
      this.$myMv.isFinished = oldData.isFinished;
      this.$myMv.orderParties = oldData.orderParties;
      this.$myMv.scrollHeight = oldData.scrollHeight;
      $(".module-scroll-ability-wrap").scrollTop(this.$myMv.scrollHeight);
      this.$AppStore.clearPageMv(AppStoreKey.ORDERPARTYSELECTION);
    } else {
      const { pageIndex, pageSize } = this.$myMv;
      this.$myMv.isLoad = true;
      this.$myMv.fetchOrderPartyList({ pageIndex, pageSize });
    }
  }
  public loadAgencyList(item) {
    this.setState({
      activeKey: item.oid,
      orderPartyName: item.name,
    });
    // const shopParams = { orderPartyId: item.oid };
    // this.$myMv.saveShopAndScheme(shopParams);
    // const params = { orderPartyId: item.oid };
    // this.$myMv.fetchAgencyList(params);
  }

  public isGoToHome = (activeKey) => {
    const { AgencyList } = this.$myMv;
    const { orderSchemeAvailCount, orderSchemeId } = AgencyList;
    if (orderSchemeAvailCount === 1) {
      const saveParams = {
        orderPartyId: activeKey,
        orderSchemeId,
      };
      this.$myMv.saveShopAndScheme(saveParams).then((data) => {
        if (data.result) {
          this.$AppStore.clearPageMv(AppStoreKey.SHOPLIST);
          this.pageWindowSkip(`/${SITE_PATH}/shop/list`);
        }
      });
    } else {
      this.$AppStore.clearPageMv(AppStoreKey.ORDERSCHEMELIST);
      this.props.history.push({
        pathname: `/${SITE_PATH}/select-scheme`,
        state: { orderPartyId: activeKey },
      });
    }
  }

  public goToAgency = (type, isNew) => {
    if (isNew === $SelectButtonType.ISNEW) {
      this.$myMv.recordinsert({ buttonCode: type });
    }
    const { activeKey } = this.$myMv;
    switch (type) {
      case "AUDIT" :
      case "PAYMENT":
      // case "DELIVER":
        this.$AppStore.clearPageMv(AppStoreKey.ALLSHOPORDERLISTSTORE);
        this.props.history.push({
          pathname: `/${SITE_PATH}/shop/all-shop-order-list`,
          state: { status: type, orderPartyRange: "All" },
        });
        break;
      case "RETURNBILL":
        this.$AppStore.clearPageMv(AppStoreKey.RETURNEDSALES);
        this.props.history.push({
          pathname: `/${SITE_PATH}/returned-sales`,
          state: { orderPartyRange: "ALL" },
        });
        break;
      // case "EXCHANGEGOODS":
      //   this.$AppStore.clearPageMv(AppStoreKey.EXCHANGEGOODSLIST);
      //   this.props.history.push({ pathname: `/${SITE_PATH}/exchange-goods-list` });
      //   break;
      case "FEEDOCUMENT":
        this.props.history.push({ pathname: `/${SITE_PATH}/expense-node-list/Payment/null` });
        break;
      case "INFORMATION":
        this.props.history.push({ pathname: `/${SITE_PATH}/message-notification` });
        break;
      case "OWEGOODS":
        this.props.history.push({ pathname: `/${SITE_PATH}/choose-shop` });
        break;
      case "OFFLINETRANSFERRECORD":
        this.$AppStore.clearPageMv(AppStoreKey.OFFLINETRANSFERRECORDLIST);
        this.props.history.push({ pathname: `/${SITE_PATH}/offline-transfer-record-list` });
        break;
      case "RECEIVE":
        this.$AppStore.clearPageMv(AppStoreKey.RECEIVEORDERLISTSTORE);
        this.props.history.push({ pathname: `/${SITE_PATH}/receive-order-list` });
        break;
      case "BILLINFO":
        this.$AppStore.clearPageMv(AppStoreKey.BILLLIST);
        this.props.history.push({ pathname: `/${SITE_PATH}/bill-list/?activeKey=wait` });
        break;
      default:
        break;
    }
  }

  public showAgencyImg = (type) => {
    let img = "";
    switch (type) {
      case "AUDIT" :
        img = "https://order.fwh1988.cn:14501/static-img/scm/ico-menu-audit.png";
        break;
      case "PAYMENT":
        img = "https://order.fwh1988.cn:14501/static-img/scm/ico-menu-obligation.png";
        break;
      case "DELIVER":
        img = "https://order.fwh1988.cn:14501/static-img/scm/ico-menu-consignment.png";
        break;
      case "RETURNBILL":
        img = "https://order.fwh1988.cn:14501/static-img/scm/ico-menu-returngoods.png";
        break;
      case "FEEDOCUMENT":
        img = "https://order.fwh1988.cn:14501/static-img/scm/ico-menu-exponse-note.png";
        break;
      case "INDIVIGUALACCOUNT":
        img = "https://order.fwh1988.cn:14501/static-img/scm/ico-menu-user.png";
        break;
      case "INFORMATION":
        img = "https://order.fwh1988.cn:14501/static-img/scm/ico-menu-news.png";
        break;
      case "SHOP":
        img = "https://order.fwh1988.cn:14501/static-img/scm/ico-menu-mall.png";
        break;
      case "OWEGOODS":
        img = "https://order.fwh1988.cn:14501/static-img/scm/ico-menu-huodui.png";
        break;
      case "OFFLINETRANSFERRECORD":
        img = "https://order.fwh1988.cn:14501/static-img/scm/icon-menu-zhuanzhang.png";
        break;
      case "RECEIVE":
        img = "https://order.fwh1988.cn:14501/static-img/scm/shouhuo.png";
        break;
      case "BILLINFO":
        img = "https://kk-hosting.oss-cn-shanghai.aliyuncs.com/kingkong/order_shop/img/icon_bill.png";
        break;
      default:
        break;
    }
    return img;
  }

  public showAgencyColor = (type) => {
    let color = "";
    switch (type) {
      case "AUDIT" :
        color = "linear-gradient(-137deg, #81B9FE 0%, #4A95F0 100%)";
        break;
      case "PAYMENT":
        color = "linear-gradient(-137deg, #FECF7E 0%, #FFBB45 100%)";
        break;
      case "DELIVER":
        color = "linear-gradient(-137deg, #FE8484 0%, #F93E6F 100%)";
        break;
      case "RETURNBILL":
        color = "linear-gradient(-137deg, #79CC57 0%, #429321 100%)";
        break;
      case "FEEDOCUMENT":
        color = "linear-gradient(-137deg, rgba(254, 111, 67, 1) 0%, rgba(255, 157, 78, 1) 100%)";
        break;
      case "INDIVIGUALACCOUNT":
        color = "linear-gradient(-137deg, #4FEBC9 0%, #40C6D3 100%)";
        break;
      case "INFORMATION":
        color = "linear-gradient(-137deg, #E4C1FF 0%, #C06DFF 100%)";
        break;
      case "SHOP":
        color = "linear-gradient(-137deg, #FCCEE6 0%, #FF7EC1 100%)";
        break;
      case "OWEGOODS":
        color = "linear-gradient(137deg,rgba(252,206,230,1) 0%,rgba(255,106,183,1) 100%)";
        break;
      case "OFFLINETRANSFERRECORD":
        color = "linear-gradient(137deg,rgba(79,235,201,1) 0%,rgba(64,198,211,1) 100%)";
        break;
      case "RECEIVE":
        color = "linear-gradient(137deg,rgba(254, 132, 132, 1) 0%,rgba(249, 62, 111, 1) 100%)";
        break;
      case "BILLINFO":
        color = "linear-gradient(127deg, #FED35F 0%, #FFB118 100%)";
        break;
      default:
        break;
    }
    return color;
  }

  public renderAgencyList = (buttonList) => {
    return (
      buttonList.map((list, index) => {
        return (
          <Agency key={index} onClick={() => {
            this.goToAgency(list.type, list.isNew);
          }}>
            <p style={{ backgroundImage: this.showAgencyColor(list.type) }}>
              <img src={this.showAgencyImg(list.type)} alt=""/>
              {
                list.isNew === $SelectButtonType.ISNEW ?
                  <span style={{ padding: "0 5px", lineHeight: "18px" }}>new</span>
                  :
                  list.quantity ? <span>{list.quantity > 99 ? "99+" : list.quantity}</span> : null
              }
            </p>
            <div>{list.name}</div>
          </Agency>
        );
      })
    );
  }

  public goToMy = (oid) => {
    const shopParams = { orderPartyId: oid };
    this.$myMv.saveShopAndScheme(shopParams).then(() => {
      this.pageWindowSkip(`/${SITE_PATH}/my?agencyParams=agency`);
    });
  }

  public goToOrder = (shopId) => {
    this.$AppStore.queryShopOverdue(shopId, () => {
      sessionStorage.setItem("editOrderId", null);
      this.$ReturnedSalesMv.businesssControlCheck({orgId:shopId,businessTypeControl:'SalesOrderControl'}).then(res=>{
        if (res.errorCode == "-1") {
          Toast.info(res.errorMessage)
        }else{
          this.isGoToHome(shopId);
        }
      })
    });
  }

  public render() {
    const { orderParties, isSpin, isShow, buttonList, isFinished, isLoad,isShowAnnouncement} = this.$myMv;
    return (
      <Page>
        <Spin spinning={isSpin}>
          <Header>
            <div>
              <span>
              <i className="scmIconfont scm-icon-gongzuotai"/>
            </span>
              <span>工作台</span>
            </div>
          </Header>
          <Wrapper>
            {
              orderParties ? orderParties.length > 0 ? <AgencyLists>
                {this.renderAgencyList(buttonList)}
              </AgencyLists> : null : null
            }
            {
              orderParties ? orderParties.length > 0 ?
                <ChoseShop>
                  <p>
                    <img src="https://order.fwh1988.cn:14501/static-img/scm/ico-bg-shop.png" alt=""/>
                    <span>&nbsp;&nbsp;我的门店</span>
                  </p>
                  <div>
                    {
                      orderParties ? orderParties.length > 0 ?
                        <ShopList
                          orderParties={orderParties}
                          isFinished={isFinished}
                          isLoad={isLoad}
                          goToOrder={this.goToOrder}
                          goToMy={this.goToMy}
                          loadData={() => {
                            this.loadData(isFinished);
                          }}
                        />
                        : <span>暂无门店</span> : null
                    }
                  </div>
                </ChoseShop> : isShow ?
                  <NoGoods title="暂无门店" height={document.documentElement.clientHeight / 2}/> : null : null
            }
          </Wrapper>
        </Spin>
        { isShowAnnouncement && <AnnouncementModel
                enterType={$EnterType.ENTER_MALL}
                isAutoNotice="Y"//'Y'自动弹框提示
                orderSchemeId={null}
                orgIdList={orderParties.map(item=>item.oid)}
              />
           }
      </Page>
    );
  }
}

export default OrderPartySelection;

const Page = styled.div`// styled
  & {
    width: 100%;
    height: calc(${document.documentElement.clientHeight}px);
    background-color: #f5f5f5;
  }
`;

const Wrapper = styled.div`// styled
  & {
    width: 100%;
    height: auto;
    background-color: #f5f5f5;
    box-sizing: border-box;
  }
`;

const AgencyLists = styled.div`// styled
  & {
    width: 100%;
    height: auto;
    background: #fff;
    padding: 15px;
    box-sizing: border-box;
  }
`;

const Agency = styled.div`// styled
  & {
    display: inline-block;
    width: 25%;
    height: auto;
    box-sizing: border-box;
    text-align: center;
    margin-bottom: 15px;

    > p {
      width: 42px;
      height: 42px;
      line-height: 42px;
      border-radius: 42px;
      text-align: center;
      margin: 5px auto;
      position: relative;

      > img {
        width: 20px;
      }

      > span {
        display: block;
        min-width: 20px;
        height: 20px;
        line-height: 20px;
        position: absolute;
        top: -5px;
        right: -5px;
        font-size: 10px;
        color: #FFFFFF;
        background: #FF3030;
        border-radius: 10px;
      }
    }

    > div {
      font-size: 12px;
      color: #666666;
    }
  }
`;

const Header = styled.div`// styled
  & {
    width: 100%;
    height: 37px;
    line-height: 37px;
    background-color: #fff;
    box-sizing: border-box;
    // border-bottom: 1px solid #D8D8D8;
    padding: 0 16px;

    > div {
      position: relative;

      :after {
        content: '';
        position: absolute;
        background-color: #D8D8D8 !important;
        display: block;
        z-index: 1;
        top: auto;
        right: auto;
        bottom: 0;
        left: 0;
        width: 100%;
        height: 1px;
        transform-origin: 50% 100%;
        transform: scaleY(0.5);
      }

      > span:nth-of-type(1) {
        display: inline-block;
        width: 20px;
        height: 20px;
        line-height: 20px;
        text-align: center;
        border-radius: 20px;
        background: #1890FF;
        color: #fff;
        .scm-icon-gongzuotai {
          z-index: 99;
        }
      }

      > span:nth-of-type(2) {
        position: relative;
        top: -2px;
        margin-left: 5px;
        font-family: "MicrosoftYaHei";
        font-size: 11px;
        color: #666666;
      }
    }
  }
`;

const ChoseShop = styled.div`// styled
  & {
    width: 100%;
    height: auto;
    background-color: #fff;
    box-sizing: border-box;
    color: #448ad2;
    margin-top: 10px;
    > p {
      margin: 0;
    }
    > p:first-child {
      height: 36px;
      line-height: 36px;
      text-align: left;
      // border-bottom: 1px solid #d8d8d8;
      box-sizing: border-box;
      margin: 0 15px;
      position: relative;
      :after {
        content: '';
        position: absolute;
        background-color: #D8D8D8 !important;
        display: block;
        z-index: 1;
        top: auto;
        right: auto;
        bottom: 0;
        left: 0;
        width: 100%;
        height: 1px;
        transform-origin: 50% 100%;
        transform: scaleY(0.5);
      }
      > img {
        width: 20px;
        position: relative;
        top: -1px;
      }
      > span:nth-of-type(1) {
        font-family: MicrosoftYaHei;
        font-size: 11px;
        color: #666666;
      }
      > span:nth-of-type(2) {
        float: right;
        font-family: PingFangSC-Regular;
        font-size: 10px;
        color: #999999;
        > img {
          width: 12px;
          height: 12px;
          margin-right: 5px;
          position: relative;
          top: -2px;
        }
      }
    }
    > p:last-child {
      width: 100%;
      height: calc(${document.documentElement.clientHeight}px - 36px - 200px - 37px - 10px);
      box-sizing: border-box;
      overflow: auto;
      -webkit-overflow-scrolling: touch;
      overflow-scrolling: touch;
    }
  }
`;
