import { autowired } from "@classes/ioc/ioc";
import { observer } from "mobx-react";
import * as React from "react";
import { withRouter } from "react-router";
import styled from "styled-components";
import { $BinningDetailListMv } from "./binning-detail-list-mv";
import { NoGoods } from "../../components/no-goods/no-goods";
import { Spin } from "antd";
import { $CartType } from "../../classes/const/$cart-type";
import { ScrollAbilityWrapComponent } from "../../components/scroll-ability/wrap";
import { LoadingTip } from "../../components/loading-marked-words";

declare let require: any;

@withRouter
@observer
class BinningDetailListWrap extends React.Component<any, any> {
  @autowired($BinningDetailListMv)
  public $myMv: $BinningDetailListMv;

  constructor(props) {
    super(props);
    this.state = {
      currentPage: 0,
      pageSize: 20,
      refundOrderOid: "",
      finished: true,
      isShow: false,
    };
  }

  public componentDidMount() {
    document.title = "装箱明细";
    this.loadBinningGoodsList("init");
  }

  public componentWillReceiveProps(
    nextProps: Readonly<any>,
    nextContext: any
  ): void {
    const { isScrollEnd } = nextProps;
    console.log("-----------isScrollEnd",nextProps,this.props);
    if (isScrollEnd && isScrollEnd !== this.props.isScrollEnd) {
      this.loadData();
    }
  }

  public loadBinningGoodsList = (sting) => {
    const { deliveryId } = this.props.match.params || { deliveryId: "" };
    this.$myMv.showSpin();
    console.log("--------props", this.props);
    // const { orderId } = this.props.match.params;
    const { currentPage, pageSize } = this.state;
    this.setState({
      isShow: true,
    });
    // console.log(this.props.match.params);
    let newPageIndex = null;
    if (sting === "init") {
      newPageIndex = currentPage;
    } else {
      newPageIndex = currentPage + 1;
      this.setState({ currentPage: this.state.currentPage + 1 });
    }
    const params = {
      pageSize,
      currentPage: newPageIndex,
      deliveryOrderId: deliveryId,
    };
    this.$myMv.fetchBinningGoodsList(params).then((bloon) => {
      this.setState({ finished: bloon, isShow: false });
      const { loadingEnd } = this.props;
      loadingEnd && loadingEnd(bloon);
    });
  };

  public loadData = () => {
    if (this.state.finished) {
      return;
    }
    this.loadBinningGoodsList("concat");
  };

  public render() {
    const { goodsList, isSpin,itemCount } = this.$myMv;
    const { finished, isShow } = this.state;
    console.log(this.state);
    return (
      <GoodsListWapper className="goodsListPage">
        <Spin spinning={isSpin}>
          <GoodsListContent className="goods-list-content">
            {goodsList &&
              goodsList.length > 0 &&
              goodsList.map((item, index) => {
                const {
                  boxingNo,
                  weight,
                  boxingInfoList,
                } = item;
                return (
                  <div className="product-item" key={boxingNo + index}>
                    <div className="productCode">
                      <i className="scmIconfont scm-icon-list" style={{marginRight:"1rem"}}></i>
                      <span style={{marginRight:"1rem"}}>箱号：{boxingNo} </span>
                      <span>重量：{weight}</span>
                    </div>
                    <div className="product-content">
                        { boxingInfoList&& boxingInfoList.length>0&& 
                        boxingInfoList.map((item,index)=>{
                          const { productSkuId,productSkuName,productSkuCode,quantity,imgUrl,unit}=item;
                          return (
                            <div className='product-content-item' key={productSkuId+index}>
                              <div className="item-img"><img src={imgUrl||"https://order.fwh1988.cn:14501/static-img/scm/ico-nopic.png"} alt=""/></div>
                              <div className='item-info'>
                                <p>{productSkuName}</p>
                                <p>数量：{quantity}</p>
                                <p>单位：{unit}</p>
                              </div>
                          </div>
                          )
                        })
                       }
                    </div>
                  </div>
                );
              })
              }
          </GoodsListContent>
          {itemCount == 0&&<NoGoods title="暂无明细" height={document.documentElement.clientHeight - 90}/>}
          {goodsList && goodsList.length > 0 && (
            <LoadingTip isFinished={finished} isLoad={isShow} />
          )}
        </Spin>
      </GoodsListWapper>
    );
  }
}

const BinningDetailList = ScrollAbilityWrapComponent(BinningDetailListWrap);
export default BinningDetailList;

const GoodsListContent = styled.div`
  // styled
  & {
    width: 100%;
    height: auto;
    background: #f2f2f2;
    padding:10px;

    .product-item {
      padding:0 12px;
      background-color:#ffff;
      margin-bottom:11px;
      border-radius:4px;
      .productCode{
        width:100%;
        height:48px;
        line-height:48px;
        font-size: 13px;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #307DCD;
      }
      .product-content{
        width:100%;
        height:auto;
        .product-content-item{
          width:100%;
          height:98px;
          border:1px solid #ffff;
          .item-img{
            padding:0;
            float:left;
            width:80px;
            height:80px;
            border:1px solid #ffff;
            img{
              margin:0;
              width:100%;
              height:100%;
            }
          }
          .item-info{
            float: left;
            height:80px;
            width:240px;
            border:1px solid #ffff;
            margin-left:8px;
            >p{
              height:18px;
              margin:0;
              margin-bottom:8px;
              line-height:18px;
            }
            >p:nth-of-type(1){
              font-size: 13px;
              font-family: PingFangSC-Regular, PingFang SC;
              font-weight: 400;
              color: #333333;
            }
            >p:nth-of-type(2){
              font-size: 12px;
              font-family: PingFangSC-Regular, PingFang SC;
              font-weight: 400;
              color: #666666;
            }
            >p:nth-of-type(3){
              font-size: 12px;
              font-family: PingFangSC-Regular, PingFang SC;
              font-weight: 400;
              color: #666666;
            }
          }
        }
      

      }
      .product-content-item:after{
          content:'';
          display:block;
          clear: both;
        }
    }
  }
`;

const GoodsListWapper = styled.div`
  // styled
  & {
    width: 100%;
    height: auto;
    min-height: calc(${document.documentElement.clientHeight}px);
    background: #f2f2f2;

    .goods-list-header {
      width: 100%;
      height: 40px;
      border-bottom: 1px solid #d8d8d8;
      line-height: 40px;
      color: #333333;
      padding: 0 16px;
      background-color: #fff;
      overflow: hidden;
    }
  }
`;
