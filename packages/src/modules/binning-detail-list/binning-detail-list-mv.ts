import { autowired, bean } from "@classes/ioc/ioc";
import { action, observable } from "mobx";
import { $ComponentService } from "../../classes/service/$component-service";

@bean($BinningDetailListMv)
export class $BinningDetailListMv {
  @autowired($ComponentService)
  public $componentService: $ComponentService;

  @observable public goodsList: any;

  @observable public isSpin: boolean = false;

  @observable public orderPriceViewPermission: string;

  @observable public retailPriceViewPermission: string;

  // @observable public refundedAmount: number;

  @observable public itemCount: number;

  @action
  public showSpin() {
    this.isSpin = true;
  }

  @action
  public hideSpin() {
    this.isSpin = false;
  }

  @action
  public fetchBinningGoodsList(params) {
    return new Promise((resolve) => {
      this.$componentService.queryBinningGoodsList(params).then((data) => {
        // console.log('----------------data---------',data)
        const { currentPage } = params;
        const { itemCount, boxingList } = data;
        // const { refundOrderOid, refundOrderDocNo, itemList, refundedAmount } = boxingList;
        this.itemCount = itemCount;
        // this.refundedAmount = refundedAmount;
        if (currentPage > 0) {
          //拼接相同箱号的数据
          const goodListLength=this.goodsList.length-1;
          if(this.goodsList[goodListLength].boxingNo==boxingList[0].boxingNo){
            this.goodsList[goodListLength].boxingInfoList=this.goodsList[goodListLength].boxingInfoList.concat(boxingList[0].boxingInfoList);
            boxingList.splice(0,1)
          }
          this.goodsList = this.goodsList.concat(boxingList);
        } else {
          this.goodsList = boxingList||[];
        }
        resolve(this.goodsList.length >= this.itemCount);
        this.hideSpin();
      }).then(() => { this.hideSpin(); });
    })
  }
}
