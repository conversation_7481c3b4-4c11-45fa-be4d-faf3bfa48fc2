import { autowired } from "@classes/ioc/ioc";
import { Spin } from "antd";
import { List } from "antd-mobile";
import { observer } from "mobx-react";
import React from "react";
import styled from "styled-components";
import { $MessageNotificationMv } from "./message-notification-mv";
import { SITE_PATH } from "../app";
import { NoGoods } from "../../components/no-goods/no-goods";
import { $MessageType } from "../../classes/const/$message-type";
import { withRouter } from "react-router";
import { $AppStore, AppStoreKey } from "../../classes/stores/app-store-mv";

declare let window: any;
const Item = List.Item;

@withRouter
@observer
class MessageNotification extends React.Component<any, any> {
  @autowired($AppStore)
  public $AppStore: $AppStore;
  @autowired($MessageNotificationMv)
  public $messageNotificationMv: $MessageNotificationMv;

  constructor(props) {
    super(props);
  }

  public componentDidMount() {
    document.title = "消息主页";
    this.$messageNotificationMv.fetchMessageCateList();
  }

  public goToBusiness = () => {
    this.$AppStore.clearPageMv(AppStoreKey.MESSAGELIST);
    window.location.href = `/${SITE_PATH}/message-business`;
  }

  public goToCompany = () => {
    this.$AppStore.clearPageMv(AppStoreKey.MESSAGECOMPANY);
    window.location.href = `/${SITE_PATH}/message-company`;
  }

  public render() {
    const { messageCateList, isSpin } = this.$messageNotificationMv;

    return (
      <MessageCompanyPage>
        <Spin spinning={isSpin}>
          {
            messageCateList ? messageCateList.length > 0 ? messageCateList.map((list) => {
              return (
                <div>
                  <List>
                    <Item arrow={"horizontal"}
                          onClick={list.type === $MessageType.type ? this.goToBusiness : this.goToCompany}
                    >
                      <div>
                        <img
                          src={list.type === $MessageType.type ? "https://order.fwh1988.cn:14501/static-img/scm/icon-message-b.png" : "https://order.fwh1988.cn:14501/static-img/scm/icon-rule-b.png"}
                          alt=""/>
                      </div>
                      <div>
                        <p>{list.title}</p>
                        <p>{list.subTitle ? list.subTitle.length > 18 ? list.subTitle.slice(0, 18) + "..." : list.subTitle : null}</p>
                      </div>
                      {
                        list.type === $MessageType.type ? <div>
                          {list.count > 99 ? "..." : list.count}
                        </div> : null
                      }
                    </Item>
                  </List>
                  <Spancing/>
                </div>
              );
            }) : <NoGoods title="暂无消息"/> : null
          }
        </Spin>
      </MessageCompanyPage>
    );
  }
}

export default MessageNotification;

const MessageCompanyPage = styled.div`// styled
  & {
    .am-list .am-list-body::before {
      height: 0;
    }
    .am-list .am-list-body::after {
      height: 0;
    }
    .am-list-item.am-list-item-middle .am-list-line {
      position: relative;
    }
    .am-list-item .am-list-line .am-list-content {
      > div {
        display: inline-block;
      }
    }
    .am-list-item img {
      width: 32px;
      height: 32px;
      border: 0;
    }
    .am-list-content {
      height: 64px;
    }
    .am-list-content div:nth-of-type(1) {
      width: 32px;
      position: absolute;
      top: 15px;
      left: 15px;
    }
    .am-list-content div:nth-of-type(2) {
      position: absolute;
      top: 10px;
      left: 60px;
      > p {
        margin-bottom: 7px;
      }
      > p:nth-of-type(1) {
        font-size: 14px;
        color: #333333;
      }
      > p:nth-of-type(2) {
        font-size: 12px;
        color: #999999;
      }
    }
    .am-list-content div:nth-of-type(3) {
      position: absolute;
      top: 22px;
      right: 38px;
      width: 29px;
      height: 20px;
      line-height: 20px;
      background: #FF3030;
      border-radius: 10px;
      font-size: 12px;
      color: #FFFFFF;
      text-align: center;
    }
  }
`;

const Spancing = styled.div`// styled
  & {
    width: 100%;
    height: 10px;
    background: #F2F2F2;
  }
`;
