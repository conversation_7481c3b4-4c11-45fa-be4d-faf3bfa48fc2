import { autowired } from "@classes/ioc/ioc";
import { getQueryString } from "@classes/utils/UrlUtils";
import { BackTop, Spin } from "antd";
import { Accordion, List } from "antd-mobile";
import { findIndex } from "lodash";
import { observer } from "mobx-react";
import React from "react";
import { withRouter } from "react-router";
import Slider from "react-slick";
import styled from "styled-components";
import { $ActiveType } from "../../classes/const/$active-type";
import { $Product } from "../../classes/entity/$product";
import { Footer } from "../../components/footer/footer";
import {
  ProductSegment,
  ProductOrderProposals,
  ProductAnchorEnum,
  ANCHOR_HEIGHT,
} from "../../components/product-component";
import Section from "../../components/product-component/product-order-proposals/section";
import { ProductCount } from "../../components/product-count/product-count";
import { $ProductListMv } from "../product-list/$product-list-mv";
import { $CartMv } from "../shop-cart/cart-mv";
import { $CommodityDetailsMv } from "./commodity-details-mv";
import { $CartType } from "../../classes/const/$cart-type";

declare let window: any;

@withRouter
@observer
class CommodityDetails extends React.Component<any, any> {

  @autowired($CommodityDetailsMv)
  public $commodityDetailsMv: $CommodityDetailsMv;

  @autowired($CartMv)
  public $CartMv: $CartMv;

  @autowired($ProductListMv)
  public $productListMv: $ProductListMv;

  private pageRef = null;
  private richRef = null;
  private orderRef = null;

  private lockScroll: boolean = false;

  constructor(props) {
    super(props);
    this.pageRef = React.createRef();
    this.richRef = React.createRef();
    this.orderRef = React.createRef();
    this.state = {
      anchor: ProductAnchorEnum.ORDER,
      showSegment: false,
    };
  }

  public componentDidMount() {
    document.title = "商品详情";
    this.$commodityDetailsMv.showUpload = false;
    this.$commodityDetailsMv.showSpin();
    const params = { productSkuId: this.props.match.params.productSkuId };
    Promise.all([
      this.$commodityDetailsMv.queryCommodityDetails(params),
      this.loadCommodityDetails(),
      this.loadOrderSuggestion(),
    ])
      .then(() => {
        this.$commodityDetailsMv.hideSpin();
        const { orderSuggestion } = this.$commodityDetailsMv;
        const val = orderSuggestion?.suggestionContent?.suggestionOrderQuantity;
        const needOrderSuggestion = (val !== undefined && val !== null && val !== "");
        this.setState({ showSegment: needOrderSuggestion }, () => {
          if (needOrderSuggestion) {
            this.toDefaultAnchor();
          }
        });
      });
    this.$CartMv.fetchShopcartproductnum();
  }

  public touchStart = (e) => {
    this.setState({
      startx: e.touches[0].pageX,
      starty: e.touches[0].pageY,
    });
  }

  public touchEnd = (e) => {
    let endx, endy;
    endx = e.changedTouches[0].pageX;
    endy = e.changedTouches[0].pageY;
    const direction = this.getDirection(this.state.startx, this.state.starty, endx, endy);
    switch (direction) {
      case 0:
        console.log("未滑动！");
        break;
      case 1:
        console.log("向上！");
        this.loadCommodityDetails();
        break;
      case 2:
        console.log("向下！");
        break;
      case 3:
        console.log("向左！");
        break;
      case 4:
        console.log("向右！");
        break;
      default:
    }
  }

  public getAngle(angx, angy) {
    return Math.atan2(angy, angx) * 180 / Math.PI;
  }

  public getDirection(startx, starty, endx, endy) {
    const angx = endx - startx;
    const angy = endy - starty;
    let result = 0;

    // 如果滑动距离太短
    if (Math.abs(angx) < 2 && Math.abs(angy) < 2) {
      return result;
    }
    const angle = this.getAngle(angx, angy);
    if (angle >= -135 && angle <= -45) {
      result = 1;
    } else if (angle > 45 && angle < 135) {
      result = 2;
    } else if ((angle >= 135 && angle <= 180) || (angle >= -180 && angle < -135)) {
      result = 3;
    } else if (angle >= -45 && angle <= 45) {
      result = 4;
    }
    return result;
  }

  public loadCommodityDetails = () => {
    const params = { productSkuId: this.props.match.params.productSkuId };
    return this.$commodityDetailsMv.queryMultimediaDesc(params);
  }

  public loadOrderSuggestion = () => {
    const params = { productSkuId: this.props.match.params.productSkuId };
    return this.$commodityDetailsMv.queryOrderSuggestion(params);
  }

  public showTab = (actionType) => {
    let type = "";
    switch (actionType) {
      case "Deduct":
        type = "减价";
        break;
      case "Discount":
        type = "折扣";
        break;
      case "Special":
        type = "特价";
        break;
      case "Gift":
        type = "赠送";
        break;
      case "PartFree":
        type = "多免一";
        break;
      case "MutiDiscount":
        type = "X折";
        break;
      case "TradeUp":
        type = "加价购";
        break;
      default:
        type = "";
        break;
    }
    return type;
  }

  public showColor = (actionType) => {
    let color = "";
    switch (actionType) {
      case "Deduct":
        color = "#8DBD00";
        break;
      case "Discount":
        color = "#F7B51C";
        break;
      case "Special":
        color = "#F25EA9";
        break;
      case "Gift":
        color = "#FF7564";
        break;
      case "PartFree":
        color = "#B385F5";
        break;
      case "MutiDiscount":
        color = "#F7B51C";
        break;
      case "TradeUp":
        color = "#FF8627";
        break;
      default:
        color = "";
        break;
    }
    return color;
  }

  public showStockStatus = (type) => {
    let status;
    switch (type) {
      case "STOCK_EMPTY":
        status = "已售罄";
        break;
      case "STOCK_URGENT":
        status = "库存告急";
        break;
      case "STOCK_FULL":
        status = "库存充足";
        break;
      default:
        break;
    }
    return status;
  }

  public cartWrapper = (product, cartProducts: $Product[]) => {
    if (product) {
      const index = findIndex(cartProducts, { productSkuId: product.productSkuId });
      if (index > -1) {
        product.setQuantity(cartProducts[index].quantity);
      } else {
        product.setQuantity(0);
      }
      this.$commodityDetailsMv.setProduct(product);
    }
  }

  public toDefaultAnchor = () => {
    const anchor = getQueryString("anchor");
    if (!!anchor) {
      setTimeout(() => this.intoAnchor(anchor), 200);
    }
  }

  public intoAnchor = (type) => {
    this.lockScroll = true;
    const node = ProductAnchorEnum.RICH === type ? this.richRef.current : this.orderRef.current;
    const top = node.getBoundingClientRect().top + this.pageRef.current.scrollTop - ANCHOR_HEIGHT;
    this.pageRef.current.scrollTo({
      top,
      behavior: "smooth",
    });
    this.setState({ anchor: type });

    let timer = null;
    const listenScroll = () => {
      if (timer) {
        clearTimeout(timer);
      }
      timer = setTimeout(() => {
        this.lockScroll = false;
        this.pageRef.current.removeEventListener("scroll", listenScroll);
      }, 200);
    };
    this.pageRef.current.addEventListener("scroll", listenScroll);
  }

  public scrollPage = () => {
    if (this.lockScroll) {
      return;
    }
    const viewportTop = this.pageRef.current.scrollTop;
    const { top: rTop } = this.richRef.current.getBoundingClientRect();
    const rOffsetY = viewportTop + rTop - ANCHOR_HEIGHT;
    const setAnchor = (v) => {
      const { anchor } = this.state;
      if (anchor !== v) {
        this.setState({ anchor: v });
      }
    };
    if (viewportTop >= rOffsetY) {
      setAnchor(ProductAnchorEnum.RICH);
      return;
    }
    setAnchor(ProductAnchorEnum.ORDER);
  }

  public render() {
    const { detailObject } = this.$productListMv;
    const { commodityDetails, multimediaDescription, showUpload, totalPrice, newProduct, isSpin, orderSuggestion } = this.$commodityDetailsMv;
    this.cartWrapper(detailObject ? detailObject : commodityDetails, this.$CartMv.products);
    const { imageList, promotionList, name, orderPrice, retailPrice, unitOfMeasure, stockStatus, briefIntroduction, orderPriceViewPermission, retailPriceViewPermission, orderSchemeType } = commodityDetails || {};
    const adSettings = {
      autoplay: true,
      autoplaySpeed: 5000,
      centerMode: true,
      centerPadding: "0px",
      dots: true,
      infinite: true,
      slidesToScroll: 1,
      slidesToShow: 1,
      speed: 500,
    };
    const {
      anchor,
      showSegment,
    } = this.state;
    const productSkuId = this.props.match.params.productSkuId
    return (
      <CommodityPage
        ref={this.pageRef}
        onScroll={() => showSegment && this.scrollPage()}
      >
        <Spin spinning={isSpin}>
          <CommoditHeader>
            <Slider {...adSettings}>
              {
                imageList ? imageList.map((ad, index) => {
                  return <img key={index}
                              src={ad ? ad : "https://order.fwh1988.cn:14501/static-img/scm/ico-banner-nopic.png"}
                              className="sliderImage"
                              alt=""/>;
                }) : null
              }
            </Slider>
            <p>
              {name}
            </p>
            <p>
              {
                (orderPriceViewPermission === $CartType.ORDERPRICEVIEWPERMISSION) && (orderPrice !== null) ?
                  <span className="redprice">
                    <span style={{ fontSize: "10px" }}>￥</span>{orderPrice}
                    <span className="blackprice">{unitOfMeasure && `/${unitOfMeasure}`}</span></span> : null
              }
              {
                (retailPriceViewPermission === $CartType.RETAILPRICEVIEWPERMISSION) && (retailPrice !== null) ?
                  <span className="blackprice">
                    {((orderPriceViewPermission === $CartType.ORDERPRICEVIEWPERMISSION) && (orderPrice !== null)) ? " (" : null}零售 ￥{retailPrice}{((orderPriceViewPermission === $CartType.ORDERPRICEVIEWPERMISSION) && (orderPrice !== null)) ? ")" : null}{((orderPriceViewPermission !== $CartType.ORDERPRICEVIEWPERMISSION) || (orderPrice === null)) ? <span className="blackprice">{unitOfMeasure && `/${unitOfMeasure}`}</span> : null}
                  </span> : <span />
              }
              <span>{this.showStockStatus(stockStatus)}</span>
            </p>
          </CommoditHeader>
          {
            promotionList ? promotionList.length > 0 ? <CommoditPromotion className="promotion">
              <Accordion defaultActiveKey="0" accordion={true} openAnimation={{}} className="my-accordion" onChange={ }>
                <Accordion.Panel header="促销活动">
                  {
                    promotionList.map((promotion, index) => {
                      return (
                        <List className="my-list">
                          <List.Item key={index}>
                            <span
                              style={{
                                borderRadius: 2,
                                color: this.showColor(promotion.promotionType),
                                border: `0.5px solid ${this.showColor(promotion.promotionType)}`,
                              }}>{this.showTab(promotion.promotionType)}</span>
                            {promotion.promotionTitle}
                          </List.Item>
                        </List>
                      );
                    })
                  }
                </Accordion.Panel>
              </Accordion>
              <PromotionItemDes>
                可享受的促销
              </PromotionItemDes>
            </CommoditPromotion> : null : null
          }
          {showSegment && <>
            <ProductSegment value={anchor} onClick={this.intoAnchor} />
            <ProductOrderProposals
              productSkuId={productSkuId}
              data={orderSuggestion}
              unit={unitOfMeasure || ""}
              wrapperRef={this.orderRef}
            />
          </>}
          <CommoditIntroduce
            data-segment={showSegment}
            ref={this.richRef}
          >
            <Section
              style={{ marginLeft: showSegment ? 12 : 0 }}
              title={"产品图文详情"}
              subTitle={briefIntroduction}
              hideTag={true}
            />
          </CommoditIntroduce>
          {
            showUpload ? <UpToSlider onTouchStart={this.touchStart} onTouchEnd={this.touchEnd}>
              <span className="line left" />
              <span className="arrow">↑</span>
              <span>  上拉查看图文详情</span>
              <span className="line right" />
            </UpToSlider> : multimediaDescription ?
              <CommoditDetail dangerouslySetInnerHTML={{ __html: multimediaDescription }} /> :
              <CommoditDetail><p style={{ color: "#333", marginBottom: 50 }}>暂无图文详情</p></CommoditDetail>}
          <BackTop style={{ marginBottom: 50 }} />
          <CommoditFooter>
            {newProduct ? newProduct.orderPriceViewPermission === $CartType.ORDERPRICEVIEWPERMISSION || newProduct.retailPriceViewPermission === $CartType.RETAILPRICEVIEWPERMISSION ?
              <span>合计：</span> : <span /> : <span />}
            {newProduct ? newProduct.orderPriceViewPermission === $CartType.ORDERPRICEVIEWPERMISSION || newProduct.retailPriceViewPermission === $CartType.RETAILPRICEVIEWPERMISSION ?
              <span>{totalPrice ? Number(totalPrice).toFixed(2) : 0}</span> : <span /> : <span />}
            <span>
              <ProductCount data={newProduct ? newProduct : { quantity: 0 }} noCart={true} source={""} orderSchemeType={orderSchemeType} fromWhere={"product"} />
            </span>
          </CommoditFooter>
        </Spin>
        <Footer activeKey={$ActiveType.MAIN_KEY} count={this.$CartMv.totalCount} />
      </CommodityPage>
    );
  }
}

export default CommodityDetails;

const CommodityPage = styled.div`// styled
  & {
    width: 100%;
    height: 100%;
    background: #F2F2F2;
    padding-bottom: 50px;
    overflow-y: auto;
    overflow-x: hidden;
    .sliderImage{
      width:100%;
      height:${document.documentElement.clientWidth}px;
    }
    .slick-slider.slick-initialized{
      overflow:hidden;
    }
    .slick-dots{
      bottom:5px;
    }
    .slick-dots li button:before{
      color:white;
      opacity:1;
    }
    .slick-dots li{
      width:16px;
      height:16px;
      margin:0;
    }
    .slick-dots li.slick-active button:before{
      opacity:1;
      color:#307DCD;
    }
    .am-accordion{
      border:0;
    }
    .promotion .am-accordion-header::after{
      content: '';
      position: absolute;
      background-color: #ddd;
      display: block;
      z-index: 1;
      top: 0;
      right: auto;
      bottom: auto;
      left: 0;
      width: 100%;
      height: 0;
      transform-origin: 50% 50%;
      transform: scaleY(0.5);
    }
    .promotion .am-accordion::before{
      content: '';
      position: absolute;
      background-color: #fff;
      display: block;
      z-index: 1;
      top: 0;
      right: auto;
      bottom: auto;
      left: 0;
      width: 100%;
      height: 0;
      transform-origin: 50% 50%;
      transform: scaleY(0.5);
    }
    .promotion .am-accordion .am-accordion-item .am-accordion-header {
      font-size: 14px;
      color: #333;
    }
    .promotion .am-accordion .am-accordion-item .am-accordion-header .arrow {
      background-image: url("https://order.fwh1988.cn:14501/static-img/scm/icon-arrow-down.svg");
    }
    .promotion .am-list-item .am-list-line .am-list-content {
      font-size: 14px;
    }
    .am-list-content span {
      display: inline-block;
      width: 40px;
      height: 20px;
      line-height: 20px;
      text-align: center;
      margin-right: 10px;
      border-radius: 2px;
      font-size: 10px;
    }
    .my-accordion {
      border: 0;
    }
  }
`;

const CommoditHeader = styled.div`// styled
  & {
    width: 100%;
    height: auto;
    background: #fff;
    > p:nth-of-type(1) {
      color: #333;
      font-size: 14px;
      padding: 0 15px;
      margin-top: 10px;
    }
    > p:nth-of-type(2) {
      padding: 0 15px;
      position: relative;
      .redprice {
        font-size: 14px;
        color: #ff3030;
        font-family: SourceHanSansCN-Normal, SourceHanSansCN;
      }
      .blackprice {
        font-size: 10px;
        color: #999999;
        font-family: SourceHanSansCN-Normal, SourceHanSansCN;
      }
      > span:last-child {
        position: absolute;
        top: 0;
        right: 15px;
        color: #999;
        font-size: 12px;
      }
    }
  }
`;

const CommoditPromotion = styled.div`// styled
  & {
    margin-top: 10px;
    position: relative;
  }
`;

const PromotionItemDes = styled.div`// styled
  & {
    font-size: 12px;
    color: #307DCD;
    position: absolute;
    top: 15px;
    right: 35px;
  }
`;

const CommoditIntroduce = styled.div`// styled
  & {
    width: 100%;
    background: #fff;
    margin-top: 12px;

    &[data-segment="true"] {
      margin-top: 0;
    }
  }
`;

const UpToSlider = styled.div`// styled
  & {
    padding: 10px 30px;
    text-align: center;
    color: #999;
    width: 100%;
    height: 40px;
    font-size: 12px;
    margin-bottom: 40px;
    .line {
      display: inline-block;
      width: 50px;
      height: 1px;
      border-top: 1px solid #D8D8D8;
      margin-bottom: 5px;
    }
    .left {
      margin-right: 10px;
    }
    .right {
      margin-left: 10px;
    }
    .arrow {
      display: inline-block;
      width: 20px;
      height: 20px;
      border-radius: 10px;
      border: 1px solid #999;
      font-size: 10px;
      font-weight: bold;
    }
  }
`;

const CommoditDetail = styled.div`// styled
  & {
    width: 100%;
    padding: 15px 15px 30px 15px;
    background: #fff;
    text-align: center;
    > .image-wrap img {
      width: 100%;
    }
  }
`;

const CommoditFooter = styled.div`// styled
  & {
    width: 100%;
    height: 44px;
    padding: 15px;
    background: #fff;
    position: fixed;
    bottom: 0;
    margin-bottom: 50px;
    z-index: 99;
    > span:nth-of-type(1) {
      font-size: 12px;
      color: #333;
    }
    > span:nth-of-type(2) {
      color: #FF3030;
      font-size: 14px;
    }
    > span:nth-of-type(3) {
      position: absolute;
      top: 7px;
      right: 15px;
    }
  }
`;
