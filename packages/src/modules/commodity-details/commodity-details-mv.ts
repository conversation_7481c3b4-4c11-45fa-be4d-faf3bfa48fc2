import { autowired, bean } from "@classes/ioc/ioc";
import { action, computed, observable } from "mobx";
import { $ProductService } from "../../classes/service/$product-service";
import { $Commodity } from "../../classes/entity/$Commodity";
import { $CartType } from "../../classes/const/$cart-type";
import VirtualsuitDetail from "./virtualsuit-detail";

@bean($CommodityDetailsMv)
export class $CommodityDetailsMv {
  @autowired($ProductService)
  public $productService: $ProductService;

  @observable public commodityDetails: $Commodity;

  @observable public multimediaDescription: string;

  @observable public orderSuggestion: any;

  @observable public newProduct: object | any;

  @observable public showUpload: boolean = true;

  @observable public isSpin: boolean = false;

  @action
  public showSpin() {
    this.isSpin = true;
  }

  @action
  public hideSpin() {
    this.isSpin = false;
  }

  @action
  public queryCommodityDetails(productSkuId) {
    return this.$productService.fetchCommodityDetails(productSkuId)
      .then((data) => {
        this.commodityDetails = new $Commodity(data);
      });
  }

  @action
  public queryVirtualsuitDetail(productSkuId) {
    return this.$productService.fetchVirtualsuitDetail(productSkuId).then((data) => {
      this.isSpin = false;
      this.commodityDetails = new $Commodity(data);
    });
  }

  @action
  public queryMultimediaDesc(productSkuId) {
    return this.$productService.fetchMultimediaDesc(productSkuId)
      .then((data) => {
        this.multimediaDescription = data.multimediaDescription;
        this.showUpload = false;
      });
  }

  @action
  public queryOrderSuggestion(params) {
    return this.$productService.fetchOrderSuggestion(params)
      .then((data) => {
        this.orderSuggestion = data;
      });
  }

  @action
  public queryOtherStoreInventory(params) {
    return new Promise((resolve, reject) => {
      this.$productService.fetchOtherStoreInventoryList(params)
      .then((data) => resolve(data))
      .catch(() => reject());
    });
  }

  @action
  public setProduct(product) {
    this.newProduct = product;
  }

  @computed
  get totalPrice() {
    if (this.newProduct) {
      if (this.newProduct.orderPriceViewPermission === $CartType.ORDERPRICEVIEWPERMISSION) {// 订购价
        return this.newProduct.orderPrice * this.newProduct.quantity;
      } else { // 零售价
        return this.newProduct.retailPrice * this.newProduct.quantity;
      }
    }
  }
}
