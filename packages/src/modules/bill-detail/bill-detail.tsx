import { autowired } from "@classes/ioc/ioc";
import { Spin } from "antd";
import { observer } from "mobx-react";
import * as React from "react";
import { withRouter } from "react-router";
import { gaEvent } from "../../classes/website-statistics/website-statistics";
import { $BillDetailMv } from "./bill-detail-mv";
import styled from "styled-components";
import { SITE_PATH } from "../app";
import moment from "moment";
import { $AppStore, AppStoreKey } from "../../classes/stores/app-store-mv";
import { Modal } from "antd-mobile";
import { getUrlParam } from "@classes/utils/UrlUtils";
import { Toast } from "antd-mobile/es";

const alert = Modal.alert;
declare let window: any;
@withRouter
@observer
class BillDetail extends React.Component<any, any> {
    @autowired($AppStore)
    public $AppStore: $AppStore;
    @autowired($BillDetailMv)
    public $myMv: $BillDetailMv;

    constructor(props) {
        super(props);
        this.state = ({
        });
    }

    public componentDidMount() {
        document.title = "账单详情";
        gaEvent("账单详情");
        this.$myMv.queryBillDetail({oid:this.props.match.params.orderId})
    }
    public componentWillUnmount() {
        this.$myMv.billDetailInfo = []
    }

    public setStatusColor = (status) => {
        let color = ""
        switch (status){
            case "wait":
                color = "#FF3636";
                break;
            default:
                color = "#437DF0";
                break;
        }
        return color;
    }

    public goToBillTurnover = (orderId,region,orgName) => {
        this.$AppStore.clearPageMv(AppStoreKey.BILLTURNOVER)//清楚账单流水缓存数据
        const timeArr = region.split("~")
        this.props.history.push({
            pathname: `/${SITE_PATH}/bill-turnover`,
            state:{
                orgId:orderId,
                beginDate:new Date(timeArr[0]),
                endDate:new Date(timeArr[1]),
                orgName,
                orderPartyId:getUrlParam("orderPartyId")
            }
        });
    }

    public confirmBill = (oid,billMonthly) => {
        alert('账单确认', `确认${billMonthly}账单吗`, [
            { text: '取消', onPress: () => console.log('cancel')},
            { text: '确认', onPress: () =>  {
                this.$myMv.confirmBill({oid}).then(res=>{
                   if(res){
                       let newBillList = JSON.parse(this.$AppStore.getPageMv(AppStoreKey.BILLLIST));//删除缓存数据中已确认的单据
                       const { BillListInfo } = newBillList;
                       newBillList.scrollHeight = 0;
                       newBillList.BillListInfo = BillListInfo.filter(item => item.id != Number(oid))
                       this.$AppStore.savePageMv(AppStoreKey.BILLLIST,newBillList)
                       Toast.info("已确认")
                       setTimeout(() => {
                        window.location.reload();
                      }, 1500);
                    //    this.$myMv.queryBillDetail({oid:this.props.match.params.orderId})
                   }
                })
            }},
          ]);
    }

    public render() {
        const {isSpin,billDetailInfo} =this.$myMv;
        const {id,docNo,status,statusValue,orgName,billMonthly,region,createdOn,confirmUser,confirmTime,hasConfirmBtnToken,itemList = []  } = billDetailInfo;
        const balanceAmount = itemList.filter(item => item.fundType === "balance")[0] || [];
        const paidNotDeliveryAmount = itemList.filter(item => item.fundType === "paid_not_delivery_amount")[0] || [];
        return (
        <DetailWapper>
            <Spin spinning={isSpin}>
            <BillInfo>
                <div className="billInfoHeader">
                    <span>账单单号：{docNo}</span>
                    <span style={{color:this.setStatusColor(status)}}>{statusValue}</span>
                </div>
                <div className="billInfoContent">
                    <div>所属组织：<span>{orgName}</span></div>
                    <div>账单月份：<span>{billMonthly}</span></div>
                    <div>账单区间：<span>{region}</span></div>
                    <div>生成时间：<span>{createdOn}</span></div>
                    {confirmUser && <div>确认人员：<span>{confirmUser}</span></div>} 
                    {confirmTime && <div>确认时间：<span>{confirmTime}</span></div>}

                </div>
            </BillInfo>
            <AmountInfo>
                    <div><div>期初余额</div><div>{balanceAmount.initAmount}</div></div>
                    <div><div>期初已付款未发货额</div><div>{paidNotDeliveryAmount.initAmount}</div></div>
                    <div><div>余额本期变动</div><div>{balanceAmount.changeAmount}</div></div>
                    <div><div>已付款未发货额本期变动</div><div>{paidNotDeliveryAmount.changeAmount}</div></div>
                    <div><div>期末余额</div><div>{balanceAmount.finalAmount}</div></div>
                    <div><div>期末已付款未发货额</div><div>{paidNotDeliveryAmount.finalAmount}</div></div>
            </AmountInfo>
            </Spin>
            <ButtonWapper>
                {hasConfirmBtnToken && <div onClick={()=>this.confirmBill(id,billMonthly)}>确认账单</div>}
                <div onClick={()=>this.goToBillTurnover(id,region,orgName)}>查看详情</div>
            </ButtonWapper>
        </DetailWapper>)
          
}
}


export default BillDetail;

const DetailWapper = styled.div`
    &{
        background: #F7F7F7;
        width:100%;
        height:${document.documentElement.clientHeight}px;
        padding:12px;
        >div{
            width:100%;
            background:#fff;
            border-radius:8px;
            margin-bottom:12px;
        }
    }
`;
const BillInfo = styled.div`
    &{
        padding:0 14px;
        border-radius:8px;
        .billInfoHeader{
            display:flex;
            align-items:center;
            justify-content:space-between;
            height:44px;
            border-bottom:1px solid #eee;
            >span:first-of-type{
                font-size: 14px;
                font-family: PingFangSC-Medium, PingFang SC;
                font-weight: 500;
                color: #333333;
            }
            >span:last-of-type{
                font-size: 12px;
                font-family: PingFangSC-Medium, PingFang SC;
                font-weight: 500;
                color: #437DF0;
            }
        }
        .billInfoContent{
            padding:12px 0;
            >div{
                margin:0 0 10px 0;
                font-size: 13px;
                font-family: PingFangSC-Regular, PingFang SC;
                font-weight: 400;
                color: #666666;
                >span{
                    font-size: 13px;
                    font-family: PingFangSC-Medium, PingFang SC;
                    font-weight: 500;
                    color: #333333;
                }
            }
        }
    }
`;
const AmountInfo = styled.div`
    &{
        padding:0 36px 14px;
        display:flex;
        flex-wrap:wrap;
        border-radius:8px;
        margin-bottom:70px;
        >div{
            &:nth-of-type(2n - 1){
                width:45%;
            }
            &:nth-of-type(2n){
                width:55%;
            }
            margin-top:14px;
            div{
                &:first-of-type{
                    font-size: 13px;
                    font-family: PingFangSC-Regular, PingFang SC;
                    font-weight: 400;
                    color: #666666;
                    margin-bottom:3px;
                }
                &:last-of-type{
                    font-size: 18px;
                    font-family: Arial-BoldMT, Arial;
                    font-weight: normal;
                    color: #333333;
                }
            }
        }
       

    }
`
const ButtonWapper = styled.div`
    &{
        width:100%;
        margin-bottom:0!important;
        padding:10px 10px;
        position:fixed;
        left:0;
        bottom:0;
        display:flex;
        >div{
            margin:0 14px;
            width:100%;
            height:46px;
            font-size: 15px;
            font-family: PingFangSC-Medium, PingFang SC;
            font-weight: 500;
            border-radius:23px;
            line-height:46px;
            text-align:center;
            color: #FFFFFF;
            background-color:#437DF0;
        }

    }
`
