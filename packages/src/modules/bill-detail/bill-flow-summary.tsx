import { autowired } from "@classes/ioc/ioc";
import { Spin } from "antd";
import { observer } from "mobx-react";
import * as React from "react";
import { withRouter } from "react-router";
import { gaEvent } from "../../classes/website-statistics/website-statistics";
import { $BillDetailMv } from "./bill-detail-mv";
import styled from "styled-components";





@withRouter
@observer
class BillFlowSummary extends React.Component<any, any> {
    @autowired($BillDetailMv)
    public $myMv: $BillDetailMv;

    constructor(props) {
        super(props);
        this.state = ({
        });
    }

    public componentDidMount() {
        document.title = "单据明细";
        gaEvent("单据明细");
        this.$myMv.clearBillFlowSummary()
        const { relatedDocNo, relatedDocType } = this.props.location.state || {}
        this.$myMv.queryBillFlowSummary({ relatedDocNo, relatedDocType })
    }

    public setItemInfoRender = (info) => {
        const { docType, docNo,feeDocumentType, amount, bizType, payMode, memo, payTime, confirmTime, inOrg, outOrg, orgName, items ,relatedDocNo,relatedDocType,submitTime} = info || {};
        const { orderPartyId } = this.props.location.state || {}
        let itemInfo: any;
        switch (docType) {
            case "StoredAccountRecharge":
                itemInfo = (<ItemInfo>
                    <div>
                        <span>充值单号：{docNo}</span>
                        <span>{amount.toFixed(2)}</span>
                    </div>
                    <div>
                        <div>充值类型：<span>{bizType}</span></div>
                        <div>付款方式：<span>{payMode}</span></div>
                        <div>充值备注：<span>{memo}</span></div>
                        <div>付款时间：<span>{payTime}</span></div>
                        <div>财务确认时间：<span>{confirmTime}</span></div>
                        <div>充值金额：<span>{amount}</span></div>
                    </div>
                </ItemInfo>);
                break;
            case "DeliveryOrder":
                itemInfo = (<ItemInfo>
                    <div>
                        <span>发货单号：{docNo}</span>
                        <span>{amount.toFixed(2)}</span>
                    </div>
                    <div>
                        <div>发货时间：<span>{payTime}</span></div>
                    </div>
                </ItemInfo>);
                break;
            case "CreditAccountRepayment":
                itemInfo = (<ItemInfo>
                    <div>
                        <span>还款单号：{docNo}</span>
                        <span>{amount.toFixed(2)}</span>
                    </div>
                    <div>
                        <div>付款方式：<span>{payMode}</span></div>
                        <div>还款备注：<span>{memo}</span></div>
                        <div>付款时间：<span>{payTime}</span></div>
                        <div>财务确认时间：<span>{confirmTime}</span></div>
                    </div>
                </ItemInfo>);
                break;
            case "StoredAccountTransfer":
                itemInfo = (<ItemInfo>
                    <div>
                        <span>转账单号：{docNo}</span>
                        <span>{amount.toFixed(2)}</span>
                    </div>
                    <div>
                        <div>转出门店：<span>{outOrg}</span></div>
                        <div>转入门店：<span>{inOrg}</span></div>
                        <div>转账时间：<span>{payTime}</span></div>
                    </div>
                </ItemInfo>);
                break;
            case "RefundDocuments":
                itemInfo = (<ItemInfo>
                    <div>
                        <span>退款单号：{docNo}</span>
                        <span>{amount.toFixed(2)}</span>
                    </div>
                    <div>
                        <div>退款类型：<span>{bizType}</span></div>
                        <div>退款时间：<span>{payTime}</span></div>
                       {relatedDocType === "SalesOrder" && <div>关联订单：<span>{relatedDocNo}</span></div>} 
                       {relatedDocType === "RefundOrder" && <div>关联退货单：<span>{relatedDocNo}</span></div>}
                       {relatedDocType === "FeeDocument" && <div>关联费用单：<span>{relatedDocNo}</span></div>} 
                    </div>
                </ItemInfo>);
                break;
            case "DeductDocuments":
                itemInfo = (<ItemInfo>
                    <div>
                        <span>扣款单号：{docNo}</span>
                        <span>{amount.toFixed(2)}</span>
                    </div>
                    <div>
                        <div>扣款类型：<span>{bizType}</span></div>
                        <div>扣款备注：<span>{memo}</span></div>
                        <div>申请时间：<span>{payTime}</span></div>
                        <div>生效时间：<span>{confirmTime}</span></div>
                    </div>
                </ItemInfo>);
                break;
            case "StoredAccountRechargePutForward":
                itemInfo = (<ItemInfo>
                    <div>
                        <span>提现单号：{docNo}</span>
                        <span>{amount.toFixed(2)}</span>
                    </div>
                    <div>
                        <div>提现备注：<span>{memo}</span></div>
                        <div>申请时间：<span>{payTime}</span></div>
                        <div>生效时间：<span>{confirmTime}</span></div>
                    </div>
                </ItemInfo>);
                break;
            case "FeeDocument":
                itemInfo = (<ItemInfo>
                    <div>
                        <span>费用单号：{docNo}</span>
                        <span>{amount.toFixed(2)}</span>
                    </div>
                    <div>
                        <div>费用项目：<span>{feeDocumentType}</span></div>
                    </div>
                </ItemInfo>);
                break;
            case "OrderPayment":
                itemInfo = (
                    <MergeOrderPayment>
                        <div className="docTypeName">合并付款信息</div>
                        <ItemInfo>
                            <div>
                                <span>合并付款单号：{docNo}</span>
                                <span>{amount.toFixed(2)}</span>
                            </div>
                            <div>
                                <div>付款时间：<span>{payTime}</span></div>
                                <div>确认收款时间：<span>{confirmTime}</span></div>
                                {payMode && <div>付款方式：<span>{payMode}</span></div>}
                            </div>
                        </ItemInfo>
                        <div className="docTypeName">付款子单</div>
                        {items.length > 0 && items.map(item => {
                            const {  orgName, payItem, payTime, docNo,amount,relatedDocNo,orgCode } = item;
                            return (
                                <ItemInfo isHighlight={orderPartyId == orgCode} >
                                    <div>
                                        <span>关联单号：{relatedDocNo}</span>
                                        <span>{amount.toFixed(2)}</span>
                                    </div>
                                    <div>
                                        <div>门店信息：<span>{orgName}</span></div>
                                        <div>付款项目：<span>{payItem}</span></div>
                                       {payTime && <div>订货时间：<span>{payTime}</span></div>}
                                        <div>子付款单号：<span>{docNo}</span></div>
                                    </div>
                                </ItemInfo>
                            )
                        })
                        }
                    </MergeOrderPayment>)
                break;
            default:
                itemInfo = null;
                break;
        }
        return itemInfo;
    }

    public render() {
        const { isSpinSummary, billFlowSummary } = this.$myMv;
        return (
            <DetailWapper>
                <Spin spinning={isSpinSummary}>
                    {this.setItemInfoRender(billFlowSummary)}
                </Spin>
            </DetailWapper>)

    }
}


export default BillFlowSummary;

const DetailWapper = styled.div`
    &{
        background: #F7F7F7;
        width:100%;
        height:${document.documentElement.clientHeight}px;
        padding:12px;
    }
`;
const ItemInfo = styled.div`
    &{
        margin-bottom:12px;
        background: url("https://kk-hosting.oss-cn-shanghai.aliyuncs.com/kingkong/order_shop/img/img_bg_returned_status.png") no-repeat;
        background:${(props) => props.isHighlight ? "url('https://kk-hosting.oss-cn-shanghai.aliyuncs.com/kingkong/order_shop/img/img_bg_returned_status.png') no-repeat;" : "#fff" };  
        /* background-size:cover; */
        >div{
            padding:0 14px;
            /* background-color:#fff; */
            &:first-of-type{
                height:44px;
                display:flex;
                align-items:center;
                justify-content:space-between;
                border-bottom:1px solid #eee;
                >span:first-of-type{
                    font-size: 14px;
                    font-family: PingFangSC-Medium, PingFang SC;
                    font-weight: 500;
                    color: #333333;
                }
                >span:last-of-type{
                    font-size: 14px;
                    font-family: Arial-BoldMT, Arial;
                    font-weight: normal;
                    color: #FF3636;
                }
            }
            &:last-of-type{
                padding:6px 14px;
                >div{
                    margin:12px 0;
                    font-size: 13px;
                    font-family: PingFangSC-Regular, PingFang SC;
                    font-weight: 400;
                    color: #666666;
                    >span{
                        font-size: 13px;
                        font-family: PingFangSC-Medium, PingFang SC;
                        font-weight: 500;
                        color: #333333;
                    }
                }
            }
        }
    }
`
const MergeOrderPayment = styled.div`
    &{
        .docTypeName{
            height:40px;
            line-height:40px;
            font-size: 14px;
            font-family: PingFangSC-Medium, PingFang SC;
            font-weight: 500;
            color: #333333;
        }
    }
`