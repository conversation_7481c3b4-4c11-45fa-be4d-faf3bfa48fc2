import * as React from "react";
import { observer } from "mobx-react";
import styled from "styled-components";
import { autowired } from "@classes/ioc/ioc";
import { Spin } from "antd";
import { Toast } from "antd-mobile";
import { transaction } from "mobx";
import { $BillDetailMv } from "./bill-detail-mv";
import { DatePickers } from "../../components/date-picker/date-picker";
import { $AppStore, AppStoreKey } from "../../classes/stores/app-store-mv";
import moment from "moment";
import { debounce } from "lodash";
import { SITE_PATH } from "../app";

@observer
class BillTurnover extends React.Component<any, any> {
  @autowired($AppStore)
  public $AppStore: $AppStore;
  @autowired($BillDetailMv)
  public myMv: $BillDetailMv;

  public constructor(props) {
    super(props);
    this.state = {
      isShowDatePicker: false,
    };
  }
  // 离开记录滚动高度
  public saveMV = () => {
    this.myMv.tableScrollHeight = $(".tableContent").scrollTop();
    this.$AppStore.savePageMv(AppStoreKey.BILLTURNOVER, this.myMv);
  }


  public componentDidMount() {
    document.title = "账单流水";
    setTimeout(() => {
      const oldData = this.$AppStore.getPageMv(AppStoreKey.BILLTURNOVER);
      if (oldData) {
        this.myMv.queryOldData(JSON.parse(oldData));
        this.$AppStore.clearPageMv(AppStoreKey.BILLTURNOVER);
        $(".tableContent").scrollTop(this.myMv.tableScrollHeight);
      } else {
        this.myMv.clearBillTurnover();
        if (this.props.location) {
          const { orgId, beginDate, endDate, orgName } = this.props.location.state || {}
          this.myMv.orgId = orgId;
          this.myMv.beginDate = beginDate;
          this.myMv.endDate = endDate;
          this.myMv.orgName = orgName;
        }
        this.loadData();
      }
    }, 50);
    document.querySelector(".tableContent").addEventListener('scroll', debounce(this.getScroll, 300), false)//监听表格数据部分scroll变化
  }


  public componentWillUnmount() {
    this.saveMV();
    document.querySelector(".tableContent").removeEventListener('scroll', debounce(this.getScroll, 300));
  }


  public loadData = () => {
    const { finished } = this.myMv;
    if (finished) {
      return;
    }
    this.BillTurnoverQuery();
  }

  public BillTurnoverQuery = () => {
    const { orgId, beginDate, endDate, pageIndex, pageSize, mvBalanceAmount, mvPaymentBalanceAmount } = this.myMv;
    console.log("看看时间是个啥",beginDate, endDate,typeof(endDate))
    const params = {
      oid: orgId,
      beginDate:  moment(beginDate).format("YYYY-MM-DD"),
      endDate: moment(endDate).format("YYYY-MM-DD") ,
      pageIndex,
      pageSize,
      balanceAmount: mvBalanceAmount,
      paymentBalanceAmount: mvPaymentBalanceAmount
    };
    this.myMv.isSpinTurnover = true;
    this.myMv.queryBillTurnover(params).then((data: any) => {
      const { totalCount, items = [], balanceAmount, paymentBalanceAmount } = data || [];
      this.myMv.isSpinTurnover = false;
      transaction(() => {
        this.myMv.itemCount = totalCount;
        this.myMv.mvBalanceAmount = balanceAmount;
        this.myMv.mvPaymentBalanceAmount = paymentBalanceAmount;
        if (items && items.length >= 0) {
          this.myMv.billTurnoverList = pageIndex ? this.myMv.billTurnoverList.concat(items) : items;
          this.myMv.changePage(pageIndex + 1);
          // console.log("pageIndex*pageSize > totalCount",this.myMv.pageIndex*pageSize)
          if (this.myMv.billTurnoverList.length >= totalCount || this.myMv.pageIndex * pageSize > totalCount) {
            this.myMv.finished = true;
          }
        }
      });
    }).catch((err) => {
      this.myMv.isSpinTurnover = false;
      Toast.fail(err.response.body.message);
    });
  }

  public datePickerConfirm = (startDateTime, endDateTime) => {
    if (moment(endDateTime).diff(moment(startDateTime), "days") > 180) {
      Toast.fail("一次查询不能超过180天")
      return
    }
    this.setState({ isShowDatePicker: false, });
    this.myMv.beginDate = startDateTime;
    this.myMv.endDate = endDateTime;
    this.myMv.changePage(0);
    this.myMv.billTurnoverList = [];
    this.myMv.mvBalanceAmount = 0;
    this.myMv.mvPaymentBalanceAmount = 0;
    this.myMv.finished = false;
    this.loadData();
  }

  public getScroll = () => {
    let scrollTop = $(".tableContent")[0].scrollTop;
    let clientHeight = $(".tableContent")[0].clientHeight;
    let scrollHeight = $(".tableContent")[0].scrollHeight;
    if (scrollHeight - scrollTop <= clientHeight) {
      this.loadData()
    }
  }

  public goBillFlowSummary = (relatedDocNo, relatedDocType) => {
    if (!relatedDocNo) { return }
    const { orderPartyId } = this.props.location.state || {}
    this.props.history.push({
      pathname: `/${SITE_PATH}/bill-flow-summary`,
      state: {
        relatedDocNo,
        relatedDocType,
        orderPartyId
      }
    });
  }

  public render() {
    const { billTurnoverList, isSpinTurnover, finished, beginDate, endDate, orgName } = this.myMv;
    const { isShowDatePicker } = this.state;
    return (
      <OweGoodsSelectWrapper>
        <Spin spinning={isSpinTurnover}>
          <div className="report-table-wrap">
            <div className="searchHearder">
              <span><i className="scmIconfont scm-mendian1" />&nbsp;{orgName.length > 10 ? orgName.substring(0, 9) + "..." : orgName}</span>
                <span onClick={() => this.setState({ isShowDatePicker: true })}>{moment(beginDate).format("YYYY.MM.DD")} ~ {moment(endDate).format("YYYY.MM.DD")} <i className="scmIconfont scm-shaixuan" /></span>
            </div>

            <ReportTable>
              <div className="tableBody">
                <div>
                  <div className="tableHeader">
                    <span>日期</span>
                    <span>摘要</span>
                    <span>余额变更</span>
                    <span>余额</span>
                    <span>已付款未发货额变动</span>
                    <span>已付款未发货余额</span>
                  </div>
                </div>
                <div className="tableContent">
                  {billTurnoverList.length > 0 ?
                    billTurnoverList.map((item, index) => {
                      const { relatedDocNo, relatedDocObjectTypeValue, createdOn, relatedSummary, changeBalance, balanceAmount, changePaymentBalance, paymentBalanceAmount } = item
                      return (
                        <div key={relatedDocNo} className="tableRow" style={{ background: index % 2 == 0 ? "#fff" : "#F6F6F6" }}>
                          <span>{createdOn}</span>
                          <span className="goDetail" style={{ color: relatedDocNo ? "#437DF0" : "" }} onClick={() => this.goBillFlowSummary(relatedDocNo, relatedDocObjectTypeValue)}>{relatedSummary}</span>
                          <span>{changeBalance.toFixed(2)}</span>
                          <span>{balanceAmount.toFixed(2)}</span>
                          <span>{changePaymentBalance.toFixed(2)}</span>
                          <span>{paymentBalanceAmount.toFixed(2)}</span>
                        </div>
                      )
                    }) :
                    <NoGoodsWrapper className="noGoods">
                      {/* <img src="https://order.fwh1988.cn:14501/static-img/scm/ico_default.png" alt=""/> */}
                      <Title>暂无数据</Title>
                    </NoGoodsWrapper>
                  }
                  {finished && billTurnoverList.length > 0 && <div className="finished">没有更多了～</div>}
                </div>
              </div>
            </ReportTable>
          </div>
        </Spin>
        {isShowDatePicker && <DatePickers
          visible={isShowDatePicker}
          type={"range"}
          beginDate={beginDate}
          endDate={endDate}
          onCancel={() => this.setState({ isShowDatePicker: false })}
          onConfirm={this.datePickerConfirm}
        />}

      </OweGoodsSelectWrapper>
    );
  }
}

export default BillTurnover;

const OweGoodsSelectWrapper = styled.div`// styled
  & {
    width: 100%;
    height: auto;
    background-color: #fff;
    min-height: 100vh;
    .searchHearder{
      width:100%;
      height:56px;
      padding:0 14px;
      background-color: #fff;
      align-items:center;
      display:flex;
      justify-content:space-between;
      position: fixed;
      top:0;
      >span{
        &:first-of-type{
          font-size: 14px;
          font-family: PingFangSC-Medium, PingFang SC;
          font-weight: 500;
          color: #333333;
        }
        &:last-of-type{
          font-size: 13px;
          font-family: PingFangSC-Regular, PingFang SC;
          font-weight: 400;
          color: #333333;
          .scmIconfont{
            font-size:12px;
          }
        }
      }
    }
  }
`;
const ReportTable = styled.div`
  &{
    margin-top:56px;
    width:100%;
    overflow-x:scroll;
    padding-bottom:16px;
    .tableBody >div{
      display:inline-block;
      >div{
        display:flex;
        justify-content:space-around;
        align-items:center;
        padding:0 14px;
        >span{
          display:inline-block;
          width:110px;
          padding:8px 0;
          text-align:right;
          margin:0 6px;
          font-size: 11px;
          font-family: PingFangSC-Regular, PingFang SC;
          font-weight: 400;
          &:nth-of-type(1){
            text-align:left;
            width:70px;
          }
          &:nth-of-type(2){
            text-align:left;
          }
        }
        /* .goDetail{
          color: #437DF0;
        } */
      }
      .tableHeader{
        height: 38px;
        background: #F6F6F6;
        box-shadow: 0px 0px 0px 0px rgba(165, 165, 165, 0.5);
        border-bottom:1px solid #eee;
        font-size: 12px;
        font-family: PingFangSC-Medium, PingFang SC;
        font-weight: 500;
        color: #333333;
      }
    }
    .tableContent{
        height:${document.documentElement.clientHeight - 56 - 38 - 30}px;
        overflow-y:scroll;
        padding-bottom:10px;
    }
    .finished{
      height:30px;
      text-align:center;
      line-height:30px;
      font-size: 11px;
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: 400;
      color: #333333;
    }
  }
`;
const Title = styled.h1`// styled
  & {
    font-size: 14px !important;
    color: #999999;
    margin:60px 0;
  }
`;
const NoGoodsWrapper = styled.div`// styled
  & {
    width:500px;
    height:auto;
    text-align:center !important;
    img{
      width: 13%;
      margin:0 auto;
    }
  }
`;