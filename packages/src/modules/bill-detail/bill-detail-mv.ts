import { autowired, bean } from "@classes/ioc/ioc";
import { action, observable } from "mobx";
import { beanMapper } from "../../helpers/bean-helpers";
import { $BillService } from "../../classes/service/$bill-service";

@bean($BillDetailMv)
export class $BillDetailMv {
  @autowired($BillService)
  public $BillService: $BillService;

  @observable public billDetailInfo:any = {};
  @observable public isSpin:boolean = false;

  @observable public billTurnoverList:any[] = [];//账单流水数据
  @observable public tableScrollHeight:any = 0;//账单流水数据
  @observable public finished:boolean = false;
  @observable public isSpinTurnover:boolean = false;
  @observable public pageIndex:number = 0;  
  @observable public pageSize:number = 50;
  @observable public orgId:string = "";//账单id
  @observable public orgName:string = "";
  @observable public beginDate:any = "";
  @observable public endDate:any = "";
  @observable public itemCount:number = 0;
  @observable public mvBalanceAmount:number = 0;
  @observable public mvPaymentBalanceAmount:number = 0;

  @observable public isSpinSummary:boolean = false;
  @observable public billFlowSummary:any = {};//账单明细数据



 
  @action 
    public queryBillDetail = (params) => {
      this.isSpin = true;
       return this.$BillService.queryBillDetail(params).then(res => {
        this.isSpin = false;
           this.billDetailInfo = res;
       }).catch(err =>  this.isSpin = false)
  }
  @action 
  public queryBillTurnover = (params) => {
      return this.$BillService.queryBillTurnover(params)
  }
  @action 
  public queryBillFlowSummary = (params) => {
    this.isSpinSummary = true;
      return this.$BillService.queryBillFlowSummary(params).then(res => {
        this.isSpinSummary = false;
        console.log("queryBillFlowSummary",res)
        this.billFlowSummary = res;
      }).catch(err =>  this.isSpinSummary = false)
  }
    // 确认
    @action
    public confirmBill(params) {
      return this.$BillService.confirmBill(params)
    }

  @action
  public changePage(pageIndex) {
    this.pageIndex = pageIndex;
  }
  @action
  public showSpin() {
    this.isSpinTurnover = true;
  }

  @action
  public hideSpin() {
    this.isSpinTurnover = false;
  }
  @action
  public queryOldData(obj) {
    beanMapper(obj, this);
  }

  @action
  public clearBillTurnover() {
      this.orgId = '';
      this.pageIndex = 0;
      this.billTurnoverList = [];
      this.tableScrollHeight = 0;
      this.itemCount = 0;
      this.finished = false;
      this.isSpinTurnover = false;
      this.beginDate = "";
      this.endDate = "";
      this.orgName = "";
      this.mvBalanceAmount = 0;
      this.mvPaymentBalanceAmount = 0;
  }
  @action clearBillFlowSummary(){
      this.isSpinSummary = false;
      this.billDetailInfo = {}
  }
}
