import { autowired } from "@classes/ioc/ioc";
import { message, Spin } from "antd";
import { observer } from "mobx-react";
import * as React from "react";
import { withRouter } from "react-router";
import styled from "styled-components";
import { NoGoods } from "../../components/no-goods/no-goods";
import { SITE_PATH } from "../app";
import { $CartMv } from "../shop-cart/cart-mv";
import { $ExpenseNodeCombinedPaymentMV } from "./expense-node-combined-payment-mv";
import { SingleExpenseOrderInfo } from "../../components/single-expense-order-info/single-expense-order-info";
import { ScrollAbilityWrapComponent } from "../../components/scroll-ability/wrap";
import { $AppStore, AppStoreKey } from "../../classes/stores/app-store-mv";
import { Checkbox} from "antd-mobile";
import { cloneDeep } from "lodash";
import { Toast } from "antd-mobile/es";
const CheckboxItem = Checkbox.CheckboxItem;
declare let window: any;

@withRouter
@observer
class ExpenseNodeCombinedPayment extends React.Component<any, any> {
  @autowired($CartMv)
  public $CartMv: $CartMv;
  @autowired($AppStore)
  public $AppStore: $AppStore;
  @autowired($ExpenseNodeCombinedPaymentMV)
  public $myMv: $ExpenseNodeCombinedPaymentMV;

  constructor(props) {
    super(props);
    this.state = ({
    });
  }

  public componentWillUnmount(): void {

  }
  public componentDidMount() {
    document.title = "合并付款费用单列表";
    if (this.props.location.state) {
      const { searchText } = this.props.location.state;
      const params = { searchText };
      this.$myMv.showSpin();
      this.$myMv.getList(params);
    }
  }


  public pageWindowSkip = (url) => {
    setTimeout(() => {
      window.location.href = url;
    }, 50);
  }
  public orderButton = (docId, canPartialPay) => {
    this.$AppStore.clearPageMv(AppStoreKey.SINGLEPAYMENTSTORAGE);
    this.pageWindowSkip(`/${SITE_PATH}/submit/order-payment/:record/${docId}/:showPaymentBtn?feeDocumentId=${docId}&canPartialPay=${canPartialPay}`);
  }
  public goToOrderDetail = (docId) => {
    console.log("orderId", docId);
    this.props.history.push({
      pathname: `/${SITE_PATH}/expense-node-detail/${docId}`,
    });
  }

 
public goPayment = (IdList) => {
    // const params = {
    //   docType:"FeeDocument",
    //   feeDocumentIds:cloneDeep(IdList)
    // }
    if(IdList.length <= 0){
      Toast.info("请选择需要合并单费用单")
      return;
    }

    this.$AppStore.clearPageMv(AppStoreKey.ALLORDERPAYMENT);
    window.location.href = `/${SITE_PATH}/shop/all-order-payment?payRange=All&comeBack=true&docType=FeeDocument`;
    localStorage.setItem("combined-payment-feeDocumentIds", JSON.stringify(cloneDeep(IdList)));


    // this.props.history.replace({
    //   pathname:  `/${SITE_PATH}/shop/all-order-payment/?payRange=All&comeBack=true`,
    //   state:{
    //     docType:"FeeDocument",
    //     feeDocumentIds:cloneDeep(IdList)
    //   }
    // });

}

public cancelCombined = () => {
  this.props.history.push({
    pathname: `/${SITE_PATH}/expense-node-list/Payment/null`,
  });
}

  public render() {
    const {  orderListInfo, isSpin, orgId,checkAll,checkOne,allSelected,allChecked,TotalAmount,getOrdersParams} = this.$myMv;
    console.log("this.$myMv", this.$myMv);
    return (
      <PageWrap className="page-wrap">
        <Spin spinning={isSpin}>
        {
             <SearchTypeBar>
              <span onClick = {() => this.cancelCombined()}>取消合并</span>
            </SearchTypeBar>
          }
          <OrderLists
            className={"orderList"}
            theme={{orgId}}
          >
            {
              orderListInfo ? orderListInfo.length > 0 ?
                orderListInfo.map((order, index) => {
                  return <div
                    key={order.docId}
                    onClick={() => this.goToOrderDetail(order.docId)}
                  >
                    <SingleExpenseOrderInfo
                      order={order}
                      index={index}
                      checkOne={checkOne}
                      orderButton={this.orderButton}
                      showCheckItem={true}
                    />
                    <MarginBottom/>
                  </div>;
                })
                : <NoGoods title="暂无费用单" height={document.documentElement.clientHeight - 90}/> :
                <NoGoods title="暂无费用单" height={document.documentElement.clientHeight - 90}/>
            }
          </OrderLists>
          {
            orderListInfo ? orderListInfo.length > 0 ? <ChooseAll>
              <CheckboxItem
                onChange={(e) => checkAll(e.target.checked)}
                checked={allChecked || allSelected}
              />
              <p>全选</p>
              <p>
                
                <span onClick={()=> this.goPayment(getOrdersParams) }>合并付款</span>
                
              </p>
              <p>
                <span>总计：<span>{Number(TotalAmount).toFixed(2)}</span></span><br/>
                <span>合并订单{getOrdersParams.length}单</span>
              </p>
            </ChooseAll> : null : null
          }
        </Spin>
      </PageWrap>
    );
  }
}

const ExpenseNodeCombPayment = ScrollAbilityWrapComponent(ExpenseNodeCombinedPayment);
export default ExpenseNodeCombPayment;

const SearchTypeBar = styled.div`// styled
  & {
    width: 100%;
    height: 32px;
    background-color: #fff;
    position: fixed;
    top: 0px;
    z-index: 99;
    padding: 0 16px;
    > span {
      display: inline-block;
      height: 100%;
      line-height: 32px;
      font-family: "PingFangSC-Regular";
      font-size: 14px;
      color: #1890FF;
      position:absolute;
      right:16px;
    }
  }
`;
const PageWrap = styled.div`// styled
  & {
    width: 100%;
    min-height: calc(${document.documentElement.clientHeight}px);
    height: auto;
    background-color: #f5f5f5;
    color: #2a2a2a;
    > .page-wrap span {
      text-align: center;
    }
  }
`;

const MarginBottom = styled.div`// styled
  & {
    width: 100%;
    height: 10px;
    background: #f5f5f5;
  }
`;



const OrderLists = styled.div`// styled
  & {
    width: 100%;
    /*height: 600px;*/
    padding-top:36px;
    padding-bottom:50px;
    // overflow-y: auto;
  }
`;

const ChooseAll = styled.div`// styled
  & {
    width: 100%;
    height: 48px;
    background-color: #fff;
    padding: 5px 12px 6px 16px;
    position: fixed;
    bottom: 0;
    z-index: 99;
    .am-list-item {
      display: inline-block;
      padding-left: 0;
      position: relative;
      top: 9px;
    }
    .am-list-item .am-list-thumb:first-child {
      margin-right: 8px;
    }
    > p {
      display: inline-block;
      margin-bottom: 0;
    }
    > p:nth-of-type(1) {
      font-family: "MicrosoftYaHei";
      font-size: 11px;
      color: #333333;
    }
    > p:nth-of-type(2) {
      float: right;
      > span {
        display: inline-block;
        font-family: "MicrosoftYaHei";
        font-size: 14px;
        height: 36px;
        line-height: 36px;
        color: #FFFFFF;
        background: #307DCD;
        border-radius: 20px;
        padding: 0 16px;
      }
    }
    > p:nth-of-type(3) {
      float: right;
      text-align: right;
      margin-right: 10px;
      > span:nth-of-type(1) {
        font-family: "MicrosoftYaHei";
        font-size: 11px;
        color: #333333;
        > span {
          font-family: "MicrosoftYaHei";
          font-size: 13px;
          color: #FF3030;
        }
      }
      > span:nth-of-type(2) {
        font-family: "MicrosoftYaHei";
        font-size: 9px;
        color: #666666;
        display: inline-block;
        height: 17px;
        line-height: 17px;
      }
    }
  }
`;
