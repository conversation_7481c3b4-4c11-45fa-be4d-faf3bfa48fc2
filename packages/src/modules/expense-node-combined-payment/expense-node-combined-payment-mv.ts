import { autowired, bean } from "@classes/ioc/ioc";
import { action, observable,computed } from "mobx";
import { $OrderService } from "../../classes/service/$order-service";
import { beanMapper } from "../../helpers/bean-helpers";
import { sum } from "lodash";
@bean($ExpenseNodeCombinedPaymentMV)
export class $ExpenseNodeCombinedPaymentMV {
  @autowired($OrderService)
  public $orderService: $OrderService;

  @observable public orgId = null;
  @observable public showOrderPop = false;
  @observable public orderListInfo = [];
  @observable public ordersParams = [];
  @observable public commonPayment:any = [];
  @observable public isSpin: boolean = false;
  @observable public allChecked: boolean = false;

  @action
  public showSpin() {
    this.isSpin = true;
  }

  @action
  public hideSpin() {
    this.isSpin = false;
  }

  @action
  public getList(params) {
    return this.$orderService.batchFilterFeedocumentList(params).then(res => {
      const { feeDocumentList } = res;
      console.log("res----res",res.feeDocumentList)
      if(feeDocumentList.length > 0){
        this.buildFeedocumentList(feeDocumentList)
      } 
       this.hideSpin()
    });
  }
  //构建列表数据，增加checked
  @action
  public buildFeedocumentList = (list) => {
    let newOrderList = []
       list.map(item => {
        newOrderList.push({...item,checked:false,disabled:false})
      })
      this.orderListInfo = newOrderList;
  }
  //全选操作
  @action checkAll = (checked) => {
    if (checked) { // 全选勾选
      console.log("all-------checked",checked)
      // 将第一个订单赋值给公共部分
      this.commonPayment = [];
      this.orderListInfo.map((info, index) => {
        if (!index) {
          this.commonPayment = info.paymentModeList;
          info.checked = true;
          info.disabled = false;
        } else {
          info.checked = this.compareArrIsEqual(this.commonPayment, info.paymentModeList);
          info.disabled = !this.compareArrIsEqual(this.commonPayment, info.paymentModeList);
        }
      });
    } else { // 全选取消
      this.commonPayment = [];
      this.orderListInfo.map((info) => {
        info.checked = checked;
        info.disabled = false;
      });
    }
    this.allChecked = checked;
  }
  //单选操作
  @action
  public checkOne = (check, docId, key) => {
    console.log(check, docId, key, this.commonPayment.length);
    this.allChecked = false;
    let selectedNumber = 0;
    this.orderListInfo.map((info) => {
      if (info.docId === docId) {
        info.checked = !check ? check : !this.commonPayment.length ? true : this.compareArrIsEqual(this.commonPayment, info.paymentModeList);
      }
      if (info.checked) {
        selectedNumber += 1
        this.commonPayment = info.paymentModeList;
      }
    });
    this.orderListInfo.map((info) => {
      if (selectedNumber === 0) {
        info.disabled = false;
        this.commonPayment = [];
      } else {
        info.disabled = this.commonPayment ? !this.compareArrIsEqual(this.commonPayment, info.paymentModeList) : false;
      }
    })
  }

  @action
  public compareArrIsEqual(a = [], b = []) {
    // if (a.length > 0 && b.length > 0 && a.length === b.length) {
    if (a.length > 0 && b.length > 0 && a.length === b.length) {
      return a.every((item) => {
        return b.filter((res) => res.oid === item.oid).length === 1;
      });
    } else if (a.length === 0 && b.length === 0) {
      return true;
    } else {
      return false;
    }
  }

  @action
  public queryOldData(obj) {
    beanMapper(obj, this);
    console.log(this);
  }
  @action
  public clearMVData() {
    this.showOrderPop = false;
    this.orderListInfo = [];
    this.isSpin = false;
  }
  
  //全选状态触发
  @computed
  get allSelected() {
    return this.orderListInfo.filter((order) => order.checked).length === this.orderListInfo.length;
  }

  //计算待付款总金额
  @computed
  get TotalAmount(){
    return sum(this.orderListInfo.map(item => item.checked ? Number(item.unPayableAmount) : 0))
  }
  //获取已选单据信息
  @computed
  get getOrdersParams(){
   return this.orderListInfo.filter(item => item.checked).map(item => item.docId);
  }
}
