import { autowired } from "@classes/ioc/ioc";
import { observer } from "mobx-react";
import React from "react";
import { withRout<PERSON> } from "react-router";
import styled from "styled-components";
import { $NewModeDebtAccountRecordMv } from "./new-mode-debt-account-record-mv";
import { Spin } from "antd";
import { ScrollAbilityWrapComponent } from "../../components/scroll-ability/wrap";
import { $AppStore } from "../../classes/stores/app-store-mv";
import { $InOutType } from "@classes/const/$in-out-type";
import moment from "moment";
import { LoadingTip } from "../../components/loading-marked-words";

const IconCounter = require("../../components/svg/icon_counter.svg");
const IconLegal = require("../../components/svg/icon_legal.svg");

@withRouter
@observer
class NewModeDebtAccountRecordWrap extends React.Component<any, any> {

  @autowired($NewModeDebtAccountRecordMv)
  public $myMv: $NewModeDebtAccountRecordMv;

  @autowired($AppStore)
  public $AppStore: $AppStore;

  public constructor(props) {
    super(props);
    this.state = {};
  }

  public componentDidMount() {
    document.title = "新模式信用";
    this.loadData(false);
  }

  public componentWillReceiveProps(nextProps: Readonly<any>, nextContext: any): void {
    const { isScrollEnd } = nextProps;
    if (isScrollEnd && isScrollEnd !== this.props.isScrollEnd) {
      this.loadData(true);
    }
  }

  public loadData = (isConcat: boolean) => {
    const { isFinished, isLoading, pageIndex } = this.$myMv;
    const { loadingEnd } = this.props;
    if (!isFinished) {
      this.$myMv.showSpin();
      if (!isLoading) {
        this.$myMv.pageIndex = isConcat ? pageIndex + 1 : 0;
        this.$myMv.fetchAccountRecord(isConcat);
        loadingEnd && loadingEnd(false);
      }
    } else {
      loadingEnd && loadingEnd(true);
    }
  }

  public getItemTitle = (item, index) => {
    const { creditList } = this.$myMv;
    const currentDate = moment(item.date);
    const currentMonth = currentDate.month() + 1; // moment的月份从0开始

    // 检查是否是第一个项目
    if (index === 0) {
      return (
        <div className="list-item-title">
          <div className="default-space" />
          <div className="month-display">{currentMonth}月</div>
          <div className="default-line" style={{ height: "30%" }} />
        </div>
      );
    }

    // 获取前一个项目的日期信息
    const prevDate = moment(creditList[index - 1].date);
    const prevMonth = prevDate.month() + 1;

    // 检查是否是新月份
    if (currentMonth !== prevMonth) {
      // 检查是否是最后一个项目
      if (index === creditList.length - 1) {
        return (
          <div className="list-item-title">
            <div className="default-line" style={{ height: "30%" }} />
            <div className="month-display">{currentMonth}月</div>
            <div className="default-space" />
          </div>
        );
      } else {
        return (
          <div className="list-item-title">
            <div className="default-line" style={{ height: "30%" }} />
            <div className="month-display">{currentMonth}月</div>
            <div className="default-line" style={{ height: "30%" }} />
          </div>
        );
      }
    }

    // 相同月份显示省略号
    return (
      <div className="list-item-title">
        <div className="default-line" style={{ height: "100%" }} />
      </div>
    );
  }

  public render() {
    const { isLoading, isFinished, creditAmount, creditList, orgName, representativeName, representativeLevel } = this.$myMv;

    return (
      <Spin spinning={isLoading} >
        <Wrapper>
          <SHead>
            <div className="head-item">
              <div className="head-title">
                <IconCounter />
                <div className="head-text">门店</div>
              </div>
              <div>
                {orgName}
              </div>
            </div>
            <div className="head-item">
              <div className="head-title">
                <IconLegal />
                <div className="head-text">所属法人</div>
              </div>
              <div>
                {representativeName}
                {
                  representativeLevel && <span className="head-level">{representativeLevel}</span>
                }
              </div>
            </div>
          </SHead>
          <SCredit>
            <div className="credit-title">新模式信用额度</div>
            <div className="credit-num">{creditAmount}</div>
            <div className="credit-content">
              <i className={"scmIconfont scm-modal-tishi"} />
              <div>从25年7月起，新模式信用额度与门店销售额、分账率、加盟商级别挂钩，每月4日更新1次额度。</div>
            </div>
          </SCredit>

          <div className="list-title">额度变化记录</div>
          <div className="list-content">
            {
              creditList && creditList.length > 0 && creditList.map((item, index) => {
                const currentDate = moment(item.date);
                const currentYear = currentDate.year();
                const prevDate = index >= 1 ? moment(creditList[index - 1].date) : null;
                const prevYear = prevDate ? prevDate.year() : null;
                if (index >= 1 && currentYear !== prevYear) {
                  return (
                    <>
                      <div className="list-item" key={item.adjustId}>
                        <div className="list-item-title">
                          <div className="default-line" style={{ height: "25%" }} />
                          <div className="year-divider">{currentYear}年</div>
                          <div className="default-line" style={{ height: "25%" }} />
                        </div>
                        <div className="list-item-year" />
                      </div>
                      <div className="list-item" key={index}>
                        {this.getItemTitle(item, index)}
                        <div className="list-item-content">
                          <div>
                            <div>{item.label}</div>
                            <div>{moment(item.date).format("MM-DD")}</div>
                          </div>
                          <div>
                            <div className={"color-black"}>
                              {item.inOutType === $InOutType.ADD ? "+" : "-"}{item.amount}
                              {
                                item.isJobAdjust && <i className={"scmIconfont scm-icon-jiantou-you"} />
                              }
                            </div>
                            <div>更新后额度为{item.afterAmount}</div>
                          </div>
                        </div>
                      </div>
                    </>
                  );
                } else {
                  return <div className="list-item" key={index}>
                    {this.getItemTitle(item, index)}
                    <div className="list-item-content">
                      <div>
                        <div>{item.label}</div>
                        <div>{moment(item.date).format("MM-DD")}</div>
                      </div>
                      <div>
                        <div className={"color-black"}>
                          {item.inOutType === $InOutType.ADD ? "+" : "-"}{item.amount}
                          {
                            item.isJobAdjust && <i className={"scmIconfont scm-icon-jiantou-you"} />
                          }
                        </div>
                        <div>更新后额度为{item.afterAmount}</div>
                      </div>
                    </div>
                  </div>;
                }
              })
            }
            {
              creditList && creditList.length > 0 ?
                <LoadingTip
                  isFinished={isFinished}
                  isLoad={isLoading}
                /> : null
            }
          </div>
        </Wrapper>
      </Spin>
    );
  }
}

const NewModeDebtAccountRecord = ScrollAbilityWrapComponent(NewModeDebtAccountRecordWrap);

export default NewModeDebtAccountRecord;

const Wrapper = styled.div`// styled
  & {
    width: 100%;
    min-height: 100vh;
    padding: 12px;
    display: flex;
    flex-direction: column;
    align-items: center;
    background: #F7F7F7;

    .list-title {
      width: 100%;
      padding-top: 16px;
      font-size: 16px;
      font-weight: 500;
      color: #333;
      text-align: left;
    }

    .list-content {
      width: 100%;
      margin-top: 8px;
      border-radius: 4px;
      padding-bottom: 24px;
      margin-bottom: 24px;

      .list-item {
        display: flex;
        justify-content: space-between;

        .list-item-year {
          height: 60px;
        }

        .list-item-title {
          width: 20%;
          color: #666666;
          font-size: 16px;
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: space-between;

          .default-space {
            width: 2px;
            height: 30%;
          }

          .default-line {
            width: 2px;
            border: #D8D8D8 dashed 1px;
          }

          .year-divider {
            height: 30%;
            font-size: 16px;
            font-weight: 500;
            color: #b9bac6;
          }

          .month-display {
            height: 30%;
            font-size: 16px;
            font-weight: 500;
            color: #9b9b9b;
          }

          .month-dots {
            font-size: 18px;
            color: #999;
            line-height: 1;
            margin-bottom: 4px;
          }

          .month-ellipsis {
            font-size: 16px;
            color: #CCC;
            letter-spacing: 2px;
          }
        }

        .list-item-content {
          margin: 6px 0;
          background: #FFF;
          display: flex;
          justify-content: space-between;
          align-items: center;
          width: 80%;
          border-radius: 4px;
          padding: 12px;
          box-shadow: 0 1px 3px rgba(0,0,0,0.1);

          > div:nth-child(1) {
            > div:nth-child(1) {
              color: #333;
              font-size: 14px;
              font-weight: 500;
            }

            > div:nth-child(2) {
              margin-top: 4px;
              font-size: 12px;
              color: #999;
            }
          }

          > div:nth-child(2) {
            display: flex;
            flex-direction: column;
            align-items: flex-end;
            justify-content: center;

            > div:nth-child(1) {
              font-size: 18px;
              font-weight: 500;
            }

            .color-red {
              color: #FF3030;
            }

            .color-black {
              color: #333;

              > i {
                color: #999;
              }
            }

            > div:nth-child(2) {
              font-size: 12px;
              color: #999;
              margin-top: 4px;
            }
          }
        }
      }

      .list-item:last-child {
        border-bottom: none;
      }
    }
  }
`;

const SHead = styled.div`
  & {
    width: 100%;
    padding: 12px;
    color: #FFF;
    background: linear-gradient(91deg, #437DF0 0%, #80AAFF 99%);
    border-radius: 4px;

    .head-item {
      display: flex;
      justify-content: space-between;

      .head-title {
        display: flex;
        align-items: center;
        width: 25%;
        justify-content: flex-start;

        .head-text {
          margin-left: 4px;
          font-size: 14px;
        }
      }
    }

    .head-item:nth-child(2) {
      margin-top: 8px;
    }

    .head-level {
      background: #F0F4FC;
      padding: 2px 8px;
      border-radius: 8px;
      font-size: 10px;
      color: #437DF0;
      margin-left: 6px;
    }
  }
`;

const SCredit = styled.div`
  & {
    width: 100%;
    padding: 12px;
    margin-top: 12px;
    background: #FFF;
    border-radius: 8px;
    display: flex;
    flex-direction: column;

    .credit-title {
      font-size: 12px;
      color: #666;
      line-height: 18px;
    }

    .credit-num {
      color: #FF3030;
      font-weight: 500;
      font-size: 32px;
    }

    .credit-content {
      display: flex;
      color: #999999;
      font-size: 12px;

      & > i {
        margin-right: 6px;
      }

      & > div {
        line-height: 24px;
      }
    }
  }
`;
