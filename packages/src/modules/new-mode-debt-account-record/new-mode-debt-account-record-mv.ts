import { autowired, bean } from "@classes/ioc/ioc";
import { action, observable } from "mobx";
import { $ComponentService } from "../../classes/service/$component-service";
import { $MyInfoService } from "@classes/service/$my-info-service";
import { $CreditItemInfo } from "@classes/entity/$credit-item-info";

@bean($NewModeDebtAccountRecordMv)
export class $NewModeDebtAccountRecordMv {

  @autowired($MyInfoService)
  public $myInfoService: $MyInfoService;

  @autowired($ComponentService)
  public $componentService: $ComponentService;

  @observable public isFinished: boolean = false;

  @observable public isLoading: boolean = false;

  @observable public pageIndex: number = 0;

  @observable public pageSize: number = 10;

  @observable public creditAmount: number = 0; // 授信额度

  @observable public creditList: $CreditItemInfo[] = [];

  @observable public orgName: string = ''; // 机构名称

  @observable public representativeName: string = ''; // 法人姓名

  @observable public representativeLevel: string = ''; // 法人级别

  @action
  public showSpin() {
    this.isLoading = true;
  }

  @action
  public hideSpin() {
    this.isLoading = false;
  }

  @action
  public fetchAccountRecord(isConcat?) {
    const params = {
      pageIndex: this.pageIndex,
      pageSize: this.pageSize,
    }
    this.$myInfoService.loadCreditAdjust(params)
      .then((res) => {
        this.hideSpin();
        if (isConcat) {
          this.creditList = this.creditList.concat(res.itemList);
        } else {
          this.creditList = res.itemList;
        }
        this.orgName = res.orgName || '';
        this.representativeName = res.representativeName || '';
        this.representativeLevel = res.representativeLevel || '';
        this.creditAmount = res.totalCreditAmount || 0;
        console.log('first', this.creditList.length >= res.itemCount)
        this.isFinished = this.creditList.length >= res.itemCount;
      })
  }

  @action
  public clearMVData() {
    this.isFinished = false;
    this.isLoading = false;
    this.creditAmount = 0;
    this.creditList = [];
    this.pageIndex = 0;
    this.pageSize = 10;
  }
}
