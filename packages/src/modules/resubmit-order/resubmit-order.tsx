import { autowired } from "@classes/ioc/ioc";
import { Button, Checkbox, DatePicker, ImagePicker, List, Progress } from "antd-mobile";
import { Toast } from "antd-mobile/es";
import { merge } from "lodash";
import { observer } from "mobx-react";
import React from "react";
import { withRouter } from "react-router";
import styled from "styled-components";
import { $CartType } from "../../classes/const/$cart-type";
import { REQUEST_SERVER } from "../../helpers/ajax-helpers";
import { $ConfirmOrderMv } from "../confirm-order/confirm-order-Mv";
import { $SubmitOrderPaymentMv } from "../submit-order-payment/submit-order-payment-mv";
import { $SubmitOrderMv } from "../submit-order/submit-order-mv";
import DateUtils from "../../classes/utils/DateUtils";

declare let require: any;
declare let window: any;

const Item = List.Item;
const Brief = Item.Brief;
const CheckboxItem = Checkbox.CheckboxItem;
const now = new Date();

@withRouter
@observer
class ResubmitOrder extends React.Component<any, any> {

  @autowired($ConfirmOrderMv)
  public $ConfirmOrderMv: $ConfirmOrderMv;

  @autowired($SubmitOrderMv)
  public $SubmitOrderMv: $SubmitOrderMv;

  @autowired($SubmitOrderPaymentMv)
  public $submitOrderPaymentMv: $SubmitOrderPaymentMv;

  constructor(props) {
    super(props);
    this.state = {
      isShowMabel: false,
      amount: "",
      date: null,
      percent: 0,
      showProgress: false,
    };
  }

  public componentDidMount() {
    this.setState({
      amount: this.props.match.params.amount,
      date: this.props.location.state ? new Date(this.props.location.state.paymentTime) : null,
    });
  }

  public onSubmit = () => {
    const { amount, date } = this.state;
    // console.log(typeof amount, typeof this.props.match.params.amount);
    /*if (parseFloat(amount) > parseFloat(this.props.match.params.amount)) {
      Toast.info("不可大于应付金额", 3);
      return;
    }*/
    if (date === null) {
      Toast.info("请选择正确的付款日期，财务将根据提交的日期核对银行流水", 5);
      return;
    }
    const { pics } = this.$ConfirmOrderMv;
    const { paymentInfo } = this.$submitOrderPaymentMv;
    const params = {
      orderPaymentId: Number(this.props.match.params.paymentModeId),
      sellerAccountId: paymentInfo.capitalAccountInfo.capitalAccountId,
      voucherImages: pics,
      amount,
      paymentTime: DateUtils.toStringFormat(date, "yyyy-MM-dd HH:mm"),
    };
    this.$ConfirmOrderMv.saveReconfirmOrder(params).then((data) => {
      if (data.result) {
        Toast.info("提交成功", 3);
        setTimeout(() => {
          history.back();
        }, 3000);
      } else {
        Toast.info("提交失败", 3);
      }
    });
  }

  public setDefaultCheck = (v, index) => {
    const { capitalAccountList, paymentInfo } = this.$submitOrderPaymentMv;
    capitalAccountList.forEach((item) => {
      item.isDefault = "N";
    });
    capitalAccountList[index].isDefault = "Y";
    paymentInfo.capitalAccountInfo.bankName = capitalAccountList[index].bankName;
    paymentInfo.capitalAccountInfo.accountCode = capitalAccountList[index].accountCode;
    paymentInfo.capitalAccountInfo.bankAccountName = capitalAccountList[index].bankAccountName;
    paymentInfo.capitalAccountInfo.capitalAccountId = capitalAccountList[index].capitalAccountId;
    this.setState({ isShowMabel: false });
  }

  public showAccountMabel = () => {
    this.setState({
      isShowMabel: true,
    });
  }

  public uploadImage = (file: any) => {
    const formData = new FormData();
    formData.append("file", file.file);
    if (file.file.size / 1024 > 20480) {
      Toast.info("图片大小不能超过20MB", 3);
      return;
    }
    const xhr = new XMLHttpRequest();

    xhr.open("POST", `${REQUEST_SERVER}/k/integration/scm/payvoucher/upload`, true);
    xhr.setRequestHeader("ssoSessionId", localStorage.getItem("token"));
    xhr.onload = () => {
      if (xhr.status === 200) {
        const url = JSON.parse(xhr.responseText).data.data.pic.url;
        const _file = merge({}, file.file, {
          url,
          thumbUrl: url,
          uid: JSON.parse(xhr.responseText).data.data.pic.uid,
        });
        this.addFile(_file);
      }

    };
    xhr.upload.addEventListener("progress", this.OnProgRess, false);
    xhr.send(formData);
  }

  public addFile = (file) => {
    const { pics } = this.$ConfirmOrderMv;
    const fileList = pics.slice();
    fileList.push(file);
    this.$ConfirmOrderMv.setPics(fileList);
    this.setState({ showProgress: false });
  }

  public onUpload = (files, type, index) => {
    if (type === "add") {
      this.setState({ files }, () => this.uploadImage(files[files.length - 1]));
    } else if (type === "remove") {
      this.$ConfirmOrderMv.setPics(files);
    }
  }

  public OnProgRess = (e) => {
    // console.log(e);
    const { pics } = this.$ConfirmOrderMv;
    if (pics) {
      if (pics.length === 0) {
        // console.log(pics.length);
        document.getElementsByClassName("am-progress-outer")[0].style.left = `8%`;
      }
      if (pics.length === 1) {
        // console.log(pics.length);
        document.getElementsByClassName("am-progress-outer")[0].style.left = `32%`;
        // console.log(document.getElementsByClassName("am-progress-outer")[0].style.left);
      }
      if (pics.length === 2) {
        // console.log(pics.length);
        document.getElementsByClassName("am-progress-outer")[0].style.left = `56%`;
        // console.log(document.getElementsByClassName("am-progress-outer")[0].style.left);
      }
      if (pics.length === 3) {
        // console.log(pics.length);
        document.getElementsByClassName("am-progress-outer")[0].style.left = `80%`;
        // console.log(document.getElementsByClassName("am-progress-outer")[0].style.left);
      }
      if (pics.length === 4) {
        // console.log(pics.length);
        document.getElementsByClassName("am-progress-outer")[0].style.left = `8%`;
        document.getElementsByClassName("am-progress-outer")[0].style.top = `68%`;
        // console.log(document.getElementsByClassName("am-progress-outer")[0].style.left);
      }
      if (pics.length === 5) {
        // console.log(pics.length);
        document.getElementsByClassName("am-progress-outer")[0].style.left = `32%`;
        document.getElementsByClassName("am-progress-outer")[0].style.top = `68%`;
        // console.log(document.getElementsByClassName("am-progress-outer")[0].style.left);
      }
      if (pics.length === 6) {
        // console.log(pics.length);
        document.getElementsByClassName("am-progress-outer")[0].style.left = `56%`;
        document.getElementsByClassName("am-progress-outer")[0].style.top = `68%`;
        // console.log(document.getElementsByClassName("am-progress-outer")[0].style.left);
      }
      if (pics.length === 7) {
        // console.log(pics.length);
        document.getElementsByClassName("am-progress-outer")[0].style.left = `80%`;
        document.getElementsByClassName("am-progress-outer")[0].style.top = `68%`;
        //document.getElementsByClassName("am-progress-outer")[0].style.top = `47%`;
        // console.log(document.getElementsByClassName("am-progress-outer")[0].style.left);
      }
      if (pics.length === 8) {
        // console.log(pics.length);
        document.getElementsByClassName("am-progress-outer")[0].style.left = `8%`;
        document.getElementsByClassName("am-progress-outer")[0].style.top = `77%`;
        // console.log(document.getElementsByClassName("am-progress-outer")[0].style.left);
      }
      if (pics.length === 9) {
        // console.log(pics.length);
        document.getElementsByClassName("am-progress-outer")[0].style.left = `32%`;
        document.getElementsByClassName("am-progress-outer")[0].style.top = `77%`;
        // console.log(document.getElementsByClassName("am-progress-outer")[0].style.left);
      }
    }
    const loaded = Math.floor(100 * (e.loaded / e.total));
    this.setState({ percent: loaded, showProgress: true });
  }

  public changeInput = (e) => {
    this.setState({
      amount: e.target.value,
    });
  }

  public render() {
    const { isShowMabel, date, percent, showProgress } = this.state;
    const { pics } = this.$ConfirmOrderMv;
    const { capitalAccountList, paymentInfo } = this.$submitOrderPaymentMv;
    const { capitalAccountInfo } = paymentInfo;
    const { bankName, bankAccountName, accountCode } = capitalAccountInfo;
    const { amount } = this.state;
    // console.log(amount);
    return (
      <Wrapper>
        <List className="bank">
          <Item
            arrow="horizontal"
            onClick={this.showAccountMabel}
          >
            <p style={{ marginTop: 5 }}>
              <span>{bankName}&nbsp;&nbsp;</span>
              <span>{accountCode}</span>
            </p>
            <p>
              <span>开户人&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
              <span>{bankAccountName}</span>
            </p>
            <Brief/>
          </Item>
        </List>
        <div className="my-list">
          <span>转账金额</span>
          <span>¥<input type="number" value={amount} onChange={(e) => {
            this.changeInput(e);
          }}/></span>
        </div>
        <PaymentDate>
          <p>
            付款日期
            <span>（银行转账时间）</span>
          </p>
          <div className="transferDate">
            <DatePicker
              value={date}
              onChange={(val) => this.setState({ date: val })}
              maxDate={new Date()}
            >
                      <span className="paymentTime">
                        {date ? DateUtils.toStringFormat(date, "yyyy-MM-dd HH:mm") : "请选择付款日期"}
                        <i className="scmIconfont scm-icon-calendar"/>
                      </span>
            </DatePicker>
          </div>
        </PaymentDate>
        <PaymentVoucher style={{ height: "auto", marginBottom: "70px" }}>
          <p>
            付款凭证
          </p>
          <div>
            <ImagePicker
              files={pics}
              selectable={pics.length < 10}
              onChange={this.onUpload}/>
            <Progress percent={percent} style={{ display: showProgress ? "block" : "none" }}/>
          </div>
        </PaymentVoucher>
        <ConfirmButton>
          <Button type="primary" onClick={this.onSubmit}>重新提交</Button>
        </ConfirmButton>
        <Mabel style={{ display: isShowMabel ? "block" : "none" }}>
          {
            capitalAccountList ? capitalAccountList.length > 0 ?
              capitalAccountList.map((capitalAccount, index) => {
                return (
                  <div>
                    {
                      capitalAccount.isDefault === $CartType.ISDEFAULE ?
                        <div>
                          <List className="capitalAccountDefault">
                            <CheckboxItem
                              onChange={(v) => this.setDefaultCheck(v, index)}
                              checked={capitalAccount.isDefault === $CartType.ISDEFAULE ? true : false}
                            >
                              <p>
                                <span>{capitalAccount.bankName}</span>
                                <span>默认</span>
                              </p>
                              <p>
                                {capitalAccount.bankAccountName ? capitalAccount.bankAccountName.length > 6 ? capitalAccount.bankAccountName.slice(0, 6) : capitalAccount.bankAccountName : null} {capitalAccount.accountCode}
                              </p>
                            </CheckboxItem>
                          </List>
                          <img className="border-img" src={require("../../components/assets/ico-colorline.png")}
                               alt=""/>
                        </div>
                        : <List className="capitalAccount" key={index}>
                          <CheckboxItem
                            onChange={(v) => this.setDefaultCheck(v, index)}
                            checked={capitalAccount.isDefault === $CartType.ISDEFAULE ? true : false}
                          >
                            <p>
                              <span>{capitalAccount.bankName}</span>
                            </p>
                            <p>
                              {capitalAccount.bankAccountName ? capitalAccount.bankAccountName.length > 6 ? capitalAccount.bankAccountName.slice(0, 6) : capitalAccount.bankAccountName : null} {capitalAccount.accountCode}
                            </p>
                          </CheckboxItem>
                        </List>
                    }
                  </div>
                );
              })
              : null : null
          }
        </Mabel>
      </Wrapper>
    )
      ;
  }
}

export default ResubmitOrder;

const Wrapper = styled.div`// styled
  & {
    background: #F2F2F2;
    color: #333;
    .my-list {
      height: 40px;
      line-height: 40px;
      background: white;
      span:first-child {
        padding-left: 15px;
        float: left;
      }
      span:last-child {
        float: right;
        padding-right: 15px;
        color: #ff4242;
        input {
          border: none;
          border-bottom: 1px solid #d8d8d8;
          height: 30px;
          line-height: 30px;
          width: 80px;
          text-align: center;
        }
      }
    }
    .am-list-content {
      font-size: 15px;
      color: #333;
    }
    .am-list-content p:first-child {
      color: #333333;
      font-size: 14px;
    }
    .am-list-content p:last-child {
      color: #999999;
      font-size: 12px;
    }
    .border-img {
      width: 100%;
      height: auto;
      margin-top: -22px;
    }
    .my-list {
      margin-top: 10px;
      &.not-mt {
        margin-top: 0px;
      }
    }
    .text-red .am-list-line .am-list-extra {
      color: #FF4242;
    }
    .am-list-item .am-list-line .am-list-content {
      font-size: 14px;
      color: #333;
    }
    .bank .am-list-line .am-list-content {
      > p {
        margin-bottom: 0;
      }
      > p span {
        font-size: 14px;
        color: #333;
      }
      > p span:first-child {
        display: inline-block;
        width: 100px;
      }
      > p span:last-child {
        color: #999999;
      }
    }
  }
`;

const Mabel = styled.div`// styled
  & {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 120%;
    background: #F2F2F2;
    z-index: 100;
    .capitalAccountDefault .am-list-content p:first-child span:last-child {
      display: inline-block;
      width: 28px;
      height: 16px;
      background: #ECF6FF;
      color: #307DCD;
      text-align: center;
      font-size: 10px;
      margin-left: 10px;
    }
    .capitalAccount {
      margin-bottom: 20px;
    }
  }
`;

const PaymentDate = styled.div`// styled
  & {
    margin-top: 10px;
    width: 100%;
    height: 130px;
    background: #fff;
    > p {
      height: 40px;
      line-height: 40px;
      border-bottom: 1px solid #ddd;
      padding-left: 15px;
      > span {
        color: #999;
        font-size: 14px;
      }
    }
    > div {
      padding: 15px;
    }
    > .transferDate {
      > div {
        margin: 0px auto;
        > .paymentTime {
          padding: 7px 10px 7px 15px;
          text-align: center;
          border: 1px solid #D8D8D8;
          border-radius: 3px;
          > .scm-icon-calendar:before {
            color: #307dcd;
            padding-left: 20px;
          }
        }
      }
    }
  }
`;

const PaymentVoucher = styled.div`// styled
  & {
    margin-top: 10px;
    width: 100%;
    height: 200px;
    background: #fff;
    .am-progress-outer {
      position: absolute;
      top: 43%;
      left: 10%;
      width: 50px;
      border-radius: 5px;
    }
    .am-progress-bar {
      border: 2px solid #307dcd;
      border-radius: 5px;
    }
    > p {
      height: 40px;
      line-height: 40px;
      border-bottom: 1px solid #ddd;
      padding-left: 15px;
    }
    > div {
      padding: 0 5px 10px 5px;
      position: relative;
    }
  }
`;

const ConfirmButton = styled.div`// styled
  & {
    padding: 15px;
    width: 100%;
    position: fixed;
    bottom: 20px;
  }
`;
