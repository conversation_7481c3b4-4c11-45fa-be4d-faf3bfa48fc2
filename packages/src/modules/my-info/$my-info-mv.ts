import { autowired, bean } from "@classes/ioc/ioc";
import { action, observable } from "mobx";
import { $ComponentService } from "../../classes/service/$component-service";
import { $MyInfoService } from "../../classes/service/$my-info-service";
import { $AccountType } from "../../classes/const/$account-type";

@bean($MyInfoMv)
export class $MyInfoMv {
  @autowired($ComponentService)
  public $componentService: $ComponentService;

  @autowired($MyInfoService)
  public $myInfoService: $MyInfoService;

  @observable public name: string = "";

  @observable public account: string = "";

  @observable public orderPartyId: string = "";

  @observable public orderPartyName: string = "";

  @observable public user: object;

  @observable public priceAccountList: any = [];

  @observable public items: any;

  @observable public coupon: object;

  @observable public isSpin: boolean = false;

  @observable public menushowformycenter: any[] = [];

  @action
  public showSpin() {
    this.isSpin = true;
  }

  @action
  public hideSpin() {
    this.isSpin = false;
  }

  @action
  public fetchInfo() {
    return this.$componentService.queryCurUser().then((data) => {
      const { user, accountList, coupon } = data;
      this.user = user;
      const accountListNew = [];
      accountList.map((item, index) => {
        switch (item.accountTypeCode) {
          case $AccountType.BALANCE:
            item.name = "余额账户";
            break;
          case $AccountType.FLZH:
            item.name = "返利账户(停用)";
            break;
          case $AccountType.CREDIT:
            item.name = "欠款账户";
            break;
          case $AccountType.ADVANCE:
            item.name = "欠数金额";
            break;
          case $AccountType.DIVIDEND_REBATE_DISCOUNT_LIMIT:
            item.name = "分账优惠额度";
            break;
          case $AccountType.DIVIDEND_REBATE:
            item.name = "分账返利账户";
            break;
          case $AccountType.DEDUCTION_LIMIT:
            item.name = "返利账户";
            break;
          case $AccountType.RETURN_FREE_DISTRIBUTION:
            item.name = "返配赠账户";
            break;
          default:
            // item.name = "";
            break;
        }
        accountListNew.push(item);
      });
      this.priceAccountList = accountListNew;
      this.coupon = coupon;
      this.hideSpin();
    });
  }

  @action
  public getInformation() {  // 获取消息通知
    return this.$myInfoService.queryInformation().then((data) => {
      const { items } = data;
      this.items = items;
    });
  }

  @action
  public setFormData(name, account, orderPartyId, orderPartyName) {
    this.name = name;
    this.account = account;
    this.orderPartyId = orderPartyId;
    this.orderPartyName = orderPartyName;
  }

  @action
  public getFormData() {
    return this;
  }

  @action
  public fetchMyInfo() {  // 获取用户信息
    return this.$componentService.queryCurUser().then((data) => {
      this.name = data.name;
      this.account = data.account;
    });
  }

  @action
  public setOrderPartyId(orderPartyId) {
    this.orderPartyId = orderPartyId;
  }

  @action
  public setOrderPartyName(orderPartyName) {
    this.orderPartyName = orderPartyName;
  }

  @action
  public ordermallMenushowformycenter() {
    this.$myInfoService.ordermallMenushowformycenter().then((data) => {
      this.menushowformycenter = data;
    });
  }
}
