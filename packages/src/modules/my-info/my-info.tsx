import { autowired } from "@classes/ioc/ioc";
import { Spin } from "antd";
import { List } from "antd-mobile";
import { observer } from "mobx-react";
import React from "react";
import { withRouter } from "react-router";
import styled from "styled-components";
import { $ActiveType } from "../../classes/const/$active-type";
import { $ComponentService } from "../../classes/service/$component-service";
import { Footer } from "../../components/footer/footer";
import { SITE_PATH } from "../app";
// import "../common.less";
import "../index.less";
import { $OrderPartySelectionMV } from "../order-party-selection/$order-party-selection-mv";
import { $CartMv } from "../shop-cart/cart-mv";
import { $MyInfoMv } from "./$my-info-mv";
import { $AccountType } from "../../classes/const/$account-type";
import { gaEvent } from "../../classes/website-statistics/website-statistics";
import { $AppStore, AppStoreKey } from "../../classes/stores/app-store-mv";

const Item = List.Item;
declare let window: any;

@withRouter
@observer
class MyInfo extends React.Component<any, any> {
  @autowired($AppStore)
  public $AppStore: $AppStore;
  @autowired($OrderPartySelectionMV)
  public $orderPartySelectionMV: $OrderPartySelectionMV;

  @autowired($MyInfoMv)
  public $myInfoMV: $MyInfoMv;

  @autowired($CartMv)
  public $CartMv: $CartMv;

  @autowired($ComponentService)
  public $componentService: $ComponentService;

  public componentDidMount() {
    document.title = "我的门店";
    gaEvent("我的门店");
    this.initPage();
  }

  public initPage = () => {
    this.$myInfoMV.showSpin();
    this.$myInfoMV.fetchInfo();
    this.$myInfoMV.getInformation();
    this.$myInfoMV.ordermallMenushowformycenter();
    if (this.props.location.search === "") {
      this.$CartMv.fetchShopcartproductnum();
    } else {
      this.$CartMv.setIsAgency();
    }
  }

  // 进入收货地址
  public toAddress = () => {
    this.$AppStore.clearPageMv(AppStoreKey.ADDRESSLIST);
    this.props.history.push({ pathname: `/${SITE_PATH}/shippingAddress` });
  }

  // 进入退货单列表
  public toReturnedList = () => {
    this.$AppStore.clearPageMv(AppStoreKey.RETURNEDSALES);
    this.props.history.push({
      pathname: `/${SITE_PATH}/returned-sales`,
      state: {
        orgId: `${this.$myInfoMV.user.orderPartyId}`,
        orderPartyName: `${this.$myInfoMV.user.orderPartyName}`,
      },
    });
  }

  public goToScheme = () => {
    // this.props.history.push({ pathname: `/${SITE_PATH}/select` });
    sessionStorage.setItem("editOrderId", null);
    this.$AppStore.clearPageMv(AppStoreKey.ORDERPARTYSELECTION);
    window.location.href = `/${SITE_PATH}/select`;
  }

  public accountInfo = (accountId, accountTypeCode) => {
    if (accountTypeCode === $AccountType.CREDIT) {
      this.$AppStore.clearPageMv(AppStoreKey.ACCOUNTINFOLIST);
      this.props.history.push({ pathname: `/${SITE_PATH}/debt-account/${accountId}` });
      // this.props.history.push({ pathname: `/${SITE_PATH}/account-info-list/${accountId}/${accountTypeCode}` });
    } else if (accountTypeCode === $AccountType.DEDUCTION_LIMIT) {
      this.$AppStore.clearPageMv(AppStoreKey.ACCOUNTINFO);
      this.props.history.push({ pathname: `/${SITE_PATH}/new-account-info/${accountId}/${accountTypeCode}` });
    } else if (accountTypeCode === $AccountType.RETURN_FREE_DISTRIBUTION) {
      this.$AppStore.clearPageMv(AppStoreKey.ACCOUNTINFO);
      this.props.history.push({ pathname: `/${SITE_PATH}/gift-account-info/${this.$myInfoMV.user.orderPartyId}` });
    } else {
      this.$AppStore.clearPageMv(AppStoreKey.ACCOUNTINFO);
      this.props.history.push({ pathname: `/${SITE_PATH}/account-info/${accountId}/${accountTypeCode}` });
    }
  }

  public toExchangeGoods = () => {
    this.$AppStore.clearPageMv(AppStoreKey.EXCHANGEGOODSLIST);
    this.props.history.push({ pathname: `/${SITE_PATH}/exchange-goods-list` });
  }

  public goToMessageNotification = () => {
    this.props.history.push({ pathname: `/${SITE_PATH}/message-notification` });
  }

  public renderIcon = (type) => {
    let icon = null;
    switch (type) {
      case $AccountType.MYBANKCARD:
        icon = "scmIconfont scm-yinhangka iconColor";
        break;
      default:
        break;
    }
    return icon;
  }

  public goToPage = (type) => {
    console.log("type", type);
    switch (type) {
      case $AccountType.MYBANKCARD:
        this.$AppStore.clearPageMv(AppStoreKey.MYBANKLIST);
        this.props.history.push({
          pathname: `/${SITE_PATH}/my-bank-list`,
        });
        break;
      case $AccountType.EXPENSENODE:
        this.props.history.push({
          pathname: `/${SITE_PATH}/expense-node-list/All/${this.$myInfoMV.user.orderPartyId}`,
        });
        break;
      case $AccountType.BILLLIST:
        this.$AppStore.clearPageMv(AppStoreKey.BILLLIST);
        this.props.history.push({ pathname: `/${SITE_PATH}/bill-list/?orderPartyId=${this.$myInfoMV.user.orderPartyId}` });
        break;
      default:
        break;
    }
  }
  public toCoupon = () => {
    this.$AppStore.clearPageMv(AppStoreKey.COUPONLIST);
    window.location.href = `/${SITE_PATH}/coupon`;
  }
  public render() {
    const { user, items, priceAccountList, coupon, isSpin, menushowformycenter } = this.$myInfoMV;
    const { headerImage, name, orderPartyName } = user || {};
    const { couponCount } = coupon || {};
    const { isAgency } = this.$CartMv;
    const rebateDiscountLimit = priceAccountList.filter((item) => item.accountTypeCode === $AccountType.DIVIDEND_REBATE_DISCOUNT_LIMIT);
    const deductionLimit = priceAccountList.filter((item) => item.accountTypeCode === $AccountType.DEDUCTION_LIMIT);
    const returnFreeDistribution = priceAccountList.filter((item) => item.accountTypeCode === $AccountType.RETURN_FREE_DISTRIBUTION);
    return (
      <Wrapper>
        <Spin spinning={isSpin}>
          <NameCardWrapper>
            <NameCard>
              <AvatarWrapper>
                <img src={headerImage} alt="" />
              </AvatarWrapper>
              <InfoWrapper>
                <AccountWrapper>
                  <span>{name}</span><br />
                  <span onClick={this.goToScheme}>
                    {orderPartyName}&nbsp;&nbsp;
                    <i className="scmIconfont scm-icon-arrow-down" style={{ color: "#fff" }} />
                  </span>
                </AccountWrapper>
              </InfoWrapper>
            </NameCard>
          </NameCardWrapper>
          {
            priceAccountList.length > 0 ?
              <AccountAmount>
                <AccountDiv>
                  {
                    priceAccountList.map((item, index) => {
                      if (item.accountTypeCode === $AccountType.RETURN_FREE_DISTRIBUTION || item.accountTypeCode === $AccountType.DEDUCTION_LIMIT) {
                        return (
                          <div key={index} onClick={() => this.accountInfo(item.accountId, item.accountTypeCode)}>
                            <p>¥{item.balanceAmount}
                              {
                                item.isShowLabel === "Y" &&
                                <div className="icon-wrap">
                                  <div className="icon-inner-wrap">
                                    <i className="scmIconfont scm-icon-qipao" />
                                  </div>
                                </div>
                              }
                            </p>
                            <p>
                              {item.name}
                            </p>
                          </div>
                        );
                      } else if (item.accountTypeCode !== $AccountType.DIVIDEND_REBATE_DISCOUNT_LIMIT
                        // item.accountTypeCode !== $AccountType.DEDUCTION_LIMIT &&
                        // item.accountTypeCode !== $AccountType.RETURN_FREE_DISTRIBUTION
                      ) {
                        return (
                          <div key={index} onClick={() => this.accountInfo(item.accountId, item.accountTypeCode)}>
                            <p>¥{item.balanceAmount}
                              {
                                item.isShowLabel === "Y" &&
                                <div className="icon-wrap">
                                  <div className="icon-inner-wrap">
                                    <i className="scmIconfont scm-icon-qipao" />
                                  </div>
                                </div>
                              }
                            </p>
                            <p>
                              {item.name}
                            </p>
                          </div>
                        );
                      }
                    })
                  }
                </AccountDiv>
                {/* {
                  deductionLimit.length > 0 && <DivideAccount>
                    <div className="title" onClick={() => this.accountInfo(deductionLimit[0].accountId, deductionLimit[0].accountTypeCode)}>{deductionLimit[0].accountTypeName}：</div>
                    <div className="content" onClick={() => this.accountInfo(deductionLimit[0].accountId, deductionLimit[0].accountTypeCode)}>{deductionLimit[0].balanceAmount}</div>
                  </DivideAccount>
                } */}
                {/* {
                  returnFreeDistribution.length > 0 && <DivideAccount>
                    <div className="title">{returnFreeDistribution[0].accountTypeName}：</div>
                    <div className="content">{returnFreeDistribution[0].balanceAmount}</div>
                  </DivideAccount>
                } */}
                {
                  rebateDiscountLimit.length > 0 && <DivideAccount>
                    <div className="title" onClick={() => this.accountInfo(rebateDiscountLimit[0].accountId, rebateDiscountLimit[0].accountTypeCode)}>{rebateDiscountLimit[0].name}：</div>
                    <div className="content" onClick={() => this.accountInfo(rebateDiscountLimit[0].accountId, rebateDiscountLimit[0].accountTypeCode)}>{rebateDiscountLimit[0].balanceAmount}</div>
                  </DivideAccount>
                }
              </AccountAmount>
              :
              null
          }
          <List>
            <Item
              arrow="horizontal"
              extra={couponCount}
              onClick={this.toCoupon}
            >
              <span><i className="scmIconfont scm-icon-youhuiquan iconColor" /></span> 我的优惠券
            </Item>
          </List>
          {
            items ? items.map((item) => {
              return (
                (item.id === "billInfo") ?
                  <List>
                    <Item
                      arrow="horizontal"
                      extra={item.count}
                      onClick={() => this.goToPage("BILLLIST")}
                    >
                      <span><i className="scmIconfont scm-zhangdan iconColor" /></span> 我的账单
                    </Item>
                  </List> : null
              )
            }) : null
          }
          {
            items ? items.map((item, index) => {
              return (
                item.id === "fyd" ?
                  <List key={index}>
                    <Item
                      arrow="horizontal"
                      extra={<div><span style={{ fontSize: "12px" }}>待付金额：</span><span style={{ color: "#333" }}>{"¥" + item.amount}</span></div>}
                      onClick={() => this.goToPage("EXPENSENODE")}
                    >
                      <span><i className="scmIconfont scm-feiyongyusuan iconColor" /></span> {item.name}
                    </Item>
                  </List> : null
              );
            }) : null
          }
          <List>
            <Item
              arrow="horizontal"
              onClick={this.toReturnedList}
            >
              <span><i className="scmIconfont scm-icon-returngoods iconColor" /></span> 退货单
            </Item>
          </List>
          <List>
            <Item
              arrow="horizontal"
              onClick={this.toAddress}
            >
              <span><i className="scmIconfont scm-icon-location iconColor" /></span> 收货地址管理
            </Item>
          </List>
          {
            menushowformycenter && menushowformycenter.length > 0 && menushowformycenter.map((menu) => {
              return (
                menu.showButton && <List key={menu.menuCode}>
                  <Item
                    arrow="horizontal"
                    onClick={() => this.goToPage(menu.menuCode)}
                  >
                    <span>
                      <i className={this.renderIcon(menu.menuCode)} />
                    </span> {menu.menuName}
                  </Item>
                </List>
              );
            })
          }
          <List>
            <Item
              arrow="horizontal"
              onClick={this.toExchangeGoods}
            >
              <span><i className="scmIconfont scm-icon-exchange iconColor" /></span> 换货社区
            </Item>
          </List>
          {
            items ? items.map((item, index) => {
              return (
                item.id === "xxtz" ?
                  <List key={index}>
                    <Item
                      arrow="horizontal"
                      extra={item.count}
                      onClick={this.goToMessageNotification}
                    >
                      <span><i className="scmIconfont scm-icon-news iconColor" /></span> {item.name}
                    </Item>
                  </List> : null
              );
            }) : null
          }
        </Spin>
        <Footer isAgency={isAgency} activeKey={$ActiveType.MY_KEY} count={this.$CartMv.totalCount} />
      </Wrapper>
    );
  }
}

export default MyInfo;

const Wrapper = styled.div`// styled
  & {
    width: 100%;
    height: auto;
    background: #F2F2F2;
    padding-bottom: 10px;
    .am-list-item {
      height: 40px;
    }
    .iconColor {
      color: #307DCD;
      font-size: 16px;
      margin-right: 17px;
    }
    .am-list-item .am-list-line .am-list-content {
      font-size: 14px;
      color: #333;
    }
    .am-list-item .am-list-line .am-list-extra {
      font-size: 14px;
      color: #999;
    }
  }
`;

const DivideAccount = styled.div`
  & {
    background-color: #D8ECFF;
    border-radius: 2px;
    font-size: 12px;
    display: flex;
    padding: 10px 12px;

    .title {
      color: #666666;
    }

    .content {
      font-size: 14px;
      color: #FF3636;
      margin-left: 5px;
    }
  }
`

const NameCardWrapper = styled.div`// styled
  & {
    display: block;
    background-color: white;
  }
`;

const NameCard = styled.div`// styled
  & {
    background-color: #5176AC;
    width: 100%;
    display: flex;
    padding: 5px 15px;
  }
`;

const AvatarWrapper = styled.div`// styled
  & {
    display: flex;
    align-items: center;
    background: #F2F2F2;
    border-radius: 5px;
    margin-right: 15px;
    img {
      width: 54px;
      height: 54px;
      border-radius: 5px;
    }
  }
`;

const InfoWrapper = styled.div`// styled
  & {
    display: flex;
    flex: 1;
    flex-direction: column;
    justify-content: space-around;
  }
`;

const TenantWrapper = styled.div`// styled
  & {
    color: #fff;
    font-size: 16px;
    margin-bottom: 16px;
    line-height: 1.5;
  }
`;

const AccountWrapper = styled.div`// styled
  & {
    color: white;
    font-size: 16px;
    line-height: 1.5;
    > span:last-child {
      font-size: 12px;
      color: rgba(255, 255, 255, 0.5);
    }
  }
`;

const AccountAmount = styled.div`
  & {
    display: flex;
    flex-direction: column;
    border-color: #ECF6FF;
    padding: 12px;
    width: 100%;
    position: relative;
  }
`

const AccountDiv = styled.div`// styled
  & {
    padding: 0 0 10px;
    display: flex;
    flex-wrap: wrap;

    > div {
      width: 33%;
      height: 100%;
      margin-top: 15px;
      text-align: center;
      border-right: 1px solid #CDDAE6;
      p {
        margin: 0;
        font-size: 12px;
        color: #666666;
      }
      p:nth-child(1) {
        margin-bottom: 9px;
        font-size: 14px;
        color: #FF3636;
        position: relative;
        .icon-wrap {
          display: inline-block;
          width: 36px;
          height: 16px;
          position: absolute;
          top: -14px;
          right: 0;
          .icon-inner-wrap {
            position: relative;
            width: 36px;
            height: 16px;
            .scm-icon-qipao {
              font-size: 16px;
              line-height: 16px;
              position: absolute;
              top: 0;
              left: 0;
            }
            &:after {
              position: absolute;
              display: inline-block;
              width: 36px;
              height: 16px;
              line-height: 16px;
              top: 0;
              left: 0;
              content: "已逾期";
              color: #fff;
              font-size: 10px;
            }
          }
        }
      }
    }
    > div:nth-child(3n) {
      border: 0;
    }

    > div:last-child {
      border: 0;
    }
  }
`;
