import { autowired } from "@classes/ioc/ioc";
import { Checkbox, List, Modal } from "antd-mobile";
import { observer } from "mobx-react";
import { transaction } from "mobx";
import React from "react";
import { withRouter } from "react-router";
import styled from "styled-components";
import { $AccountInfoMv } from "./account-info-mv";
import { $AccountType } from "../../classes/const/$account-type";
import { NoGoods } from "../../components/no-goods/no-goods";
import { SITE_PATH } from "../app";
import { Toast } from "antd-mobile/es";
import { Spin } from "antd";
import { $CreditType } from "../../classes/const/$credit-type";
import { findIndex, remove } from "lodash";
import { getUrlParam } from "../../classes/utils/UrlUtils";
import { ScrollAbilityWrapComponent } from "../../components/scroll-ability/wrap";
import { LoadingTip } from "../../components/loading-marked-words";
import { $InterfaceErrorCode } from "../../classes/const/$interface-error-code";
import NoAuthority from "../no-authority/no-authority";
import disabledOprate from "../../components/noWX-disabled-operate";
import { $OrderType } from "../../classes/const/$order-type";
import { gaEvent } from "../../classes/website-statistics/website-statistics";
import { $AppStore, AppStoreKey } from "../../classes/stores/app-store-mv";
import { $ValidityType } from "@classes/const/$validity-type";

const alert = Modal.alert;
const Item = List.Item;
const Brief = Item.Brief;
const CheckboxItem = Checkbox.CheckboxItem;
declare let window: any;

@withRouter
@observer
class AccountInfoWrap extends React.Component<any, any> {
  @autowired($AccountInfoMv)
  public $myMv: $AccountInfoMv;
  @autowired($AppStore)
  public $AppStore: $AppStore;
  public constructor(props) {
    super(props);
    this.state = {
    };
  }
  public saveMV = () => {
    this.$myMv.scrollHeight = $(".scroll-ability-wrap").scrollTop();
    this.$AppStore.savePageMv(AppStoreKey.ACCOUNTINFO, this.$myMv);
  }
  // 离开记录滚动高度
  public componentWillUnmount(): void {
    this.saveMV();
  }
  public componentDidMount() {
    this.$myMv.buttonList = [];
    this.$myMv.accountFlowList = [];
    const { accountTypeCode } = this.props.match.params || { accountTypeCode: "" };
    if (accountTypeCode === $AccountType.BALANCE) {
      document.title = "余额账户";
      gaEvent("余额账户");
    } else if (accountTypeCode === $AccountType.FLZH) {
      document.title = "返利账户";
      gaEvent("返利账户");
    } else if (accountTypeCode === $AccountType.DIVIDEND_REBATE_DISCOUNT_LIMIT) {
      document.title = "分账优惠额度账户";
      gaEvent("分账优惠额度账户");
    } else if (accountTypeCode === $AccountType.DIVIDEND_REBATE) {
      document.title = "分账返利账户";
      gaEvent("分账返利账户");
    }
    this.$myMv.clearMVData();
    this.initPage();
  }

  public componentWillReceiveProps(nextProps: Readonly<any>, nextContext: any): void {
    const { isScrollEnd } = nextProps;
    if (isScrollEnd && isScrollEnd !== this.props.isScrollEnd) {
      this.loadData();
    }
  }
  public initPage = () => {
    const orderPartyId = getUrlParam("orderPartyId");
    const { accountTypeCode, accountId } = this.props.match.params || { accountTypeCode: "", accountId: "" };
    const { pageSize, pageIndex, key } = this.$myMv;
    this.$myMv.showSpin();
    this.$myMv.isLoading = true;
    const params = accountTypeCode === $AccountType.CREDIT ? {
      condition: key,
      pageIndex,
      pageSize,
    } : { accountId, condition: key, pageIndex, pageSize };
    if (orderPartyId) {
      this.$myMv.saveShopAndScheme({ orderPartyId }).then((res) => {
        const message = res.errorMessage;
        if (message) {
          Toast.fail(message);
        } else {
          this.firstLoadFetchAccountInfo(params);
        }
      });
    } else {
      this.firstLoadFetchAccountInfo(params);
    }
  }
  public firstLoadFetchAccountInfo = (params) => {
    transaction(() => {
      this.$myMv.fetchAccountInfo(params, false).then((balanceAmount) => {
        const oldData = this.$AppStore.getPageMv(AppStoreKey.ACCOUNTINFO)
        if (oldData) {
          this.$myMv.queryOldData(JSON.parse(oldData));
          this.$myMv.balanceAmount = Number(balanceAmount);
          this.$AppStore.clearPageMv(AppStoreKey.ACCOUNTINFO);
          $(".scroll-ability-wrap").scrollTop(this.$myMv.scrollHeight);
        }
      });
    });
  }
  public queryAccountInfo = (activeKey) => {
    this.$myMv.key = activeKey;
    this.$myMv.isFinished = false;
    this.$myMv.pageIndex = 0;
    this.$myMv.key = activeKey;
    this.initPage();
  }

  public accountFlowDetail = (accountFlowId) => {
    this.props.history.push({ pathname: `/${SITE_PATH}/account-info-detail/${accountFlowId}` });
  }

  public goToRepaymentRecord = () => {
    sessionStorage.setItem("validityType", $ValidityType.REGULAR_CREDIT);
    this.props.history.push({ pathname: `/${SITE_PATH}/repayment-record` });
  }

  public goToRecharge = (key, availableCreditAmount) => {
    if (localStorage.getItem("scanningLogin") === "Y") {
      disabledOprate();
      return;
    }
    const { accountId } = this.props.match.params;
    const { accountTypeCode } = this.props.match.params || { accountTypeCode: "" };
    if (parseFloat(availableCreditAmount) <= 0) {
      Toast.info("无需还款", 3);
      return;
    }
    switch (key) {
      case "recharge":
        sessionStorage.setItem("rechargeStorage", null);
        this.$AppStore.clearPageMv(AppStoreKey.RECHARGESTORAGE);
        this.pageWindowSkip(`/${SITE_PATH}/my-recharge/${accountId}?accountId=${accountId}`);
        break;
      case "transfer":
        this.props.history.push({ pathname: `/${SITE_PATH}/my-transfer/${accountId}/${accountTypeCode}` });
        break;
      case "repayment":
        this.$myMv.showCreditMedal = "block";
        break;
      default:
        break;
    }
  }

  public checkTongLianPaymentStatus = () => {
    const { selectCreditAccountList } = this.$myMv;
    const params = {
      creditAccountList: selectCreditAccountList,
      docType: $OrderType.CREDITACCOUNTREPAYMENT,
    };
    this.$myMv.showSpin();
    this.$myMv.checkTongLianPaymentStatus(params).then((data) => {
      console.log("data", data);
      const { errorCode, errorMsg, docInfo } = data;
      this.$myMv.hideSpin();
      if (errorCode && errorCode !== "0") {
        Toast.fail(errorMsg);
      } else if (docInfo && docInfo.oid) {
        const { oid, code, totalAmount } = docInfo
        alert(`提示`, `你有${totalAmount.toFixed(2)}元正在付款`, [
          {
            text: "不付款了", onPress: () => {
              this.cancelTongLianPay(oid);
            },
          },
          {
            text: "去付款", onPress: () => {
              this.checkContinuePay(oid);
            },
          },
        ]);
      } else {
        // 继续主流程操作
        this.goToRepayment();
      }
    }).catch((err) => {
      console.log("checkTongLianPaymentStatus", err);
      this.$myMv.hideSpin();
    });
  }

  public checkContinuePay = (oid) => {
    this.$myMv.showSpin();
    const params = {
      docType: $OrderType.CREDITACCOUNTREPAYMENT,
      oid,
    }
    this.$myMv.checkContinuePay(params).then((data) => {
      this.$myMv.hideSpin();
      console.log("checkContinuePay", data);
      const { errorCode, errorMessage, redirectUrl } = data;
      if (errorCode && errorCode !== "0") {
        Toast.fail(errorMessage);
      } else if (redirectUrl) {
        window.location.href = `${redirectUrl}&redirectUrl=${document.location.protocol}//${document.domain}:${window.location.port}/scm/payment-result-show/allinpay`;
      } else {
        console.log(data);
      }
    }).catch(() => {
      this.$myMv.hideSpin();
    });
  }

  public cancelTongLianPay = (oid) => {
    const params = {
      docType: $OrderType.CREDITACCOUNTREPAYMENT,
      oid,
    }
    this.$myMv.showSpin();
    this.$myMv.cancelTongLianPay(params).then((data) => {
      this.$myMv.hideSpin();
      console.log("cancelTongLianPay", data);
      const { errorCode, errorMessage } = data;
      if (errorCode && errorCode !== "0") {
        alert(`提示`, "显示接口里的错误信息", [
          {
            text: "我知道了", onPress: () => {
              window.location.reload();
            },
          },
        ]);
      } else {
        window.location.reload();
      }
    }).catch(() => {
      this.$myMv.hideSpin();
    });
  }

  public goToRepayment = () => {
    const { selectSurplusToReturnAmount, selectCreditAccountList } = this.$myMv;
    const { accountId } = this.props.match.params;
    if (selectSurplusToReturnAmount > 0) {
      this.$myMv.showSpin();
      const params = {
        creditAccountList: selectCreditAccountList,
      };
      this.$myMv.queryCreditrepaymentAuditedAmount(params)
        .then((data) => {
          this.$myMv.hideSpin();
          if (data.message) {
            if (selectSurplusToReturnAmount - data.amount <= 0) { // 为了不跳转
              Toast.info(`${data.message}`, 5);
            } else {
              this.$myMv.selectSurplusToReturnAmount -= data.amount;
              Toast.info(`${data.message}`, 5, () => {
                localStorage.setItem("selectCreditAccountList", JSON.stringify(selectCreditAccountList));
                this.$AppStore.clearPageMv(AppStoreKey.REPAYMENTSTORAGE);
                this.pageWindowSkip(`/${SITE_PATH}/my-repayment/${this.$myMv.selectSurplusToReturnAmount && Number(this.$myMv.selectSurplusToReturnAmount).toFixed(2)}?accountId=${accountId}`);
              });
            }
          } else {
            localStorage.setItem("selectCreditAccountList", JSON.stringify(selectCreditAccountList));
            this.$AppStore.clearPageMv(AppStoreKey.REPAYMENTSTORAGE);
            this.pageWindowSkip(`/${SITE_PATH}/my-repayment/${this.$myMv.selectSurplusToReturnAmount && Number(this.$myMv.selectSurplusToReturnAmount).toFixed(2)}?accountId=${accountId}`);
          }
        }).catch(() => {
          this.$myMv.hideSpin();
        });
    } else {
      Toast.info("请选择还款金额", 3);
    }
  }

  public goToCreditList = () => {
    this.props.history.push({
      pathname: `/${SITE_PATH}/credit-list`,
    });
  }

  public goToCreditDetail = (accountId) => {
    this.props.history.push({
      pathname: `/${SITE_PATH}/credit-detail`,
      state: {
        creditId: accountId,
      },
    });
  }

  public onCheckedCredit = (e, accountId, surplusToReturnAmount) => {
    const { selectCreditAccountList } = this.$myMv;
    // console.log(selectCreditAccountList);
    if (e.target.checked) {
      const index = findIndex(this.$myMv.creditAccountList, { accountId });
      this.$myMv.creditAccountList[index].checkFlag = true;
      selectCreditAccountList.push(accountId);
      this.$myMv.selectSurplusToReturnAmount = this.$myMv.selectSurplusToReturnAmount + surplusToReturnAmount;
      // console.log(this.$myMv.selectSurplusToReturnAmount);
    } else {
      const index = findIndex(this.$myMv.creditAccountList, { accountId });
      this.$myMv.creditAccountList[index].checkFlag = false;
      remove(selectCreditAccountList, (val) => val === accountId);
      this.$myMv.selectSurplusToReturnAmount = Number(this.$myMv.selectSurplusToReturnAmount).toFixed(2) - surplusToReturnAmount;

      // console.log(this.$myMv.selectSurplusToReturnAmount);
    }
  }

  public changeAutomaticType = (e) => {
    const { configId } = this.$myMv;
    if (e) {
      this.$myMv.automaticType = "Yes";
      const params = { configId, automaticType: "Yes" };
      this.$myMv.accountAutomaticUpdate(params).then(() => {

      });
    } else {
      this.$myMv.automaticType = "No";
      const params = { configId, automaticType: "No" };
      this.$myMv.accountAutomaticUpdate(params).then(() => {

      });
    }
  }

  public hideModel = () => {
    this.$myMv.showCreditMedal = "none";
  }

  public loadData = () => {
    const { isFinished, isLoading, pageIndex } = this.$myMv;
    if (!isFinished) {
      this.$myMv.showSpin();
      if (!isLoading) {
        this.$myMv.pageIndex = pageIndex + 1;
        this.changeClasses();
      }
    }
  }

  public changeClasses = () => {
    const { accountTypeCode } = this.props.match.params || { accountTypeCode: "" };
    const { pageSize, key, pageIndex } = this.$myMv;
    this.$myMv.showSpin();
    const params = accountTypeCode === $AccountType.CREDIT ? {
      condition: key,
      pageIndex,
      pageSize,
    } : { accountId: this.props.match.params.accountId, condition: key, pageIndex, pageSize };
    this.$myMv.isLoading = true;
    const { loadingEnd } = this.props;
    this.$myMv.fetchAccountInfo(params, true).then(() => {
      loadingEnd && loadingEnd();
    });
    // 发送请求改变 isFinished， isLoading状态
  }
  public pageWindowSkip = (url) => {
    this.saveMV()
    setTimeout(() => {
      window.location.href = url;
    }, 50);
  }
  public goToRechargeReCord = () => {
    this.pageWindowSkip(`/${SITE_PATH}/recharge-record`);
  }

  public render() {
    const { accountTypeCode } = this.props.match.params || { accountTypeCode: "" };
    const { buttonList, accountFlowList, balanceAmount, allSurplusToReturnAmount, effectiveCreditAccount, creditAccountList, selectSurplusToReturnAmount, key, showCreditMedal, isLoading, isFinished, isHavePermissions, isGetPermissions } = this.$myMv;
    return (
      <Spin spinning={isLoading}>
        {
          isGetPermissions ? isHavePermissions ?
            <Wrapper
              className="account-content-info-warp"
              style={{ overflow: accountFlowList ? accountTypeCode !== $AccountType.CREDIT && accountFlowList.length < 1 ? "hidden" : "auto" : null }}>
              {
                accountTypeCode === $AccountType.BALANCE &&
                <div className="recharge-record" onClick={this.goToRechargeReCord}>充值记录</div>
              }
              {
                accountTypeCode === $AccountType.DIVIDEND_REBATE_DISCOUNT_LIMIT ? <AdvanceAccountWrapper>
                  <p>
                    <i className="scmIconfont scm-icon-account" />
                  </p>
                  <p>
                    {balanceAmount}
                  </p>
                  <p>
                    分账优惠额度账户
                  </p>
                  <p>
                    {
                      buttonList ? buttonList.map((btn, index) => {
                        return (
                          <span key={index} onClick={() => this.goToRecharge(btn.key, null)}
                            style={{ margin: buttonList.length === 1 ? 0 : null }}>{btn.value}</span>
                        );
                      }) : null
                    }
                  </p>
                </AdvanceAccountWrapper> : null
              }
              {
                accountTypeCode === $AccountType.DIVIDEND_REBATE ? <AdvanceAccountWrapper>
                  <p>
                    <i className="scmIconfont scm-icon-account" />
                  </p>
                  <p>
                    ¥ {balanceAmount}
                  </p>
                  <p>
                    分账返利账户
                  </p>
                  <p>
                    {
                      buttonList ? buttonList.map((btn, index) => {
                        return (
                          <span key={index} onClick={() => this.goToRecharge(btn.key, null)}
                            style={{ margin: buttonList.length === 1 ? 0 : null }}>{btn.value}</span>
                        );
                      }) : null
                    }
                  </p>
                </AdvanceAccountWrapper> : null
              }
              {
                accountTypeCode === $AccountType.FLZH ? <AdvanceAccountWrapper>
                  {/*// 返利账户*/}
                  <p>
                    <i className="scmIconfont scm-icon-account" />
                  </p>
                  <p>
                    ¥ {balanceAmount}
                  </p>
                  <p>
                    可用返利金额
                  </p>
                  <p>
                    {
                      buttonList ? buttonList.map((btn, index) => {
                        return (
                          <span key={index} onClick={() => this.goToRecharge(btn.key, null)}
                            style={{ margin: buttonList.length === 1 ? 0 : null }}>{btn.value}</span>
                        );
                      }) : null
                    }
                  </p>
                </AdvanceAccountWrapper> : null
              }
              {
                accountTypeCode === $AccountType.BALANCE ? <BalanceAccountWrapper>
                  {/*// 余额储值 余额账户*/}
                  <p>
                    <i className="scmIconfont scm-icon-account" />
                  </p>
                  <p>
                    ¥ {balanceAmount}
                  </p>
                  <p>
                    可用余额（元)
                  </p>
                  <p>
                    {
                      buttonList ? buttonList.map((btn, index) => {
                        return (
                          <span key={index} onClick={() => this.goToRecharge(btn.key, null)}
                            style={{ margin: buttonList.length === 1 ? 0 : null }}>{btn.value}</span>
                        );
                      }) : null
                    }
                  </p>
                </BalanceAccountWrapper> : null
              }
              {
                accountTypeCode === $AccountType.CREDIT ?
                  <div>
                    <CreditAccountWrapper>
                      {/*// 信用账户 欠款账户*/}
                      <div onClick={this.goToRepaymentRecord} className="repayment-record">还款记录</div>
                      <p>
                        <i className="scmIconfont scm-icon-account" />
                      </p>
                      <div>
                        {/*<div>
                  <p>￥{balanceAmount}</p>
                  <p>可用额度（元）</p>
                </div>*/}
                        <div>
                          <p>￥{allSurplusToReturnAmount}</p>
                          <p>全部欠款（元）</p>
                        </div>
                      </div>
                      <p>
                        <span onClick={() => this.goToRecharge("repayment", allSurplusToReturnAmount)}>还款</span>
                      </p>
                    </CreditAccountWrapper>
                    <CreditAccountInfo>
                      {/*// 信用账户 欠款账户*/}
                      <CreditAccountInfoHeader>
                        <span>使用中的信用（{effectiveCreditAccount}）</span>
                        <span onClick={this.goToCreditList} style={{ cursor: "pointer" }}>全部信用记录</span>
                      </CreditAccountInfoHeader>
                      <CreditAccountInfoContents>
                        {
                          creditAccountList.length > 0 ? creditAccountList.map((credit, index) => {
                            return (
                              <CreditAccountInfoContent onClick={() => this.goToCreditDetail(credit.accountId)}
                                key={index}>
                                <p>
                                  {credit.code}（{credit.ruleType}）
                                </p>
                                <div>
                                  <p>
                                    <span className="range">￥{Number(credit.creditAmount).toFixed(2)}</span>
                                    <br />
                                    <span>信用额度</span>
                                  </p>
                                  <p>
                                    <span className="value">￥{Number(credit.surplusToReturnAmount).toFixed(2)}</span>
                                    <br />
                                    <span>欠款金额</span>
                                  </p>
                                  <p>
                                    <span className="value"
                                      style={{ color: credit.creditStatus === $CreditType.USEING ? "#7ED321" : credit.creditStatus === $CreditType.OVERDUE ? "#FF3030" : "#307DCD" }}
                                    >
                                      {credit.creditStatusDesc}
                                    </span>
                                    <br />
                                    <span>还款日：{credit.repaymentTime}</span>
                                  </p>
                                  <p>
                                    <b class="right">
                                      <i class="right-arrow1"></i>
                                      <i class="right-arrow2"></i>
                                    </b>
                                  </p>
                                </div>
                                <p>
                                  有效期：{credit.creditAmountExpiryDateFrom}至{credit.creditAmountExpiryDateTo}
                                </p>
                              </CreditAccountInfoContent>
                            );
                          }) : <NoGoods title="暂无可用的信用~" />
                        }
                      </CreditAccountInfoContents>
                    </CreditAccountInfo>
                  </div>
                  : null
              }
              <AccountInfoWrapper>
                <AccountInfoHeader>
                  <span className={key === "ALL" ? "active" : ""} onClick={() => this.queryAccountInfo("ALL")}>明细</span>
                  <span className={key === "IN" ? "active" : ""} onClick={() => this.queryAccountInfo("IN")}>收入</span>
                  <span className={key === "OUT" ? "active" : ""} onClick={() => this.queryAccountInfo("OUT")}>支出</span>
                </AccountInfoHeader>
                <div
                  className="products"
                >
                  <div ref="pullChild" className="pro-box">
                    {
                      accountFlowList ? accountFlowList.length > 0 ? accountFlowList.map((accountFlow, index) => {
                        return (
                          <AccountInfoContent key={index}
                            onClick={() => this.accountFlowDetail(accountFlow.accountFlowId)}>
                            <div>
                              <p>{accountFlow.accountUse}</p>
                              <p>{accountFlow.docDate}</p>
                            </div>
                            <div style={{
                              color: accountFlow.inOutType === $AccountType.ADD
                                ? "#FF4242" : "#333",
                            }}>
                              {accountFlow.inOutType === $AccountType.ADD ? "+" : "-"}{accountFlow.amount}
                            </div>
                          </AccountInfoContent>
                        );
                      }) : <NoGoods title="暂无数据" /> : null
                    }
                  </div>
                  {
                    accountFlowList && accountFlowList.length > 0 ?
                      <LoadingTip
                        isFinished={isFinished}
                        isLoad={isLoading}
                      /> : null
                  }
                </div>
              </AccountInfoWrapper>
              <Model style={{ display: showCreditMedal }}>
                <LayerStyle onClick={this.hideModel} />
                <EffectiveCreditAccountInfos>
                  <EffectiveCreditAccountInfoHeader>
                    <span>还款</span>
                  </EffectiveCreditAccountInfoHeader>
                  <EffectiveCreditAccountInfoContents>
                    {
                      creditAccountList.length > 0 ? creditAccountList.filter((list) => list.surplusToReturnAmount !== 0).map((credit, index) => {
                        return (
                          <div key={index}>
                            <CheckboxItem
                              checked={credit.checkFlag}
                              onChange={(e) => this.onCheckedCredit(e, credit.accountId, credit.surplusToReturnAmount)} />
                            <EffectiveCreditAccountInfoContent>
                              <p>
                                {credit.code}（{credit.ruleType}）
                              </p>
                              <div>
                                <p>
                                  <span className="range">￥{Number(credit.creditAmount).toFixed(2)}</span>
                                  <br />
                                  <span>信用额度</span>
                                </p>
                                <p>
                                  <span className="value">￥{Number(credit.surplusToReturnAmount).toFixed(2)}</span>
                                  <br />
                                  <span>欠款金额</span>
                                </p>
                                <p>
                                  <span className="value"
                                    style={{ color: credit.creditStatus === $CreditType.USEING ? "#7ED321" : credit.creditStatus === $CreditType.OVERDUE ? "#FF3030" : "#307DCD" }}
                                  >
                                    {credit.creditStatusDesc}
                                  </span>
                                  <br />
                                  <span>还款日：{credit.repaymentTime}</span>
                                </p>
                                {/*<p>
                              <b class="right">
                                <i class="right-arrow1"></i>
                                <i class="right-arrow2"></i>
                              </b>
                            </p>*/}
                              </div>
                              <p>
                                有效期：{credit.creditAmountExpiryDateFrom}至{credit.creditAmountExpiryDateTo}
                              </p>
                            </EffectiveCreditAccountInfoContent>
                          </div>
                        );
                      }) : <NoGoods title="暂无可用的信用~" />
                    }
                  </EffectiveCreditAccountInfoContents>
                  <EffectiveCreditAccountInfoButton onClick={this.checkTongLianPaymentStatus}>
                    立即还款 ￥{Number(selectSurplusToReturnAmount).toFixed(2)}
                  </EffectiveCreditAccountInfoButton>
                </EffectiveCreditAccountInfos>
              </Model>
            </Wrapper>
            :
            <NoAuthority /> : <div />
        }
      </Spin>
    );
  }
}

const AccountInfo = ScrollAbilityWrapComponent(AccountInfoWrap);
export default AccountInfo;

const Wrapper = styled.div`// styled
  & {
    width: 100%;
    min-height: calc(${document.documentElement.clientHeight}px);
    height: auto;
    background: #ECF6FF;
    padding: 15px;
    overflow:auto;
    -webkit-overflow-scrolling: touch;
    .repayment-record,.recharge-record{
      position: absolute;
      top: 25px;
      right: 31px;
      display: inline-block;
      float: left;
      color: #595959;
      font-size: 12px;
    }
  }
`;

const AdvanceAccountWrapper = styled.div`// styled
  & {
    width: 100%;
    height: auto;
    background: url("https://order.fwh1988.cn:14501/static-img/scm/icon-card-bg.png");
    border-radius: 5px;
    text-align: center;
    padding: 30px;
    > p:nth-of-type(1) {
      margin-bottom: 0;
      > i {
        color: #307DCD;
        font-size: 44px;
        margin-bottom: 0;
      }
    }
    > p:nth-of-type(2) {
      color: #307DCD;
      font-size: 20px;
      margin-bottom: 8px;
    }
    > p:nth-of-type(3) {
      color: #666666;
      font-size: 12px;
      margin-bottom: 8px;
    }
    > p:nth-of-type(4) {
      display: flex;
      margin-bottom: 0;
      > span {
        width: 100%;
        height: 28px;
        line-height: 28px;
        background: #307DCD;
        color: #fff;
        border: 1px solid transparent;
        border-radius: 14px;
        text-align: center;
      }
      > span:nth-of-type(1) {
        margin-right: 80px;
      }
    }
  }
`;

const BalanceAccountWrapper = styled.div`// styled
  & {
    width: 100%;
    height: auto;
    text-align: center;
    padding: 15px 24px;
    background: url("https://order.fwh1988.cn:14501/static-img/scm/icon-card-bg.png");
    border-radius: 5px;
    > p:nth-of-type(1) {
      margin-bottom: 12px;
      > i {
        color: #307DCD;
        font-size: 44px;
      }
    }
    > p:nth-of-type(2) {
      color: #307DCD;
      font-size: 20px;
      margin-bottom: 8px;
    }
    > p:nth-of-type(3) {
      color: #666666;
      font-size: 12px;
      margin-bottom: 15px;
    }
    > p:nth-of-type(4) {
      display: flex;
      margin-bottom: 0;
      > span {
        width: 100%;
        height: 28px;
        line-height: 28px;
        background: #307DCD;
        color: #fff;
        border: 1px solid transparent;
        border-radius: 14px;
        text-align: center;
      }
      > span:nth-of-type(1) {
        margin-right: 80px;
      }
    }
  }
`;

const CreditAccountWrapper = styled.div`// styled
  & {
    width: 100%;
    height: auto;
    text-align: center;
    padding: 15px 15px 0 15px;
    background: url("https://order.fwh1988.cn:14501/static-img/scm/icon-card-bg.png");
    border-radius: 5px;
    > p:nth-of-type(1) i {
      color: #307DCD;
      font-size: 44px;
    }
    > div {
      display: flex;
      > div {
        width: 100%;
        text-align: center;
      }
      > div:nth-of-type(1) {
        border-right: 1px solid #F2F2F2;
        > p:nth-of-type(1) {
          font-size: 20px;
          color: #307DCD;
          margin-bottom: 8px;
        }
        > p:nth-of-type(2) {
          font-size: 12px;
          color: #666;
          margin-bottom: 0;
        }
      }
      > div:nth-of-type(2) {
        > p:nth-of-type(1) {
          font-size: 20px;
          color: #FF3030;
          margin-bottom: 8px;
        }
        > p:nth-of-type(2) {
          font-size: 12px;
          color: #666;
          margin-bottom: 0;
        }
      }
    }
    > p:nth-of-type(2) {
      display: flex;
      margin-bottom: 0;
      > span {
        width: 100%;
        height: 28px;
        line-height: 28px;
        background: #307DCD;
        color: #fff;
        border: 1px solid transparent;
        border-radius: 14px;
        text-align: center;
        margin: 15px 90px;
      }
    }
  }
`;

const AccountInfoWrapper = styled.div`// styled
  & {
    width: 100%;
    height: auto;
    background: #fff;
    margin-top: 15px;
    border: 1px solid transparent;
    border-radius: 8px;
    > .noGoods {
      height: 350px;
    }
    .products {
      overflow: auto;
    }
    .common-bottomTotal {
      width: 100%;
      text-align: center;
      min-height: 54px;
      .pt20 {
        position: relative;
        top: 15px;
      }
    }
  }
`;

const AccountInfoHeader = styled.div`// styled
  & {
    width: 100%;
    height: 40px;
    line-height: 40px;
    border-bottom: 1px solid #D8D8D8;
    display: flex;
    > span {
      display: inline-block;
      width: 100%;
      text-align: center;
      color: #666;
      margin: 0 30px;
      font-size: 14px;
    }
    .active {
      border-bottom: 2px solid #307DCD;
      color: #307DCD;
      font-size: 14px;
    }
  }
`;

const AccountInfoContent = styled.div`// styled
  & {
    width: 100%;
    height: 64px;
    border-bottom: 1px solid #D8D8D8;
    padding: 10px 15px;
    position: relative;
    > div:nth-of-type(1) {
      position: absolute;
      > p:nth-of-type(1) {
        color: #666;
        font-size: 12px;
        margin-bottom: 12px;
      }
      > p:nth-of-type(2) {
        color: #999;
        font-size: 12px;
        margin-bottom: 0;
      }
    }
    > div:nth-of-type(2) {
      position: absolute;
      top: 24px;
      right: 15px;
      color: #FF4242;
      font-size: 14px;
    }
  }
`;

const CreditAccountInfo = styled.div`// styled
  & {
    width: 100%;
    height: auto;
    background: #fff;
    margin-top: 15px;
    border: 1px solid transparent;
    border-radius: 8px;
  }
`;

const CreditAccountInfoHeader = styled.div`// styled
  & {
    width: 100%;
    height: 40px;
    line-height: 40px;
    background: #fff;
    border-bottom: 1px solid #D8D8D8;
    padding: 0px 15px;
    box-sizing: border-box;
    > span:nth-of-type(1) {
      font-family: MicrosoftYaHei;
      font-size: 14px;
      color: #333333;
    }
    > span:nth-of-type(2) {
      float: right;
      font-family: MicrosoftYaHei;
      font-size: 14px;
      color: #307DCD;
    }
  }
`;

const CreditAccountInfoContents = styled.div`// styled
  & {
    width: 100%;
    height: auto;
    padding: 15px 15px 0px 15px;
  }
`;

const CreditAccountInfoContent = styled.div`// styled
  & {
    width: 100%;
    height: 110px;
    padding: 8px 12px;
    border: 0.5px solid #D8D8D8;
    border-radius: 4px;
    margin-bottom: 15px;
    > p {
      margin-bottom: 5px;
      color: #999999;
      font-family: MicrosoftYaHei;
      font-size: 12px;
    }
    > div {
      > p {
        display: inline-block;
        margin-bottom: 5px;
        width: 30%;
        > span {
          font-family: MicrosoftYaHei;
          font-size: 12px;
          color: #999999;
        }
        > .range {
          font-family: MicrosoftYaHei;
          font-size: 13px;
          color: #333333;
        }
        > .value {
          font-family: MicrosoftYaHei;
          font-size: 13px;
          color: #FF3030;
        }
      }
      > p:nth-of-type(4) {
        width: 15px;
        height: 15px;
        position: relative;
        float: right;
        .right {
          width: 15px;
          height: 15px;
          position: absolute;
          left: 0;
          top: 10px;
        }
        .right-arrow1, .right-arrow2 {
          width: 0;
          height: 0;
          display: block;
          position: absolute;
          left: 0;
          top: 0;
          border-top: 10px transparent dashed;
          border-right: 10px transparent dashed;
          border-bottom: 10px transparent dashed;
          border-left: 10px white solid;
          overflow: hidden;
        }
        .right-arrow1 {
          left: 1px; /*重要*/
          border-left: 10px #999999 solid;
        }
        .right-arrow2 {
          border-left: 10px white solid;
        }
      }
    }
  }
`;

const Model = styled.div`// styled
  & {
    width: 100%;
    height: calc(${document.documentElement.clientHeight}px);
    position: fixed;
    top: 0;
    left:0;
    background: rgba(0,0,0,0.5);
    z-index: 99;
    -webkit-transition: all 4s ease-in;
	-moz-transition: all 4s ease-in;
	-o-transition: all 4s ease-in;
	transition: all 4s ease-in;
  }
`;

const LayerStyle = styled.div`// styled
  & {
    width: 100%;
    height: calc(${document.documentElement.clientHeight / 2}px);;
  }
`;

const EffectiveCreditAccountInfos = styled.div`// styled
  & {
    & {
      width: 100%;
      height: auto;
      background: #fff;
      border: 1px solid transparent;
    }
  }
`;

const EffectiveCreditAccountInfoHeader = styled.div`// styled
  & {
    width: 100%;
    height: 46px;
    line-height: 46px;
    background: #fff;
    padding: 0px 15px;
    box-sizing: border-box;
    > span:nth-of-type(1) {
      font-family: MicrosoftYaHei;
      font-size: 14px;
      color: #333333;
    }
    > span:nth-of-type(2) {
      float: right;
      font-family: MicrosoftYaHei;
      font-size: 14px;
      color: #333333;
      > .am-switch {
        padding-left: 12px;
      }
    }
  }
`;

const EffectiveCreditAccountInfoContents = styled.div`// styled
  & {
    width: 100%;
    height: 400px;
    padding: 15px 15px 180px 15px;
    overflow-y: auto;
    > div {
      > .am-list-item {
        width: 10%;
        padding-left: 0;
        //vertical-align: unset;
        display: inline-block;
      }
    }
    .noGoods {
      img {
        width: 30%;
      }
    }
  }
`;

const EffectiveCreditAccountInfoContent = styled.div`// styled
  & {
    display: inline-block;
    width: 90%;
    height: 110px;
    padding: 8px 12px;
    border: 0.5px solid #D8D8D8;
    border-radius: 4px;
    margin-bottom: 15px;
    vertical-align: middle;
    > p {
      margin-bottom: 5px;
      color: #999999;
      font-family: MicrosoftYaHei;
      font-size: 12px;
    }
    > div {
      > p {
        display: inline-block;
        margin-bottom: 5px;
        width: 30%;
        > span {
          font-family: MicrosoftYaHei;
          font-size: 12px;
          color: #999999;
        }
        > .range {
          font-family: MicrosoftYaHei;
          font-size: 13px;
          color: #333333;
        }
        > .value {
          font-family: MicrosoftYaHei;
          font-size: 13px;
          color: #FF3030;
        }
      }
      > p:nth-of-type(4) {
        width: 15px;
        height: 15px;
        position: relative;
        float: right;
        .right {
          width: 15px;
          height: 15px;
          position: absolute;
          left: 0;
          top: 12px;
        }
        .right-arrow1, .right-arrow2 {
          width: 0;
          height: 0;
          display: block;
          position: absolute;
          left: 0;
          top: 0;
          border-top: 10px transparent dashed;
          border-right: 10px transparent dashed;
          border-bottom: 10px transparent dashed;
          border-left: 10px white solid;
          overflow: hidden;
        }
        .right-arrow1 {
          left: 1px; /*重要*/
          border-left: 10px #999999 solid;
        }
        .right-arrow2 {
          border-left: 10px white solid;
        }
      }
    }
  }
`;

const EffectiveCreditAccountInfoButton = styled.div`// styled
  & {
    width: 100%;
    height: 42px;
    line-height: 42px;
    background: #307DCD;
    font-family: PingFangSC-Regular;
    font-size: 14px;
    color: #FFFFFF;
    text-align: center;
    position: fixed;
    bottom: 0;
    z-index: 99;
  }
`;
