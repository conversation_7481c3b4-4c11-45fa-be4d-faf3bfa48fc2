import { autowired, bean } from "@classes/ioc/ioc";
import { action, observable } from "mobx";
import { $ComponentService } from "../../classes/service/$component-service";
import { $OrderPartyService } from "../../classes/service/$order-party-service";
import { $MyInfoService } from "../../classes/service/$my-info-service";
import { $CreditAccount } from "../../classes/entity/$credit-account";
import { $InterfaceErrorCode } from "../../classes/const/$interface-error-code";
import { beanMapper } from '../../helpers/bean-helpers';
import { $ValidityType } from "@classes/const/$validity-type";

@bean($AccountInfoMv)
export class $AccountInfoMv {
  @autowired($ComponentService)
  public $componentService: $ComponentService;

  @autowired($MyInfoService)
  public $myInfoService: $MyInfoService;

  @autowired($OrderPartyService)
  public $orderPartyService: $OrderPartyService;

  @observable public isSpin: boolean = false;
  @observable public isFinished: boolean = false;
  @observable public isLoading: boolean = false;
  @observable public isHavePermissions = false;
  @observable public isGetPermissions: boolean = false;
  @observable public isShowLoading: boolean = true;
  @observable public balanceAmount: number = 0;
  @observable public accountFlowListSize: number;
  @observable public allSurplusToReturnAmount: number;
  @observable public selectSurplusToReturnAmount: number = 0;
  @observable public effectiveCreditAccount: number;
  @observable public pageIndex: number = 0;
  @observable public scrollHeight: number = 0;
  @observable public pageSize: number = 20;
  @observable public automaticType: string;
  @observable public configId: string;
  @observable public key: string = "ALL";
  @observable public showCreditMedal: string = "none";
  @observable public buttonList: any[];
  @observable public accountFlowList: any[];
  @observable public creditAccountList: any[] = [];
  @observable public selectCreditAccountList: any[] = [];
  @action
  public showSpin() {
    this.isSpin = true;
  }

  @action
  public hideSpin() {
    this.isSpin = false;
  }

  @action
  public saveAccountId(params) {
    return this.$componentService.saveAccountId(params);
  }

  @action
  public saveShopAndScheme(params) {
    return this.$orderPartyService.saveShopAndScheme(params);
  }

  @action
  public fetchAccountInfo(params, blean) {
    const { accountId } = params;
    return new Promise((resolve, reject) => {
      this.$componentService.queryAccountInfo(params).then((data) => {
        this.isGetPermissions = true;
        this.isLoading = false;
        if (data.errorCode === $InterfaceErrorCode.NO_PAGE_VIEW_PERMISSIONS) {
          resolve(this.balanceAmount);
          return;
        }
        this.isHavePermissions = true;
        const { buttonList, balanceAmount, accountFlowList, accountFlowListSize, errorCode } = data;
        this.buttonList = buttonList;
        this.accountFlowListSize = accountFlowListSize;
        this.accountFlowList = blean ? this.accountFlowList.concat(accountFlowList) : accountFlowList;
        this.balanceAmount = balanceAmount;
        this.isFinished = this.accountFlowList.length >= accountFlowListSize;
        if (!accountId) {
          const paramsCredit = { creditType: "Effective" , validityType: $ValidityType.REGULAR_CREDIT };
          this.fetchCreditaccountListLoad(paramsCredit);
        }
        this.hideSpin();
        data.isFinished = this.accountFlowList.length >= accountFlowListSize;
        resolve(this.balanceAmount);
      }).catch((err) => {
        reject(err);
      });
    });
  }

  @action
  public fetchCreditaccountListLoad(params) {
    this.$componentService.queryCreditaccountListLoad(params).then((data) => {
      this.allSurplusToReturnAmount = data.allSurplusToReturnAmount;
      this.automaticType = data.automaticType;
      this.configId = data.configId;
      this.effectiveCreditAccount = data.effectiveCreditAccount;
      this.creditAccountList = data.creditAccountList.map((credit) => new $CreditAccount(credit));
      data.creditAccountList.filter((account) => account.surplusToReturnAmount !== 0).map((val) => {
        this.selectCreditAccountList.push(val.accountId);
        this.selectSurplusToReturnAmount += val.surplusToReturnAmount;
      });
    });
  }

  @action
  public accountAutomaticUpdate(params) {
    return this.$componentService.accountAutomaticUpdate(params);
  }

  @action
  public queryCreditrepaymentAuditedAmount(params) {
    return this.$componentService.queryCreditrepaymentAuditedAmount(params);
  }
  @action
  public checkTongLianPaymentStatus(params) {
    return this.$componentService.checkTongLianPaymentStatus(params);
  }
  @action
  public checkContinuePay(params) {
    return this.$componentService.checkContinuePay(params);
  }
  @action
  public cancelTongLianPay(params) {
    return this.$componentService.cancelTongLianPay(params);
  }
  @action
  public queryOldData(obj) {
    beanMapper(obj, this);
    console.log(this);
  }
  @action
  public clearMVData() {
  this.isSpin = false;
  this.isFinished = false;
  this.isLoading = false;
  this.isHavePermissions = false;
  this.isGetPermissions = false;
  this.isShowLoading = true;
  this.balanceAmount = 0;
  this.accountFlowListSize = 0;
  this.allSurplusToReturnAmount = 0;
  this.selectSurplusToReturnAmount = 0;
  this.effectiveCreditAccount = 0;
  this.scrollHeight = 0;
  this.pageIndex = 0;
  this.pageSize = 20;
  this.automaticType = "";
  this.configId = "";
  this.key = "ALL";
  this.showCreditMedal = "none";
  this.buttonList = [];
  this.accountFlowList = [];
  this.creditAccountList = [];
  this.selectCreditAccountList = [];
  }
}
