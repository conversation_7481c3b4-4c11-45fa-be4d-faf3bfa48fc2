import {autowired} from "@classes/ioc/ioc";
import {Button, Checkbox, List, Toast, WingBlank} from "antd-mobile";
import {findIndex} from "lodash";
import {transaction} from "mobx";
import {observer} from "mobx-react";
import React from "react";
import {withRouter} from "react-router";
import styled from "styled-components";
import {$ActiveType} from "../../classes/const/$active-type";
import {$Product} from "../../classes/entity/$product";
import {Footer} from "../../components/footer/footer";
import IScroll from "../../components/iScroll/iScroll";
import { $ProductMv } from "../../components/product/product-mv";
import {ProductItem} from "../../components/table/product-item";
import {SITE_PATH} from "../app";
import { $CartMv } from "../shop-cart/cart-mv";
import {$ProductDetailMv} from "./$product-detail-mv";

declare let window: any;

@withRouter
@observer
export class ProductDetail extends React.Component<any, any> {
  @autowired($ProductDetailMv)
  public $productDetailMv: $ProductDetailMv;
  @autowired($ProductMv)
  public $ProductMv: $ProductMv;
  @autowired($CartMv)
  public $CartMv: $CartMv;

  constructor(props) {
    super(props);
    this.state = {
      fixTop: "",
    };
  }

  public componentDidMount() {
    this.$productDetailMv.queryProductDetail(this.$ProductMv.product.productSkuId,
      this.$ProductMv.product.productUomId);
  }

  public cartWrapper = (product: $Product, cartProducts: $Product[]) => {
    const index = findIndex(cartProducts, {productIdentifier: product.productIdentifier});
    if (index > -1) {
      product.setQuantity(cartProducts[index].quantity);
    } else {
      product.setQuantity(0);
    }
    return product;
  }

  public onScroll = (iScrollInstance) => {
    const distanceY = this.refs.fixTop;
  }
  public onRefresh = (iScrollInstance) => {
    const yScroll = iScrollInstance.y;

    // console.log("vertical position:" + yScroll);

    if (this.state.y !== yScroll) {
      this.setState({y: yScroll});
    }
  }

  public render() {
    const renderContent = () => {
      return (<div>
        <ProductImg>
          {/*<img src={this.$ProductMv.product.imageUrl}/>*/}
        </ProductImg>
        <div className={this.state.fixTop}>
          <ProductItem
            checkable={false}
            isCart={false}
            isDetail={true}
            data={this.cartWrapper(this.$ProductMv.product, this.$CartMv.products)}
          />
        </div>
        <DetailTitle>详情</DetailTitle>
        <DetailTextarea
          dangerouslySetInnerHTML={{
            __html: this.$productDetailMv.multimediaDescription ?
              this.$productDetailMv.multimediaDescription :
              "<div style='background: #fff;height: 300px'>无详情描述。</div>",
          }}
        />
      </div>);
    };

    return (
      <div className="iScroll">
        <IScroll
          onScroll={this.onScroll}
          renderContent={renderContent}
        />
        <Footer activeKey={$ActiveType.CATEGORY_KEY} count={this.$CartMv.totalCount}/>
      </div>
    );
  }
}

const DetailTextarea = styled.div`// styled
  & {
    margin-bottom: 44px;
    padding: 10px;
    min-height: 200px;
    background: #fff;
  }
`;
const DetailTitle = styled.div`// styled
  & {
    margin-top: 10px;
    padding: 10px;
    text-align: center;
    color: #6085dd;
    font-size: 18px;
    background: #fff;
    border-top: 1px solid #ddd;
    border-bottom: 1px solid #ddd;
  }
`;
const ProductImg = styled.div`// styled
  & {
    position: relative;
    overflow: hidden;
    padding-top: 90%;
    width: 100%;
    height: 0;
    border-bottom: 1px solid #ddd;
    img {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
    }
  }
`;
