import {autowired, bean} from "@classes/ioc/ioc";
import {action, observable} from "mobx";
import {$Product} from "../../classes/entity/$product";
import {$ProductService} from "../../classes/service/$product-service";

@bean($ProductDetailMv)
export class $ProductDetailMv {
  @autowired($ProductService)
  public $productService: $ProductService;
  @observable public briefIntroduction: string;
  @observable public descriptionMedia: string;
  @observable public descriptionTitle: string;
  @observable public productOrderPrice: string;
  @observable public multimediaDescription: string;

  @action
  public queryProductDetail(productSkuId, productUomId) {
    return this.$productService.queryProductDetail({productSkuId, productUomId})
      .then((data) => {
        this.briefIntroduction = data.briefIntroduction;
        this.descriptionMedia = data.descriptionMedia;
        this.descriptionTitle = data.descriptionTitle;
        this.productOrderPrice = data.productOrderPrice;
        this.multimediaDescription = data.multimediaDescription;
      });
  }
}
