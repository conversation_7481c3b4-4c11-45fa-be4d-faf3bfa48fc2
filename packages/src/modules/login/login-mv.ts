import { autowired, bean } from '@classes/ioc/ioc';
import { action, observable } from "mobx"
import { $LoginService } from '../../classes/service/$login-service';
import { $ComponentService } from '../../classes/service/$component-service';

@bean($LoginMV)
export class $LoginMV {
  @autowired($LoginService)
  $loginService: $LoginService;

  @autowired($ComponentService)
  $componentService: $ComponentService;

  @observable tenant: string;

  @observable tenantName: string;

  @observable username: string;

  @observable password: string;

  @observable token: string;

  @action
  public login(successCb?) {
    this.$loginService.login({
      username: this.username,
      password: this.password,
      tenant: this.tenantName,
    }).then((res) => {
        // todo: result wrapper
        const { data } = res;
        const { tenant, token } = data;
        this.setLocalStorage("tenant", tenant);
        this.setLocalStorage("token", token);
        if (this.isWeiXin()) {
          this.$componentService.getOpenId().then((data1) => {
            this.setLocalStorage("openId", data1.openId);
          });
        }
        successCb && successCb();
      }
    )
  }

  @action
  public isWeiXin() {
    //window.navigator.userAgent属性包含了浏览器类型、版本、操作系统类型、浏览器引擎类型等信息，这个属性可以用来判断浏览器类型
    var ua = window.navigator.userAgent.toLowerCase();
    //通过正则表达式匹配ua中是否含有MicroMessenger字符串
    if (ua.match(/MicroMessenger/i) == 'micromessenger') {
      return true;
    } else {
      return false;
    }
  }

  @action
  setValue(path, value) {
    this[path] = value
  }

  @action
  setLocalStorage(path, value) {
    localStorage.setItem(path, value)
  }
}
