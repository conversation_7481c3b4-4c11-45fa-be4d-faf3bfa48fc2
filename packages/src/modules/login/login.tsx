import { autowired } from "@classes/ioc/ioc";
import { But<PERSON> } from "antd-mobile";
import React from "react";
import { withRouter } from "react-router";
import styled from "styled-components";
import { $LoginMV } from "./login-mv";
import { $AppStore, AppStoreKey } from "../../classes/stores/app-store-mv";
import { AnnouncementModelMv } from "../../components/announcement-model/announcement-model-mv"


declare let window: any;
const IconPwd = require("./svg/ico_cipher.svg");
const IconCorp = require("./svg/ico_corp.svg");
const IconUser = require("./svg/ico_use.svg");
const Logo = require("./svg/logo.svg");

@withRouter
class Login extends React.Component<any, any> {
  @autowired($AppStore)
  public $AppStore: $AppStore;
  @autowired($LoginMV)
  public $mv: $LoginMV;

  @autowired(AnnouncementModelMv)
  public AnnouncementModelMv:AnnouncementModelMv;
  

  public componentDidMount(): void {
    document.title = "登陆";
    this.AnnouncementModelMv.removeSessionStorage();
  }

  public onLogin = () => {
    // gio("track", "loginClick");
    this.$mv.login(() => {
      this.$AppStore.clearPageMv(AppStoreKey.ORDERPARTYSELECTION);
      window.location.href = `${window.ENV.sitePath}/select`;
    });
  }

  public render() {
    return (
      <Wrapper>
        <LogoWrapper>
          <Logo/>
        </LogoWrapper>
        <InputWrapper>
          <InputIcon>
            <IconCorp/>
          </InputIcon>
          <Input name="tenantName"
                 value={this.$mv.tenantName}
                 onChange={(e) => this.$mv.setValue("tenantName", e.target.value)}/>
        </InputWrapper>
        <InputWrapper>
          <InputIcon>
            <IconUser/>
          </InputIcon>
          <Input name="username"
                 value={this.$mv.username}
                 onChange={(e) => this.$mv.setValue("username", e.target.value)}/>
        </InputWrapper>
        <InputWrapper>
          <InputIcon>
            <IconPwd/>
          </InputIcon>
          <Input name="password" type="password"
                 value={this.$mv.password}
                 onChange={(e) => this.$mv.setValue("password", e.target.value)}/>
        </InputWrapper>
        <Button style={{ marginBottom: 50 }} type="primary" onClick={() => this.onLogin()}>登录</Button>
        <Footer>
          <FooterWrapper>Copyright © 2018 PEKON 秉坤 保留所有权利 | 隐私</FooterWrapper>
        </Footer>
      </Wrapper>
    );
  }
}

export default Login;

const Wrapper = styled.div`// styled
  & {
    display: flex;
    flex-direction: column;
    padding: 22px 24px 16px 24px;
    background: #FFFFFF;
    height:${window.innerHeight}px;
  }
`;

const LogoWrapper = styled.div`// styled
  & {
    height: 60px;
    text-align: center;
    line-height: 60px;
    margin: 30px 0;
    > svg {
      height: 50px;
      path {
        fill: #108ee9;
      }
    }
  }
`;

const InputWrapper = styled.div`// styled
  & {
    display: flex;
    flex: 0 0 46px;
    margin-bottom: 20px;
    background: #FFFFFF;
    border: 1px solid #E6E6E6;
    border-radius: 4px;
  }
`;

const InputIcon = styled.div`// styled
  & {
    display: flex;
    flex: 0 0 54px;
    justify-content: center;
    align-items: center;
    > svg {
      path {
        fill: #108ee9;
      }
    }
  }
`;

const Input: any = styled.input`// styled
  & {
    display: flex;
    flex: 1;
    font-size: 14px;
    color: #303030;
    outline: none;
    border: none;
  }
`;

const Footer = styled.footer`// styled
  & {
    background-color: white;
  }
`;

const FooterWrapper = styled.div`// styled
  & {
    display: flex;
    flex: 0 0 60px;
    align-items: center;
    justify-content: center;
    font-family: MicrosoftYaHeiUI, sans-serif;
    font-size: 12px;
    color: #9A9A9A;
  }
`;
