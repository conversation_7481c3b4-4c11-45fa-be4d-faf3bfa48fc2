import { autowired } from "@classes/ioc/ioc";
import { Radio, Spin } from "antd";
import { ActivityIndicator, Button, Checkbox, List, Modal, Toast } from "antd-mobile";
import { isEmpty, sum } from "lodash";
import { observer } from "mobx-react";
import * as React from "react";
import { withRouter } from "react-router";
import TransferHint from "../../components/transfer-hint/transfer-hint"
import PaymentLine from "../../components/payment-line/payment-line";
import { PaymentListComponent } from "src/components/payment-list-component/payment-list-component";
import styled from "styled-components";
import DateUtils, { toFixedOptimizing } from "../../classes/utils/DateUtils";
import { SITE_PATH } from "../app";
import { $SubmitOrderMv } from "../submit-order/submit-order-mv";
import { $AllOrderPaymentMv } from "./all-order-payment-mv";
import { $OrderType } from "../../classes/const/$order-type";
import { $Product } from "../../classes/entity/$product";
import { CapitalAccountListMobel } from "../../components/capital-account-list-mobel/capital-account-list-mobel";
import { $PaymentModeType } from "../../classes/const/$payment-mode-type";
import { ExcessIndication } from "../../components/excess-indication/excess-indication";
import { PaymentModeList } from "../../components/payment-mode-list/payment-mode-list";
import { ThridPartyPaymentModeList } from "../../components/thrid-party-payment-mode-list";
import { WxUploadImgMv } from "../../components/wx-upload-img/wx-upload-img-mv";
import { ScrollFreezeTitleBar } from "../../components/scroll-freeze-title-bar";
import { ChooseBankCardModal } from "../../components/choose-bank-card-modal/choose-bank-card-modal";
import { AdjustAmountModal } from "../../components/adjust-amount-modal/adjust-amount-modal";
import { ChooseBankCardMv } from "../../components/choose-bank-card-modal/choose-bank-card-mv";
import { $PaymentType } from "../../classes/const/$payment-type";
import { OrderPaymentAccountShow } from "../../components/order-payment-account-show/order-payment-account-show";
import { $AppStore, AppStoreKey } from "../../classes/stores/app-store-mv";
import { $ScrollFreezeTitleBarMV } from "../../components/scroll-freeze-title-bar/index-mv";
import { getUrlParam } from "../../classes/utils/UrlUtils";
import { $CartType } from "@classes/const/$cart-type";
import { ViewRatesModal } from "../../components/view-rates-modal/view-rates-modal";
import { toJS } from "mobx";
import { $TransferType } from "@classes/const/$transfer-type";
import { $ValidityType, ValidityTypeName } from "@classes/const/$validity-type";
const Item = List.Item;
const Brief = Item.Brief;
const CheckboxItem = Checkbox.CheckboxItem;
const RadioGroup = Radio.Group;
const now = new Date();
const alert = Modal.alert;
declare let WeixinJSBridge: any;
declare let window: any;
const scmDestination = "toBindBankCard";
@withRouter
@observer
class AllOrderPayment extends React.Component<any, any> {
  @autowired($AppStore)
  public $AppStore: $AppStore;

  @autowired($AllOrderPaymentMv)
  public $myMv: $AllOrderPaymentMv;
  @autowired($ScrollFreezeTitleBarMV)
  public $ScrollFreezeTitleBarMV: $ScrollFreezeTitleBarMV;

  @autowired($SubmitOrderMv)
  public $SubmitOrderMv: $SubmitOrderMv;

  @autowired(WxUploadImgMv)
  public wxUploadImgMv: WxUploadImgMv;

  @autowired(ChooseBankCardMv)
  public $chooseBankCardMv: ChooseBankCardMv;

  constructor(props) {
    super(props);
    this.state = {
      isShowMabel: false,
      offLineTransPaymentData: null,
      selectCapitalAccountInfo: {
        accountCode: "",
        bankAccountName: "",
        bankName: "",
        capitalAccountId: "",
      },
      // showQuestionHelp: false,
      activekey: "",
      overtop: false,
      animating: false,
      offLineTransferAmount: 0,
      thridPartyKey: "",
      isChooseAccount: true,
      isShowChooseBankCardModal: "",
      tpTransferAmount: 0,
      nextTpTransferAmount: 0, // 第三方支付输入框随时改变的金额
      showAmountModal: false,
      selectedBankName: "",
      selectedBankCardType: "",
      selectedBankCardNo: "",
      selectBankCardCode: "",
      isHaveAllInPay: false,
      salesOrderIds: [],
      currentThridParityAmount: 0,
      mostThridParityAmount: 0,
      currentThridParityServerAmount: 0,
      needBind: false,
      bindUrl: null,
      rateCode: "",
      showRate: false,
      selectRateList: null,
      bankInfo: {
        bankCardRate: 0,
      },
      docType:"SalesOrder",//合并单据类型
      feeDocumentIds:[],//费用单合并列表id
      isShowPaymentLine: false,   // 是否展示付款线路
      subMerchantList: [],  // 可选付款线路
      originSubMerchantList: [],  // 用于恢复初始状态
      isShowTransferHint: false,  // 是否展示转账
      totalWaitPayAmount: 0,  // 转账相关参数
      accountIdList: [],  // 转账相关参数
      divideRebateTransferAmount: 0, // 分账返利转账金额
      disabledDivideRebate: false, // 禁用分账返利
      disabledTransfer: false, // 禁用转账
      isShowCreditModal: false, // 显示信用支付模式弹窗
      validityType: $ValidityType.NEW_CREDIT, // 默认新模式信用（无限期信用）：Y，常规信用：N
      isCreditLimit: false, // 是否限制信用支付方式
      disabledCredit: false, // 禁用信用支付
      isShowCreditTips: false, // 是否显示信用提示
    };
  }
  public saveData = (cb?) => {
    const { imgList } = this.wxUploadImgMv;
    const { offLineTransPaymentData } = this.state;
    const allOrderPaymentStorage = {
      $myMv: JSON.stringify(this.$myMv),
      $state: JSON.stringify(this.state),
      imgList: JSON.stringify(imgList),
      offLineTransPaymentData: offLineTransPaymentData ? offLineTransPaymentData.toString() : "",
      scrollHeight: $("#pageWrap").scrollTop(),
      scrollFreezeTitleBarMV: JSON.stringify(this.$ScrollFreezeTitleBarMV),
    };
    this.$AppStore.savePageMv(AppStoreKey.ALLORDERPAYMENT, allOrderPaymentStorage);
    cb && cb();
  }

  public componentWillUnmount(): void {
    this.saveData();
  }

  public pageWindowSkip = (url) => {
    // 跳转到其他页面需要保存数据
    this.saveData();
    setTimeout(() => {
      window.location.href = url;
    }, 50);
  }

  public componentDidMount() {
    document.title = "合并付款";
    const docType = getUrlParam("docType")
    if(getUrlParam("reLoad")){
      if(docType && docType == "FeeDocument"){
       window.location.href = `/${SITE_PATH}/shop/all-order-payment?payRange=All&comeBack=true&docType=FeeDocument`;
      }else{
      window.location.href = `/${SITE_PATH}/shop/all-order-payment?comeBack=true`;
      }
    }
    this.$myMv.clearPics();
    this.$myMv.clearAmountPayable();
    const salesOrderIds = [];
    let feeDocumentIds = [];
    if(docType && docType === "FeeDocument"){
        localStorage.removeItem("combined-payment-salesOrderIds")
        feeDocumentIds = JSON.parse(localStorage.getItem("combined-payment-feeDocumentIds")) || [];
    }else{
      localStorage.removeItem("combined-payment-feeDocumentIds")
      const combinedPaymentSalesOrderIds = JSON.parse(localStorage.getItem("combined-payment-salesOrderIds")) || [];
      combinedPaymentSalesOrderIds.map((order) => {
        salesOrderIds.push(order.orderId);
      });
    }
    this.setState({ salesOrderIds,
                    docType:docType === "FeeDocument" ? docType : "SalesOrder",
                    feeDocumentIds }, () => {
                    this.loadPaymentInfo();
                  });
    this.pageGoBack();
  }


  public pageGoBack = () => {
    window.addEventListener("popstate", (res) => {
      // 触发函数
      this.setState({
        isShowMabel: false,
      });
    });
  }
  public checkTongLianPaymentStatus = () => {
    const { salesOrderIds,docType,feeDocumentIds} = this.state;
    const params = {
      docType:docType === $OrderType.FEEDOCUMENT ? $OrderType.FEEDOCUMENT : $OrderType.ORDERPAYMENT,
      salesOrderIdList:salesOrderIds,
      feeDocumentIdList:feeDocumentIds
    }
    this.$myMv.checkTongLianPaymentStatus(params).then((data) => {
      const { errorCode, errorMsg, docInfo } = data;
      this.$myMv.hideSpin();
      if (errorCode && errorCode !== "0") {
        Toast.fail(errorMsg);
        this.clearStorage();
      } else if (docInfo && docInfo.oid) {
        const { oid, code, totalAmount } = docInfo
        alert(`提示`, `你有${totalAmount.toFixed(2)}元正在付款`, [
          {
            text: "不付款了", onPress: () => {
              this.cancelTongLianPay(oid);
            },
          },
          {
            text: "去付款", onPress: () => {
              this.checkContinuePay(oid);
            },
          },
        ]);
      } else {
        // 继续主流程操作
        this.clearStorage();
      }
    }).catch((err) => {
      this.clearStorage();
      this.$myMv.hideSpin();
    });
  }

  public checkContinuePay = (oid) => {
    this.$myMv.showSpin();
    const params = {
      docType: $OrderType.ORDERPAYMENT,
      oid,
    }
    this.$myMv.checkContinuePay(params).then((data) => {
      this.$myMv.hideSpin();
      const { errorCode, errorMessage, redirectUrl } = data;
      if (errorCode && errorCode !== "0") {
        this.clearStorage();
        Toast.fail(errorMessage);
      } else if (redirectUrl) {
        this.pageWindowSkip(`${redirectUrl}&redirectUrl=${document.location.protocol}//${document.domain}:${window.location.port}/scm/payment-result-show/${$PaymentModeType.ALLINPAY}`);
      } else {
        this.clearStorage();
      }
    }).catch(() => {
      this.clearStorage();
      this.$myMv.hideSpin();
    });
  }

  public cancelTongLianPay = (oid) => {
    const params = {
      docType: $OrderType.ORDERPAYMENT,
      oid,
    }
    this.$myMv.showSpin();
    this.$myMv.cancelTongLianPay(params).then((data) => {
      this.$myMv.hideSpin();
      const { errorCode, errorMessage } = data;
      if (errorCode && errorCode !== "0") {
        alert(`提示`, "显示接口里的错误信息", [
          {
            text: "我知道了", onPress: () => {
              window.location.reload();
            },
          },
        ]);
      } else {
        this.clearStorage();
        window.location.reload();
      }
    }).catch(() => {
      this.clearStorage();
      this.$myMv.hideSpin();
    });
  }

  public clearStorage = () => {
    const $sessionMyMv = JSON.parse(sessionStorage.getItem("$myMv"));
    const state = JSON.parse(sessionStorage.getItem("state"));
    const paymentMessage = JSON.parse(localStorage.getItem("paymentMessage"));
    if ($sessionMyMv) {
      sessionStorage.removeItem("$myMv");
    }
    if (state) {
      sessionStorage.removeItem("state");
    }
    if (paymentMessage) {
      // localStorage.removeItem("paymentMessage");
    }
  }

  public loadPaymentInfo = (isReload?, callback?) => {
    const { salesOrderIds,docType,feeDocumentIds } = this.state;
    let params = null;
    if (getUrlParam("payRange") === "All" || getUrlParam("payRange") === "Freight") {
      params = {
        isReload,
        paymentModeList: JSON.parse(localStorage.getItem("combined-payment-paymentModeList")),
        salesOrderIds,
        payRange: getUrlParam("payRange"),
        docType,
        feeDocumentIds:docType === "FeeDocument" ? feeDocumentIds : null
      };
    } else {
      params = {
        isReload,
        paymentModeList: JSON.parse(localStorage.getItem("combined-payment-paymentModeList")),
        salesOrderIds,
        docType,
        feeDocumentIds:docType === "FeeDocument" ? feeDocumentIds : null
      };
    }
    this.$myMv.showSpin();
    const oldData = this.$AppStore.getPageMv(AppStoreKey.ALLORDERPAYMENT);
    const allOrderPaymentStorage = oldData && JSON.parse(oldData);
    this.$AppStore.clearPageMv(AppStoreKey.ALLORDERPAYMENT);
    const comeBack = getUrlParam("comeBack");
    const destination = getUrlParam("destination");
    this.$myMv.loadTransferRecordList({ salesOrderIdList: salesOrderIds });
    this.$myMv.fetchPaymentInfo(params).then((data) => {
      this.$myMv.freightPayInfo = data.freightPayInfo;
      this.$myMv.hideSpin();
      this.checkTongLianPaymentStatus();
      const { paymentModeList, orgAmountInfoList, capitalAccountInfo, orderTotalAmountInfo } = data || {};
      if (!paymentModeList || (paymentModeList.length === 0)) {
        Toast.info("无可用的支付方式，请联系管理员", 3);
        return;
      }
      if (getUrlParam("payRange") === "All") {
        orgAmountInfoList.map((orgItem) => {
          if (orgItem.orderSchemePayLimit) {
            this.setState({ isCreditLimit: true, disabledCredit: true, isShowCreditTips: true });
          }
        })
      }
      this.$myMv.getOrderTotalAmountInfo(orderTotalAmountInfo);
      // 还原原始状态
      if (allOrderPaymentStorage && comeBack !== "true") {
        const $sessionMyMv = JSON.parse(allOrderPaymentStorage.$myMv);
        const $state = JSON.parse(allOrderPaymentStorage.$state);
        const { scrollHeight, scrollFreezeTitleBarMV, imgList } = allOrderPaymentStorage;
        this.$ScrollFreezeTitleBarMV.queryOldData(JSON.parse(scrollFreezeTitleBarMV));
        this.$myMv.queryOldData($sessionMyMv);
        if (this.$myMv.paymentProductList && this.$myMv.paymentProductList.length > 0) {
          this.setState({
            isHaveAllInPay: true,
          });
        }
        this.wxUploadImgMv.recordOldImgList(JSON.parse(imgList), this.$myMv.pics ? this.$myMv.pics.length : 0);
        this.$myMv.getOrderTotalAmountInfo(orderTotalAmountInfo);
        const {
          isShowMabel,
          offLineTransPaymentData,
          selectCapitalAccountInfo,
          activekey,
          overtop,
          animating,
          offLineTransferAmount,
          thridPartyKey,
          isChooseAccount,
          isShowChooseBankCardModal,
          tpTransferAmount,
          nextTpTransferAmount, // 第三方支付输入框随时改变的金额
          showAmountModal,
          selectedBankName,
          selectedBankCardType,
          selectedBankCardNo,
          selectBankCardCode,
          currentThridParityAmount,
          mostThridParityAmount,
          currentThridParityServerAmount,
          needBind,
          bindUrl,
          rateCode,
          subMerchantList,
           } = $state;
        this.setState({
          isShowMabel,
          offLineTransPaymentData: offLineTransPaymentData ? new Date(offLineTransPaymentData) : null,
          selectCapitalAccountInfo,
          activekey,
          overtop,
          animating: false,
          offLineTransferAmount,
          thridPartyKey,
          isChooseAccount,
          isShowChooseBankCardModal,
          tpTransferAmount,
          nextTpTransferAmount, // 第三方支付输入框随时改变的金额
          showAmountModal,
          selectedBankName,
          selectedBankCardType,
          selectedBankCardNo,
          selectBankCardCode,
          salesOrderIds: $state.salesOrderIds || [],
          currentThridParityAmount,
          mostThridParityAmount,
          currentThridParityServerAmount,
          needBind,
          bindUrl,
          rateCode,
          subMerchantList,
        }, () => {
          this.loadPaymentTpBankList(false);
        });
        setTimeout(() => {
          $("#pageWrap").scrollTop(scrollHeight);
        }, 50);
        localStorage.removeItem("paymentMessage");
        return;
      }
      this.$myMv.getShopListInfo(orgAmountInfoList, paymentModeList);
      this.$myMv.capitalAccountList = capitalAccountInfo.map((capitalAccount) => new $Product(capitalAccount));
      this.setState({
        offLineTransferAmount: 0,
      });
      if (paymentModeList) {
        data.paymentModeList.map((lis) => {
          if (lis.code === $PaymentModeType.WECHAT) {
            this.$SubmitOrderMv.fetchWxToken();
          } else if (lis.code === $PaymentModeType.BANKTRANSFER) {
            this.loadCapitalAccount();
          } else if (lis.code === $PaymentModeType.ALLINPAY) {
            // 加载银行卡信息
            this.loadPaymentTpBankList(destination === scmDestination);
            this.setState({ isHaveAllInPay: true });
            lis.paymentProductList.map((item) => {
              if (item.paymentCode === $PaymentType.ALLINPAY_WECHAT) {
                this.setState({
                  originSubMerchantList: item.subMerchantList,
                  subMerchantList: this.handleSubMerchantList(item.subMerchantList),
                });
              }
            });
          }
        });
      }
      // 支付失败重新支付

      if (allOrderPaymentStorage && comeBack === "true") {
        const $sessionMyMv = JSON.parse(allOrderPaymentStorage.$myMv);
        const $state = JSON.parse(allOrderPaymentStorage.$state);
        const { thridPartyKey, selectedBankCardNo, selectedBankCardType, selectedBankId, selectedBankName, selectBankCardCode } = $state;
        this.setState({ thridPartyKey });
        const { shopListInfo } = this.$myMv;
        // 支付方式记录状态
        this.$myMv.paymentModeList.map((item) => {
          const targetItem = $sessionMyMv.paymentModeList.find((res) => res.code === item.code);
          if (item.code === $PaymentType.ALLINPAY) {
            // 通联支付的状态记录
            item.isCheck = targetItem && targetItem.isCheck;
          } else {
            // 内部支付方式状态几率
            item.isCheck = item.availableTotalAmount > 0 ? (targetItem && targetItem.isCheck) : false;
          }
          shopListInfo.map((res) => {
            if (res[item.code]) {
              res[item.code].isCheck = item.isCheck;
            }
          });
        })
        // 保存银行卡信息
        this.setState({
          selectedBankCardNo,
          selectedBankCardType,
          selectedBankId,
          selectedBankName,
          selectBankCardCode,
        });
        this.calcPaymentModeData(this.$myMv.paymentModeList);
      }
      if (callback) {
        callback();
      }
    }).catch((err) => {
      this.$myMv.hideSpin();
    });
  }

  public handleSubMerchantList = (subMerchantList) => {
    if (subMerchantList.length > 0) {
      subMerchantList.map((item) => {
        item.checked = item.defaulted === "Y";
      });
    }
    return subMerchantList;
  }

  public loadPaymentTpBankList = (isUseSession) => {
    this.$chooseBankCardMv.loadPaymentTpBankList().then(() => {
      if (isUseSession) {
        // 刚从绑定银行卡页面返回把添加的作为默认银行卡
        const defaultCard = this.$chooseBankCardMv.bankList && this.$chooseBankCardMv.bankList[0];
        const { bankName, bankCardTypeName, bankCardNo, id, bankCardTypeCode } = defaultCard || {};
        this.setState({
          selectedBankCardNo: bankCardNo,
          selectedBankCardType: bankCardTypeName,
          selectedBankId: id,
          selectedBankName: bankName,
          selectBankCardCode: bankCardTypeCode,
        }, () => {
          this.thridPartyModeChangeNext(true, $PaymentModeType.ALLINPAY_BANK_CARD);
        });
      }
    });
  }

  public showAccountMabel = () => {
    // 防止在选在账户页面点击浏览器返回返回到付款页面的上级页面
    this.props.history.push({
      pathname: window.location.pathname,
    });
    this.setState({
      isShowMabel: true,
    });
  }

  public loadCapitalAccount = () => {
    const { paymentModeList, orderPartyIds } = this.$myMv;
    let paymentModeId = "";
    if (paymentModeList) {
      paymentModeList.map((paymentMode) => {
        if (paymentMode.code === $PaymentModeType.BANKTRANSFER) {
          paymentModeId = paymentMode.oid;
        }
      });
    }
    const params = { paymentModeId, orderPartyIdList: orderPartyIds };
    this.$myMv.fetchCapitalAccount(params).then((data) => {
      const { capitalAccountList } = data;
      this.$myMv.capitalAccountList = capitalAccountList.map((capitalAccount) => new $Product(capitalAccount));
      if (this.$myMv.capitalAccountList.length === 1) {
        this.setDefaultCheck("", 0, true);
      }
    });
  }

  public setDefaultCheck = (v, index, isFirstEnter = false) => {
    const { capitalAccountList } = this.$myMv;
    capitalAccountList.forEach((item) => {
      item.isDefault = "N";
    });
    capitalAccountList[index].isDefault = "Y";
    if (!isFirstEnter) {
      this.props.history.goBack();
    }
    this.setState(
      {
        isChooseAccount: false,
        isShowMabel: false,
        selectCapitalAccountInfo: {
          bankName: capitalAccountList[index].bankName,
          accountCode: capitalAccountList[index].accountCode,
          bankAccountName: capitalAccountList[index].bankAccountName,
          capitalAccountId: capitalAccountList[index].capitalAccountId,
        },
      });
  }

  public confirmPay = () => {
    const offLineTransferAmount = String(this.state.offLineTransferAmount);
    const { paymentModeList, amountPayable, pics, orderTotalAmountInfo } = this.$myMv;
    const { unPayableAmount } = orderTotalAmountInfo;
    if (!paymentModeList.some((lis) => lis.isCheck)) {
      Toast.info("请选择支付方式", 3);
      return;
    }
    if (paymentModeList.filter((mode) => mode.code === $PaymentModeType.BANKTRANSFER)[0] && paymentModeList.filter((mode) => mode.code === $PaymentModeType.BANKTRANSFER)[0].isCheck) {
      if (!offLineTransferAmount && !Number(offLineTransferAmount)) {
        Toast.info("请输入金额");
        return;
      }
      if (offLineTransferAmount && offLineTransferAmount.indexOf(".") > -1) {
        if (!/^[0-9]+(.[0-9]{0,2})?$/.test(offLineTransferAmount)) {
          Toast.info("支付金额最多有两位小数");
          return;
        }
      }
      if (this.state.selectCapitalAccountInfo.accountCode === "") {
        Toast.info("请选择收款账户", 3);
        return;
      }
      if (this.state.offLineTransPaymentData === null) {
        Toast.info("请选择正确的付款日期，财务将根据提交的日期核对银行流水", 5);
        return;
      }
      if (!pics.length && this.wxUploadImgMv.isWx === false) {
        Toast.info("请上传付款凭证", 5);
        return;
      }
      if (this.wxUploadImgMv.imgList && this.wxUploadImgMv.imgList.length === 0 && this.wxUploadImgMv.isWx === true) {
        Toast.info("请上传付款凭证", 5);
        return;
      }
    }
    if (amountPayable > unPayableAmount ) { // 超额
      const { salesOrderIds,feeDocumentIds,docType} = this.state;
      const params = { salesOrderIds,feeDocumentIds,docType:docType === "FeeDocument" ? "FeeDocument" : "SalesOrder" };
      this.$myMv.queryBatchLoadShops(params).then(() => {
        // this.setState({
        //   showQuestionHelp: true,
        // });
      });
      this.setState({
        overtop: true,
      });
    } else {
      this.batchSave();
    }
  }

  public batchSave = () => {
    const { offLineTransPaymentData, selectCapitalAccountInfo, activekey, offLineTransferAmount, salesOrderIds,
      feeDocumentIds, thridPartyKey, selectedBankId, currentThridParityServerAmount, docType, subMerchantList,
      validityType
    } = this.state;
    const { orderTotalAmountInfo, pics, orderOrgList, paymentModeList, amountPayable, shopListInfo, paymentProductList } = this.$myMv;
    const { unPayableAmount } = orderTotalAmountInfo || {};
    if (!activekey && amountPayable > unPayableAmount && orderOrgList.length > 1) { // 超额未选择退款门店
      Toast.info("请选择退款门店");
      return;
    }
    let paymentType = "";
    const paymentModeAndPayInfoList1 = paymentModeList.filter((item) => item.isCheck && item.totalAmount > 0).map((item) => {
      const { oid, code, name, availableTotalAmount, availableTotalAmountForNoLimitPeriod, totalAmount, isCheck } = item || {};
      if (code === $PaymentModeType.BANKTRANSFER) {
        paymentType = $PaymentModeType.BANKTRANSFER;
        return {
          allExpectedAmount: totalAmount,
          code,
          name,
          oid,
        };
      } else if (code === $PaymentModeType.ALLINPAY) {
        paymentType = $PaymentModeType.ALLINPAY;
        const thirdPartyProduct = paymentProductList.filter((res) => res.paymentCode === thridPartyKey)[0]
        return {
          allExpectedAmount: totalAmount,
          bankCardId: selectedBankId,
          code: thirdPartyProduct && thirdPartyProduct.paymentCode, // 第三方产品code
          name,
          oid: thirdPartyProduct && thirdPartyProduct.oid, // 第三方产品oid,
          serviceFee: currentThridParityServerAmount,
        };
      } else {

        const payInfo = shopListInfo.filter((res) => res[item.code] && res[item.code].isCheck).map((res) => {
          const { orgOid, orgCode } = res;
          return {
            orgCode,
            orgOid,
            usableAmount: validityType === $ValidityType.NEW_CREDIT ? res[item.code].availableTotalAmountForNoLimitPeriod : res[item.code].availableTotalAmount,
            expectedAmount: res[item.code].expectedAmount,
            orderExpectedAmount: res[item.code].orderAmount || null,
            freightExpectedAmount: res[item.code].freightExpectedAmount || null,
          };
        });
        return {
          allExpectedAmount: totalAmount,
          allUsableAmount: validityType === $ValidityType.NEW_CREDIT ? availableTotalAmountForNoLimitPeriod : availableTotalAmount ,
          code,
          isCheck,
          name,
          oid,
          payInfo,
          validityType,
        };
      }
    })
    if (paymentModeAndPayInfoList1.filter((mode) => mode.code === $PaymentModeType.ALLINPAY_WECHAT).length > 0) { // 通联微信选中
      const paymentLineIndex = subMerchantList && subMerchantList.length > 0 ? subMerchantList.findIndex((line) => line.checked === true) : -1;
      const subMerchantCode = paymentLineIndex > -1 ? subMerchantList[paymentLineIndex].code : "";
      this.setState({
        animating: true,
      }, () => {
        this.$chooseBankCardMv.paygatewayGetchanneluser().then((res) => {
          this.setState({
            needBind: res.needBind,
            bindUrl: res.bindUrl,
          }, () => {
            if (this.state.needBind) {
              // sessionStorage.setItem("$myMv", JSON.stringify(this.$myMv));
              // sessionStorage.setItem("state", JSON.stringify(this.state));
              // localStorage.setItem("paymentMessage", JSON.stringify({ destination: "toBindBankCard" }))
              this.pageWindowSkip(docType != "FeeDocument" ? `${this.state.bindUrl}&redirectUrl=${window.location.href}?comeBack=true&destination=${scmDestination}`:`${this.state.bindUrl}&redirectUrl=${encodeURIComponent(`${window.location.href}&destination=${scmDestination}`)}`);
            } else {
              const paymentList = [];
              let paymentType = "";
              // const
              paymentList.push({
                paymentDate: DateUtils.toStringFormat(offLineTransPaymentData, "yyyy-MM-dd HH:mm"),
                paymentMode: paymentModeList.filter((mode) => mode.code === $PaymentModeType.BANKTRANSFER)[0] && paymentModeList.filter((mode) => mode.code === $PaymentModeType.BANKTRANSFER)[0].oid,
                receivableAccount: selectCapitalAccountInfo.capitalAccountId,
                amount: offLineTransferAmount,
                voucherImages: pics && pics.length === 0 ? this.wxUploadImgMv.imgList : pics,
                subMerchantCode,
              });
              const paymentModeAndPayInfoList = paymentModeList.filter((item) => item.isCheck && item.totalAmount > 0).map((item) => {
                const { oid, code, name, availableTotalAmount, availableTotalAmountForNoLimitPeriod, totalAmount, isCheck } = item || {};
                if (code === $PaymentModeType.BANKTRANSFER) {
                  paymentType = $PaymentModeType.BANKTRANSFER;
                  return {
                    allExpectedAmount: totalAmount,
                    code,
                    name,
                    oid,
                  };
                } else if (code === $PaymentModeType.ALLINPAY) {
                  paymentType = $PaymentModeType.ALLINPAY;
                  const thirdPartyProduct = paymentProductList.filter((res) => res.paymentCode === thridPartyKey)[0]
                  return {
                    allExpectedAmount: totalAmount,
                    bankCardId: selectedBankId,
                    code: thirdPartyProduct && thirdPartyProduct.paymentCode, // 第三方产品code
                    name,
                    oid: thirdPartyProduct && thirdPartyProduct.oid, // 第三方产品oid,
                    serviceFee: currentThridParityServerAmount,
                    subMerchantCode,
                  };
                } else {
                  const payInfo = shopListInfo.filter((res) => res[item.code] && res[item.code].isCheck).map((res) => {
                    const { orgOid, orgCode } = res;
                    return {
                      orgCode,
                      orgOid,
                      usableAmount: validityType === $ValidityType.NEW_CREDIT ? res[item.code].availableTotalAmountForNoLimitPeriod : res[item.code].availableTotalAmount,
                      expectedAmount: res[item.code].expectedAmount,
                      orderExpectedAmount: res[item.code].orderAmount || null,
                      freightExpectedAmount: res[item.code].freightExpectedAmount || null,
                    };
                  });
                  return {
                    allExpectedAmount: totalAmount,
                    allUsableAmount: validityType === $ValidityType.NEW_CREDIT ? availableTotalAmountForNoLimitPeriod : availableTotalAmount,
                    code,
                    isCheck,
                    name,
                    oid,
                    payInfo,
                    validityType,
                  };
                }
              })
              const params = {
                paymentList,
                paymentModeAndPayInfoList,
                salesOrderIdList: salesOrderIds,
                feeDocumentIdList:feeDocumentIds,
                selectedOrgCode: activekey ? activekey : orderOrgList.length === 1 ? orderOrgList[0].code : "",
              };
              this.setState({
                animating: true,
              }, () => {
                this.$myMv.batchSave(params,docType).then((data) => {
                  if (data.errorCode === $OrderType.SUCCESSCODE) {
                    this.setState({
                      overtop: false,
                    });
                    const paymentMessage = {
                      destination: paymentType === $PaymentModeType.ALLINPAY ? "toPayment" : "",
                      fromWhere: docType === $OrderType.FEEDOCUMENT ? "expense-node-list" : "all-order-payment",
                      feeDocumentId:docType === $OrderType.FEEDOCUMENT ? feeDocumentIds : null
                    }
                    localStorage.setItem("paymentMessage", JSON.stringify(paymentMessage))
                    // 付款完成
                    if (paymentType === $PaymentModeType.BANKTRANSFER) {
                      this.props.history.push({
                        pathname: `/${SITE_PATH}/payment-result-show/${$PaymentModeType.BANKTRANSFER}`,
                      });
                      // this.setState({
                      //   animating: false,
                      // });
                    } else if (paymentType === $PaymentModeType.ALLINPAY) {
                      // sessionStorage.setItem("$myMv", JSON.stringify(this.$myMv));
                      // sessionStorage.setItem("state", JSON.stringify(this.state));
                      this.pageWindowSkip(`${data.redirectUrl}&redirectUrl=${document.location.protocol}//${document.domain}:${window.location.port}/scm/payment-result-show/${$PaymentModeType.ALLINPAY}&paymentMethod=${paymentModeList.filter((lis) => lis.isCheck)[0].code}`);
                      // this.setState({
                      //   animating: false,
                      // });
                    } else {
                      this.props.history.push({
                        pathname: `/${SITE_PATH}/payment-result-show/""`,
                      });
                      // this.setState({
                      //   animating: false,
                      // });
                    }
                  } else if (data.errorCode === $OrderType.EXCESSCODE) { // 超额
                  } else if (data.errorCode === $OrderType.INVALIDPAYMENT) { // 支付方式无效
                    alert(`${data.errorMessage}`, "", [
                      {
                        text: "确定", onPress: () => {
                          this.loadPaymentInfo(true);
                        },
                      },
                    ]);
                  }
                }).catch(() => {
                  this.setState({
                    animating: false,
                  });
                });
              });
            }
          });
        }).catch(() => {
          this.setState({
            animating: false,
          });
        });
      });
    } else {
      const paymentList = [];
      let paymentType = "";
      // const
      paymentList.push({
        paymentDate: DateUtils.toStringFormat(offLineTransPaymentData, "yyyy-MM-dd HH:mm"),
        paymentMode: paymentModeList.filter((mode) => mode.code === $PaymentModeType.BANKTRANSFER)[0] && paymentModeList.filter((mode) => mode.code === $PaymentModeType.BANKTRANSFER)[0].oid,
        receivableAccount: selectCapitalAccountInfo.capitalAccountId,
        amount: offLineTransferAmount,
        voucherImages: pics && pics.length === 0 ? this.wxUploadImgMv.imgList : pics,
      });
      const paymentModeAndPayInfoList = paymentModeList.filter((item) => item.isCheck && item.totalAmount > 0).map((item) => {
        const { oid, code, name, availableTotalAmount, availableTotalAmountForNoLimitPeriod, totalAmount, isCheck } = item || {};
        if (code === $PaymentModeType.BANKTRANSFER) {
          paymentType = $PaymentModeType.BANKTRANSFER;
          return {
            allExpectedAmount: totalAmount,
            code,
            name,
            oid,
          };
        } else if (code === $PaymentModeType.ALLINPAY) {
          paymentType = $PaymentModeType.ALLINPAY;
          const thirdPartyProduct = paymentProductList.filter((res) => res.paymentCode === thridPartyKey)[0]
          return {
            allExpectedAmount: totalAmount,
            bankCardId: selectedBankId,
            code: thirdPartyProduct && thirdPartyProduct.paymentCode, // 第三方产品code
            name,
            oid: thirdPartyProduct && thirdPartyProduct.oid, // 第三方产品oid,
            serviceFee: currentThridParityServerAmount,
          };
        } else {
          const payInfo = shopListInfo.filter((res) => res[item.code] && res[item.code].isCheck).map((res) => {
            const { orgOid, orgCode } = res;
            return {
              orgCode,
              orgOid,
              usableAmount: validityType === $ValidityType.NEW_CREDIT ? res[item.code].availableTotalAmountForNoLimitPeriod : res[item.code].availableTotalAmount,
              expectedAmount: res[item.code].expectedAmount,
              orderExpectedAmount: res[item.code].orderAmount || null,
              freightExpectedAmount: res[item.code].freightExpectedAmount || null,
            };
          });
          return {
            allExpectedAmount: totalAmount,
            allUsableAmount: validityType === $ValidityType.NEW_CREDIT ? availableTotalAmountForNoLimitPeriod : availableTotalAmount,
            code,
            isCheck,
            name,
            oid,
            payInfo,
            validityType,
          };
        }
      })
      const params = {
        paymentList,
        paymentModeAndPayInfoList,
        salesOrderIdList: salesOrderIds,
        feeDocumentIdList:feeDocumentIds,
        selectedOrgCode: activekey ? activekey : orderOrgList.length === 1 ? orderOrgList[0].code : "",
      };
      this.setState({
        animating: true,
      }, () => {
        this.$myMv.batchSave(params,docType).then((data) => {
          if (data.errorCode === $OrderType.SUCCESSCODE) {
            this.setState({
              overtop: false,
            });
            const paymentMessage = {
              destination: paymentType === $PaymentModeType.ALLINPAY ? "toPayment" : "",
              fromWhere:docType === $OrderType.FEEDOCUMENT ? "expense-node-list" : "all-order-payment",
              feeDocumentId:docType === $OrderType.FEEDOCUMENT ? feeDocumentIds : null
            }
            localStorage.setItem("paymentMessage", JSON.stringify(paymentMessage))
            // 付款完成
            if (paymentType === $PaymentModeType.BANKTRANSFER) {
              this.props.history.push({
                pathname: `/${SITE_PATH}/payment-result-show/${$PaymentModeType.BANKTRANSFER}`,
              });
              // this.setState({
              //   animating: false,
              // });
            } else if (paymentType === $PaymentModeType.ALLINPAY) {
              // sessionStorage.setItem("$myMv", JSON.stringify(this.$myMv));
              // sessionStorage.setItem("state", JSON.stringify(this.state));
              this.pageWindowSkip(`${data.redirectUrl}&redirectUrl=${document.location.protocol}//${document.domain}:${window.location.port}/scm/payment-result-show/${$PaymentModeType.ALLINPAY}&paymentMethod=${paymentModeList.filter((lis) => lis.isCheck)[0].code}`);
              // this.setState({
              //   animating: false,
              // });
            } else {
              this.pageWindowSkip(`/${SITE_PATH}/payment-result-show/""`);
              // this.setState({
              //   animating: false,
              // });
            }
          } else if (data.errorCode === $OrderType.EXCESSCODE) { // 超额
          } else if (data.errorCode === $OrderType.INVALIDPAYMENT) { // 支付方式无效
            alert(`${data.errorMessage}`, "", [
              {
                text: "确定", onPress: () => {
                  this.loadPaymentInfo(true);
                },
              },
            ]);
          }
        }).catch(() => {
          this.setState({
            animating: false,
          });
        });
      });
    }
  }

  // public showQuestionHelp = () => {
  //   const { questionHelp } = this.$myMv;
  //   const params = { orderId: this.props.match.params.orderId };
  //   this.$myMv.fetchQuestionHelp(params);
  //   if (questionHelp) {
  //     this.setState({ showQuestionHelp: true });
  //   }
  // }

  public hideQuestionHelp = () => {
    this.setState({
      overtop: false,
      activekey: "",
    });
  }

  public changeOrg = (code) => {
    this.setState({
      activekey: code,
    });
  }

  public setPics = (files) => {
    this.$myMv.setPics(files);
  }

  public getSmallNum = (a, b) => {
    const res = a < b ? a : b;
    return res ? res : 0;
  }

  public calcPaymentModeData = (list, shopThridPartyAmount ?) => {
    const { shopListInfo } = this.$myMv;
    // 作为是否手动改变第三方支付的判断
    const isHanderChangeThridPartyAmount = shopThridPartyAmount;
    shopListInfo.map((item) => {
      let remainAmount = item.waitPayTotalAmount; // 剩余待支付金额
      let remainFreightAmount = item.orgFreightAmount; // 剩余待支付运费金额
      item.shopThridPartyAmount = 0;

      const diviteRebateList = list.filter((items) => items.code === $PaymentModeType.DIVIDE_REBATE && items.isCheck)
      if (diviteRebateList.length > 0) {
        diviteRebateList.map((deviteRebate) => {
          const currentPaymentMode = item[deviteRebate.code];
          currentPaymentMode.freightExpectedAmount = this.getSmallNum(remainFreightAmount, currentPaymentMode.availableFreightAmount); // 运费支付金额
          remainAmount = Number((remainAmount - currentPaymentMode.freightExpectedAmount).toFixed(2));
          remainFreightAmount = Number((remainFreightAmount - currentPaymentMode.freightExpectedAmount).toFixed(2));
          // 订单剩余支付金额
          const orderRemainAmount = remainFreightAmount ? Number((remainAmount -  remainFreightAmount).toFixed(2)) : Number((remainAmount).toFixed(2));
          currentPaymentMode.orderAmount = this.getSmallNum(currentPaymentMode.availableOrderAmount, orderRemainAmount);
          currentPaymentMode.expectedAmount = this.getSmallNum(currentPaymentMode.availableOrderAmount, orderRemainAmount);
          remainAmount = Number((remainAmount - currentPaymentMode.orderAmount).toFixed(2));
        })
      }

      list.filter((obj) => obj.isCheck && obj.code !== $PaymentModeType.DIVIDE_REBATE)
        .map((res) => {
          const currentPaymentMode = item[res.code];
          if (res.code === $PaymentModeType.BANKTRANSFER) {
            // 线下转账单独摘除
          } else if (res.code === $PaymentModeType.ALLINPAY) {
            // 按门店顺序补充各个门店的金额 todo 会造成内部每中支付方式不是按照最优分配规则分配（内部每中支付方式都充分利用）
            if (shopThridPartyAmount && shopThridPartyAmount >= 0) {
              item.shopThridPartyAmount = this.getSmallNum(shopThridPartyAmount, remainAmount);
              shopThridPartyAmount = shopThridPartyAmount - item.shopThridPartyAmount;
            } else {
              item.shopThridPartyAmount = isHanderChangeThridPartyAmount ? 0 : remainAmount;
            }
            remainAmount = remainAmount - item.shopThridPartyAmount;
            if (remainFreightAmount > 0) {
              remainFreightAmount = remainFreightAmount - item.shopThridPartyAmount > 0 ? remainFreightAmount - item.shopThridPartyAmount : 0;
            }
          } else if (res.code === $PaymentModeType.CREDIT) { // 信用支付
            if (currentPaymentMode.isCheck) {
              const availableFreightAmount = this.state.validityType === $ValidityType.NEW_CREDIT ? currentPaymentMode.availableFreightAmountForNoLimitPeriod : currentPaymentMode.availableFreightAmount;
              currentPaymentMode.freightExpectedAmount = this.getSmallNum(remainFreightAmount, availableFreightAmount); // 运费支付金额
              remainAmount = Number((remainAmount - currentPaymentMode.freightExpectedAmount).toFixed(2));
              remainFreightAmount = Number((remainFreightAmount - currentPaymentMode.freightExpectedAmount).toFixed(2));
              // 订单信用可用额度
              const availableTotalAmount = this.state.validityType === $ValidityType.NEW_CREDIT ? currentPaymentMode.availableTotalAmountForNoLimitPeriod : currentPaymentMode.availableTotalAmount;
              const orderAvailableAmount = Number((availableTotalAmount - currentPaymentMode.freightExpectedAmount).toFixed(2)) || 0;
              // 订单剩余支付金额
              const orderRemainAmount = remainFreightAmount ? Number((remainAmount -  remainFreightAmount).toFixed(2)) : Number((remainAmount).toFixed(2));
              currentPaymentMode.orderAmount = this.getSmallNum(orderAvailableAmount, orderRemainAmount);
              currentPaymentMode.expectedAmount = currentPaymentMode.orderAmount + currentPaymentMode.freightExpectedAmount;
              remainAmount = Number((remainAmount - currentPaymentMode.orderAmount).toFixed(2));
            } else {
              currentPaymentMode.freightExpectedAmount = 0;
              currentPaymentMode.orderAmount = 0;
              currentPaymentMode.expectedAmount = 0;
            }
          } else if ( res.code !== $PaymentType.BANK_TRANSFER
            && res.code !== $PaymentType.ALLINPAY_WECHAT
            && res.code !== $PaymentModeType.CREDIT
            && currentPaymentMode.isCheck
            && remainAmount > 0
          ) {

            if (currentPaymentMode.isCheck) {
              //2021.6.22  -->
              currentPaymentMode.freightExpectedAmount = this.getSmallNum(remainFreightAmount, currentPaymentMode.availableFreightAmount); // 运费支付金额
              //<--
              currentPaymentMode.expectedAmount = this.getSmallNum(remainAmount, currentPaymentMode.availableTotalAmount);
              remainAmount = Number((remainAmount - currentPaymentMode.expectedAmount).toFixed(2));
              if (remainFreightAmount > 0) {
                remainFreightAmount = remainFreightAmount - currentPaymentMode.expectedAmount > 0 ? Number((remainFreightAmount - currentPaymentMode.expectedAmount).toFixed(2)) : 0;
              }
            } else {
              currentPaymentMode.expectedAmount = 0;
            }
          } else if (remainAmount <= 0) {
            currentPaymentMode.expectedAmount = 0;
          } else {
            // console.log("计算逻辑之前的情况", item, res.code);
          }
        });
      item.remainAmount = remainAmount;
    })
    this.getPaymentModeAmount();
  }

  // 改变支付模式
  public changePaymentMode = (mode) => {
    const { paymentModeList, shopListInfo } = this.$myMv;
    const { tpTransferAmount } = this.state;
    let isClearTpTransferAmount = true;
    let tpTransferAmountPriority = 0;
    paymentModeList.map((item, index) => {
      // 判读是否清除手动修改的通联支付金额
      if (item.code === $PaymentModeType.ALLINPAY && item.isCheck) {
        tpTransferAmountPriority = index;
      }
      // 改变当前支付方式选中状态与数据初始化
      if (mode.code === item.code) {
        if (index > tpTransferAmountPriority) {
          isClearTpTransferAmount = false;
        }
        item.isCheck = !mode.isCheck;
        item.totalAmount = 0;
        shopListInfo.map((res) => {
          if (res[mode.code]) {
            res[mode.code].isCheck = item.isCheck;
            res[mode.code].expectedAmount = 0;
            res.shopThridPartyAmount = 0;
          }
        });
      }
    })
    if (mode.code === $PaymentModeType.BANKTRANSFER) {
      this.setState({ offLineTransferAmount: 0 }, () => {
        this.calcPaymentModeData(paymentModeList);
      });
    } else {

      const { isCreditLimit } = this.state;
      if (isCreditLimit) {
        // 当选中非信用支付方式时，启用信用支付
        if (mode.code !== $PaymentModeType.CREDIT && mode.isCheck) {
          this.setState({ disabledCredit: false });
        }

        // 当仅选中信用支付方式时，禁用并取消选中
        const creditPayment = this.$myMv.paymentModeList.find(
          mode => mode.code === $PaymentModeType.CREDIT && mode.isCheck
        );
        const checkedPayments = this.$myMv.paymentModeList.filter(mode => mode.isCheck);
        if (creditPayment && checkedPayments.length === 1)  {
          this.setState({ disabledCredit: true, currentThridParityServerAmount: 0 });
          creditPayment.isCheck = false;
          this.$myMv.shopListInfo.filter(item => item[$PaymentModeType.CREDIT] && item[$PaymentModeType.CREDIT].isCheck).map(item => {
            item[$PaymentModeType.CREDIT].isCheck = false;
            item[$PaymentModeType.CREDIT].orderAmount = 0;
            item[$PaymentModeType.CREDIT].expectedAmount = 0;
          });
        } else if (checkedPayments.length === 0) {
          this.setState({ disabledCredit: true });
        }
      }


      const newTpTransferAmount = isClearTpTransferAmount ? 0 : tpTransferAmount
      // 清空第三方支付金额
      this.setState({ tpTransferAmount: newTpTransferAmount }, () => {
        this.calcPaymentModeData(paymentModeList, newTpTransferAmount);
      });
    }
  }

  public getCheckedTransferInfo = (checkedPayment) => {
    const { shopListInfo, freightPayInfo, orderPartyId, orderTotalAmountInfo } = this.$myMv;
    const { unPayableAmount } = orderTotalAmountInfo;
    let freight = 0
    if (freightPayInfo) {
      const { freightNotPayAmount, isCreateFreightFeeDocument } = freightPayInfo;
      freight = isCreateFreightFeeDocument === 'Y' ? freightNotPayAmount : 0;
    }
    let isShowTransferMode = false;
    let transferShopList = [];
    let divideRebateTransferAmount = 0;
    let totalWaitPayAmount = 0;
    if (checkedPayment.length === 1) {
      if([$PaymentModeType.STOREDVALUE, $PaymentModeType.DIVIDE_REBATE].indexOf(checkedPayment[0].code) > -1) {
        isShowTransferMode = true;
        shopListInfo.map((shop) => {
          let transferType = '';
          let availableAmount = 0;
          let amount = 0;
          if (checkedPayment[0].code === $PaymentModeType.DIVIDE_REBATE) {
            transferType = $TransferType.FZFL;
            amount = shop.waitPayTotalAmount - shop[checkedPayment[0].code].availableOrderAmount - freight;
            availableAmount = shop[checkedPayment[0].code].availableOrderAmount;
            divideRebateTransferAmount += shop[checkedPayment[0].code].transferAmount; // 分账可转账金额
            checkedPayment[0].totalAmount = this.getSmallNum(shop[checkedPayment[0].code].availableOrderAmount, shop.waitPayTotalAmount);
          } else if (checkedPayment[0].code === $PaymentModeType.STOREDVALUE) {
            transferType = $TransferType.YECZ;
            amount = shop.waitPayTotalAmount - shop[checkedPayment[0].code].availableTotalAmount;
            availableAmount = shop[checkedPayment[0].code].availableTotalAmount;
            totalWaitPayAmount += shop.remainAmount;
          }
          transferShopList.push({
            transferInOrgId: shop.orgOid,
            availableAmount,
            amount: (amount).toFixed(2),
            transferType,
          })
        })

        if (checkedPayment[0].code === $PaymentModeType.DIVIDE_REBATE && divideRebateTransferAmount === 0) {
          isShowTransferMode = false;
        }
      }
    } else if (checkedPayment.length === 2) {
      let itemRemain = 0;
      let showTransferDivede = false;
      let showTransferStore = false;
      shopListInfo.map((shop) => {
        let shopRemain = shop.waitPayTotalAmount;
        const checkedDivideRebateList = checkedPayment.filter((item) => item.code === $PaymentModeType.DIVIDE_REBATE)
        const checkedDivideRebateInfo: any = checkedDivideRebateList.length === 1 ? checkedDivideRebateList[0] : []
        if (!isEmpty(checkedDivideRebateInfo)) {
          divideRebateTransferAmount += shop[checkedDivideRebateInfo.code].transferAmount;
          itemRemain += shop[checkedDivideRebateInfo.code].availableOrderAmount;
          shopRemain = (shopRemain - shop[checkedDivideRebateInfo.code].availableOrderAmount).toFixed(2);
          if (shop[checkedDivideRebateInfo.code].transferAmount > 0) {
            showTransferDivede = true
            transferShopList.push({
              transferInOrgId: shop.orgOid,
              availableAmount: shop[checkedDivideRebateInfo.code].availableOrderAmount,
              amount: shop[checkedDivideRebateInfo.code].transferAmount,
              transferType: $TransferType.FZFL,
            })
          }
        }

        const checkedStoredValueList = checkedPayment.filter((item) => item.code === $PaymentModeType.STOREDVALUE)
        const checkedStoredValueInfo: any = checkedDivideRebateList.length === 1 ? checkedStoredValueList[0] : []
        if (!isEmpty(checkedStoredValueInfo)) {
          itemRemain += shop[checkedStoredValueInfo.code].availableTotalAmount;
          const shopAvailableTotalAmount = this.getSmallNum(shop[checkedStoredValueInfo.code].availableTotalAmount, shopRemain)
          if (shopRemain - shopAvailableTotalAmount > 0) {
            showTransferStore = true
            transferShopList.push({
              transferInOrgId: shop.orgOid,
              availableAmount: shopAvailableTotalAmount,
              amount:  (shopRemain - shopAvailableTotalAmount).toFixed(2),
              transferType: $TransferType.YECZ,
            })
          }
        }
      })
      isShowTransferMode = showTransferDivede || showTransferStore;
      totalWaitPayAmount = showTransferStore ? (toFixedOptimizing(unPayableAmount - itemRemain) < 0 ? 0 : toFixedOptimizing(unPayableAmount - itemRemain)) : 0;
    } else if (checkedPayment.length > 2) {
      const checkedPaymentList = checkedPayment.filter((item) => item.code === $PaymentModeType.DIVIDE_REBATE)
      if (checkedPaymentList.length > 0) {
        let itemRemain = 0;
        shopListInfo.map((shop) => {
          let shopRemain = shop.waitPayTotalAmount;
          const checkedDivideRebateList = checkedPayment.filter((item) => item.code === $PaymentModeType.DIVIDE_REBATE)
          const checkedDivideRebateInfo: any = checkedDivideRebateList.length === 1 ? checkedDivideRebateList[0] : []
          if (!isEmpty(checkedDivideRebateInfo)) {
            divideRebateTransferAmount += shop[checkedDivideRebateInfo.code].transferAmount;
            itemRemain += shop[checkedDivideRebateInfo.code].availableOrderAmount;
            shopRemain = (shopRemain - shop[checkedDivideRebateInfo.code].availableOrderAmount).toFixed(2);
            if (shop[checkedDivideRebateInfo.code].transferAmount > 0) {
              isShowTransferMode = true;
              transferShopList.push({
                transferInOrgId: shop.orgOid,
                availableAmount: shop[checkedDivideRebateInfo.code].availableOrderAmount,
                amount: shop[checkedDivideRebateInfo.code].transferAmount,
                transferType: $TransferType.FZFL,
              })
            }
          }
        })
      }
    }
    return {
      isShowTransferMode,
      totalWaitPayAmount,
      transferShopList,
      divideRebateTransferAmount,
    }
  }

  public checkIsShowTransferHint = () => {
    const { shopListInfo, paymentModeList } = this.$myMv;
    const checkedPayment = paymentModeList.filter((item) => toJS(item).isCheck);
    const checkedTransferInfo = this.getCheckedTransferInfo(checkedPayment);
    const { isShowTransferMode, totalWaitPayAmount, transferShopList, divideRebateTransferAmount } = checkedTransferInfo
    this.setState({
      isShowTransferHint: isShowTransferMode,
      totalWaitPayAmount,
      accountIdList: transferShopList,
      divideRebateTransferAmount,
    });
  }

  // 改变门店状态
  public changeOneShop = (mode, shop) => {
    // 清空第三方支付金额
    this.setState({ tpTransferAmount: 0 })
    const { shopListInfo, paymentModeList } = this.$myMv;
    const { tpTransferAmount } = this.state;
    let isClearTpTransferAmount = true;
    let tpTransferAmountPriority = 0;
    // 改变当前门店当前支付方式的选中状态
    shopListInfo.map((item) => {
      if (item.orgOid === shop.orgOid) {
        item[mode.code].isCheck = !item[mode.code].isCheck;
        if (!item[mode.code].isCheck) {
          item[mode.code].expectedAmount = 0;
        }
      }
    })
    // 改变门店状态
    paymentModeList.map((item, index) => {
      // 判读是否清除手动修改的通联支付金额
      if (item.code === $PaymentModeType.ALLINPAY && item.isCheck) {
        tpTransferAmountPriority = index;
      }
      if (item.code === mode.code) {
        if (index > tpTransferAmountPriority) {
          isClearTpTransferAmount = false;
        }
        item.isCheck = shopListInfo.filter((res) => res[item.code]).some((res, index) => {
          return res[item.code].isCheck;
        });
      }
    })
    const newTpTransferAmount = isClearTpTransferAmount ? 0 : tpTransferAmount
    // 清空第三方支付金额
    this.setState({ tpTransferAmount: newTpTransferAmount }, () => {
      this.calcPaymentModeData(paymentModeList, newTpTransferAmount);
    });
  }

  // 加入第三方支付
  public thridPartyModeChange = (mode) => {
    // 清空第三方支付金额
    this.setState({ tpTransferAmount: 0 })
    const thirdPartyIsSelect = mode.paymentCode !== this.state.thridPartyKey;
    // 当选择银行卡支付时弹出选中银行卡弹窗
    if (mode.paymentCode === $PaymentModeType.ALLINPAY_BANK_CARD && thirdPartyIsSelect) {
      // 绑定银行卡选择弹窗选择通联支付后
      this.showChooseBankCardModal();
    } else {
      this.thridPartyModeChangeNext(thirdPartyIsSelect, mode.paymentCode);
    }
  }

  public thridPartyModeChangeNext = (thirdPartyIsSelect, paymentCode) => {
    const { paymentProductList, paymentModeList, shopListInfo } = this.$myMv;
    const { selectBankCardCode, originSubMerchantList } = this.state;
    this.handleSubMerchantList(originSubMerchantList);
    // 开始加入第三方支付方式计算逻辑
    paymentProductList.map((item) => {
      item.isCheck = thirdPartyIsSelect ? paymentCode === item.paymentCode : false;
    })
    paymentModeList.map((item) => {
      // 改变第三方支付方式选中状态
      if (item.code === $PaymentModeType.ALLINPAY) {
        item.isCheck = thirdPartyIsSelect;
      }
      // 清空原有数据
      shopListInfo.map((res) => {
        if (res[item.code]) {
          res[item.code].expectedAmount = 0;
          res.shopThridPartyAmount = 0;
        }
      });
    });
    const thridPartyKey = thirdPartyIsSelect ? paymentCode : "";
    let rateCode = "";
    const { WECHATRATE, CREDITCARTRATE, STOREDVALUECARDRATE, CREDITBANKCARTCODE } = $PaymentModeType;
    switch (thridPartyKey) {
      case $PaymentType.ALLINPAY_WECHAT:
        rateCode = WECHATRATE;
        break;
      case $PaymentType.ALLINPAY_BANK_CARD:
        rateCode = selectBankCardCode === CREDITBANKCARTCODE ? CREDITCARTRATE : STOREDVALUECARDRATE
        break;
      default:
        break;

    }

    this.setState({ thridPartyKey, rateCode }, () => {
      this.calcPaymentModeData(paymentModeList);
    });
  }

  // 第三方支付方式金额改变
  public confirmAdjustAmount = () => { // 改变调整金额
    const { paymentModeList } = this.$myMv;
    const { nextTpTransferAmount, tpTransferAmount, mostThridParityAmount } = this.state
    if (nextTpTransferAmount > mostThridParityAmount) {
      Toast.info(`本次最多支付${mostThridParityAmount}元`);
      // 数据未发生改变不需要重新计算
      return;
    } else if (tpTransferAmount === nextTpTransferAmount) {
      this.setState({ showAmountModal: false });
      return;
    } else if (Number(nextTpTransferAmount) === 0) {
      Toast.info(`输入结果不能为0`);
      return;
    } else if (nextTpTransferAmount === "") {
      Toast.info(`输入结果不能为空`);
      return;
    }
    this.setState({
      showAmountModal: false,
      tpTransferAmount: Number(nextTpTransferAmount),
    }, () => {
      this.calcPaymentModeData(paymentModeList, Number(nextTpTransferAmount));
    });
  }

  public changeAdjustAmount = (value) => {
    // const preValue = this.formatValue(value, "thirdParty")
    this.setState({
      nextTpTransferAmount: value,
    });
  }

  // 线下转账调整金额
  public changeOffLineTransferAmount = (value) => {
    // const offLineTransferAmount = this.formatValue(value);
    this.setState({
      offLineTransferAmount: value,
    }, () => {
      this.getPaymentModeAmount();
    });
  }

  public showMore = (mode) => {
    mode.showMore = !mode.showMore;
  }

  public formatValue = (value, type ?) => {
    const { mostThridParityAmount } = this.state;
    let offLineTransferAmount = null;
    offLineTransferAmount = value.replace(/[^\d.]/g, "").replace(/\.{2,}/g, ".").replace(".", "$#$").replace(/\./g, "").replace("$#$", ".").replace(/^(\-)*(\d+)\.(\d\d).*$/, "$1$2.$3")
    if (offLineTransferAmount.indexOf(".") < 0 && offLineTransferAmount !== "") {// 以上已经过滤，此处控制的是如果没有小数点，首位不能为类似于 01、02的金额
      offLineTransferAmount = parseFloat(offLineTransferAmount);
    }
    if (type === "thirdParty" && offLineTransferAmount > mostThridParityAmount) {
      return mostThridParityAmount;
    } else if (offLineTransferAmount == 0 || offLineTransferAmount === "") {
      return offLineTransferAmount;
    } else {
      return Number(offLineTransferAmount);
    }
  }

  public changeDate = (val) => {
    this.setState({ offLineTransPaymentData: val });
  }

  public isWxUploadingPicture = (blon) => {
    if (blon) {
      this.$myMv.showSpin();
    } else {
      this.$myMv.hideSpin();
    }
  }

  public goBack = () => {
    const { selectedBankCardNo, thridPartyKey } = this.state;
    const { paymentModeList, shopListInfo, paymentProductList } = this.$myMv;
    this.setState({
      isShowChooseBankCardModal: false,
      thridPartyKey: selectedBankCardNo ? thridPartyKey : "",
    }, () => {
      // 清空第三方支付数据数据
      if (!selectedBankCardNo) {
        paymentProductList.map((item) => {
          item.isCheck = false;
        })
        paymentModeList.map((item) => {
          // 改变第三方支付方式选中状态
          if (item.code === $PaymentModeType.ALLINPAY) {
            item.isCheck = false;
          }
          // 清空原有数据
          shopListInfo.map((res) => {
            if (res[item.code]) {
              res[item.code].expectedAmount = 0;
              res.shopThridPartyAmount = 0;
            }
          });
        });
        this.calcPaymentModeData(paymentModeList);
      }
    });
  }

  public showChooseBankCardModal = () => {
    this.setState({
      isShowChooseBankCardModal: true,
    });
  }

  public chooseBank = (bank) => {
    this.setState({
      isShowChooseBankCardModal: false,
      selectedBankName: bank.bankName,
      selectedBankCardType: bank.bankCardTypeName,
      selectedBankCardNo: bank.bankCardNo,
      selectedBankId: bank.id,
      selectBankCardCode: bank.bankCardTypeCode,
      tpTransferAmount: 0,
      bankInfo: bank,
    }, () => {
      if (this.state.selectedBankId) {
        bank.isCheck = true;
        this.$chooseBankCardMv.bankList.filter((lis) => lis.id !== bank.id).map((card) => {
          card.isCheck = false;
        });
        // 重新计算金额
        this.thridPartyModeChangeNext(true, $PaymentModeType.ALLINPAY_BANK_CARD);
      }
    });
  }

  // public skipToBindCard = (cb) => {
  //   sessionStorage.setItem("$myMv", JSON.stringify(this.$myMv));
  //   sessionStorage.setItem("state", JSON.stringify(this.state));
  //   localStorage.setItem("paymentMessage", JSON.stringify({ destination: "toBindBankCard" }))
  //   cb && cb();
  // }

  public adjustAmount = (e) => { // 调整金额弹窗
    e.stopPropagation();
    this.setState({
      showAmountModal: true,
    });
  }

  public hideAmountModal = () => {
    this.setState({
      nextTpTransferAmount: this.state.currentThridParityAmount,
      showAmountModal: false,
    });
  }

  public getPaymentModeAmount = () => {
    const { paymentModeList, shopListInfo, paymentProductList } = this.$myMv;
    const { offLineTransferAmount, tpTransferAmount, thridPartyKey, mostThridParityAmount, rateCode } = this.state;
    let payAmount = 0;
    let beforeThirdParityModePayAmount = 0;
    let currentThridParityServerAmount = 0;
    let isBeforeThirdParityMode = true;
    paymentModeList.map((item) => {
      if (item.code === $PaymentModeType.ALLINPAY) {
        const calcThridParityAmount = Number(Number(sum(shopListInfo.map((res) => res.shopThridPartyAmount))).toFixed(2));
        isBeforeThirdParityMode = false;
        item.totalAmount = calcThridParityAmount; // tpTransferAmount 与 calcThridParityAmount 共同作用 currentThridParityAmount 值
        // 计算服务费
        paymentProductList.map((mode) => {
          if (mode.paymentCode === thridPartyKey) {
            let rate = 0;
            if (mode.rateList && mode.rateList.length > 0) {
              mode.rateList.map((res) => {
                if (res.rateCode === rateCode) {
                  // todo totalAmount 做上下限比较
                  if (res.rateBookList.length > 0) {
                    res.rateBookList.map((lis) => {
                      if (lis.endValue === null || item.endValue === "" || lis.endValue === undefined) {
                        if (lis.beginValue <= item.totalAmount) {
                          rate = lis.rate;
                        }
                      } else {
                        if ((lis.beginValue <= item.totalAmount) && (item.totalAmount < lis.endValue)) {
                          rate = lis.rate;
                        } else if (item.totalAmount >= lis.endValue) {
                          rate = lis.rate;
                        }
                      }
                    });
                  }
                }
              });
            }
            currentThridParityServerAmount = toFixedOptimizing(item.totalAmount * rate, 100);
          }
        })
        this.setState({
          currentThridParityAmount: item.totalAmount,
          currentThridParityServerAmount,
          nextTpTransferAmount: calcThridParityAmount,
          mostThridParityAmount: tpTransferAmount === 0 ? calcThridParityAmount : mostThridParityAmount, // 只有在重新计算数据时（非调整第三方支付金额）才会改变
        });
        if (thridPartyKey && !calcThridParityAmount) {
          item.isCheck = false;
          this.setState({ thridPartyKey: "" });
        }
      } else if (item.code === $PaymentModeType.BANKTRANSFER) {
        item.totalAmount = offLineTransferAmount ? Number(Number(offLineTransferAmount).toFixed(2)) : 0;
        this.setState({ disabledTransfer: item.isCheck })
      } else if (item.code === $PaymentModeType.DIVIDE_REBATE) {
        item.totalAmount = Number(sum(shopListInfo.map((res) => res[item.code] ? res[item.code].expectedAmount : 0)).toFixed(2));
        this.setState({ disabledDivideRebate: item.isCheck })
      } else {
        item.totalAmount = Number(sum(shopListInfo.map((res) => res[item.code] ? res[item.code].expectedAmount : 0)).toFixed(2));
        // item.totalAmount = Number(sum(shopListInfo.map((res) => res[item.code].expectedAmount)).toFixed(2));
      }
      payAmount += item.totalAmount;
      if (isBeforeThirdParityMode && item.code !== $PaymentModeType.BANKTRANSFER) {
        beforeThirdParityModePayAmount += item.totalAmount;
      }
    });
    // this.$myMv.amountPayable = Number((payAmount + currentThridParityServerAmount).toFixed(2));
    this.$myMv.amountPayable = Number(payAmount.toFixed(2));
    this.$myMv.beforeThirdParityModePayAmount = beforeThirdParityModePayAmount;
    this.checkIsShowTransferHint();
  }

  public renderIcon = (code) => {
    let icon = "";
    switch (code) {
      // case $PaymentModeType.STOREDVALUE:
      //   icon = "scmIconfont scm-icon-caiwu scm-icon-caiwu-self";
      //   break;
      // case $PaymentModeType.CREDIT:
      //   icon = "scmIconfont scm-icon-xinyongqia";
      //   break;
      // case $PaymentModeType.REBATEDEDUCTION:
      //   icon = "scmIconfont scm-icon-fanli";
      //   break;
      // case $PaymentModeType.BANKTRANSFER:
      //   icon = "scmIconfont scm-icon-zhuanzhang";
      //   break;
      case $PaymentType.ALLINPAY_BANK_CARD:
        icon = "scmIconfont scm-yinhangka";
        break;
      case $PaymentType.ALLINPAY_WECHAT:
        icon = "scmIconfont scm-Fill";
        break;
      default:
        break;
    }
    return icon;
  }

  public showRateModal = (list) => {
    const { bankInfo } = this.state; // bankCardRate STORED_VALUE_CARD_RATE CREDIT_CARD_RATE
    if (list.paymentCode === $PaymentType.ALLINPAY_WECHAT) { // 微信
      this.setState({
        showRate: true,
        selectRateList: list && list.rateList && list.rateList[0] && list.rateList[0].rateBookList && list.rateList[0],
      }, () => {
        $("html").css("overflow", "hidden");
        $("body").css("overflow", "hidden");
      });
    } else { // 银行卡
      const data = list.rateList && list.rateList.filter((item) => item.rateCode === bankInfo.bankCardRate);
      this.setState({
        showRate: true,
        selectRateList: data && data[0],
      }, () => {
        $("html").css("overflow", "hidden");
        $("body").css("overflow", "hidden");
      });
    }
  }

  public hideRateModal = () => {
    this.setState({
      showRate: false,
    }, () => {
      $("html").css("overflow", "scroll");
      $("body").css("overflow", "scroll");
    });
  }

  public showPaymentLine = () => {
    this.setState({isShowPaymentLine: true});
  }

  public changePaymentLine = (line) => {
    const {subMerchantList} = this.state;
    if (subMerchantList.length > 0) {
      subMerchantList.map((item) => {
        item.checked = false;
        if (item.code === line.code) {
          item.checked = true;
        }
      });
    }
    this.setState({
      subMerchantList,
      isShowPaymentLine: false,
    });
  }

  public transferCallback = () => {
    this.loadPaymentInfo(true, () => {
      const { paymentModeList } = this.$myMv;
      const storedValuedMode = paymentModeList.find((mode) => mode.code === $PaymentModeType.STOREDVALUE);
      if (storedValuedMode) {
        this.changePaymentMode(storedValuedMode);
      }
    });
  }

  public showCreditModal = (isShowCreditModal) => {
    this.setState({ isShowCreditModal });
  }

  public onChangeValidity = (validityType) => {
    this.setState({ validityType, isShowCreditModal: false }, () => {
      this.calcPaymentModeData(this.$myMv.paymentModeList);
    });
  }

  public showCreditTips = (isShowCreditTips) => {
    this.setState({ isShowCreditTips });
  }

  public render() {
    const {
      isShowMabel, activekey, overtop, selectCapitalAccountInfo, offLineTransPaymentData,
      offLineTransferAmount, thridPartyKey, isChooseAccount, isShowChooseBankCardModal, showAmountModal, isShowCreditTips,
      tpTransferAmount, selectedBankName, selectedBankCardType, selectedBankCardNo, nextTpTransferAmount, validityType,
      isHaveAllInPay, currentThridParityAmount, currentThridParityServerAmount, mostThridParityAmount, isShowCreditModal,
      showRate, selectRateList, isShowPaymentLine, subMerchantList, isShowTransferHint, totalWaitPayAmount,
      accountIdList, salesOrderIds, divideRebateTransferAmount, disabledTransfer, disabledDivideRebate, disabledCredit
    } = this.state;
    const { pics, capitalAccountList, amountPayable, isSpin, orderOrgList, shopListInfo, paymentModeList, orderTotalAmountInfo, paymentProductList, beforeThirdParityModePayAmount, freightPayInfo, createdTransferList } = this.$myMv;
    const { payableAmount, auditedAmount, unAuditedAmount, unPayableAmount } = orderTotalAmountInfo || {};
    const { docType } = this.state;
    let remainUnpaymentAmount = Number((unPayableAmount - amountPayable).toFixed(2))
    remainUnpaymentAmount = remainUnpaymentAmount > 0 ? remainUnpaymentAmount : 0;
    const paymentAmount = Number(amountPayable + currentThridParityServerAmount).toFixed(2);
    const paymentLineIndex = subMerchantList && subMerchantList.length > 0 ? subMerchantList.findIndex((line) => line.checked === true) : -1;
    const selectedSubMerchant = paymentLineIndex > -1 && subMerchantList[paymentLineIndex].name;
    return (
      <PaymentPage id="pageWrap" style={{ position: overtop ? "fixed" : "relative"}}>
        <Spin spinning={isSpin}>
          <OrderPaymentAccountShow
            payableAmount={(Number(payableAmount) + currentThridParityServerAmount).toFixed(2)}
            auditedAmount={auditedAmount}
            unAuditedAmount={unAuditedAmount}
            unPayableAmount={(Number(unPayableAmount) + currentThridParityServerAmount).toFixed(2)}
          />
          {
            ((isShowTransferHint && (divideRebateTransferAmount > 0 || totalWaitPayAmount > 0 ))
             || createdTransferList.length > 0 ) &&
              <TransferHint
                salesOrderIdList={salesOrderIds}
                createdTransferList={createdTransferList}
                totalWaitPayAmount={totalWaitPayAmount}
                accountIdList={accountIdList}
                divideRebateTransferAmount={divideRebateTransferAmount}
                reload={this.transferCallback}
              />
          }
          <div style={{ marginTop: "10px", paddingBottom: "90px" }}>
            <PayMethod>
              <ListName>
                <span>支付方式</span>
                {
                  freightPayInfo && freightPayInfo.isCreateFreightFeeDocument === $CartType.ISCREATEFREIGHTFEEDOCUMENT && <span className={"freight"}>注：首付款需 ≥ 运费金额 <span>{freightPayInfo.freightNotPayAmount}</span> 元</span>
                }
              </ListName>
              <PaymentModeList
                paymentModeList={paymentModeList}
                isChooseAccount={isChooseAccount}
                shopListInfo={shopListInfo}
                changePayment={this.changePaymentMode}
                changeShop={this.changeOneShop}
                showMore={this.showMore}
                selectCapitalAccountInfo={selectCapitalAccountInfo}
                showAccountMabel={this.showAccountMabel}
                date={offLineTransPaymentData}
                changeDate={this.changeDate}
                renderIcon={this.renderIcon}
                disabledDivideRebate={disabledDivideRebate}
                disabledTransfer={disabledTransfer}
                pics={pics}
                offLineTransferAmount={offLineTransferAmount}
                changeOffLineTransferAmount={this.changeOffLineTransferAmount}
                setPics={this.setPics}
                isWxUploadingPicture={this.isWxUploadingPicture}
                showCreditModal={this.showCreditModal}
                validityType={validityType}
                validityTypeName={ValidityTypeName[validityType]}
                disabledCredit={disabledCredit}
                showCreditTips={this.showCreditTips}
              />
              {
                isHaveAllInPay &&
                <ThridPartyPaymentModeList
                  thridPartyPaymentModeList={paymentProductList}
                  beforeThirdParityModePayAmount={beforeThirdParityModePayAmount}
                  thridPartyKey={thridPartyKey}
                  currentThridParityAmount={currentThridParityAmount}
                  currentThridParityServerAmount={currentThridParityServerAmount}
                  selectedBankName={selectedBankName}
                  selectedBankCardType={selectedBankCardType}
                  selectedBankCardNo={selectedBankCardNo}
                  showChooseBankCardModal={this.showChooseBankCardModal}
                  adjustAmount={this.adjustAmount}
                  renderIcon={this.renderIcon}
                  shopListInfo={shopListInfo}
                  // amountPayable={amountPayable}
                  unPayableAmount={unPayableAmount}
                  showPaymentLine={this.showPaymentLine}
                  thridPartyModeChange={this.thridPartyModeChange}
                  showRateModal={this.showRateModal}
                  selectedSubMerchant={selectedSubMerchant}
                  subMerchantList={subMerchantList}
                />
              }
              <div className="remain-to-payment">剩余支付：{remainUnpaymentAmount}</div>
              {
                isHaveAllInPay &&
                <ChooseBankCardModal
                  isShow={isShowChooseBankCardModal}
                  goBack={this.goBack}
                  chooseBank={this.chooseBank}
                  skipToBindCard={this.saveData}
                  bankRedirectUrl={docType != "FeeDocument" ? `${window.location.href}&comeBack=true&destination=${scmDestination}` : encodeURIComponent(`${window.location.href}&destination=${scmDestination}` )}
                />
              }
              {
                isHaveAllInPay &&
                <AdjustAmountModal
                  confirmAdjustAmount={this.confirmAdjustAmount}
                  showAmountModal={showAmountModal}
                  mostAmount={mostThridParityAmount}
                  transferAmount={nextTpTransferAmount}
                  hideAmountModal={this.hideAmountModal}
                  changeAdjustAmount={this.changeAdjustAmount}
                />
              }
              {
                isHaveAllInPay && <ViewRatesModal
                  showModal={showRate}
                  hideRateModal={this.hideRateModal}
                  selectRateList={selectRateList}
                />
              }
            </PayMethod>
            <ScrollFreezeTitleBar
              styled={{ paddingBottom: "0px" }}
              isHaveBankTranfer={(paymentModeList.filter((mode) => mode.code === $PaymentModeType.BANKTRANSFER).length > 0) && (paymentModeList.filter((mode) => mode.code === $PaymentModeType.ALLINPAY).length <= 0)}
            />
          </div>
        </Spin>
        <ConfirmButton>
          <Button
            type="primary"
            disabled={!(Number(paymentAmount) > 0)}
            onClick={this.confirmPay}
          >
            确认支付 ¥{paymentAmount}
          </Button>
        </ConfirmButton>
        <CapitalAccountListMobel
          isShowMabel={isShowMabel}
          capitalAccountList={capitalAccountList}
          setDefaultCheck={this.setDefaultCheck}
        />
        <ExcessIndication
          overtop={overtop}
          orderOrgList={orderOrgList}
          activekey={activekey}
          changeOrg={this.changeOrg}
          hideQuestionHelp={this.hideQuestionHelp}
          batchSave={this.batchSave}
        />
        <ActivityIndicator
          toast={true}
          text="Loading..."
          animating={this.state.animating}
        />
        <Modal
          visible={isShowCreditModal}
          transparent={true}
          maskClosable={true}
          popup
          animationType="slide-up"
          onClose={() => this.showCreditModal(false)}
          title="信用支付模式"
        >
          <List style={{ paddingBottom: 30 }}>
            <CheckboxItem checked={validityType === $ValidityType.NEW_CREDIT} onClick={() => this.onChangeValidity($ValidityType.NEW_CREDIT)}>
              新模式信用
            </CheckboxItem>
            <CheckboxItem checked={validityType === $ValidityType.REGULAR_CREDIT} onClick={() => this.onChangeValidity($ValidityType.REGULAR_CREDIT)}>
              常规信用
            </CheckboxItem>
          </List>
        </Modal>
        {
          isShowPaymentLine &&
          <PaymentLine
            subMerchantList={subMerchantList}
            onChange={this.changePaymentLine}
            goBack={() => this.setState({isShowPaymentLine: false})}
          />
        }
        <Modal
          visible={isShowCreditTips}
          transparent={true}
          maskClosable={false}
          onClose={() => this.showCreditTips(false)}
          title="提示"
          footer={[{
            text: "我知道了",
            onPress: () => this.showCreditTips(false)
          }]}
        >
          <div style={{ height: "auto", overflow: "scroll", textAlign: "left" }}>
            您支付的订单中含有“欠货补订”订单不支持信用支付，请选择其他支付方式
          </div>
        </Modal>
      </PaymentPage>
    );
  }

}

export default AllOrderPayment;

const PaymentPage = styled.div`// styled
  & {
    width: 100%;
    background: #F2F2F2;
    padding-bottom: 66px;
    color: #333333;
    height: ${document.documentElement.clientHeight}px;
    overflow-y: scroll;
    .remain-to-payment {
      height: 40px;
      background-color: #F2F2F2;
      text-align: right;
      font-size: 13px;
      line-height: 40px;
      padding-right: 19px;
    }
    .gray-bg {
      display: block;
      height: 10px;
      background: #f2f2f2;
    }
    .hide {
      display: none;
    }
    .bank .am-list-body {
      border: 0;
    }
    .bank .am-list-line {
      margin-left: 0 !important;
    }
    .bank .am-list-item {
      padding-left: 0;
    }
    .bank .am-list-body .choose .am-list-line .am-list-content {
      padding-top: 20px;
      color: #333;
      font-size: 14px;
    }
    .ant-radio-wrapper {
      height: 23px;
      color: #333;
    }
    .am-list-line::after {
      height: 1px !important;
    }
    .am-list-body::before {
      height: 1px !important;
    }
    @media (min-resolution: 2dppx) {
      html:not([data-scale]) .am-list-body::after {
        display: none !important;
      }

      .am-list-body::after {
        display: none !important;
      }
    }
    //.date {
    //  border-bottom: none !important;
    //}
  }
`;

const ConfirmButton = styled.div`// styled
  & {
    padding: 12px 15px;
    background: #fff;
    position: fixed;
    bottom: 0;
    width: 100%;
    z-index: 99;
    .am-button-primary.am-button-disabled {
      background: #d9d9d9 !important;
    }
    .am-button-primary {
      border-radius: 3px;
      background: #307dcd;
      height: 42px;
      line-height: 42px;
      font-size: 16px;
      font-family: "PingFangSC-Regular";
    }
  }
`;

const PayMethod = styled.div`// styled
  & {
    width: 100%;
    /* margin-top: 10px;*/
    .balance .am-list-content {
      font-size: 14px;
      color: #333;
      > span:first-child {
        display: inline-block;
        width: 40%;
        font-size: 14px;
        color: #333;
      }
      > span:last-child {
        color: #999;
        font-size: 12px;
      }
    }
    .balance .am-list-extra {
      width: 16%;
      flex-basis: 15%;
    }
    .pay {
      .ant-radio-group {
        width: 100%;
      }
      .list-item {
        margin: 10px 15px 15px 15px;
      }
      .ant-radio-group div {
        overflow: hidden;
        /*margin-top: 10px;*/
      }
      .right {
        float: right;
        color: #999;
        > input {
          border: 0;
        }
      }
      .ant-radio-wrapper {
        float: left;
      }
    }
  }
`;

const ListName = styled.div`// styled
  & {
    width: 100%;
    height: 40px;
    background: #fff;
    padding: 10px 16px;
    font-size: 13px;
    font-family: "SourceHanSansCN-Normal";
    font-weight: 400;
    color: rgba(51, 51, 51, 1);
    position: relative;
    :after {
      content: '';
      position: absolute;
      background-color: #D8D8D8 !important;
      display: block;
      z-index: 1;
      top: auto;
      right: auto;
      bottom: 0;
      left: 0;
      width: 100%;
      height: 1px;
      transform-origin: 50% 100%;
      transform: scaleY(0.5);
    }
    .freight {
      float: right;
      font-size:12px;
      font-family:SourceHanSansCN-Regular,SourceHanSansCN;
      font-weight:400;
      color:rgba(48,125,205,1);
      margin-right: 16px;
      > span {
        color: #FF3030;
      }
    }
  }
`;
