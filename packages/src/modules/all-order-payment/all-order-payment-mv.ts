import { autowired, bean } from "@classes/ioc/ioc";
import { $TransferHintService } from "@classes/service/$transfer-hint-service"
import { action, observable, toJS } from "mobx";
import { $OrderService } from "../../classes/service/$order-service";
import { $ComponentService } from "../../classes/service/$component-service";
import { $File } from "../../classes/entity/$file";
import { $QuestionHelp } from "../../classes/entity/$question-help";
import { $PaymentModeInfo } from "../../classes/entity/$payment-mode-info";
import { $PaymentMode } from "../../classes/entity/$payment-mode";
import { beanMapper } from '../../helpers/bean-helpers';

@bean($AllOrderPaymentMv)
export class $AllOrderPaymentMv {
  @autowired($OrderService)
  public $orderService: $OrderService;
  @autowired($ComponentService)
  public $ComponentService: $ComponentService;
  @autowired($TransferHintService)
  public $transferHintService: $TransferHintService;

  @observable public pics: $File[] = [];

  @observable public paymentModeId: number;

  @observable public capitalAccountList: any[] = [];

  @observable public amountPayable: number = 0;

  @observable public beforeThirdParityModePayAmount: number = 0;

  @observable public isSpin: boolean = false;

  @observable public orderPartyId: number;

  @observable public questionHelp: object = new $QuestionHelp();

  @observable public orderOrgList: any[] = [];

  @observable public shopListInfo: any[] = [];

  @observable public paymentModeList: any[] = [];

  @observable public paymentProductList: any[] = [];

  @observable public orderTotalAmountInfo: any = {};

  @observable public orderPartyIds: any[] = [];

  @observable public freightPayInfo: any;

  @observable public createdTransferList: any[] = []; // 已生成转账单

  // @observable public paymentModeList: $PaymentMode[] = [];

  @action
  public getShopListInfo(shopListInfo, paymentModeList) {
    const orderPartyIds = [];
    shopListInfo.map((item) => {
      item.remainAmount = item.waitPayTotalAmount;
      item.shopThridPartyAmount = 0;
      orderPartyIds.push(item.orgOid)
      paymentModeList.map((res) => {
        if (item[res.code]) {
          item[res.code].expectedAmount = 0;
          item[res.code].isCheck = false;
          item.shopThridPartyAmount = 0;
          item[res.code] = new $PaymentModeInfo(item[res.code]);
        }
      });
    })
    paymentModeList.map((item, index) => {
      item.priority = index;
      item.isCheck = false;
      item.showMore = false;
      const { paymentProductList } = item;
      if (paymentProductList && paymentProductList.length > 0) {
        this.paymentProductList = paymentProductList;
      }
    });
    this.shopListInfo = shopListInfo;
    this.orderPartyIds = orderPartyIds;
    this.paymentModeList = paymentModeList.map((item) => new $PaymentMode(item));
    // console.log("看看数据初始化结果货",this.paymentModeList)
  }
  @action
  public getOrderTotalAmountInfo(obj = {}) {
    this.orderTotalAmountInfo = obj;
  }

  @action
  public showSpin() {
    this.isSpin = true;
  }

  @action
  public hideSpin() {
    this.isSpin = false;
  }

  @action
  public clearPics() {
    this.pics = [];
  }
  @action
  public formatShopListInfoData(list) {
    // list.map((item) => {
    //   const shopList = item.payInfo.map((res) => {
    //     res[`${item.code}`] = item.usableAmount
    //   })
    // })
  }

  @action
  public clearAmountPayable() {
    this.amountPayable = 0;
  }
  @action
  public clearBeforeThirdParityModePayAmount() {
    this.beforeThirdParityModePayAmount = 0;
  }
  //
  // @action
  // public setPaymentModeList(data) {
  //   this.paymentModeList = data.paymentModeAndPayInfoList.map((mode) => new $PaymentMode(mode));
  // }

  @action
  public fetchPaymentInfo(params) {
    return this.$orderService.queryAllPaymentInfo(params);
  }

  @action
  public queryBatchLoadShops(params) {
    return this.$orderService.queryBatchLoadShops(params).then((data) => {
      console.log(data.orderPartyInfo);
      this.orderOrgList = data.orderPartyInfo;
    }).catch((err) => {
      console.log(err);
    });
  }

  @action
  public batchSave(params,docType) {
    if(docType === "FeeDocument"){
     return this.$orderService.batchFeeDocumentPaymentSave(params)
    }else{
      return this.$orderService.batchSave(params);
    }
  }
  @action
  public checkTongLianPaymentStatus(params) {
    return this.$ComponentService.checkTongLianPaymentStatus(params);
  }
  @action
  public checkContinuePay(params) {
    return this.$ComponentService.checkContinuePay(params);
  }
  @action
  public cancelTongLianPay(params) {
    return this.$ComponentService.cancelTongLianPay(params);
  }

  @action
  public setPics(pics: any[]) {
    this.pics = pics;
  }

  @action
  public fetchCapitalAccount(params) {
    return this.$orderService.queryCapitalAccount(params);
  }

  // @action
  // public saveOrder(params) {
  //   return this.$orderService.saveOrderInfo(params);
  // }
  @action
  public fetchQuestionHelp(params) {
    this.$orderService.queryQuestionHelp(params).then((data) => {
      this.questionHelp = data;
    }).catch((err) => {
      console.log(err);
    });
  }
  @action
  public loadTransferRecordList = (params) => {
    return this.$transferHintService.transferRecordList(params).then((res) => {
      this.createdTransferList = res.transferList;
    });
  }
  @action
  public queryOldData(obj) {
    beanMapper(obj, this);
    console.log(this);
  }
}
