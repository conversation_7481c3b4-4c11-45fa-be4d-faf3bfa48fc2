import { bean } from "@classes/ioc/ioc";
import { findIndex } from "lodash";
import { action, computed, observable } from "mobx";
import { $BankAccount } from "../entity/$bank-account";
import { $File } from "../entity/$file";

@bean($SubmitPayMv)
export class $SubmitPayMv {
  @observable public bankList: $BankAccount[] = [];
  @observable public payTime: string;
  @observable public memo: string;
  @observable public payType: string;
  @observable public payAmount: string;
  @observable public pics: $File[] = [];
  @observable public salesOrderId: string;
  @observable public enterpriseReceivableId: number;

  @action
  public setBankList(bankList: $BankAccount[]) {
    this.bankList = bankList;
  }

  @action
  public setBankListChecked(checked, id) {
    const index = findIndex(this.bankList, { id });
    if (index > -1) {
      this.bankList.map((item) => item.checked = false);
      this.bankList[index].checked = checked;
    }
    if (checked) {
      this.setEnterpriseReceivableId(id);
    }
  }

  @action
  public setPayTime(payTime: string) {
    this.payTime = payTime;
  }

  @action
  public setMemo(memo: string) {
    this.memo = memo;
  }

  @action
  public setPayType(payType: string) {
    this.payType = payType;
  }

  @action
  public setPayAmount(payAmount: string) {
    this.payAmount = payAmount;
  }

  @action
  public setPics(pics: any[]) {
    this.pics = pics;
  }

  @action
  public setSalesOrderId(salesOrderId: string) {
    this.salesOrderId = salesOrderId;
  }

  @action
  public setEnterpriseReceivableId(enterpriseReceivableId: number) {
    this.enterpriseReceivableId = enterpriseReceivableId;
  }

  @action
  public getFormData() {
    return {
      enterpriseReceivableId: this.enterpriseReceivableId,
      memo: this.memo,
      payAmount: this.payAmount,
      payTime: this.payTime,
      payType: this.payType,
      pics: this.pics,
      salesOrderId: this.salesOrderId,
    };
  }

  @computed
  get formData() {
    return {
      enterpriseReceivableId: this.enterpriseReceivableId,
      memo: this.memo,
      payAmount: this.payAmount,
      payTime: this.payTime,
      payType: this.payType,
      pics: this.pics,
      salesOrderId: this.salesOrderId,
    };
  }

}
