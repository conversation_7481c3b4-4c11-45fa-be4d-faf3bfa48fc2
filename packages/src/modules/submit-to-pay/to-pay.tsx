import {autowired} from "@classes/ioc/ioc";
import {Checkbox, DatePicker, ImagePicker, List, TextareaItem, Toast} from "antd-mobile";
import {isEmpty, merge} from "lodash";
import {toJS, transaction} from "mobx";
import {observer} from "mobx-react";
import React from "react";
import styled from "styled-components";
import {$BankAccount} from "../../classes/entity/$bank-account";
import {$ComponentService} from "../../classes/service/$component-service";
import DateUtils from "../../classes/utils/DateUtils";
import {REQUEST_SERVER} from "../../helpers/ajax-helpers";
import {SITE_PATH} from "../app";
import { $CartMv } from "../shop-cart/cart-mv";
import { $SubmitOrderMv } from "../submit-order/submit-order-mv";
import { $SubmitPayMv } from "./submit-pay-mv";
import { $AppStore, AppStoreKey } from "../../classes/stores/app-store-mv";

declare let window: any;
const sitePath = window.ENV.sitePath;

const Item = List.Item;
const CheckboxItem = Checkbox.CheckboxItem;

@observer
export class ToPay extends React.Component<any, any> {
  @autowired($CartMv)
  public $CartMv: $CartMv;
  @autowired($AppStore)
  public $AppStore: $AppStore;
  @autowired($SubmitPayMv)
  public $SubmitPayMv: $SubmitPayMv;

  @autowired($SubmitOrderMv)
  public $SubmitOrderMv: $SubmitOrderMv;

  @autowired($ComponentService)
  public $componentService: $ComponentService;

  constructor(props) {
    super(props);
    this.state = {
      checkId: null,
    };
  }

  public componentDidMount() {
    this.$componentService.queryPaymentAccount().then((data) => {
      const { financialAccountList } = data;
      transaction(() => {
        this.$SubmitPayMv.setBankList(financialAccountList);
        this.$SubmitPayMv.setPayType(this.$SubmitOrderMv.payType);
        this.$SubmitPayMv.setPayAmount(this.$CartMv.totalAmount);
      });
    }).catch((reason) => console.log(reason.response.body.message));
  }

  public onDateChange = (date) => {
    this.$SubmitPayMv.setPayTime(DateUtils.toStringFormat(date, "yyyy-MM-dd"));
  }

  public onTextAreaChange = (v) => {
    this.$SubmitPayMv.setMemo(v);
  }

  public onEnterpriseReceivableIdChange = (e, id: number) => {
    if (e.target.checked) {
      this.setState({ checkId: id });
      this.$SubmitPayMv.setEnterpriseReceivableId(id);
    }
  }

  public renderAccountList = (bankList) => {
    return bankList && bankList.map((item: $BankAccount) => {
      return <div>
              <span className="checkbox">
                <CheckboxItem key={item.oid} checked={this.state.checkId == item.oid}
                              onChange={(e) => this.onEnterpriseReceivableIdChange(e, item.oid)}/></span>
        <PanelItem>
          <div>开户名称 {item.bankAccount}</div>
          <div>银行账户 {item.name}</div>
          <div>开户银行 {item.openBank}</div>
        </PanelItem>
      </div>;
    });
  }

  public onSubmit = () => {
    const { payType, consigneeInfo, salesOrderId } = this.$SubmitOrderMv;
    const params = {...this.$SubmitPayMv.getFormData(),
      ...payType, ...consigneeInfo, ...salesOrderId,
    };
    Toast.loading("Loading...");
    this.$componentService.createReceiptPayment(params).then((res) => {
      this.$AppStore.clearPageMv(AppStoreKey.SINGLESHOPORDERLISTSTORE);
      this.props.history.push({ pathname: `${sitePath}/shop/order-list` });
      Toast.hide();

    }).catch((reason) => console.log(reason.response.body.message));
  }

  public onConfirm = () => {
    const { memo, payType, consigneeInfo } = this.$SubmitOrderMv;
    const { selectedRows } = this.$CartMv;
    this.$SubmitOrderMv.setFormData(consigneeInfo, payType, memo, selectedRows);
    Toast.loading("Loading...");
    this.$componentService.createOrder(toJS(this.$SubmitOrderMv.getFormData())).then((data) => {
      Toast.hide();
      const { id } = data;
      this.$AppStore.clearPageMv(AppStoreKey.SINGLESHOPORDERLISTSTORE);
      this.props.history.push({ pathname: `/${SITE_PATH}/shop/order-list` });
    }).catch((reason) => console.log(reason.response.body.message));
  }

  public uploadImage = (file: any) => {
    const formData = new FormData();
    formData.append("file", file.file);

    const xhr = new XMLHttpRequest();

    xhr.open("POST", `${REQUEST_SERVER}/k/integration/scm/payvoucher/upload`, true);
    xhr.setRequestHeader("tenant", localStorage.getItem("tenant"));
    xhr.setRequestHeader("token", localStorage.getItem("token"));
    xhr.onload = () => {
      if (xhr.status === 200) {
        const url = JSON.parse(xhr.responseText).data.imageUrl.url;
        const _file = merge({}, file.file, { url });
        this.addFile(_file);
      }

    };
    xhr.send(formData);
  }

  public addFile = (file) => {
    const { pics } = this.$SubmitPayMv;
    const fileList = pics.slice();
    fileList.push(file);
    this.$SubmitPayMv.setPics(fileList);
  }

  public onUpload = (files, type, index) => {
    if (type === "add") {
      this.setState({ files }, () => this.uploadImage(files[files.length - 1]));
    } else if (type === "remove") {
      this.$SubmitPayMv.setPics(files);
    }
  }
  public cancelOperate = () => {
    this.$AppStore.clearPageMv(AppStoreKey.SHOPCART);
    this.props.history.push({ pathname: `${window.ENV.sitePath}/shop/cart` });
  }
  public render() {
    const { payTime, pics, bankList } = this.$SubmitPayMv;
    return (
      <Wrapper>
        <Panel>
          <DatePicker
            mode="date"
            value={isEmpty(payTime) ? null : new Date(DateUtils.toStringFormat(payTime) as any)}
            onChange={this.onDateChange}>
            <Item arrow="horizontal">转账日期</Item>
          </DatePicker>
        </Panel>
        <Panel>
          <Title>汇款账号</Title>
          {this.renderAccountList(bankList)}
        </Panel>
        <Panel>
          <TextareaItem
            rows={3}
            placeholder="汇款备注（可选填）"
            onChange={this.onTextAreaChange}/>
        </Panel>
        <Panel className="last">
          <Title>上传汇款凭证 <Tip>限上传5张，单张不超过4M</Tip></Title>
          <ImagePicker
            files={pics}
            selectable={pics.length < 5}
            onChange={this.onUpload}/>
        </Panel>
        <Bottom>
          <span onClick={this.cancelOperate}>取消</span>
          <span onClick={() => this.onSubmit()}>确定汇款 ¥{this.$CartMv.totalAmount}</span>
        </Bottom>
      </Wrapper>
    );
  }
}

const Wrapper = styled.div`// styled
  & {

  }
`;
const Panel = styled.div`// styled
  & {
    background: #ffffff;
    padding: 12px 14px;
    border-top: 1px solid #D8D8D8;
    border-bottom: 1px solid #D8D8D8;
    margin-top: 20px;
    &:first-child {
      padding: 0;
      border-top: 0;
      margin-top: 0
    }
    &.last {
      margin-bottom: 50px;
    }

    .checkbox {
      display: inline-block;
      width: 10%;
      .am-list-item {
        padding-left: 0;
      }
      .am-list-item .am-list-thumb:first-child {
        margin-right: 0;
      }
    }
  }
`;
const Title = styled.div`// styled
  & {
    font-size: 17px;
    color: #202020;
    &.dark-title {
      color: #8A8A8A;
    }
  }
`;
const PanelItem = styled.span`// styled
  & {
    border: 1px solid #D8D8D8;
    border-radius: 10px;
    padding: 12px;
    margin-top: 10px;
    display: inline-block;
    width: 90%;
    vertical-align: middle;
    > div {
      line-height: 24px;
    }
  }
`;
const Tip = styled.span`// styled
  & {
    float: right;
    font-size: 14px;
    color: #8A8A8A;
  }
`;
const Bottom = styled.div`// styled
  & {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    border-top: 1px solid #D8D8D8;
    > span {
      display: inline-block;
      height: 50px;
      line-height: 50px;
      text-align: center;
      &:first-child {
        width: 40%;
        background: #ffffff;
      }
      &:last-child {
        width: 60%;
        background: #008FED;
        color: #fff;
      }
    }
  }
`;
