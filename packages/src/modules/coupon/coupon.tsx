import { autowired } from "@classes/ioc/ioc";
import { Spin } from "antd";
import { Checkbox, List } from "antd-mobile";
import { observer } from "mobx-react";
import * as React from "react";
import { withRouter } from "react-router";
import styled from "styled-components";
import { SITE_PATH } from "../app";
import { $CouponMv } from "./coupon-mv";
import { NoGoods } from "../../components/no-goods/no-goods";
import queryString from "querystring";
import { $AppStore, AppStoreKey } from "../../classes/stores/app-store-mv";
const Item = List.Item;
const CheckboxItem = Checkbox.CheckboxItem;

@withRouter
@observer
class Coupon extends React.Component<any, any> {
  @autowired($AppStore)
  public $AppStore: $AppStore;
  @autowired($CouponMv)
  public $myMv: $CouponMv;

  constructor(props) {
    super(props);
    this.state = {
    };
  }
  // 离开记录滚动高度
  public saveData = () => {
    this.$myMv.scrollHeight = $(".cart-coupon-wrapper").scrollTop();
    this.$AppStore.savePageMv(AppStoreKey.COUPONLIST, this.$myMv);
  }
  public componentWillUnmount(): void {
    this.saveData();
  }
  public componentDidMount() {
    document.title = "优惠卷列表";
    setTimeout(() => {
      const oldData = this.$AppStore.getPageMv(AppStoreKey.COUPONLIST)
      if (oldData) {
        this.$myMv.queryOldData(JSON.parse(oldData));
        this.$AppStore.clearPageMv(AppStoreKey.COUPONLIST);
        $(".cart-coupon-wrapper").scrollTop(this.$myMv.scrollHeight);
      } else {
        this.$myMv.clearMVData();
        this.initPage();
      }
    }, 50);
  }
  public initPage = () => {
    this.$myMv.navLists.map((nav, index) => {
      if (nav.type === "NotUse") {
        nav.checked = true;
      } else {
        nav.checked = false;
      }
    });
    const { queryType } = this.$myMv;
    this.loadCouponList(queryType);
  }

  public loadCouponList = (params) => {
    const paramsnew = { queryType: params, ...queryString.parse(this.props.location.search.substr(1)) };
    console.log(this.props.location)
    console.log(this.props.location.search.substr(1))
    this.$myMv.showSpin();
    this.$myMv.fetchCouponList(paramsnew);
  }

  public onChange = (val) => {
    $(".cart-coupon-wrapper").scrollTop(0);
    this.$myMv.queryType = val.type;
    this.$myMv.changeCoupon(val);
    this.loadCouponList(val.type);
  }

  public goToDetail = (couponId) => {
    this.props.history.push({ pathname: `/${SITE_PATH}/coupon-detail`, state: { couponId } });
  }

  public render() {
    const { navLists, couponList, isSpin, queryType } = this.$myMv;
    return (
      <CartCouponWrapper className="cart-coupon-wrapper">
        <div className="coupon-content">
          <CouponNav>
            {
              navLists.map((v, index) => {
                return <div key={index} onClick={() => this.onChange(v)}><span
                  className={v.checked ? "active" : ""}>{v.name}</span>
                </div>;
              })
            }
          </CouponNav>
          <Spin spinning={isSpin}>
            <CouponItem>
              {
                couponList ? couponList.length > 0 ? queryType === "NotUse" ? couponList.map((coupon, index) => {
                  return (
                    <div className="coupon-box" key={index} onClick={() => this.goToDetail(coupon.couponId)}>
                      <div className="box-left">
                        <p>{coupon.promotionTagLabel}</p>
                        <p>{coupon.condition ? coupon.condition.slice(2) : null}</p>
                      </div>
                      <div className="box-right">
                        <h4>{coupon.title ? coupon.title.length > 10 ? `${coupon.title.slice(0, 10)}...` : coupon.title : null}</h4>
                        <div className="bottom">
                          <p>适用范围：{coupon.scope}</p>
                          <p>有效期至：{coupon.endDate}</p>
                        </div>
                        <div className="coupon-img normal">
                          <div>{coupon.tag}</div>
                        </div>
                      </div>
                    </div>
                  );
                }) : couponList.map((coupon, index) => {
                  return (
                    <div className="coupon-box" key={index} onClick={() => this.goToDetail(coupon.couponId)}>
                      <div className="box-left gray">
                        <p>{coupon.promotionTagLabel}</p>
                        <p>{coupon.condition ? coupon.condition.slice(2) : null}</p>
                      </div>
                      <div className="box-right">
                        <h4>{coupon.title ? coupon.title.length > 10 ? `${coupon.title.slice(0, 10)}...` : coupon.title : null}</h4>
                        <div className="bottom">
                          <p>适用范围：{coupon.scope}</p>
                          <p>有效期至：{coupon.endDate}</p>
                        </div>
                        <div className="coupon-img overdue">
                          <div>{coupon.tag}</div>
                        </div>
                      </div>
                    </div>
                  );
                }) : <NoGoods title="暂无卡券"/> : null
              }
            </CouponItem>
          </Spin>
        </div>
      </CartCouponWrapper>
    );
  }
}

export default Coupon;

const CartCouponWrapper = styled.div`
  &{
    width: 100%;
    height: 100%;
    background: #ECF6FF;
    overflow: scroll;
    .coupon-content{
      padding:0 15px;
      width:100%;
      height: auto;
    }
  }
`;

const CouponItem = styled.div`
  &{
    padding-top:55px;
    .coupon-box{
      background:url("https://order.fwh1988.cn:14501/static-img/scm/ico-voucher-bg-s.png") no-repeat;
      background-size:cover;
      display:flex;
      align-items:center;
      height:${(document.documentElement.clientWidth - 30) * 160 / 614}px;
      margin-bottom:15px;
      .box-left{
        flex:2;
        text-align:center;
        color:#307DCD;
        font-size:10px;
        &.gray{
          color:#A0A0A0;
        }
        p{
          margin:0;
        }
        P:first-child{
          font-size:20px;
        }
      }
      .box-right{
        flex:5;
        position:relative;
        height:${(document.documentElement.clientWidth - 30) * 160 / 614}px;
        h4{
          font-size:14px;
          color:#333333;
          margin:0;
          position:absolute;
          top:9px;
          left:12px;
        }
        .bottom{
          font-size:10px;
          color:#999999;
          margin:0;
          position:absolute;
          bottom:9px;
          left:13px;
          p{
            margin:0;
          }
        }
        .coupon-img{
          position:absolute;
          right:13px;
          top:14px;
          height:${(document.documentElement.clientWidth - 30) * 160 / 614 - 30}px;
          width:${((document.documentElement.clientWidth - 30) * 160 / 614 - 30) * 128 / 110}px;
          div{
            font-size:10px;
            position:absolute;
            width:100%;
            bottom:0;
            text-align:center;
            height:12px;
            line-height:12px;
          }
          &.normal{
            background:url("https://order.fwh1988.cn:14501/static-img/scm/ico-voucher-state-normal.png");
            background-size:cover;
          }
          &.overdue{
            background:url("https://order.fwh1988.cn:14501/static-img/scm/ico-voucher-state-overdue.png");
            background-size:cover;
          }
        }
      }
    }
  }
`;

const CouponNav = styled.div`// styled
  & {
    height: 40px;
    line-height: 40px;
    background: white;
    width: 100%;
    position: fixed;
    top: 0;
    left: 0;
    z-index: 10;
    display: flex;
    div {
      color: #666666;
      font-size: 14px;
      text-align: center;
      flex: 1;
      span {
        padding: 8px;
        border-bottom: 2px solid white;
        &.active {
          border-bottom: 2px solid #307DCD;
          color: #307DCD;
        }
      }
    }
  }
`;
