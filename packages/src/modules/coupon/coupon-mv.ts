import { autowired, bean } from "@classes/ioc/ioc";
import { action, extendObservable, observable } from "mobx";
import { $MyInfoService } from "../../classes/service/$my-info-service";
import { beanMapper } from '../../helpers/bean-helpers';

@bean($CouponMv)
export class $CouponMv {
  @autowired($MyInfoService)
  public $myInfoService: $MyInfoService;

  @observable public navLists: any[] = [
    {
      name: "未使用",
      checked: true,
      type: "NotUse",
    },
    {
      name: "已使用",
      checked: false,
      type: "Used",
    },
    {
      name: "已过期",
      checked: false,
      type: "Expired",
    },
  ];

  @observable public paymentModeList: any[];

  @observable public paymentModeId: number;

  @observable public balanceAmount: number;

  @observable public couponList: any[];

  @observable public isSpin: boolean = false;
  @observable public queryType: string = "NotUse";
  @observable public scrollHeight: number = 0;

  @action
  public showSpin() {
    this.isSpin = true;
  }

  @action
  public hideSpin() {
    this.isSpin = false;
  }

  @action
  public fetchRepayment(params) {
    this.$myInfoService.loadRechargePage(params).then((data) => {
      this.paymentModeId = data.paymentModeList[0].oid;
      data.paymentModeList.map((v, index) => {
        if (v.code !== "wechat") {
          this.balanceAmount = v.balanceAmount;
        }
        extendObservable(v, {
          isShow: index === 0 ? true : false,
        });
      });
      this.paymentModeList = data.paymentModeList;
    });
  }

  @action
  public submitRepay(params) {
    return this.$myInfoService.toRecharge(params);
  }

  @action
  public setIsShow(index) {
    this.paymentModeList.map((v) => {
      v.isShow = false;
    });
    this.paymentModeList[index].isShow = true;
  }

  @action
  public fetchCouponList(params) {
    this.$myInfoService.queryCouponList(params).then((res) => {
      const { couponList } = res;
      this.couponList = couponList;
      this.hideSpin();
    }).catch((err) => {
      this.hideSpin();
      this.couponList = [];
    });
  }

  @action
  public changeCoupon(val) {
    this.navLists.map((nav, index) => {
      if (nav.type === val.type) {
        nav.checked = true;
      } else {
        nav.checked = false;
      }
    });
  }
  @action
  public queryOldData(obj) {
    beanMapper(obj, this);
    console.log(this);
  }
  @action
  public clearMVData() {
   this.navLists = [
      {
        name: "未使用",
        checked: true,
        type: "NotUse",
      },
      {
        name: "已使用",
        checked: false,
        type: "Used",
      },
      {
        name: "已过期",
        checked: false,
        type: "Expired",
      },
    ];

   this.paymentModeList = [];

   this.paymentModeId = 0;

   this.balanceAmount = 0;

   this.couponList = [];

   this.isSpin = false;
   this.queryType = "NotUse";
   this.scrollHeight = 0;
  }
}
