import { $SelectOrderparty } from "@classes/entity/$select-orderparty"
import { $SelectScheme } from "@classes/entity/$select-scheme"
import { autowired, bean } from "@classes/ioc/ioc";
import { action, computed, observable } from "mobx";
import { $OrderService } from "../../classes/service/$order-service";
import { $AllOrder } from "../../classes/entity/$all-order";
import { filter, isEmpty, map, sum } from "lodash";
import { $OrderPartyService } from "../../classes/service/$order-party-service";

@bean($CombinedPaymentMv)
export class $CombinedPaymentMv {
  @autowired($OrderService)
  public $orderService: $OrderService;
  @autowired($OrderPartyService)
  public $orderPartyService: $OrderPartyService;

  @observable public orderStatus = [
    {
      activeKey: "ALL",
      name: "全部",
      value: "ALL",
    },
    {
      activeKey: "PAYMENT",
      name: "待付款",
      value: "PAYMENT",
    },
    {
      activeKey: "AUDIT",
      name: "待审核",
      value: "AUDIT",
    },
    {
      activeKey: "DELIVER",
      name: "待收货",
      value: "DELIVER",
    },
    {
      activeKey: "DONE",
      name: "已完成",
      value: "DONE",
    },
  ];

  @observable public page = 0;

  @observable public orderListInfo: $AllOrder[] = [];

  @observable public orderItemList = [];

  @observable public expand = false;

  @observable public orderDetail: any;

  @observable public editOrderId: number;

  @observable public isSpin: boolean = false;

  @observable public allChecked: boolean = false;

  @observable public retailPriceViewPermission: string;

  @observable public commonPayment: any[] = [];

  @observable public currentCommonPayment: any[] = [];

  @observable public time: number = 0;

  @observable public isCreateFreightFeeDocument: string;

  @observable public orderPartyList: $SelectOrderparty[] = [];

  @observable public schemeList: $SelectScheme[] = [];

  @observable public filterOrderListInfo: any[] = [];

  @action
  public clearCommonPayment() {
    this.commonPayment = [];
  }

  @action
  public clearTime() {
    this.time = 0;
  }

  @computed
  get totalAmout() {
    return sum(this.orderListInfo.filter((info) => info.checked).map((order) => {
      return Number(order.payableAmount);
    }));
  }

  @computed
  get orderCount() {
    return Number(this.orderListInfo.filter((info) => info.checked).length);
  }

  @computed
  get shopCount() {
    return sum(this.orderListInfo.filter((info) => info.checked).map((order) => {
      return Number(order.totalQuantity);
    }));
  }

  @action
  public loadOrderPartyList(params) {
    return new Promise((resolve) => {
      this.$orderService.orderpartyList(params)
        .then((data) => {
          this.orderPartyList = data.orderPartyList.map((party) => new $SelectOrderparty(party));
          resolve();
        })
        .catch(() => resolve());
    });
  }

  @action
  public loadSchemeList(params) {
    return new Promise((resolve) => {
      this.$orderService.querySchemeList(params)
        .then((data) => {
          this.schemeList = data.map((scheme) => new $SelectScheme(scheme));
          resolve();
        })
        .catch(() => resolve());
    });
  }

  @action
  public clearAllChecked() {
    this.allChecked = false;
  }

  @action
  public showSpin() {
    this.isSpin = true;
  }

  @action
  public hideSpin() {
    this.isSpin = false;
  }

  @action
  public setEditOrderId(editOrderId) {
    this.editOrderId = editOrderId;
  }

  @action
  public cancleEditOrderId(editOrderId) {
    this.editOrderId = null;
  }

  @action
  public changePage() {
    this.page++;
  }

  @action
  public setPage() {
    this.page = 0;
  }

  @action
  public batchFilterSalesOrder(params) {
    this.$orderService.batchFilterSalesOrder(params).then((data) => {
      this.hideSpin();
      // this.retailPriceViewPermission = data.retailPriceViewPermission;
      this.orderListInfo = data.salesOrderList.map((order) => new $AllOrder(order));
      this.filterOrderListInfo = this.orderListInfo;
      this.isCreateFreightFeeDocument = data.isCreateFreightFeeDocument;
    }).catch((err) => {
      console.log(err);
      this.hideSpin();
    });
  }

  @action
  public setAllChecked(check) {
    if (check) { // 全选勾选
      // 将第一个订单赋值给公共部分
      this.commonPayment = [];
      this.orderListInfo.map((info, index) => {
        const filter = this.filterOrderListInfo.filter((item) => item.docNo === info.docNo)
        if (filter.length > 0) {
          if (isEmpty(this.commonPayment)) {
            this.commonPayment = info.paymentModeInfo.paymentModeList;
            info.checked = true;
            info.disabled = false;
          } else {
            info.checked = this.compareArrIsEqual(this.commonPayment, info.paymentModeInfo.paymentModeList);
            info.disabled = !this.compareArrIsEqual(this.commonPayment, info.paymentModeInfo.paymentModeList);
          }
        }
      });
    } else { // 全选取消
      this.commonPayment = [];
      this.orderListInfo.map((info) => {
        info.checked = check;
        info.disabled = false;
      });
    }
    this.allChecked = check;
  }

  @action
  public setChooseOne(check, orderId, key) {
    let selectedNumber = 0;
    this.orderListInfo.map((info) => {
      if (info.orderId === orderId) {
        info.checked = !check ? check : !this.commonPayment.length ? true : this.compareArrIsEqual(this.commonPayment, info.paymentModeInfo.paymentModeList);
      }
      if (info.checked) {
        selectedNumber += 1
        this.commonPayment = info.paymentModeInfo.paymentModeList;
      }
    });
    this.orderListInfo.map((info) => {
      if (selectedNumber === 0) {
        info.disabled = false;
        this.commonPayment = [];
      } else {
        info.disabled = this.commonPayment ? !this.compareArrIsEqual(this.commonPayment, info.paymentModeInfo.paymentModeList) : false;
      }
    });
    this.allChecked = selectedNumber === this.orderListInfo.length;
  }

  @action
  public compareArrIsEqual(a = [], b = []) {
    // if (a.length > 0 && b.length > 0 && a.length === b.length) {
    if (a.length > 0 && b.length > 0 && a.length === b.length) {
      return a.every((item) => {
        return b.filter((res) => res.oid === item.oid).length === 1;
      });
    } else if (a.length === 0 && b.length === 0) {
      return true;
    } else {
      return false;
    }
  }
  @action
  public getList(params) {
    return this.$orderService.queryOrderList(params);
  }

  @action
  public getDetail(params) {
    return this.$orderService.queryOrderDetail(params);
  }

  @action
  public getTracking(params) {
    return this.$orderService.queryLogisticsTracking(params);
  }

  @action
  public cancleOrder(orderId) {
    return this.$orderService.reoveOrder(orderId);
  }

  @action
  public editOrder(orderId) {
    return this.$orderService.compileOrder(orderId);
  }

  @action
  public auditOrder(orderId) {
    return this.$orderService.examineOrder(orderId);
  }
  @action
  public copyOrder(params) {
    return this.$orderService.copyOrder(params);
  }
  @action
  public saveShopAndScheme(params) {
    return this.$orderPartyService.saveShopAndScheme(params);
  }
  @action
  public setOrderDetail(obj) {
    this.orderDetail = obj;
  }

  @action
  public checkBatchPaymentMode(params) {
    return this.$orderService.checkBatchPaymentMode(params);
  }
}
