import { autowired } from "@classes/ioc/ioc";
import { Spin } from "antd";
import { Checkbox, Modal, Toast } from "antd-mobile";
import { map } from "lodash";
import { toJS } from "mobx";
import { observer } from "mobx-react";
import * as React from "react";
import { withRouter } from "react-router";
import { ScreeningStores } from "../../components/screening-stores/screening-stores";
import styled from "styled-components";
import { NoGoods } from "../../components/no-goods/no-goods";
import { $CartMv } from "../shop-cart/cart-mv";
import { $CombinedPaymentMv } from "./combined-payment-mv";
import { SITE_PATH } from "../app";
import { SingleOrderInfo } from "../../components/single-order-info/single-order-info";
import { $OrderType } from "../../classes/const/$order-type";
import { $AppStore, AppStoreKey } from "../../classes/stores/app-store-mv";
import { CustomModal } from "../../components/custom-modal/custom-modal";
import { $OrderService } from "@classes/service/$order-service";
import { $CartType } from "@classes/const/$cart-type";

declare let window: any;
declare let $: any;
const alert = Modal.alert;
const CheckboxItem = Checkbox.CheckboxItem;

@withRouter
@observer
class CombinedPayment extends React.Component<any, any> {
  @autowired($AppStore)
  public $AppStore: $AppStore;
  @autowired($CartMv)
  public $CartMv: $CartMv;

  @autowired($CombinedPaymentMv)
  public $combinedPaymentMv: $CombinedPaymentMv;

  @autowired($OrderService)
  public $orderService: $OrderService;

  constructor(props) {
    super(props);
    this.state = ({
      finished: false,
      isShow: false,
      page_size: 5,
      startx: 0,
      starty: 0,
      key: "ALL",
      searchContent: "",
      showPayInfo: false,
      payInfo: null,
      isCheckOrder: true,
      isShowOrder: false,
      isShowFreight: false,
      showOrderPop: false,
    });
  }

  public componentDidMount() {
    document.title = "合并付款订单列表";
    this.$combinedPaymentMv.orderListInfo = [];
    this.$combinedPaymentMv.page = 0;
    this.$combinedPaymentMv.clearAllChecked();
    this.$combinedPaymentMv.clearCommonPayment();
    this.$combinedPaymentMv.clearTime();
    if (this.props.location.state) {
      const { searchText } = this.props.location.state;
      const params = { searchText };
      this.loadFilters().then(() => {
        this.$combinedPaymentMv.showSpin();
        this.$combinedPaymentMv.batchFilterSalesOrder(params);
      });
    }
  }

  public loadFilters = () => {
    const filterP = Promise.all([
      this.$combinedPaymentMv.loadOrderPartyList({ pageIndex: 0, pageSize: 99999999 }),
      this.$combinedPaymentMv.loadSchemeList({}),
    ]);
    return filterP.then(() => {
      Promise.resolve();
    }).catch(() => Promise.resolve());
  };

  public goToOrderDetail = (orderId, orderSchemeName, paymentModeListLength) => {
    this.props.history.push({
      pathname: `/${SITE_PATH}/shop/order-detail/${orderId}/${orderSchemeName}`,
      state: {
        backSource: "all",
        paymentModeListLength,
      },
    });
  }

  public orderButton = (btnType, orderId, orderOrgId, orderSchemeId, payRange) => {
    switch (btnType) {
      case "cancel":
        this.$combinedPaymentMv.cancleOrder(orderId).then((data) => {
          if (data.result) {
            Toast.info("订单已取消", 3);
            setTimeout(() => {
              window.location.reload();
            }, 2500);
            /*const { key } = this.state;
            this.searchOrder(key);*/
          }
        });
        break;
      case "reject":
        this.$combinedPaymentMv.cancleOrder(orderId).then((data) => {
          if (data.result) {
            window.location.reload();
            /*const { key } = this.state;
            this.searchOrder(key);*/
          }
        });
        break;
      case "edit":
        this.$combinedPaymentMv.editOrder(orderId).then((data) => {
          if (data.result) {
            // this.$OrderMv.setEditOrderId(orderId);
            this.$AppStore.clearPageMv(AppStoreKey.SHOPCART);
            this.props.history.push({
              pathname: `/${SITE_PATH}/shop/cart`, state: {
                orderId,
                backSource: "all",
              },
            });
            sessionStorage.setItem("editOrderId", orderId);
          }
        });
        break;
      case "auditPass":
        this.$combinedPaymentMv.auditOrder(orderId).then((data) => {
          if (data.result) {
            Toast.info("审核通过，请等待客服人员审核", 3);
            setTimeout(() => {
              window.location.reload();
            }, 2500);
          }
        });
        break;
      case "submitFinance":
        /*this.$OrderMv.auditOrder(orderId).then((data) => {
          if (data.result) {
            Toast.info("提交成功，请等待财务审核", 3);
            setTimeout(() => {
              window.location.reload();
            }, 2500);
          }
        });*/
        this.$AppStore.clearPageMv(AppStoreKey.SINGLEPAYMENTSTORAGE);
        window.location.href = `/${SITE_PATH}/submit/order-payment/34657/${orderId}/notPay?backSource=all&payRange=${payRange}`;
        break;
      case "nowPay":
        this.$AppStore.clearPageMv(AppStoreKey.SINGLEPAYMENTSTORAGE);
        window.location.href = `/${SITE_PATH}/submit/order-payment/34657/${orderId}/notPay?payRange=${payRange}`;
        break;
      case "goOnPay":
        this.$AppStore.clearPageMv(AppStoreKey.SINGLEPAYMENTSTORAGE);
        window.location.href = `/${SITE_PATH}/submit/order-payment/3456/${orderId}/notPay?payRange=${payRange}`;
        break;
      case "deliveryOrder":
        window.location.href = `/${SITE_PATH}/delivery-order/${orderId}`;
        break;
      case "capyOrder":
        console.log("capyOrder");
        sessionStorage.setItem("editOrderId", null);
        this.$combinedPaymentMv.copyOrder({ slaesOrderId: orderId }).then((res) => {
          const { errorMessage, isSkip } = res;
          if (errorMessage) {
            Toast.info(errorMessage);
          }
          if (isSkip) {
            const saveParams = { orderPartyId: orderOrgId, orderSchemeId };
            this.$combinedPaymentMv.saveShopAndScheme(saveParams).then((data) => {
              const message = data.errorMessage;
              if (message) {
                Toast.info(message);
              } else {
                this.$AppStore.clearPageMv(AppStoreKey.SHOPCART);
                this.$CartMv.openDiscount();
                this.props.history.push({
                  pathname: `/${SITE_PATH}/shop/cart`,
                });
              }
            });
          }
        })
        break;
      default:
        break;
    }
  }

  public chooseAll = (check) => {
    this.$combinedPaymentMv.setAllChecked(check);
  }

  public chooseOne = (check, orderId, key) => {
    this.$combinedPaymentMv.setChooseOne(check, orderId, key);
  }

  public goBack = () => {
    window.history.go(-1);
  }

  public goToAllPayment = () => {
    const { orderListInfo, isCreateFreightFeeDocument } = this.$combinedPaymentMv;
    const { isCheckOrder } = this.state;
    // sessionStorage.setItem("paymentModeList", JSON.stringify(commonPayment));
    const salesOrderIds = [];
    const checkedOrderIds = [];
    orderListInfo.map((order) => {
      if (order.checked) {
        salesOrderIds.push(order);
        checkedOrderIds.push(order.orderId);
      }
    });
    // console.log(salesOrderIds);
    if (salesOrderIds.length <= 0) {
      Toast.info("请勾选订单", 3);
    } else {
      let payRange = null;
      if (isCreateFreightFeeDocument === $CartType.ISCREATEFREIGHTFEEDOCUMENT) {
        payRange = isCheckOrder ? "All" : "Freight";
      } else {
        payRange = null;
      }
      const params = {
        salesOrderIds: checkedOrderIds,
      }
      this.$combinedPaymentMv.checkBatchPaymentMode(params).then((res) => {
        if (res && res.errorCode === "0") {
          this.$AppStore.clearPageMv(AppStoreKey.ALLORDERPAYMENT);
          window.location.href = `/${SITE_PATH}/shop/all-order-payment?payRange=${payRange}`;
          localStorage.setItem("combined-payment-paymentModeList", JSON.stringify(res.paymentModeList));
          localStorage.setItem("combined-payment-salesOrderIds", JSON.stringify(salesOrderIds));
        } else if (res && res.errorCode === $OrderType.INVALIDPAYMENT) { // 支付方式无效
          alert(`${res.errorMessage}`, "", [
            {
              text: "确定", onPress: () => {
                // console.log(909090);
                window.location.reload();
              },
            },
          ]);
        }
      });
    }
  }

  public closePay = () => {
    this.setState({
      showPayInfo: false,
    });
    $("html").css("overflow", "scroll");
    $("body").css("overflow", "scroll");
    $(".orderPage").css("overflow", "scroll");
  }

  public showPayInfo = () => {
    const { orderListInfo } = this.$combinedPaymentMv;
    const salesOrderIds = [];
    const checkedOrderIds = [];
    orderListInfo.map((order) => {
      if (order.checked) {
        salesOrderIds.push(order);
        checkedOrderIds.push(order.orderId);
      }
    });
    if (salesOrderIds.length <= 0) {
      Toast.info("请勾选订单", 3);
    } else {
      this.$orderService.salesorderPayinfo({ salesOrderIdList: checkedOrderIds }).then((data) => {
        console.log("支付信息", data);
        this.setState({
          payInfo: data,
          isLimitPaymentMode: data.isLimitPaymentMode,
          sumAmount: data.totalPayInfo && data.totalPayInfo.totalPayAmount,
        });
        if (data.totalPayInfo && data.totalPayInfo.freightTotalAmount === 0) {
          this.goToAllPayment();
        } else {
          this.setState({
            showPayInfo: true,
            isCheckOrder: true,
          }, () => {
            if ($(".info")) {
              $(".info").scrollTop(0);
            }
            $("html").css("overflow", "hidden");
            $("body").css("overflow", "hidden");
            $(".orderPage").css("overflow", "hidden");
            $(".orderPage").scrollTop(0);
            const { isLimitPaymentMode } = this.state;
            if (isLimitPaymentMode) {
              this.changeCheck({ target: { checked: false } });
            }
          });
        }
      });
    }
  }

  public changeCheck = (e) => {
    const { payInfo } = this.state;
    console.log(e.target.checked, "isCheckOrder");
    this.setState({
      isCheckOrder: e.target.checked,
    }, () => {
      if (e.target.checked) {
        this.setState({
          sumAmount: payInfo.totalPayInfo && (payInfo.totalPayInfo.salesOrderTotalAmount + payInfo.totalPayInfo.freightTotalAmount),
        }, () => {
          console.log(this.state.sumAmount, "true");
        });
      } else {
        this.setState({
          sumAmount: payInfo.totalPayInfo && payInfo.totalPayInfo.freightTotalAmount,
        }, () => {
          console.log(this.state.sumAmount, "false");
        });
      }
    });
  }

  public changeOrder = () => {
    this.setState({
      isShowOrder: !this.state.isShowOrder,
    });
  }

  public changeFreight = () => {
    this.setState({
      isShowFreight: !this.state.isShowFreight,
    });
  }

  public renderPayContent = (isLimitPaymentMode) => {
    const { isCheckOrder, payInfo, sumAmount, isShowOrder, isShowFreight } = this.state;
    return (
      <PayContent>
        {
          isLimitPaymentMode && <div className={"title-notice"}>由于订货方案限制了货款的支付方式，请先单独支付运费！</div>
        }
        <div className={"info"}>
          <p>
            <CheckboxItem onChange={(val) => this.changeCheck(val)} checked={isCheckOrder} disabled={isLimitPaymentMode}>
              <i className={"scmIconfont scm-dingdanjilu"}/>
              <span className={"title"}>订单</span>
            </CheckboxItem>
            <span onClick={this.changeOrder} className={"operation"}>{isShowOrder ? "收起明细" : "查看明细"}
              {isShowOrder ? <i className="scmIconfont scm-icon-jiantou-shang"/> :
                <i className="scmIconfont scm-jiantou-xia"/>}
              </span>
          </p>
          {
            isShowOrder && <InfoList>
              <div className={"title"}>
                <span>订单号</span>
                <span>待付金额</span>
              </div>
              {
                payInfo && payInfo.salesOrderPayInfoList && payInfo.salesOrderPayInfoList.length > 0 && payInfo.salesOrderPayInfoList.map((lis) => {
                  return (
                    <div className={"content"}>
                      <span className={"name"}>{lis.docNo}</span>
                      <span className={"value"}>{lis.waitPayAmount}</span>
                    </div>
                  );
                })
              }
            </InfoList>
          }
          <p>
            <CheckboxItem disabled={true} checked={true}>
              <i className={"scmIconfont scm-kuaidi"}/>
              <span className={"title"}>运费</span>
            </CheckboxItem>
            <span onClick={this.changeFreight} className={"operation"}>{isShowFreight ? "收起明细" : "查看明细"}
              {isShowFreight ? <i className="scmIconfont scm-icon-jiantou-shang"/> :
                <i className="scmIconfont scm-jiantou-xia"/>}
              </span>
          </p>
          {
            isShowFreight && <InfoList>
              <div className={"title"}>
                <span>门店</span>
                <span>待付金额</span>
              </div>
              {
                payInfo && payInfo.freightPayInfoList && payInfo.freightPayInfoList.length > 0 && payInfo.freightPayInfoList.map((lis) => {
                  return (
                    <div className={"content"}>
                      <span className={"name"}>{lis.orgName ? lis.orgName.length > 13 ? lis.orgName.slice(0, 14) + "..." : lis.orgName : null}</span>
                      <span className={"value"}>{lis.waitPayAmount}</span>
                    </div>
                  );
                })
              }
            </InfoList>
          }
        </div>
        <div className={"total"}>
          <CheckboxItem checked={isCheckOrder} onChange={(val) => this.changeCheck(val)} disabled={true}>
            <span className={"sum"}>合计</span>
            <span>
              <span className={"info"}>总计:</span>
              <span className={"Symbol"}>￥</span>
              <span className={"Symbol red"}>{Number(sumAmount).toFixed(2)}</span>
            </span>
            <span>
              <span className={"info"}>商品总数:</span>
              <span className={"black"}> {payInfo && payInfo.totalPayInfo && payInfo.totalPayInfo.totalProductSkuCount} </span>
              <span className={"info"}>件</span>
            </span>
          </CheckboxItem>
        </div>
      </PayContent>
    );
  }

  public cancelPop = () => {
    this.setState({ showOrderPop: false });
    $("html").css("overflow", "scroll");
    $("body").css("overflow", "scroll");
    $(".scroll-ability-wrap").css("overflow", "scroll");
  }

  public showChooseShop = () => {
    this.setState({ showOrderPop: true });
    $("html").css("overflow", "hidden");
    $("body").css("overflow", "hidden");
    $(".scroll-ability-wrap").css("overflow", "hidden");
    $(".scroll-ability-wrap").scrollTop(0);
  }

  public confirmSearch = (selectObj) => {
    const { orderPartyList, schemeList } = selectObj;
    this.$combinedPaymentMv.orderPartyList = orderPartyList || [];
    this.$combinedPaymentMv.schemeList = schemeList || [];
    this.$combinedPaymentMv.filterOrderListInfo = this.getFilterOrderListInfo();
    this.$combinedPaymentMv.setAllChecked(false);
    $("html").css("overflow", "scroll");
    $("body").css("overflow", "scroll");
    $(".scroll-ability-wrap").css("overflow", "scroll");
    this.setState({ showOrderPop: false });
  }

  public getFilterOrderListInfo = () => {
    const { orderPartyList, schemeList, orderListInfo } = this.$combinedPaymentMv;
    const selectedOrderParty = map(orderPartyList.filter((party) => party.checked), "oid");
    const selectedScheme = map(schemeList.filter((scheme) => scheme.checked), "value");
    const needFilterOrderParty = orderPartyList.length !== selectedOrderParty.length;
    const needFilterScheme = schemeList.length !== selectedScheme.length;
    return orderListInfo.filter((order) => {
      const { orderOrgId, orderSchemeId } = order;
      return (needFilterOrderParty ? selectedOrderParty.findIndex((id) => id == orderOrgId) > -1 : true) &&
        (needFilterScheme ? selectedScheme.findIndex((id) => id == orderSchemeId) > -1 : true);
    });
  }

  public render() {
    const { orderListInfo, isSpin, allChecked, totalAmout, orderCount, shopCount, isCreateFreightFeeDocument, orderPartyList, schemeList } = this.$combinedPaymentMv;
    const { showPayInfo, showOrderPop, isLimitPaymentMode } = this.state;
    const filterOrderListInfo = this.getFilterOrderListInfo();
    return (
      <OrderPage className="orderPage">
        <Spin spinning={isSpin}>
          <SearchTypeBar>
            <span onClick={this.showChooseShop}>筛选</span>
            <span onClick={this.goBack}>取消合并</span>
          </SearchTypeBar>
          <OrderLists style={{ marginBottom: filterOrderListInfo ? filterOrderListInfo.length > 2 ? 30 : 0 : 0 }}>
            {
              filterOrderListInfo ? filterOrderListInfo.length > 0 ?
                filterOrderListInfo.map((order, index) => {
                  return <div key={index}>
                    <SingleOrderInfo
                      order={order}
                      goToOrderDetail={this.goToOrderDetail}
                      // retailPriceViewPermission={retailPriceViewPermission}
                      orderPriceViewPermission={"Y"}
                      orderButton={this.orderButton}
                      componentAfreshDidMount={() => {
                        this.componentDidMount()
                      }}
                      showCheckboxItem={true}
                      chooseOne={this.chooseOne}
                      index={index}
                    />
                    <MarginBottom/>
                  </div>
                    ;
                })
                : <NoGoods title="暂无订单" height={document.documentElement.clientHeight / 2}/> :
                <NoGoods title="暂无订单" height={document.documentElement.clientHeight / 2}/>
            }
          </OrderLists>
          {
            orderListInfo ? orderListInfo.length > 0 ? <ChooseAll>
              <CheckboxItem
                onChange={(e) => this.chooseAll(e.target.checked)}
                checked={allChecked}
              />
              <p>全选</p>
              <p>
                <span>共{shopCount}件
                  <span className={"total"}>总计：</span>
                  <span className={"total-num"}>{totalAmout ? Number(totalAmout).toFixed(2) : 0.00}</span>
                </span>
                <br/>
                <span>合并{orderCount}单，(未合并{orderListInfo.length - orderCount}单)</span>
              </p>
              <p>
                {
                  isCreateFreightFeeDocument === $CartType.ISCREATEFREIGHTFEEDOCUMENT ? <span onClick={this.showPayInfo}>合并付款</span> : <span onClick={this.goToAllPayment}>合并付款</span>
                }
              </p>
            </ChooseAll> : null : null
          }
        </Spin>
        <CustomModal
          header={"合并支付信息"}
          confirm={() => this.goToAllPayment()}
          content={this.renderPayContent(isLimitPaymentMode)}
          confirmName={"付款"}
          visible={showPayInfo}
          close={this.closePay}
          isCancle={true}
        />
        {
          showOrderPop && <ScreeningStores
            confirmSearch={this.confirmSearch}
            canclePop={this.cancelPop}
            isHideOrderStatus={true}
            schemeList={toJS(schemeList)}
            orderPartyList={toJS(orderPartyList)}
          />
        }
      </OrderPage>
    );
  }
}

export default CombinedPayment;

const OrderPage = styled.div`// styled
  & {
    width: 100%;
    height: 100%;
    background-color: #f5f5f5;
    // overflow-y: auto;
    color: #2a2a2a;
    // margin-bottom: 50px;
    > .orderPage span {
      text-align: center;
    }
  }
`;

const ChooseAll = styled.div`// styled
  & {
    display: flex;
    align-items: center;
    width: 100%;
    height: 48px;
    background-color: #fff;
    padding: 5px 12px 6px 16px;
    position: fixed;
    bottom: 0;
    z-index: 99;
    .am-list-item {
      display: inline-block;
      padding-left: 0;
      position: relative;
      top: 9px;
    }
    .am-list-item .am-list-thumb:first-child {
      margin-right: 8px;
    }
    > p {
      display: inline-block;
      margin-bottom: 0;
    }
    > p:nth-of-type(1) {
      font-family: "MicrosoftYaHei";
      font-size: 11px;
      color: #333333;
    }
    > p:nth-of-type(2) {
      flex: 1;
      text-align: right;
      margin-right: 10px;
      > span{
        font-family: "MicrosoftYaHei";
        font-size: 12px;
        line-height: 16px;
        color: #666;
        .total {
          color: #333;
          margin-left: 5px;
        }
        .total-num {
          font-weight: 500;
          color: #FF0000;
        }
      }
    }
    > p:nth-of-type(3) {
      > span {
        display: inline-block;
        font-family: "MicrosoftYaHei";
        font-size: 14px;
        height: 36px;
        line-height: 36px;
        color: #FFFFFF;
        background: #307DCD;
        border-radius: 20px;
        padding: 0 16px;
      }
    }
  }
`;

const SearchTypeBar = styled.div`// styled
  & {
    display: flex;
    justify-content: flex-end;
    width: 100%;
    height: 32px;
    background-color: #fff;
    position: fixed;
    top: 0px;
    z-index: 99;
    padding: 0 16px;
    > span {
      height: 100%;
      line-height: 32px;
      font-family: "PingFangSC-Regular";
      font-size: 14px;
      color: #1890FF;
      margin-left: 10px;
    }
    :after {
      content: '';
      position: absolute;
      background-color: #D8D8D8 !important;
      display: block;
      z-index: 1;
      top: auto;
      right: auto;
      bottom: 0;
      left: 0;
      width: 100%;
      height: 1px;
      transform-origin: 50% 100%;
      transform: scaleY(0.5);
    }
  }
`;

const OrderLists = styled.div`// styled
  & {
    width: 100%;
    /*height: 600px;*/
    padding-top: 32px;
    // overflow-y: auto;
    padding-bottom: 50px;
  }
`;

const MarginBottom = styled.div`// styled
  & {
    width: 100%;
    height: 10px;
    background: #f5f5f5;
  }
`;

const PayContent = styled.div`// styled
  & {
    width: 100%;
    height: auto;
    .scm-dingdanjilu {
      color: #FF8627;
    }
    .scm-kuaidi {
      color: #60B547;
    }
    .scmIconfont {
      margin-right: 8px;
    }
    .title {
      font-size:14px;
      font-family:SourceHanSansCN-Regular,SourceHanSansCN;
      font-weight:400;
      color:rgba(51,51,51,1);
      margin-bottom: 8px;
    }
    .toPay {
      font-size:13px;
      font-family:SourceHanSansCN-Regular,SourceHanSansCN;
      font-weight:400;
      color:#999999;
    }
    .amount {
      color: #333333;
    }
    .am-list-item .am-list-thumb:first-child {
      margin-right: 8px;
    }
    .am-list-item {
      padding-left: 0;
    }
    .info {
      max-height: calc(375px - 48px - 50px - 64px - 30px);
      overflow-y: scroll;
    }
    > div {
      > p {
        margin-bottom: 0px;
        position: relative;
      }
    }
    .total {
      //border-top: 1px solid #EFEFEF;
      //padding-top: 20px;
    }
    .sum {
      font-size:14px;
      font-family:SourceHanSansCN-Regular,SourceHanSansCN;
      font-weight:400;
      color:rgba(51,51,51,1);
      margin-right: 8px;
    }
    .info {
      font-size:12px;
      font-family:SourceHanSansCN-Regular,SourceHanSansCN;
      font-weight:400;
      color:rgba(153,153,153,1);
    }
    .Symbol {
      font-size:12px;
      font-family:SourceHanSansCN-Regular,SourceHanSansCN;
      font-weight:400;
      color: #FF3030;
    }
    .red {
      font-size: 16px;
      margin-right: 8px;
    }
    .black {
      color: #333333;
      font-size: 12px;
    }
    .title-notice {
      font-size: 16px;
      color: red;
      font-weight: bold;
    }
    .am-checkbox.am-checkbox-disabled.am-checkbox-checked .am-checkbox-inner {
      border-color: #D9D9D9;
      background: #D9D9D9;
    }
    .am-checkbox.am-checkbox-disabled.am-checkbox-checked .am-checkbox-inner:after {
      border-color: #fff;
    }
    .am-list-item .am-list-line {
      padding-right: 0px;
    }
    .operation {
      //float: right;
      font-size:13px;
      font-family:SourceHanSansCN-Regular,SourceHanSansCN;
      font-weight:400;
      color:rgba(48,125,205,1);
      position: absolute;
      right: 0;
      top: 9px;
    }
    .scm-jiantou-xia, .scm-icon-jiantou-shang {
      margin-right: 0;
      margin-left: 8px;
      position: relative;
      top:2px;
    }
  }
`;

const InfoList = styled.div`// styled
  & {
    width: 100%;
    height: auto;
    background: #F4F4F4;
    border-radius:8px;
    padding: 12px;
    margin-bottom: 10px;
    > div {
      > span:last-child {
        float: right;
      }
    }
    .title {
      > span {
        font-size:12px;
        font-family:SourceHanSansCN-Regular,SourceHanSansCN;
        font-weight:400;
        color:rgba(102,102,102,1);
      }
    }
    .name {
      font-size:13px;
      font-family:SourceHanSansCN-Regular,SourceHanSansCN;
      font-weight:400;
      color:rgba(153,153,153,1);
    }
    .content {
      margin-bottom: 8px;
    }
    .value {
      font-size:13px;
      font-family:SourceHanSansCN-Regular,SourceHanSansCN;
      font-weight:400;
      color:rgba(51,51,51,1);
    }
  }
`;
