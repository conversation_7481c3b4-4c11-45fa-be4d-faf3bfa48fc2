import { autowired, bean } from "@classes/ioc/ioc";
import { Toast } from "antd-mobile";
import { action, observable } from "mobx";
import { $ShippingAddressService } from "../../classes/service/$shipping-address-service";
import { $ComponentService } from "../../classes/service/$component-service";
import { beanMapper } from '../../helpers/bean-helpers';

@bean($ShippingAddressMv)
export class $ShippingAddressMv {
  @autowired($ShippingAddressService)
  public $shippingAddressService: $ShippingAddressService;

  @autowired($ComponentService)
  public $componentService: $ComponentService;

  @observable public addressList: any[] = [];

  @observable public buttonPermission: object = { add: "" };

  @observable public addressInfo: {
    contactPerson: "",
    phoneNumber: "",
    regionId: any[],
    location: "",
    postalCode: "",
    isDefault: "",
    delete: "",
    edit: "",
  };

  @observable public isSpin: boolean = false;
  @observable public scrollHeight: number = 0;

  @action
  public showSpin() {
    this.isSpin = true;
  }

  @action
  public hideSpin() {
    this.isSpin = false;
  }

  @action  // 获取地址列表
  public fetchShippingAddress() {
    this.$shippingAddressService.queryShippingAddress().then((data) => {
      const { addressList, buttonPermission } = data;
      // console.log(data);
      // console.log(addressList);
      this.addressList = addressList;
      this.buttonPermission = buttonPermission;
      // console.log(this.addressList);
      this.hideSpin();
    });
  }

  @action  // 设置默认地址
  public setDefaultAddress(data) {
    return this.$shippingAddressService.setDefaultAddress(data);
  }

  @action  // 删除地址
  public deleteAddress(id) {
    const params = {
      addressId: id,
    }
    this.$shippingAddressService.deleteAddress(params).then((data) => {
      if (data.result) {
        this.addressList.splice(this.addressList.findIndex((v) => v.addressId === id), 1);
        Toast.info("删除地址成功", 3);
      } else {
        Toast.info("删除地址失败", 3);
      }
    });
  }

  @action
  public fetchAddressInfo(params) {
    this.$shippingAddressService.queryAddressById(params).then((data) => {
      this.addressInfo = data;
      // console.log(data);
    });
  }

  @action
  public defaultAddressInfo() {
    this.addressInfo = {
      contactPerson: "",
      phoneNumber: "",
      regionId: [],
      location: "",
      postalCode: "",
      isDefault: "N",
    };
  }

  @action
  public saveAddress(params) {
    return this.$shippingAddressService.editAddress(params);
  }

  @action
  public setContactPerson(PersonName) {
    this.addressInfo.contactPerson = PersonName;
  }

  @action
  public setPostalCode(postalCode) {
    if (postalCode.length <= 6) {
      this.addressInfo.postalCode = postalCode;
    }
  }

  @action
  public setLocation(location) {
    this.addressInfo.location = location;
  }

  @action
  public setPhoneNumber(phoneNumber) {
    if (phoneNumber.length <= 11) {
      this.addressInfo.phoneNumber = phoneNumber;
    }
  }

  @action
  public setAddressRegionId(regionId) {
    this.addressInfo.regionId = regionId;
  }
  @action
  public queryOldData(obj) {
    beanMapper(obj, this);
    console.log(this);
  }
  @action
  public clearMVData() {
   this.addressList = [];
   this.buttonPermission = { add: "" };
   this.addressInfo = {
     contactPerson: "",
     phoneNumber: "",
     regionId: [],
     location: "",
     postalCode: "",
     isDefault: "",
     delete: "",
     edit: "",
    };
   this.isSpin = false;
   this.scrollHeight = 0;
  }
}
