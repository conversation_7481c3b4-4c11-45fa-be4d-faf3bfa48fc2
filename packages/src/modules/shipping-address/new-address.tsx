import { autowired } from "@classes/ioc/ioc";
import { Spin } from "antd";
import { Button, InputItem, List, Picker, TextareaItem, WingBlank } from "antd-mobile";
import { Toast } from "antd-mobile/es";
import { observer } from "mobx-react";
import React from "react";
import { withRouter } from "react-router";
import styled from "styled-components";
import { SITE_PATH } from "../app";
import { $NewAddressMv } from "../new-address/$new-address-mv";
import { $AddressMv } from "../submit-address-form/submit-address-mv";
import { $ShippingAddressMv } from "./$shipping-address-mv";
import { $AppStore, AppStoreKey } from "../../classes/stores/app-store-mv";

declare let window: any;

@withRouter
@observer
class NewAddress extends React.Component<any, any> {
  @autowired($AppStore)
  public $AppStore: $AppStore;
  @autowired($AddressMv)
  public $AddressMv: $AddressMv;

  @autowired($NewAddressMv)
  public $newAddressMv: $NewAddressMv;

  @autowired($ShippingAddressMv)
  public $shippingAddressMv: $ShippingAddressMv;

  public componentDidMount() {
    document.title = "新增地址";
    this.$newAddressMv.showSpin();
    this.$shippingAddressMv.defaultAddressInfo();
    this.$newAddressMv.fetchAddressTree();
  }

  public saveAddress = () => {
    const { addressInfo } = this.$shippingAddressMv;
    const { contactPerson, regionId, location, phoneNumber, postalCode } = addressInfo;
    if (contactPerson === "") {
      Toast.info("请填写收货人");
      return;
    }
    if (phoneNumber === "") {
      Toast.info("请填写联系方式");
      return;
    }
    if (regionId[0] === undefined) {
      Toast.info("请选择地区");
      return;
    }
    if (location === "") {
      Toast.info("请填写详细街道");
      return;
    }
    this.$newAddressMv.showSpin();
    this.$shippingAddressMv.saveAddress(addressInfo).then((data) => {
      if (data.result) {
        this.$AppStore.clearPageMv(AppStoreKey.ADDRESSLIST);
        this.props.history.push({
          pathname: `/${SITE_PATH}/shippingAddress`, state: {
            shopCartTotalAmount: this.props.location.state.shopCartTotalAmount,
            promotionList: this.props.location.state ? this.props.location.state.promotionList : [],
            hasPromotion: this.props.location.state ? this.props.location.state.hasPromotion : null,
          },
        });
      } else {
        this.$newAddressMv.hideSpin();
        Toast.info("提交失败", 3);
      }
    }).catch(() => {
      this.$newAddressMv.hideSpin();
    });
  }

  public chooseAddress(orgionId) {
    this.$shippingAddressMv.setAddressRegionId(orgionId);
  }

  public render() {
    const { addressTree, isSpin } = this.$newAddressMv;
    const { addressInfo } = this.$shippingAddressMv;
    return (
      <Wrap>
        <Spin spinning={isSpin}>
          {
            addressInfo ? <List style={{ position: "relative" }}>
              <InputItem
                className="address_showInput"
                placeholder="收货人"
                value={addressInfo.contactPerson}
                onChange={(e) => this.$shippingAddressMv.setContactPerson(e)}
              >
                收货人
              </InputItem>
              <InputItem
                className="address_showInput"
                type="number"
                placeholder="联系方式"
                value={addressInfo.phoneNumber}
                onChange={(e) => this.$shippingAddressMv.setPhoneNumber(e)}
              >
                联系方式
              </InputItem>
              <InputItem
                className="address_showInput"
                type="digit"
                placeholder="邮政编码"
                value={addressInfo.postalCode}
                onChange={(e) => this.$shippingAddressMv.setPostalCode(e)}
              >
                邮政编码
              </InputItem>
              <Picker
                cols={3}
                data={addressTree}
                // data={addressTree2}
                extra="请选择(可选)"
                onOk={(e) => this.chooseAddress(e)}
                title="选择地区"
                value={addressInfo.regionId}
                onChange={(v) => this.$AddressMv.setAddressId(v)}
              >
                <List.Item arrow="horizontal">地区</List.Item>
              </Picker>
              <TextareaItem
                autoHeight={true}
                title="详细街道"
                placeholder="街道、门牌号"
                value={addressInfo.location}
                onChange={(e) => this.$shippingAddressMv.setLocation(e)}
              />
            </List> : null}
        </Spin>
        <WingBlank className="bottom_btn">
          <Button type="primary" onClick={this.saveAddress} disabled={isSpin}>
            保存
          </Button>
        </WingBlank>
      </Wrap>
    );
  }
}

export default NewAddress;

const WingBlank = styled.div`
  & {
    position:fixed;
    bottom:20px;
    width:100%;
    padding:0 20px;
  }
`;

const Wrap = styled.div`
  & {
    .bottom_btn{
      z-index: 999;
    }
    .am-input-control{
      height: 29px;
      input{
        height: 29px;
      }
      input::-webkit-input-placeholder, input:-moz-placeholder, input:-ms-input-placeholder {
        line-height: 29px;
      }
    }
  }
`;
