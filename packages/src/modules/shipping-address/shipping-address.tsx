import { autowired } from "@classes/ioc/ioc";
import { Spin } from "antd";
import { Button, Checkbox, WingBlank } from "antd-mobile";
import { transaction } from "mobx";
import { observer } from "mobx-react";
import React from "react";
import { withRouter } from "react-router";
import styled from "styled-components";
import { $ShippingAddressType } from "../../classes/const/$shipping-address-type";
import { $Address } from "../../classes/entity/$address";
import { NoGoods } from "../../components/no-goods/no-goods";
import { SITE_PATH } from "../app";
import { $PromotionMatchMv } from "../promotion-match/promotion-match-mv";
import { $AddressMv } from "../submit-address-form/submit-address-mv";
import { $SubmitOrderMv } from "../submit-order/submit-order-mv";
import { $ShippingAddressMv } from "./$shipping-address-mv";
import { $AppStore, AppStoreKey } from "../../classes/stores/app-store-mv";

declare let window: any;
const CheckboxItem = Checkbox.CheckboxItem;
// 通过this.props.history.push 跳转没有问题，但是如果通过window跳转需要储存 $AddressMv，$myMv 这两个数据
@withRouter
@observer
class ShippingAddress extends React.Component<any, any> {

  @autowired($AppStore)
  public $AppStore: $AppStore;
  @autowired($AddressMv)
  public $AddressMv: $AddressMv;

  @autowired($ShippingAddressMv)
  public $myMv: $ShippingAddressMv;

  @autowired($SubmitOrderMv)
  public $SubmitOrderMv: $SubmitOrderMv;

  @autowired($PromotionMatchMv)
  public $PromotionMatchMv: $PromotionMatchMv;
  // 离开记录滚动高度
  public saveData = () => {
    this.$myMv.scrollHeight = $(".address-list").scrollTop();
    this.$AppStore.savePageMv(AppStoreKey.ADDRESSLIST, this.$myMv);
  }
  public componentWillUnmount(): void {
    this.saveData();
  }
  public componentDidMount() {
    document.title = "地址列表";
    setTimeout(() => {
      const oldData = this.$AppStore.getPageMv(AppStoreKey.ADDRESSLIST)
      if (oldData) {
        this.$myMv.queryOldData(JSON.parse(oldData));
        this.$AppStore.clearPageMv(AppStoreKey.ADDRESSLIST);
        $(".address-list").scrollTop(this.$myMv.scrollHeight);
      } else {
        this.$myMv.clearMVData();
        this.initPage();
      }
    }, 50);
  }
  public initPage() {
    transaction(() => {
      this.$myMv.showSpin();
      this.$myMv.fetchShippingAddress();
    });
  }

  public setDefaultCheck = (v, index) => {
    const { addressList } = this.$myMv;
    addressList.forEach((item) => {
      item.isDefault = "N";
    });
    addressList[index].isDefault = "Y";
    const data = {
      addressId: addressList[index].addressId,
    };
    this.$myMv.setDefaultAddress(data);
  }

  public selectAddress = (address: $Address) => {
    if (this.$AddressMv.isFormPay) {
      this.$AddressMv.setIsSelectedOnce(true);
      this.$AddressMv.setSelectedAddress(address);
      this.$AddressMv.isFormPay = false;
      // toJS(this.$PromotionMatchMv.promotionList),
      this.props.history.push({
        pathname: `/${SITE_PATH}/submit/order`, state: {
          promotionList: this.props.location.state ? this.props.location.state.promotionList : [],
          shopCartTotalAmount: this.props.location.state ? this.props.location.state.shopCartTotalAmount : null,
          backSource: this.props.location.state ? this.props.location.state.backSource : null,
          prePageSource: this.props.location.state ? this.props.location.state.prePageSource : null,
          hasPromotion: this.props.location.state ? this.props.location.state.hasPromotion : null,
        },
      });
    }
  }

  public editAddress = (e, id) => {
    e.stopPropagation();
    this.props.history.push({
      pathname: `/${SITE_PATH}/editAddress/${id}`, state: {
        promotionList: this.props.location.state ? this.props.location.state.promotionList : [],
        shopCartTotalAmount: this.props.location.state ? this.props.location.state.shopCartTotalAmount : null,
        backSource: this.props.location.state ? this.props.location.state.backSource : null,
        prePageSource: this.props.location.state ? this.props.location.state.prePageSource : null,
        hasPromotion: this.props.location.state ? this.props.location.state.hasPromotion : null,
      },
    });
  }

  public newAddress = () => {
    this.$AddressMv.setNewAddress();
    this.props.history.push({
      pathname: `/${SITE_PATH}/newAddress`, state: {
        promotionList: this.props.location.state ? this.props.location.state.promotionList : [],
        shopCartTotalAmount: this.props.location.state ? this.props.location.state.shopCartTotalAmount : null,
        backSource: this.props.location.state ? this.props.location.state.backSource : null,
        prePageSource: this.props.location.state ? this.props.location.state.prePageSource : null,
        hasPromotion: this.props.location.state ? this.props.location.state.hasPromotion : null,
      },
    });
  }

  public deleteAddress = (e, id) => {
    e.stopPropagation();
    this.$myMv.deleteAddress(id);
    this.$AddressMv.selectedAddress = null;
    this.$SubmitOrderMv.calculatefreight = 0;
  }

  public renderAddressItem = (addressList: $Address[]) => {
    if (!addressList) {
      return false;
    }
    return addressList.map((address, index) => {
      return (
        <AddressItem key={address.addressId}>
          <div className="content" onClick={() => this.selectAddress(address)}>
            <p>
              <span className="name">{address.contactPerson}</span>
              <span>{address.phoneNumber}</span>
            </p>
            <p>邮编: {address.postalCode}</p>
            <p>{address.location}</p>
          </div>
          <div className="controller">
            {this.$AddressMv.onlySelect ? "" :
              <div className="checkbox">
                <CheckboxItem
                  onChange={(v) => address.isDefault === "N" && this.setDefaultCheck(v, index)}
                  checked={address.isDefault === "Y" ? true : false}
                >
                  设为默认
                </CheckboxItem>
              </div>}
            <div className="f_right">
              {
                address.edit === $ShippingAddressType.EDIT ?
                  <a className="edit" onClick={(e) => this.editAddress(e, address.addressId)}>编辑</a> : null
              }
              {
                address.delete === $ShippingAddressType.DELETE ?
                  <a className="del" onClick={(e) => this.deleteAddress(e, address.addressId)}>删除</a> : null
              }
            </div>
          </div>
        </AddressItem>
      );
    });
  }

  public render() {
    const { addressList, isSpin, buttonPermission } = this.$myMv;
    // console.log(addressList);
    return (
      <AddressListWrap className="address-list">
        <Spin spinning={isSpin}>
          <div className="addressList" style={{ marginBottom: 80 }}>
            {addressList ? addressList.length > 0 ? this.renderAddressItem(addressList) :
              <NoGoods title="暂无收货地址" height={document.documentElement.clientHeight / 2}/> : null}
          </div>
        </Spin>
        {
          buttonPermission.add === $ShippingAddressType.ADD ? <WingBlank className="bottom_btn">
            <Button type="primary" onClick={this.newAddress}>
              新建地址
            </Button>
          </WingBlank> : null
        }
      </AddressListWrap>
    );
  }
}

export default ShippingAddress;
const AddressListWrap = styled.div`
  & {
    width: 100%;
    height: 100%;
    overflow: scroll;
  }
`;
const AddressItem = styled.div`// styled
  & {
    margin-bottom: 12px;
    overflow: hidden;
    font-size: 16px;
    background: #fff;
    border: 1px solid #ddd;
    border-right: none;
    border-left: none;
    .content {
      border-bottom: 1px solid #ddd;
      padding: 10px 10px;
      span {
        margin-right: 10px;
      }
      .name {
        font-size: 18px;
      }
    }
  }
`;

const Typetips = styled.span`// styled
  & {
    display: inline-block;
    height: 20px;
    width: 40px;
    color: #6085dd;
    font-size: 12px;
    line-height: 20px;
    text-align: center;
    border: 1px solid #6085dd;
    border-radius: 4px;
  }
`;

const WingBlank = styled.div`
  & {
    position:fixed;
    bottom:20px;
    width:100%;
    padding:0 20px;
  }
`;

