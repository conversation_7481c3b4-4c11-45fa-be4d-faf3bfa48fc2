import { autowired, bean } from "@classes/ioc/ioc";
import { action } from "mobx";
import { $ScanningLoginService } from "../../classes/service/$scanning-login-service";

@bean(ScanningLoginMv)
export class ScanningLoginMv {
  @autowired($ScanningLoginService)
  public $scanningLoginService: $ScanningLoginService;

  @action
  public getScanningPath() {
    return this.$scanningLoginService.getScanningPath();
  }

  @action
  public getOpenid(params) {
    return this.$scanningLoginService.getOpenid(params);
  }

  @action
  public userFindOne(params) {
    return this.$scanningLoginService.userFindOne(params);
  }

  @action
  public densityFreeLogin(params) {
    return this.$scanningLoginService.densityFreeLogin(params);
  }
}
