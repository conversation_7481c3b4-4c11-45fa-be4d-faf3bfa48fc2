import * as React from "react";
import { observer } from "mobx-react";
import styled from "styled-components";
import { autowired } from "@classes/ioc/ioc";
import { ScanningLoginMv } from "./scanning-login-mv";
import { Toast } from "antd-mobile";
import QRCode from "qrcode.react";
import { SITE_PATH } from "../app";
import { msgError } from "../../helpers/msg-helper";
import { $AppStore, AppStoreKey } from "../../classes/stores/app-store-mv";
import { AnnouncementModelMv } from "../../components/announcement-model/announcement-model-mv"


const ERRORCODE1 = "P001";

@observer
export class ScanningLogin extends React.Component<any, any> {

  @autowired(ScanningLoginMv)
  public scanningLoginMv: ScanningLoginMv;
  @autowired($AppStore)
  public $AppStore: $AppStore;
  @autowired(AnnouncementModelMv)
  public AnnouncementModelMv:AnnouncementModelMv;

  public constructor(props) {
    super(props);
    this.state = {
      currentKey: null,
      scanningPath: null,
      logo: null,
      openid: null,
      oid: null,
      scanningLoginSsoSessionId: null,
    };
  }

  public componentDidMount() {
    document.title = "扫码登陆";
    this.AnnouncementModelMv.removeSessionStorage();
    this.scanningLoginMv.getScanningPath().then((data) => {
      this.setState({
        scanningPath: data.scanningPath,
        currentKey: data.currentKey,
        logo: data.logo,
      }, () => {
        this.getOpenid(this.state.currentKey);
      });
    }).catch((ex) => {
      const { response } = ex;
      msgError(response.data.message);
    });
  }

  public getOpenid = (currentKey) => {
    const interval = setInterval(() => {
      this.scanningLoginMv.getOpenid({ currentKey }).then((data) => {
        this.setState({
          openid: data.openid,
        }, () => {
          if (this.state.openid) {
            clearInterval(interval);
            this.userFindOne();
          }
        });
      }).catch((ex) => {
        const { response } = ex;
        msgError(response.data.message);
      });
    }, 1500);
  }

  public userFindOne = () => {
    const params = {
      conditionType: "OpenId",
      conditionContent: this.state.openid,
    }
    this.scanningLoginMv.userFindOne(params).then((data) => {
      if (data === null) {
        Toast.fail("未查询到数据", 3);
        return;
      }
      if (data.code === ERRORCODE1) {
        Toast.fail(`${data.message}`, 3);
        return;
      }
      this.setState({
        oid: data.oid,
      }, () => {
        this.scanningLoginMv.densityFreeLogin({ userId: this.state.oid }).then((res) => {
          if (res && res.token) {
            localStorage.setItem("scanningLogin", "Y"); // 判断非公众号时限制付款功能
            this.$AppStore.clearPageMv(AppStoreKey.ORDERPARTYSELECTION);
            window.location.href = `/${SITE_PATH}/select?sessionId=${res.token}`;
          }
        }).catch((ex) => {
          const { response } = ex;
          msgError(response.data.message);
          this.getOpenid(this.state.currentKey);
        });
      });
    }).catch((ex) => {
      const { response } = ex;
      msgError(response.data.message);
      this.getOpenid(this.state.currentKey);
    });
  }

  public render() {
    const { scanningPath, logo } = this.state;
    return (
      <ScanningLoginPage>
        <BrandLogo>
          <img src={logo ? logo : "https://order.fwh1988.cn:14501/static-img/scm/logo-brand.png"} alt=""/>
        </BrandLogo>
        <QRCodePage>
          <div>
            {
              scanningPath ? <QRCode value={scanningPath} size={(160 / 375) * document.documentElement.clientWidth}/> :
                <img src="https://order.fwh1988.cn:14501/static-img/scm/scanning-loading.gif" alt=""/>
            }
          </div>
          {
            scanningPath && <div>请使用微信扫码登录</div>
          }
        </QRCodePage>
        <QRCodeShadow/>
        <ScanningLoginFooter>
          <div>
            <img src="https://order.fwh1988.cn:14501/static-img/scm/dxt-logo.png" alt=""/>
          </div>
          <div>Copyright &copy; 2019 PEKON 秉坤 保留所有权利</div>
        </ScanningLoginFooter>
      </ScanningLoginPage>
    );
  }
}

const ScanningLoginPage = styled.div`// styled
  & {
    width: 100%;
    height: 100%;
    background: url("https://order.fwh1988.cn:14501/static-img/scm/login-bg.png") no-repeat center center;
    background-size: 100% 100%;
    @media (min-width: 1025px) {
      background: none;
    }
  }
`;

const BrandLogo = styled.div`// styled
  & {
    width: 100%;
    text-align: center;
    padding: ${28 / 375 * document.documentElement.clientWidth}px 0;
    @media (max-width: 767px) {
      margin-bottom: ${28 / 375 * document.documentElement.clientWidth}px;
    }
    @media (min-width: 1025px) {
      padding: 28px 0;
    }
    img {
      width: ${(96 / 375) * document.documentElement.clientWidth}px;
      height: auto;
      @media (min-width: 1025px) {
        width: 200px;
      }
    }
  }
`;

const QRCodePage = styled.div`// styled
  & {
    width: ${(200 / 375) * document.documentElement.clientWidth}px;
    height: ${(230 / 375) * document.documentElement.clientWidth * (250 / 256)}px;
    background: #F5F5F5;
    border-radius: 8px;
    border: 1px solid rgba(232, 232, 232, 1);
    box-shadow: 0px 2px 2px 0px rgba(0, 0, 0, 0.08);
    padding: ${15 / 375 * document.documentElement.clientWidth}px;
    text-align: center;
    margin: 0 auto;
    @media (min-width: 1025px) {
      width: 256px;
      height: 285px;
      padding: 15px;
    }
    > div:first-child {
      margin-bottom: ${8 / 667 * document.documentElement.clientHeight}px;
      position: relative;
      @media (min-width: 1025px) {
        margin-bottom: 8px;
      }
      > img {
        width: ${(128 / 375) * document.documentElement.clientWidth}px;
        height: auto;
        position: absolute;
        top: 50%;
        left: 50%;
        margin-top: ${((128 / 375) * document.documentElement.clientWidth) / 2}px;
        margin-left: -${((128 / 375) * document.documentElement.clientWidth) / 2}px;
        @media (min-width: 1025px) {
          width: 128px;
          margin-top: 64px;
          margin-left: -64px;
        }
      }
      @media (min-width: 1025px) {
        canvas {
          width: 224px !important;
          height: 224px !important;
        }
      }
    }
    > div:nth-of-type(2) {
      font-size: 16px;
      font-family: SourceHanSansCN-Regular;
      font-weight: 400;
      color: rgba(47, 47, 47, 1);
    }
    @media (min-width: 768px) {
      > div:first-child {
      margin-bottom: ${20 / 667 * document.documentElement.clientHeight}px;
    }
      > div:nth-of-type(2) {
      font-size: 32px;
    }
    }
    @media (min-width: 1025px) {
      > div:first-child {
      margin-bottom: 10px;
    }
      > div:nth-of-type(2) {
      font-size: 16px;
    }
    }
  }
`;

const ScanningLoginFooter = styled.div`// styled
  & {
    width: 100%;
    text-align: center;
    position: absolute;
    bottom: ${(20 / 667) * document.documentElement.clientHeight}px;
    @media (min-width: 1025px) {
      bottom: 20px;
    }
    > div:first-child {
      img {
        width: ${(72 / 375) * document.documentElement.clientWidth}px;
        height: ${(40 / 667) * document.documentElement.clientHeight}px;
        @media (min-width: 1025px) {
          width: 72px;
          height: 40px;
        }
      }
    }
    > div:last-child {
      font-size: 10px;
      font-family: MicrosoftYaHeiUI;
      color: rgba(153, 153, 153, 1);
    }
    @media (min-width: 768px) {
      > div:last-child {
       font-size: 20px;
      }
    }
    @media (min-width: 1025px) {
      > div:last-child {
       font-size: 10px;
      }
    }
  }
`;

const QRCodeShadow = styled.div`// styled
  & {
    width: ${(180 / 375) * document.documentElement.clientWidth}px;
    height: ${(10 / 667) * document.documentElement.clientHeight}px;
    background: rgba(0, 0, 0, 0.08);
    box-shadow: 0px 6px 8px 0px rgba(0, 0, 0, 0.08);
    border-radius: 9px;
    margin: 0 auto;
    margin-top: -${(8 / 667) * document.documentElement.clientHeight}px;
    position: relative;
    z-index: -1;
    @media (min-width: 1025px) {
      width: 180px;
      height: 10px;
      margin-top: -8px;
    }
  }
`;
