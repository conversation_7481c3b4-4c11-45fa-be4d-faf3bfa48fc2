import { autowired } from "@classes/ioc/ioc";
import { Radio, Spin } from "antd";
import { ActivityIndicator, Button, Checkbox, InputItem, List, Modal, Toast } from "antd-mobile";
import { transaction } from "mobx";
import { observer } from "mobx-react";
import React from "react";
import { withRouter } from "react-router";
import styled from "styled-components";
import { $CartType } from "../../classes/const/$cart-type";
import { $Address } from "../../classes/entity/$address";
import { $ComponentService } from "../../classes/service/$component-service";
import DateUtils from "../../classes/utils/DateUtils";
import { PayProductItem } from "../../components/table/pay-product-item";
import { SITE_PATH } from "../app";
import { $PromotionMatchMv } from "../promotion-match/promotion-match-mv";
import { $CartMv } from "../shop-cart/cart-mv";
import { $OrderMv } from "../shop-order/order-mv";
import { $AddressMv } from "../submit-address-form/submit-address-mv";
import { $SubmitOrderMv } from "./submit-order-mv";
import { $PayType } from "../../classes/const/$pay-type";
import { ShippingModeOfDistribution } from "../shipping-mde-of-distribution/shipping-mode-of-distribution";
import { $PromotionType } from "../../classes/const/$promotion-type";
import { $AppStore, AppStoreKey } from "../../classes/stores/app-store-mv";

declare let require: any;
const IconLocation = require("../../components/svg/ico-location.svg");
const CheckboxItem = Checkbox.CheckboxItem;
const RadioGroup = Radio.Group;
declare let window: any;
declare let $: any;
declare let WeixinJSBridge: any;
const alert = Modal.alert;
const Item = List.Item;
const Brief = Item.Brief;

@withRouter
@observer
class SubmitOrder extends React.Component<any, any> {
  @autowired($CartMv)
  public $CartMv: $CartMv;
  @autowired($AppStore)
  public $AppStore: $AppStore;
  @autowired($OrderMv)
  public $OrderMv: $OrderMv;

  @autowired($AddressMv)
  public $AddressMv: $AddressMv;

  @autowired($SubmitOrderMv)
  public $SubmitOrderMv: $SubmitOrderMv;

  @autowired($ComponentService)
  public $componentService: $ComponentService;

  @autowired($PromotionMatchMv)
  public $PromotionMatchMv: $PromotionMatchMv;

  constructor(props) {
    super(props);
    this.state = {
      payTypeModal: false,
      switchChecked: false,
      isShowPayList: true,
      defaultAddress: {
        contactPerson: "",
        phoneNumber: "",
        location: "",
      },
      orderInfo: {
        totalAmount: "",
        payableAmount: "",
        orderPartyId: "",
        orderId: "",
      },
      payOrderList: [],
      isAllPay: false,
      isDealer: true,
      instalmentList: [],
      accountInfo: {
        accountTypeName: "",
        balanceAmount: "",
        paymentModeId: "",
      },
      isFetchDealerPayList: true,
      paymentModeList: [],
      payCount: "",
      totalDiscountAmount: 0,
      allAmount: "",
      balanceCount: "",
      value: "",
      instalmentCount: 0,
      animating: false,
      isShowMoreDistribution: false,
      isShowBtn: false,
    };
  }

  public showPayTypeModal = () => {
    this.setState({ payTypeModal: true });
  }

  public onClosePayTypeModal = () => {
    this.setState({ payTypeModal: false });
  }

  public componentDidMount() {
    document.title = "提交订单";
    this.getRandomNum();
    this.$SubmitOrderMv.showSpin();
    const { isSelectedOnce, selectedAddress } = this.$AddressMv;
    console.log(this.props.location.state);
    // if ((this.props.location.state === undefined) || (this.props.location.state === null) || (this.props.location.state === "")) {
    //   alert("您的网络开小差了", "", [
    //     {
    //       text: "重试", onPress: () => {
    //         this.props.history.push({
    //           pathname: `/${SITE_PATH}/shop/cart`,
    //           state: {
    //             backSource: this.props.location.state ? this.props.location.state.backSource : null,
    //           },
    //         });
    //       },
    //     },
    //   ]);
    //   return;
    // }
    const locationState = this.props.location.state || {};
    const params = {
      promotionList: locationState.promotionList || [],
      hasPromotion: locationState.hasPromotion || "",
      shopCartItemList: sessionStorage.getItem("productsParams") && JSON.parse(sessionStorage.getItem("productsParams")),
    };
    // console.log(this.props.location.state.hasPromotion);
    // this.$SubmitOrderMv.fetchWxToken(); // 获取微信支付所需参数
    this.$SubmitOrderMv.fetchPayPageDetail(params).then((data) => {
      this.$SubmitOrderMv.hideSpin();
      console.log(data);
      if (data.errorCode === $CartType.HASPROMOTION) {
        alert(`${data.errorMessage}`, "", [
          {
            text: "重试", onPress: () => {
              this.$AppStore.clearPageMv(AppStoreKey.SHOPCART);
              this.props.history.push({
                pathname: `/${SITE_PATH}/shop/cart`,
                state: {
                  backSource: this.props.location.state ? this.props.location.state.backSource : null,
                },
              });
            },
          },
        ]);
        this.setState({ isFetchDealerPayList: false })
        return;
      }
      this.setState({
        isShowBtn: true,
        orderInfo: data.orderInfo,
        payOrderList: data.orderItemList,
      });
      this.$SubmitOrderMv.setIsAllowModifyDistributionModeForMall(data.isAllowModifyDistributionModeForMall);
      this.$SubmitOrderMv.setOrderInfo(data.orderInfo);
      this.$SubmitOrderMv.setOrderItemAdjustmentList(data.orderItemAdjustmentList);
      this.$SubmitOrderMv.setDiscountAmountViewPermission(data.discountAmountViewPermission);
      this.$SubmitOrderMv.setFreightViewPermission(data.freightViewPermission);
      this.$SubmitOrderMv.setOrginDistributionModeList(data.distributionModeInfo);
      this.$SubmitOrderMv.setUsedCouponList(data.usedCouponList);
      this.$SubmitOrderMv.setIsUseFreight(data.isUseFreight);
      this.$SubmitOrderMv.setOrderItemListForSave(data.orderItemListForSave);
      this.$SubmitOrderMv.setOrderPriceViewPermission(data.orderPriceViewPermission, data.retailPriceViewPermission);
      if (!isSelectedOnce) {
        this.$AddressMv.setSelectedAddress(data.addressInfo);
      }
      if (((selectedAddress && selectedAddress.addressId) || (data.addressInfo && data.addressInfo.addressId)) && (data.isUseFreight === $PayType.ISUSEFREIGHT)) { // 如果有默认账户展示即计算运费
        this.calculateFreight(selectedAddress && selectedAddress.addressId ? selectedAddress.addressId : data.addressInfo && data.addressInfo.addressId, data.orderInfo.totalDiscountAmount, data.orderInfo.totalAmount, data.orderItemListForSave, data.orderInfo.orderPartyId, data.orderInfo.orderSchemeId);
      }
    }).then(() => {
      const params1 = {
        totalAmount: this.state.orderInfo.totalAmount,
      };
      if (this.props.location.state.hasPromotion === null || !this.state.isFetchDealerPayList) {
        return;
      }
      this.$SubmitOrderMv.fetchDealerPayList(params1).then((data1) => {
        // console.log(data);
        this.$SubmitOrderMv.setInstalmentList(data1.instalmentList);
        this.setState({
          instalmentList: data1.instalmentList,
          paymentModeList: data1.paymentModeList,
          payCount: this.state.orderInfo.totalAmount,
          totalDiscountAmount: this.state.orderInfo.totalDiscountAmount,
          allAmount: parseFloat(this.state.orderInfo.totalAmount) + parseFloat(this.state.orderInfo.totalDiscountAmount),
        });
        if (this.state.instalmentList.length === 0) {
          this.setState({
            isAllPay: true,
          });
        } else {
          this.state.instalmentList.map((list) => {
            if (list.checked === "Y") {
              this.$SubmitOrderMv.setInstalmentType(list.instalmentType);
              this.setState({
                instalmentCount: list.amount,
              });
            }
          });
        }
      });
    });
  }

  public calculateFreight = (addressId, totalDiscountAmount, totalAmount, orderItemListForSave, orderPartyId, orderSchemeId) => {
    const { defaultDistributionMode } = this.$SubmitOrderMv;
    const calculateFreightParams = {
      addressId,
      totalDiscountAmount,
      totalAmount,
      orderItemListForSave,
      orderPartyId,
      orderSchemeId,
      distributionModeId: defaultDistributionMode && defaultDistributionMode.distributionModeId,
    };
    this.$SubmitOrderMv.calculateFreight(calculateFreightParams);
  }

  // 选择收货地址
  public toSelectAddress = () => {
    this.$AddressMv.isFormPay = true;
    this.$AppStore.clearPageMv(AppStoreKey.ADDRESSLIST);
    this.props.history.push({
      pathname: `/${SITE_PATH}/shippingAddress`,
      state: {
        promotionList: this.props.location.state ? this.props.location.state.promotionList : [],
        shopCartTotalAmount: this.props.location.state.shopCartTotalAmount,
        backSource: this.props.location.state ? this.props.location.state.backSource : null,
        prePageSource: this.props.location.state ? this.props.location.state.prePageSource : null,
        hasPromotion: this.props.location.state ? this.props.location.state.hasPromotion : null,
      },
    });
  }

  public isShowMoreModeOfDistribution = (flag = false) => {
    const { replaceDistributionModeList } = this.$SubmitOrderMv
    if (!replaceDistributionModeList.length) {
      return
    }
    this.setState({
      isShowMoreDistribution: flag,
    })
  }

  public WeixinJSBridge = (wxInfo, params) => {
    if (typeof WeixinJSBridge == "undefined") {
      if (document.addEventListener) {
        document.addEventListener("WeixinJSBridgeReady", this.onBridgeReady, false);
      } else if (document.attachEvent) {
        document.attachEvent("WeixinJSBridgeReady", this.onBridgeReady);
        document.attachEvent("onWeixinJSBridgeReady", this.onBridgeReady);
      }
    } else {
      this.onBridgeReady(wxInfo, params);
    }
  }

  public onBridgeReady = (wxInfo, params) => {

    WeixinJSBridge.invoke(
      "getBrandWCPayRequest", {
        appId: wxInfo.appId,     // 公众号名称，由商户传入
        timeStamp: wxInfo.timeStamp,         // 时间戳，自1970年以来的秒数
        nonceStr: wxInfo.nonceStr, // 随机串
        package: wxInfo.package,
        signType: "MD5",         // 微信签名方式：
        paySign: wxInfo.paySign, // 微信签名
      },
      (res) => {
        if (res.err_msg === "get_brand_wcpay_request:ok") {
          // 使用以上方式判断前端返回,微信团队郑重提示：
          // res.err_msg将在用户支付成功后返回ok，但并不保证它绝对可靠。
          const l = params.paymentItemList ? params.paymentItemList.length : 0;
          params.paymentItemList[l - 1].resultCode = res.err_msg;
          this.$SubmitOrderMv.saveOrderPay(params).then((data1) => {
            if (data1.result === true) {
              Toast.info("支付成功", 3);
              setTimeout(() => {
                this.$AppStore.clearPageMv(AppStoreKey.SINGLESHOPORDERLISTSTORE);
                this.props.history.push({ pathname: `/${SITE_PATH}/shop/order-list` });
              }, 3000);
            }
          });
        }
      });
  }

  public getRandomNum = () => {
    let result = "";
    const date = new Date();
    const year = date.getFullYear();
    const month = this.twoNum(date.getMonth());
    const onedate = this.twoNum(date.getDate());
    const hour = this.twoNum(date.getHours());
    const min = this.twoNum(date.getMinutes());
    const sec = this.twoNum(date.getSeconds());
    const msec = this.threeNum(date.getMilliseconds());
    let randomStr = "";
    for (let i = 0; i < 6; i++) {
      randomStr += String(parseInt(Math.random() * 10));
    }
    result = year + month + onedate + hour + min + sec + msec + randomStr;
    return result;
  }

  public twoNum = (num) => {
    if (String(num).length < 2) {
      num = "0" + String(num);
    }
    return String(num);
  }

  public threeNum = (num) => {
    if (String(num).length < 2) {
      num = "00" + num;
    } else if (String(num).length < 3) {
      num = "0" + num;
    }
    return num;
  }

  // 订单确认
  public confirmOrder = () => {
    const { calculatefreight, orderItemListForSave } = this.$SubmitOrderMv;
    if (this.$AddressMv.selectedAddress === null) {
      Toast.info("请选择地址");
      return;
    }
    const { defaultDistributionMode } = this.$SubmitOrderMv
    if (!defaultDistributionMode || !defaultDistributionMode.distributionModeId) {
      Toast.info("请选择配送方式或联系品牌客服设置配送方式");
      return;
    }
    const { addressId } = this.$AddressMv.selectedAddress;
    const locationState = this.props.location.state || {};
    const params = {
      memo: this.$SubmitOrderMv.memo,
      receiveAddressId: addressId,
      orderItemList: this.state.payOrderList,
      orderItemAdjustmentList: this.$SubmitOrderMv.orderItemAdjustmentList,
      usedCouponList: this.$SubmitOrderMv.usedCouponList,
      orderId: sessionStorage.getItem("editOrderId"),
      distributionModeId: defaultDistributionMode.distributionModeId,
      shopCartTotalAmount: this.state.orderInfo.totalAmount ? Number(this.state.orderInfo.totalAmount).toFixed(2) : 0,
      orderItemListForSave,
      freightAmount: calculatefreight >= 0 ? Number(calculatefreight).toFixed(2) : 0,
      promotionList: locationState.promotionList || [],
      hasPromotion: locationState.hasPromotion || "",
      shopCartItemList: sessionStorage.getItem("productsParams") && JSON.parse(sessionStorage.getItem("productsParams")),
    };
    this.setState({
      animating: true,
    });
    this.$SubmitOrderMv.saveOrder(params).then((data) => {
      if (data.errorCode === $CartType.ERRORCODEXE) { // 限额
        this.setState({
          animating: false,
        });
        alert(`${data.errorMessage}`, "", [
          {
            text: "确认", onPress: () => {
            },
          },
        ]);
        return;
      } else if (data.errorCode === $CartType.ONEMINORDERLIMIT) { // 最小限额
        this.setState({
          animating: false,
        });
        alert(`${data.errorMessage}`, "", [
          {
            text: "确认", onPress: () => {
            },
          },
        ]);
        return;
      } else if (data.errorCode === $PromotionType.NOSTANDARD) { // 数量未达到赠送标准
        this.setState({
          animating: false,
        });
        alert(`${data.errorMessage}`, "", [
          {
            text: "确认", onPress: () => {
            },
          },
        ]);
        return;
      } else if (data.errorCode === $CartType.TIMELIMIT) { // 下单次数限制
        this.setState({
          animating: false,
        });
        alert(`${data.errorMessage}`, "", [
          {
            text: "确认", onPress: () => {

            },
          },
        ]);
        return;
      } else if (data.errorCode === $CartType.COUNTLIMIT) { // 最大购买数量限制
        this.setState({
          animating: false,
        });
        alert(`${data.errorMessage}`, "", [
          {
            text: "确认", onPress: () => {

            },
          },
        ]);
        return;
      } else if (data.errorCode === $CartType.AMOUNTLIMIT) { // 下单金额限制
        this.setState({
          animating: false,
        });
        alert(`${data.errorMessage}`, "", [
          {
            text: "确认", onPress: () => {
            },
          },
        ]);
        return;
      } else if (data.errorCode === $CartType.NOORDER) { // 逾期不可订货
        this.setState({
          animating: false,
        });
        alert(`${data.errorMessage}`, "", [
          {
            text: "取消", onPress: () => {
            },
          },
          {
            text: "去还款", onPress: () => {
              // console.log(909090);
              this.$AppStore.clearPageMv(AppStoreKey.ACCOUNTINFOLIST);
              this.props.history.push({
                pathname: `/${SITE_PATH}/account-info-list/89898/XYZH`,
              });
            },
          },
        ]);
        return;
      } else if (data.errorCode === $CartType.NOSEND) { // 费用单预期
        this.setState({
          animating: false,
        });
        alert(`${data.errorMessage}`, "", [
          {
            text: "取消", onPress: () => {
            },
          },
          {
            text: "去付款", onPress: () => {
              // this.$AppStore.clearPageMv(AppStoreKey.ACCOUNTINFOLIST);
              this.props.history.push({ pathname: `/${SITE_PATH}/expense-node-list/Payment/null` });
            },
          },
        ]);
        return;
      } else if (data.errorCode === $CartType.HASPROMOTION) { //   促销标识
        alert(`${data.errorMessage}`, "", [
          {
            text: "重试", onPress: () => {
              this.$AppStore.clearPageMv(AppStoreKey.SHOPCART);
              this.props.history.push({
                pathname: `/${SITE_PATH}/shop/cart`,
                state: {
                  backSource: this.props.location.state ? this.props.location.state.backSource : null,
                },
              });
            },
          },
        ]);
        return;
      }else if(data.errorCode === $CartType.ORG_ORDER_ISORDEMUST_ERROR || data.errorCode === $CartType.NOT_SUPPORTING_GIFT_ACCOUNT){
        this.setState({
          animating: false,
        });
          alert(`${data.errorMessage}`, "", [
            {
              text: "确认", onPress: () => {
              },
            },
          ]);
          return;
      } else if (data.errorCode === $CartType.NO_USE_DEDUCTION) {
        this.setState({
          animating: false,
        });
        alert(`${data.errorMessage}`, "", [
          {
            text: "确认", onPress: () => {
              window.location.reload();
            },
          },
        ]);
      }

      if (data.result) {
        sessionStorage.setItem("editOrderId", null);
        sessionStorage.setItem("productsParams", "[]");
        let money = 0;
        if (this.state.isAllPay) {
          money = this.state.payCount;
        } else {
          money = this.state.instalmentCount;
        }
        this.setState({
          animating: false,
        });
        const { orderPriceViewPermission } = this.$SubmitOrderMv;
        this.props.history.push({
          pathname: `/${SITE_PATH}/submit/success`, state: {
            docNo: data.docNo,
            payCount: Number(money + calculatefreight).toFixed(2),
            orderId: data.orderId,
            backSource: this.props.location.state ? this.props.location.state.backSource : null,
            orderPriceViewPermission,
          },
        });
      } else {
        this.setState({
          animating: false,
        });
      }
    }).catch((err) => {
      this.setState({
        animating: false,
      });
    });
  }

  // 支付下单 不使用该方法
  public onConfirm = () => {
    const { balanceAmount } = this.state.accountInfo;
    const { totalAmount } = this.state.orderInfo;
    if (this.$AddressMv.selectedAddress === null) {
      Toast.info("请选择地址");
      return;
    }
    if (this.$SubmitOrderMv.isRole) {  // 经销商角色
      if (this.state.switchChecked === false && this.state.value === "") {
        Toast.info("请选择支付方式");
        return;
      }
      if (this.state.isShowPayList === true) {
        if (!document.querySelector(".ant-radio-wrapper-checked")) {
          Toast.info("请选择支付方式");
          return;
        }
        const payMoney = document.querySelector(".ant-radio-wrapper-checked").nextSibling.querySelector(".input").value;
        if (isNaN(parseFloat(payMoney))) {
          Toast.info("请输入金额");
          return;
        }
        if (parseFloat(payMoney) <= 0) {
          Toast.info("金额必须大于0");
          return;
        }
        if (this.state.switchChecked === true) {
          if (parseFloat(balanceAmount) + parseFloat(payMoney) > totalAmount) {
            Toast.info("实际付款金额不能大于应付金额");
            return;
          }
        } else {
          if (parseFloat(payMoney) > totalAmount) {
            Toast.info("实际付款金额不能大于应付金额");
            return;
          }
        }
        if (payMoney.indexOf(".") > -1) {
          if (!/^[0-9]+(.[0-9]{1})?$/.test(payMoney)) {
            Toast.info("支付金额最多有一位小数");
            return;
          }
        }
      }
    }
    const { orderInfo } = this.state;
    const { addressId } = this.$AddressMv.selectedAddress;
    const locationState = this.props.location.state || {};
    const params = {
      memo: this.$SubmitOrderMv.memo,
      receiveAddressId: addressId,
      orderItemList: this.state.payOrderList,
      orderItemAdjustmentList: this.$SubmitOrderMv.orderItemAdjustmentList,
      shopCartTotalAmount: this.state.allAmount ? Number(this.state.allAmount).toFixed(2) : 0,
      shopCartItemList: JSON.parse(sessionStorage.getItem("productsParams")),
      promotionList: locationState.promotionList || [],
      hasPromotion: locationState.hasPromotion || "",
    };
    if (this.$OrderMv.editOrderId) {
      params.orderId = this.$OrderMv.editOrderId;
    }
    this.$SubmitOrderMv.saveOrder(params).then((data) => {
      if (data) {
        if (this.$SubmitOrderMv.isRole === false) {
          Toast.info("订单确认成功", 3);
          setTimeout(() => {
            this.$AppStore.clearPageMv(AppStoreKey.SINGLESHOPORDERLISTSTORE);
            this.props.history.push({ pathname: `/${SITE_PATH}/shop/order-list` });
            return;
          }, 3000);
        } else {
          let linkKey = null;
          const docNo = data.docNo;
          if (this.state.isShowPayList !== false) {
            linkKey = document.querySelector(".ant-radio-wrapper-checked").nextSibling.nextSibling.value;
          }
          let theAmount = 0;
          const paymentItemList = [];
          const myPaymentItemList = [];
          const confirmPaymentItemList = [];
          if (this.state.isShowPayList === false) { // 余额完全支付
            theAmount = this.state.payCount;
            if (this.state.isAllPay === false) {
              theAmount = this.state.instalmentCount;
            }
            const arr = {
              paymentModeId: this.state.accountInfo.paymentModeId,
              paymentAccountId: this.state.accountInfo.accountId,
              amount: theAmount,
            };
            paymentItemList.push(arr);
            myPaymentItemList.push(arr);
          } else if (this.state.switchChecked) {  // 余额支付部分
            const input = parseFloat(document.querySelector(".ant-radio-wrapper-checked").nextSibling.querySelector(".input").value);
            theAmount = parseFloat(this.state.balanceCount) + input;
            const arr1 = {
              paymentModeId: this.state.accountInfo.paymentModeId,
              paymentAccountId: this.state.accountInfo.accountId,
              amount: this.state.balanceCount,
            };
            paymentItemList.push(arr1);
            myPaymentItemList.push(arr1);
            const arr2 = {
              paymentModeId: this.state.value,
              amount: input,
            };
            paymentItemList.push(arr2);
            if (linkKey === "bank_transfer") {
              confirmPaymentItemList.push(arr2);
            }
          } else {
            theAmount = parseFloat(document.querySelector(".ant-radio-wrapper-checked").nextSibling.querySelector(".input").value);
            const arr2 = {
              paymentModeId: this.state.value,
              amount: theAmount,
            };
            paymentItemList.push(arr2);
            if (linkKey === "bank_transfer") {
              confirmPaymentItemList.push(arr2);
            }
          }
          this.$OrderMv.setEditOrderId();
          this.$SubmitOrderMv.setRelatedDocId(data.orderId);
          const payParams = {
            relatedDocId: data.orderId,
            relatedDocObjectType: orderInfo.orderSchemeName,
            memo: this.$SubmitOrderMv.memo,
            paymentItemList,
          };
          const payParams2 = {
            relatedDocId: data.orderId,
            relatedDocObjectType: orderInfo.orderSchemeName,
            memo: this.$SubmitOrderMv.memo,
            paymentItemList: confirmPaymentItemList,
          };
          this.$SubmitOrderMv.setSaveOrderPayData(payParams2);
          this.$SubmitOrderMv.setRelatedDocObjectType(orderInfo.orderSchemeName);
          this.$SubmitOrderMv.setPaymentModeId(this.state.value);
          this.$SubmitOrderMv.setPaymentAccountId(this.state.accountInfo.accountTypeId);
          this.$SubmitOrderMv.setAmount(theAmount);
          payParams.paymentItemList = myPaymentItemList;
          if (linkKey === "wechat") {
            theAmount = parseFloat(document.querySelector(".ant-radio-wrapper-checked").nextSibling.querySelector(".input").value);
            const detail = {
              outTradeNo: this.getRandomNum(),
              docType: "OrderPayment",
            };
            const params = {
              brandId: "pekon",
              signType: "JSAPI",
              body: "樊文花-订货单",
              detail: JSON.stringify(detail),
              spbillCreateIp: window.ENV.userIp, // IP
              outTradeNo: detail.outTradeNo,
              totalFee: theAmount, // 实付金额  --元 在后台需要转成分
            };
            this.$SubmitOrderMv.wxPay(params, this.$SubmitOrderMv.wxToken).then((wxResult) => {
              if (wxResult.payStatus === "SUCCESS") {
                const saveParams = {
                  paymentModeId: this.state.value,
                  amount: theAmount,
                  outTradeNo: detail.outTradeNo,
                  resultCode: "",
                };
                payParams.paymentItemList.push(saveParams);
                this.WeixinJSBridge(wxResult.resultContent, payParams);
              }
            });
          } else {
            this.$SubmitOrderMv.saveOrderPay(payParams).then((data1) => {
              if (data1.result === true) {
                if (linkKey === "bank_transfer") {
                  this.props.history.push({ pathname: `/${SITE_PATH}/confirm/order` });
                } else {
                  Toast.info("支付成功", 3);
                  setTimeout(() => {
                    this.$AppStore.clearPageMv(AppStoreKey.SINGLESHOPORDERLISTSTORE);
                    // this.props.history.push({ pathname: `/${SITE_PATH}/shop/order-list` });
                    window.location.href = `/${SITE_PATH}/shop/order-list`;
                  }, 3000);
                }
              }
            });
          }
        }
      }
    });
  }

  public renderAddressList = (addressList: $Address[]) => {
    return addressList.map((address) => {
      return (
        <Item
          key={address.addressId}
          onClick={() => {
            transaction(() => {
              this.$SubmitOrderMv.setConsigneeInfo(address);
              this.$SubmitOrderMv.closeModal();
            });
          }}
        >
          姓名: {address.contactPerson} <br/>
          手机: {address.phoneNumber} <br/>
          地址: {address.location}
        </Item>
      );
    });
  }

  public changeSwitch = () => {
    this.setState({
      switchChecked: !this.state.switchChecked,
      isShowPayList: true,
    });
    if (this.state.balanceCount >= this.state.payCount && !this.state.switchChecked) {
      this.setState({
        isShowPayList: false,
      });
    }
  }

  public onChange = (e) => {
    this.setState({
      value: e.target.value,
    });
  }

  public toPrevPage = () => {
    const params = {
      shopCartTotalAmount: this.props.location.state.shopCartTotalAmount,
      oldSalesOrderId: sessionStorage.getItem("editOrderId") === "null" ? null : sessionStorage.getItem("editOrderId"),
    };
    if (this.props.location.state && this.props.location.state.prePageSource === "cart") {
      this.$AppStore.clearPageMv(AppStoreKey.SHOPCART);
      this.props.history.push({
        pathname: `/${SITE_PATH}/shop/cart`,
        state: {
          backSource: this.props.location.state ? this.props.location.state.backSource : null,
        },
      });
    } else {
      this.$AppStore.clearPageMv(AppStoreKey.PROMOTIONMATCH);
      this.props.history.push({
        pathname: `/${SITE_PATH}/promotion/match`,
        state: {
          prePromotionList: this.props.location.state.promotionList,
          prePage: true,
          shopCartTotalAmount: this.props.location.state.shopCartTotalAmount,
          backSource: this.props.location.state ? this.props.location.state.backSource : null,
        },
      });
    }
    // this.$CartMv.fetchProductPage(params).then((data) => {
    //   if (data.nextPage === $CartType.NEXTPAGE) { // 去促销
    //     this.props.history.push({
    //       pathname: `/${SITE_PATH}/promotion/match`,
    //       state: {
    //         prePromotionList: this.props.location.state.promotionList,
    //         prePage: true,
    //         shopCartTotalAmount: this.props.location.state.shopCartTotalAmount,
    //         backSource: this.props.location.state ? this.props.location.state.backSource : null,
    //       },
    //     });
    //   } else {
    //     this.props.history.push({
    //       pathname: `/${SITE_PATH}/shop/cart`,
    //       state: {
    //         backSource: this.props.location.state ? this.props.location.state.backSource : null,
    //       },
    //     });
    //   }
    // });
  }

  public hideTel = (tel) => {
    let str = "";
    for (let i = 0; i < tel.length; i++) {
      let item = "";
      if (i > 2 && i < 7) {
        item = "*";
      } else {
        item = tel[i];
      }
      str += item;
    }
    return str;
  }

  public changeDefaultModeOfDistribution(id) {
    const { selectedAddress } = this.$AddressMv;
    const { orderInfo, orderItemListForSave } = this.$SubmitOrderMv;
    this.isShowMoreModeOfDistribution(false);
    this.$SubmitOrderMv.changeReplaceDistributionModeList(id);
    this.calculateFreight(selectedAddress && selectedAddress.addressId, orderInfo.totalDiscountAmount, orderInfo.totalAmount, orderItemListForSave,  orderInfo.orderPartyId, orderInfo.orderSchemeId);
  }

  public getPriceShowHtml(data, name) {
    const { isBySkuType, totalAmount, amountList } = data;
    return isBySkuType ? amountList.map((item) => {
        return <div><span>{item.name} </span><span>{"¥ " + item.amountBySkuType}</span></div>
      })
      :
      <div><span>{name}</span> <span>{"¥ " + totalAmount}</span></div>
  }

  public render() {
    const { selectedAddress } = this.$AddressMv;
    const { discountAmountViewPermission, freightViewPermission, isRole, payType, addressList, payTypeList, showModal, calculatefreight, isUseFreight, orderPriceViewPermission, calcInstalmentList, retailPriceViewPermission, orderInfo, orginDistributionModeList, replaceDistributionModeList, defaultDistributionMode, isSpin } = this.$SubmitOrderMv;
    const { selectedRows, totalCategories, totalCount, totalAmount } = this.$CartMv;
    const payOrderList = this.state.payOrderList;
    const { isShowMoreDistribution, isAllPay, instalmentList, totalDiscountAmount } = this.state;
    const lastTotalDiscountAmount = isAllPay ? orderInfo.totalDiscountAmount : totalDiscountAmount;
    const paymentModeList = this.state.paymentModeList || [];
    return (
      <Wrapper>
        <div className={isShowMoreDistribution ? "fixed" : "relative"}>
          <Spin spinning={isSpin}>
            <List>
              <Item
                arrow="horizontal"
                onClick={this.toSelectAddress}
              >
                {
                  selectedAddress === null ?
                    <div>
                      <p>请选择地址</p>
                    </div> :
                    <div className="address">
                      <p>
                        <span className="user-name">{selectedAddress.contactPerson}</span>
                        <span
                          className="user-tel">{selectedAddress.phoneNumber ? this.hideTel(selectedAddress.phoneNumber) : null}</span>
                        {
                          selectedAddress.isDefault === "N" ? null :
                            <span className="default-icon">默认</span>
                        }
                      </p>
                      <p className="user-address"><IconLocation/>{selectedAddress.location}</p>
                    </div>
                }
              </Item>
            </List>
            <img className="border-img" src={require("../../components/assets/ico-colorline.png")} alt=""/>
            {
              retailPriceViewPermission === $CartType.RETAILPRICEVIEWPERMISSION && orderInfo.retailAmount && orderInfo.retailAmount.amountList.length > 0 &&
              <div className="retail-price">
                <div className="price-header">
                  <span className="scmIconfont-wrap"><i className="scmIconfont scm-icon-baoliujine"></i></span>
                  <h5>零售价</h5>
                </div>
                <div className="price-content">{this.getPriceShowHtml(orderInfo.retailAmount, "零售价总额")}</div>
              </div>
            }
            <div className={`${
              (orderPriceViewPermission !== $CartType.ORDERPRICEVIEWPERMISSION &&
                discountAmountViewPermission !== $CartType.ORDERPREFERENTIALACTIVITIESPERMISSION &&
                (freightViewPermission !== $CartType.ORDERFREIGHTVIEWPERMISSION ||
                  isUseFreight !== $PayType.ISUSEFREIGHT ||
                  calculatefreight <= 0
                )) ?
                "order-price displayNone"
                :
                "order-price"
              }`}>
              {
                console.log(discountAmountViewPermission, orderPriceViewPermission, freightViewPermission, isUseFreight, calculatefreight)
              }
              <div className="price-header">
                <span className="scmIconfont-wrap"><i className="scmIconfont scm-icon-jiageguanli"></i></span>
                <h5>订货价</h5>
              </div>
              <div className="price-content">
                {
                  orderPriceViewPermission === $CartType.ORDERPRICEVIEWPERMISSION && orderInfo.orderAmount &&
                  this.getPriceShowHtml(orderInfo.orderAmount, "订货价总额")
                }
                {
                  discountAmountViewPermission === $CartType.ORDERPREFERENTIALACTIVITIESPERMISSION &&
                  <div>
                    <span>活动优惠</span><span>{`${lastTotalDiscountAmount === 0 ? "¥ 0" : "- ¥ " + lastTotalDiscountAmount}`}</span>
                  </div>
                }
                {
                  orderPriceViewPermission === $CartType.ORDERPRICEVIEWPERMISSION && orderInfo.orderAmount &&
                  <div>
                    <span>货款</span><span>{`¥${Number(orderInfo.totalAmount).toFixed(2)}`}</span>
                  </div>
                }
                {
                  freightViewPermission === $CartType.ORDERFREIGHTVIEWPERMISSION && isUseFreight === $PayType.ISUSEFREIGHT && calculatefreight > 0 ?
                    <div><span>运费</span><span>{`¥ ${Number(calculatefreight).toFixed(2)}`}</span></div> : null
                }
              </div>
              {
                orderPriceViewPermission === $CartType.ORDERPRICEVIEWPERMISSION &&
                <div className="total-price">
                  <span>{`¥ ${Number(orderInfo.totalAmount + calculatefreight).toFixed(2)}`}</span><span>订单合计：</span>
                </div>
              }
            </div>
            {
              orderInfo.totalDeductionAmount > 0 && <div className="order-price">
                <div className="price-header">
                  <span className="scmIconfont-wrap"><i className="scmIconfont scm-icon-jiageguanli"></i></span>
                  <h5>订货抵扣额度</h5>
                </div>
                <div className="price-content">
                  <div>
                    <span>应付额度</span><span>{`${"¥ " + orderInfo.totalDeductionAmount}`}</span>
                  </div>
                </div>
                <div className="total-price">
                  <span>{`¥ ${Number(orderInfo.totalDeductionAmount).toFixed(2)}`}</span><span>订货额度抵扣合计：</span>
                </div>
              </div>
            }
            {
              instalmentList.map((v) => {
                return <List>
                  <Item
                    extra={`¥ ${v.amount}`}
                    className=""
                    key={v.instalmentType}
                  >
                    {v.instalmentName}{v.checked === "Y" ? ` (${DateUtils.toStringFormat(v.startTime, "yyyy-MM-dd HH:mm:ss")} 开始支付尾款) ` : ""}
                  </Item>
                </List>;
              })
            }
            <List className="my-list h40">
              <DistributionWrap>
                <div
                  className="mode-of-distribution"
                  onClick={() => {
                    this.isShowMoreModeOfDistribution(true)
                  }}
                >
                  <span>配送方式</span>
                  <div className="defaultMode">
                    {defaultDistributionMode ?
                    <span>{defaultDistributionMode.distributionModeName}</span> : <span>请选择</span>
                    }
                    {
                      replaceDistributionModeList.length > 0 &&
                      <span style={{ marginLeft: "12px" }}> > </span>
                    }
                  </div>
                </div>
              </DistributionWrap>
              <InputItem
                className="remark"
                placeholder="请输入备注"
                clear={true}
                value={this.$SubmitOrderMv.memo}
                onChange={(v) => this.$SubmitOrderMv.setMemo(v)}
              >
                备注信息
              </InputItem>
            </List>
            {/*{*/}
            {/*isRole ?*/}
            {/*<List className="my-list pay-list">*/}
            {/*<List.Item className="balance"*/}
            {/*extra={<Switch*/}
            {/*color="#307DCD"*/}
            {/*checked={this.state.switchChecked}*/}
            {/*onChange={this.changeSwitch}*/}
            {/*disabled={this.state.balanceCount === 0 ? true : false}*/}
            {/*/>}*/}
            {/*>余额支付 <span>最多可用￥{this.state.balanceCount}</span></List.Item>*/}
            {/*<div className={this.state.isShowPayList ? "" : "hide"}>*/}
            {/*<RadioGroup onChange={this.onChange} value={this.state.value}>*/}
            {/*{*/}
            {/*paymentModeList.map((value) => {*/}
            {/*return <div key={value.oid}>*/}
            {/*<Radio value={value.oid}>*/}
            {/*{value.name}*/}
            {/*</Radio>*/}
            {/*<span className="right">￥*/}
            {/*<input type="number" className="input moneyInput"*/}
            {/*defaultValue={this.state.isAllPay === false ? this.state.instalmentCount : this.state.payCount}/>*/}
            {/*</span>*/}
            {/*<input type="hidden" value={value.code} className="linkKey"/>*/}
            {/*<div className="line1"></div>*/}
            {/*</div>;*/}
            {/*})*/}
            {/*}*/}
            {/*</RadioGroup>*/}
            {/*</div>*/}
            {/*</List> : null*/}
            {/*}*/}

            <List className="my-list h40">
              <Item extra={<span>数量 <span>{this.state.orderInfo.totalQuantity}</span></span>}>商品清单</Item>
            </List>
            <List className="product-list">
              {
                payOrderList.map((value) => {
                  // return <Item className="pro-list-item" key={value.productSkuId} extra={<span>x{value.quantity}</span>}>
                  return (
                    <PayProductItem
                      data={value}
                      noCart={true}
                      hideCount={true}
                      pricePermission={orderPriceViewPermission}
                      retailPriceViewPermission={retailPriceViewPermission}
                    />
                  );
                  // </Item>;
                })
              }
            </List>
            <div style={{ height: "10px", background: "F2F2F2" }}/>
          </Spin>
          <ToPay>
            <div style={{ display: "flex", flex: 1 }}>
              {
                orderPriceViewPermission === $CartType.ORDERPRICEVIEWPERMISSION ?
                  <div style={{ textAlign: "right", flex: 1 }}>
                    应付金额：
                    <span style={{ color: "#FF3030", fontSize: 14 }}>
                {
                  `¥ ${this.state.isAllPay === false ? Number(this.state.instalmentCount + calculatefreight).toFixed(2) : Number(this.state.payCount + calculatefreight).toFixed(2)}`
                }
                </span>
                  </div> : null
              }
            </div>
            {/*<span className="to-prev" onClick={this.toPrevPage}>上一页</span>*/}
            {
              this.state.isShowBtn && <Button
                disabled={isSpin}
                className="pay"
                onClick={this.confirmOrder}>
                订单确认
              </Button>
            }
          </ToPay>
          <StyledModal
            visible={showModal}
            transparent={true}
            maskClosable={false}
          >
            <Content>
              {this.renderAddressList(addressList)}
            </Content>
          </StyledModal>
          <ActivityIndicator
            toast={true}
            text="Loading..."
            animating={this.state.animating}
          />
        </div>
        {
          isShowMoreDistribution &&
          <ShippingModeOfDistribution
            replaceDistributionModeList={replaceDistributionModeList}
            defaultDistributionMode={defaultDistributionMode}
            closeShippingDistribution={() => {
              this.isShowMoreModeOfDistribution(false)
            }}
            changeDefaultShippingDistribution={(id) => {
              this.changeDefaultModeOfDistribution(id)
            }}
          ></ShippingModeOfDistribution>
        }
      </Wrapper>
    )
      ;
  }
}

export default SubmitOrder;

const Wrapper = styled.div`// styled
  & {
    background: #F2F2F2;
    min-height:${document.documentElement.clientHeight - 44}px;
    .fixed{
      position: fixed;
    }
    .relative{
      position: relative;
    }

    .left {
      float: left;
    }
    .right {
      float: right;
    }
    .h40{
      .am-list-item{
        height:40px!important;
        min-height:40px!important;
      }
    }
    .am-list-item .am-input-label.am-input-label-5{
      color: #333333;
    }
     .retail-price, .order-price{
      width: 100%;
      height: auto;
      padding: 20px 16px 0 48px;
      background-color: #fff;
      font-size: 13px;
      line-height: 13px;
      margin-bottom: 11px;
      position: relative;
      .price-header{
        width: 100%;
        .scmIconfont-wrap{
          display: inline-block;
          width: 20px;
          height: 20px;
          text-align: center;
          line-height: 17px;
          border: 1px solid #FF4D4F;
          border-radius: 100%;
          position: absolute;
          top: 16px;
          left: 16px;
          >i{
            color: #FF4D4F;
            font-size: 10px;
          }
        }
        h5{
          width: 100%;
          height: 13px;
          color: #666666;
          margin: 0;
        }
      }
      .price-content{
        padding-bottom: 16px;
        >div{
          color:#333333;
          height: 13px;
          margin-top: 20px;
          >span:first-of-type{
            float: left;
          }
          >span:last-of-type{
            float: right;
          }
        }
      }
    }
    .displayNone{
      display: none;
    }
    .order-price{
      .scmIconfont-wrap{
        border: 1px solid #307DCD;
        >i{
          color:#307DCD;
        }
      }
      .total-price{
        height: 40px;
        line-height: 40px;
        position: relative;
        &:before{
          content: "";
          display: inline-block;
          position: absolute;
          height: 1px;
          background-color: #D8D8D8;
          top: 0;
          right: -16px;
          width: calc(100% + 16px);
        }
        >span{
          float: right!important;
        }
        >span:first-of-type{
          color: #FF4242;
        }
      }
    }
    .price-show-perfect-list{
      margin-top: 10px;
      .price-show-perfect{
        height: auto!important;
        padding-left: 16px;
        p{
          margin:0;
          font-size: 12px;
          color: #333333;
          line-height:24px;
          span{
            float: right;
            color: #333333;
          }
        }
      }
    }
    .total-price{
      .am-list-line{
        display: block;
        text-align: right;
        .am-list-extra{
          display: inline-block;
          color: #FF4242!important;
        }
        .am-list-content{
          display: inline-block;
          color: #333333;
          font-size: 12px!important;
        }
      }
    }
    .am-list-body::before{
      height:0px!important;
    }
    .am-list-item .am-list-line .am-list-extra{
      color:#666666;
    }
    .pay-list {
      small {
        color: #999999;
        font-size: 12px;
      }
      .balance {
        span {
          font-size: 12px;
          color: #999999;
          float: right;
          padding-top: 4px;
        }
        .am-list-extra {
          flex-basis: 24%;
        }
      }
    }
    .product-list {
      margin-bottom: 44px;
      b {
        font-weight: normal;
        color: #333333;
      }
      .pro-list-item {
        .am-list-line::after{
          height:0px!important;
        }
        .am-list-extra {
          flex-basis: 10%;
          span{
            font-size:12px;
            color: #999999;
          }
        }
      }
      .am-list-body{
        padding-bottom:3px;
      }
      .am-list-item.am-list-item-middle .am-list-line{
        align-items:baseline;
      }
    }
    .ant-radio-group {
      width: 100%;
      padding-left: 15px;
      > div {
        height: 42px;
        line-height: 42px;
        position: relative;
        .right {
          position: absolute;
          right: 15px;
          .input {
            height: 32px;
          }
        }
      }
      .line1 {
      position:relative
      }
      .line1:after {
      content:'';
      position:absolute;
      bottom:0;left:0;width:100%;
      height:1px;
      border-bottom:1px solid #e0e0e0;
      -webkit-transform:scaleY(.3);
      transform:scaleY(.3);
      }
     }
    .input {
      border: 1px solid #f2f2f2;
      width: 63px;
      position: relative;
      z-index: 10;
    }
    .am-input-label {
      font-size: 14px !important;
    }
    .hide {
      display: none;
    }
    .my-list {
      margin-top: 10px;
      &.not-mt {
        //margin-top: -10px;
      }
      .pricecolor {
        .am-list-item .am-list-line .am-list-extra {
          color: #FF4242;
        }
      }
    }
    .am-list-content {
      font-size: 14px !important;
      color: #333333!important;
    }
    .am-list-extra {
      font-size: 14px !important;
      color: #999999!important;
    }
    .remark {
      margin-top: 10px;
      input {
        text-align: right;
        font-size: 14px !important;
      }
    }
    .text-red {
      .am-list-extra {
        color: #FF4242;
      }
    }
    .line-red {
      .am-list-content {
        color: #FF4242;
      }
      .am-list-extra {
        color: #FF4242;
      }
    }
    .am-list-brief {
      white-space: normal !important;
    }
    .address{
      padding:10px 0 16px;
      p{
        margin-bottom:0;
        margin-top:3px;
      }
    }
    .user-name {
      font-size: 14px;
      color: #333333;
      margin-right: 16px;
    }
    .user-tel {
      font-size: 14px;
      color: #999999;
      margin-right: 16px;
    }
    .default-icon {
      background: #ECF6FF;
      border-radius: 2px;
      font-size: 10px;
      color: #307DCD;
      padding:1px 4px;
    }
    .user-address {
      font-size: 12px;
      color: #999999;
      svg {
        position: relative;
        top: 5px;
        margin-right: 6px;
      }
    }
    .border-img {
      width: 100%;
      height: 2px;
      margin: 0;
      position: relative;
      top: -12px;
    }
  }
`;

const ToPay = styled.div`// styled
  & {
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: fixed;
    width: 100%;
    bottom: 0px;
    height: 44px;
    padding-left: 14px;
    background: #FFFFFF;
    border-top: 1px solid #D8D8D8;
    color: #202020;
    z-index: 10;
    > span {
      display: inline-block;
      &.to-prev {
        width: 90px;
        text-align: center;
        height: 44px;
        line-height: 44px;
        color: #ffffff;
        background: #cccccc;
        margin-left: 10px;
      }
      .am-list-item .am-list-line {
        padding-right: 0;
      }
      .am-list-item .am-list-thumb:first-child {
        margin-right: 5px;
      }
    }
    .pay {
      width: 88px;
      text-align: center;
      height: 36px;
      line-height: 36px;
      color: #ffffff;
      font-size: 14px;
      background: #307DCD;
      border-radius: 18px;
      margin: 0 12px;
    }
  }
`;

const StyledModal = styled(Modal)`// styled
  & {
    .am-modal-content {
      border-radius: 0 !important;
      padding: 0 !important;
    }
    .am-modal-body {
      padding: 0 !important;
    }
  }
`;

const Content = styled.div`// styled
  & {
    max-height: 100%;
    overflow: scroll;
  }
`;

const DistributionWrap = styled.div`// styled
  & {
    width: 100%;
    height: 41px;
    .mode-of-distribution {
      width: 100%;
      height: 100%;
      border-bottom: 1px solid rgba(216, 216, 216, 1);
      font-size: 14px;
      line-height: 14px;
      padding: 13px 16px;
      > span {
        display: inline-block;
        float: left;
        color: #000;
      }
      > .defaultMode {
        display: inline-block;
        float: right;
        color: #666;
      }
    }
  }
`;

const ListItem = styled.div`// styled
  & {
    font-size: 16px;
    color: #202020;
    padding: 8px 0;
  }

  & + & {
    border-top: 1px solid #cccccc;
  }
`;

const PopTitle = styled.p`// styled
  & {
    position: relative;
    overflow: hidden;
    margin: 0;
    height: 50px;
    line-height: 50px;
    font-size: 18px;
    text-align: center;
    color: #000;
    border-bottom: 1px solid #ddd;
    .close {
      position: absolute;
      display: block;
      left: 0;
      top: 0;
      height: 50px;
      width: 50px;
      line-height: 50px;
      text-align: center;
      font-size: 14px;
    }
  }
`;

const PopContent = styled.div`// styled
  & {
    .money {
      margin-top: 0;
      color: #000;
      font-size: 28px;
      font-weight: 700;
      text-align: center;
    }
  }
`;
