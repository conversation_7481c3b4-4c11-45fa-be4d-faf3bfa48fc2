import { observer } from "mobx-react";
import { withRouter } from "react-router";
import * as React from "react";
import styled from "styled-components";
import { Button, List } from "antd-mobile";
import { SITE_PATH } from "../app";
import { $CartType } from "../../classes/const/$cart-type";
import { $AppStore, AppStoreKey } from "../../classes/stores/app-store-mv";
import { autowired } from "@classes/ioc/ioc";
import { $OrderPartySelectionMV } from "../order-party-selection/$order-party-selection-mv";
const Item = List.Item;

@withRouter
@observer
class SubmitOrderSuccess extends React.Component<any, any> {
  @autowired($AppStore)
  public $AppStore: $AppStore;
  constructor(props) {
    super(props);
  }
  public componentDidMount(): void {
    document.title = "提交订单成功";
  }

  public toOrderDetail = () => {
    const { orderId } = this.props.location.state;
    /*this.props.history.push({pathname: `${SITE_PATH}/shop/order-detail/${orderId}/3213`});*/
    //window.location.href = `/${SITE_PATH}/shop/order-detail/${orderId}/3213`;
    if (this.props.location.state) {
      if (this.props.location.state.backSource === "single") {
        this.$AppStore.clearPageMv(AppStoreKey.SINGLESHOPORDERLISTSTORE);
        window.location.href = `/${SITE_PATH}/shop/order-list`;
      } else {
        this.$AppStore.clearPageMv(AppStoreKey.ALLSHOPORDERLISTSTORE);
        window.location.href = `/${SITE_PATH}/shop/all-shop-order-list`;
      }
    } else {
      this.$AppStore.clearPageMv(AppStoreKey.SINGLESHOPORDERLISTSTORE);
      window.location.href = `/${SITE_PATH}/shop/order-list`;
    }
  }

  public render() {
    const { docNo, payCount, orderPriceViewPermission } = this.props.location.state;
    return (
      <SuccessWrapper>
        <SuccessHeader>
          <p>
            <i className="scmIconfont scm-icon-success"/>
          </p>
          <p>
            提交订单成功
          </p>
        </SuccessHeader>
        <SuccessBorder/>
        <List><Item extra={`${docNo}`}>订货单号</Item></List>
        {
          orderPriceViewPermission === $CartType.ORDERPRICEVIEWPERMISSION ? <List className="amount"><Item
            extra={`¥${payCount ? Number(payCount).toFixed(2) : 0}`}>应付金额</Item></List> : null
        }
        <ConfirmButton>
          <Button type="primary" onClick={this.toOrderDetail}>查看订单</Button>
        </ConfirmButton>
      </SuccessWrapper>
    );
  }
}

export default SubmitOrderSuccess;

const SuccessWrapper = styled.div`// styled
  & {
    width: 100%;
    overflow: hidden;
    .am-list-item .am-list-line .am-list-content {
      font-size: 14px;
      color: #333;
    }
    .am-list-item .am-list-line .am-list-extra {
      font-size: 14px;
      color: #666;
    }
    .amount .am-list-item .am-list-line .am-list-extra {
      color: #FF3030;
    }
  }
`;

const SuccessHeader = styled.div`// styled
  & {
    width: 100%;
    height: 200px;
    background: #fff;
    text-align: center;
    .scm-icon-success {
      color: #307DCD;
      font-size: 50px;
    }
    > p:nth-of-type(1) {
      margin: 70px auto 10px auto;
    }
    > p:nth-of-type(2) {
      font-size: 14px;
      color: #307DCD;
    }
  }
`;

const SuccessBorder = styled.div`// styled
  & {
    width: 100%;
    height: 10px;
    background: #F2F2F2;
  }
`;

const ConfirmButton = styled.div`
  &{
    padding: 15px;
    left:0;
    width:100%;
    position:fixed;
    bottom: 0px;
    background:#ffffff;
    .am-button{
      height:42px;
      line-height:42px;
      font-size:16px;
    }
    .am-button-primary{
      border-radius:3px;
    }
  }
`;
