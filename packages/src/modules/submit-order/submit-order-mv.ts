import { autowired, bean } from "@classes/ioc/ioc";
import { action, computed, observable } from "mobx";
import { $Address } from "../../classes/entity/$address";
import { $Product } from "../../classes/entity/$product";
import { $ComponentService } from "../../classes/service/$component-service";
import { $OrderPartyService } from "../../classes/service/$order-party-service";
import { $OrderInfo } from "../../classes/entity/$order-info";
import { assign, sum } from "lodash";

@bean($SubmitOrderMv)
export class $SubmitOrderMv {
  @autowired($OrderPartyService)
  public $orderPartyService: $OrderPartyService;

  @autowired($ComponentService)
  public $componentService: $ComponentService;

  @observable public consigneeInfo: $Address = new $Address();

  @observable public payType: number = 0;

  @observable public memo: string;

  @observable public orderItemList: $Product[];

  @observable public payTypeList: any[] = [];

  @observable public addressList: any[] = [];

  @observable public showModal: boolean = false;

  @observable public salesOrderId: number;

  @observable public isRole: boolean = false;

  @observable public relatedDocId: string;

  @observable public relatedDocObjectType: string;

  @observable public paymentModeId: string;

  @observable public paymentAccountId: string;

  @observable public amount: string;

  @observable public instalmentType: string;

  @observable public saveOrderPayData: object;

  @observable public orderItemAdjustmentList: any;

  @observable public usedCouponList: any;

  @observable public wxToken: string;

  @observable public calculatefreight: number = 0;

  @observable public orderInfo: $OrderInfo = new $OrderInfo();

  @observable public isUseFreight: string;

  @observable public orderPriceViewPermission: string;

  @observable public retailPriceViewPermission: string;

  @observable public discountAmountViewPermission: string;

  @observable public freightViewPermission: string;

  @observable public source: boolean = true;

  @observable public instalmentList: any[] = [];

  @observable public replaceDistributionModeList: any[] = [];

  @observable public orginDistributionModeList: any[] = [];

  @observable public defaultDistributionMode: object;

  @observable public orderItemListForSave: any[] = [];

  @observable public orderOrgList: any[] = [];

  @observable public isCreateFreightFeeDocument: string;

  @observable public freightFeeDocumentPaymentStatusName: string;

  @observable public isAllowModifyDistributionModeForMall: string;

  @observable public isSpin: boolean = false;

  @action
  public showSpin() {
    this.isSpin = true;
  }

  @action
  public hideSpin() {
    this.isSpin = false;
  }

  @action
  public setSource() {
    this.source = false;
  }

  @action
  public setIsAllowModifyDistributionModeForMall(isAllowModifyDistributionModeForMall) {
    this.isAllowModifyDistributionModeForMall = isAllowModifyDistributionModeForMall;
  }

  @action
  public setFormData(purchaseInfo, payType: number, orderMemo: string, skuList: any[]) {
    this.consigneeInfo = purchaseInfo;
    this.payType = payType;
    this.memo = orderMemo;
    this.orderItemList = skuList;
  }

  @action
  public setOrderItemListForSave(orderItemListForSave) {
    this.orderItemListForSave = orderItemListForSave;
  }

  @action
  public setOrderInfo(orderInfo) {
    this.orderInfo = orderInfo;
  }

  @action
  public getFormData() {
    return this;
  }

  @action
  public setSaveOrderPayData(data) {
    this.saveOrderPayData = data;
  }

  @action
  public fetchAddressList() {
    this.$orderPartyService.queryAddress({}).then((data) => {
      const { addressList } = data;
      this.addressList = addressList.map((address) => {
        if (this.consigneeInfo.contactPersonName === ""
          && this.consigneeInfo.address === ""
          && address.isDefault === "Y") {
          this.consigneeInfo = address;
        }
        return new $Address(address);
      });
      // this.addressList = addressList.map((address) => new $Address(address));
    });
  }

  @action
  public queryBatchLoadShops(params) {
    return this.$orderPartyService.queryBatchLoadShops(params).then((data) => {
      console.log(data.orderPartyInfo);
      this.orderOrgList = data.orderPartyInfo;
    }).catch((err) => {
      console.log(err);
    });
  }

  @action
  public fetchPayTypeList() {
    this.$componentService.queryPayType().then((data) => {
      const { payTypeList } = data;
      this.payTypeList = payTypeList;
      this.payType = payTypeList[0].oid;
    });
  }

  @action
  public fetchQueryRole() {
    return this.$componentService.queryRole();
  }

  @action
  public fetchPayPageDetail(params) {
    return this.$componentService.queryPayPageList(params);
  }

  @action
  public fetchDealerPayList(params) {
    return this.$componentService.queryDearlerPayList(params);
  }

  @action
  public setInstalmentList(instalmentList) {
    this.instalmentList = instalmentList;
  }

  @computed
  get calcInstalmentList() {
    return sum(this.instalmentList.map((data) => {
      return data.amount;
    }));
  }

  @action
  public calculateFreight(params) {
    this.showSpin();
    return this.$componentService.calculateFreight(params).then((data) => {
      this.hideSpin();
      this.calculatefreight = data.freightAmount;
      return data;
    }).catch((err) => {
      console.log(err);
      this.hideSpin();
    });
  }

  @action
  public setIsRole(isRole) {
    this.isRole = isRole;
  }

  @action
  public saveOrder(params) {
    return this.$componentService.saveOrder(params);
  }

  @action
  public saveOrderPay(params) {
    return this.$componentService.saveOrderPay(params);
  }

  @computed
  get addressInfo() {
    const receiveUserName = this.consigneeInfo.contactPersonName;
    const detailAddress = this.consigneeInfo.address;
    const receiveUserMobile = this.consigneeInfo.mobile;
    return receiveUserName + " " + receiveUserMobile + " " + detailAddress;
  }

  @action
  public setPayType(payType: any) {
    this.payType = payType;
  }

  @action
  public setConsigneeInfo(address: any) {
    this.consigneeInfo = address;
  }

  @action
  public setMemo(memo: string) {
    this.memo = memo;
  }

  @action
  public openModal() {
    this.showModal = true;
  }

  @action
  public closeModal() {
    this.showModal = false;
  }

  @action    // 余额付款
  public createPayment(params) {
    return this.$orderPartyService.createPayment(params);
  }

  @action
  public setSalesOrderId(salesOrderId) {
    this.salesOrderId = salesOrderId;
  }

  @action
  public setRelatedDocId(relatedDocId) {
    this.relatedDocId = relatedDocId;
  }

  @action
  public setRelatedDocObjectType(relatedDocObjectType) {
    this.relatedDocObjectType = relatedDocObjectType;
  }

  @action
  public setPaymentModeId(paymentModeId) {
    this.paymentModeId = paymentModeId;
  }

  @action
  public setPaymentAccountId(paymentAccountId) {
    this.paymentAccountId = paymentAccountId;
  }

  @action
  public setInstalmentType(instalmentType) {
    this.instalmentType = instalmentType;
  }

  @action
  public setAmount(amount) {
    this.amount = amount;
  }

  @action
  public setOrderItemAdjustmentList(list: any) {
    this.orderItemAdjustmentList = list;
  }

  @action
  public setDiscountAmountViewPermission(string) {
    this.discountAmountViewPermission = string;
  }

  @action
  public setFreightViewPermission(string) {
    this.freightViewPermission = string;
  }

  @action
  public setDefaultDistributionMode(defaultDistributionMode) {
    this.defaultDistributionMode = assign(this.defaultDistributionMode, {}, defaultDistributionMode);
  }

  @action
  public setOrginDistributionModeList(list: any) {
    this.orginDistributionModeList = list || [];
    this.replaceDistributionModeList = [];
    this.defaultDistributionMode = null;
    this.orginDistributionModeList.map((mode, index) => {
      if (index !== 0) {
        mode.isDefault = "N";
      }
    });
    // 商城创建订单允许修改配送方式为是/否，如果只有一个启用的商城端展示的配送方式，则商城端创建订单的时候，配送方式就取这个配送方式，无论这个配送方式是不是默认配送方式，且界面不展示配送方式选择的图标
    if (this.orginDistributionModeList.length === 1) {
      this.setDefaultDistributionMode(this.orginDistributionModeList[0]);
      return;
    }
    console.log(this.isAllowModifyDistributionModeForMall);
    if (this.isAllowModifyDistributionModeForMall === "Y") {
      // 商城创建订单允许修改配送方式为是，如果有多个启用的商城端展示的配送方式，则按现有配送方式取值逻辑，能取到默认配送方式就展示默认配送方式，取不到则为空，且界面展示配送方式选择图标
      this.orginDistributionModeList.map((item, index) => {
        if (index === 0 && item.isDefault === "Y") {
          this.setDefaultDistributionMode(item);
        } else {
          this.replaceDistributionModeList.push(item);
        }
      });
    } else {
      // 商城创建订单允许修改配送方式为否，如果有多个启用的商城端展示的配送方式，则按现有配送方式取值逻辑，能取到默认配送方式就展示默认配送方式，取不到则为空，且界面不展示配送方式选择图标
      this.orginDistributionModeList.map((item, index) => {
        if (index === 0 && item.isDefault === "Y") {
          this.setDefaultDistributionMode(item);
        }
      });
    }
  }

  public changeReplaceDistributionModeList(id) {
    this.replaceDistributionModeList = [];
    this.orginDistributionModeList.map((item) => {
      if (item.distributionModeId === id) {
        this.setDefaultDistributionMode(item);
      } else {
        this.replaceDistributionModeList.push(item);
      }
    });
  }

  @action
  public setUsedCouponList(list: any) {
    this.usedCouponList = list;
  }

  @action
  public setIsUseFreight(isUseFreight) {
    this.isUseFreight = isUseFreight;
  }

  @action
  public setOrderPriceViewPermission(permission, retailPriceViewPermission) {
    this.orderPriceViewPermission = permission;
    this.retailPriceViewPermission = retailPriceViewPermission;
  }

  @action
  public wxPay(params, ssoSessionId) {
    return this.$componentService.wxPay(params, ssoSessionId);
  }

  @action
  public fetchWxToken() {
    const params = {
      tenant: "aligateway.aligatewayuat",
      username: "alipay",
      password: "alipay@12qw",
    };
    this.$componentService.wxToken(params).then((data) => {
      this.wxToken = data.token;
    });
  }
}
