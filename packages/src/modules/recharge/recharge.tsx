import { autowired } from "@classes/ioc/ioc";
import { observer } from "mobx-react";
import { withRouter } from "react-router";
import * as React from "react";
import PaymentLine from "../../components/payment-line/payment-line";
import { $RechargeMv } from "./recharge-mv";
import styled from "styled-components";
import { ActivityIndicator, Button, Checkbox, List } from "antd-mobile";
import { Radio, Spin } from "antd";
import { Toast } from "antd-mobile/es";
import { find, findIndex } from "lodash";
import { $SubmitOrderMv } from "../submit-order/submit-order-mv";
import DateUtils, { toFixedOptimizing } from "../../classes/utils/DateUtils";
import { CapitalAccountListMobel } from "../../components/capital-account-list-mobel/capital-account-list-mobel";
import { WxUploadImgMv } from "../../components/wx-upload-img/wx-upload-img-mv";
import { ScrollFreezeTitleBar } from "../../components/scroll-freeze-title-bar";
import { InputItemComponent } from "../../components/input-item-component/input-item-component";
import { PaymentListComponent } from "../../components/payment-list-component/payment-list-component";
import { $PaymentType } from "../../classes/const/$payment-type";
import { ChooseBankCardMv } from "../../components/choose-bank-card-modal/choose-bank-card-mv";
import { SITE_PATH } from "../app";
import { $PaymentModeType } from "../../classes/const/$payment-mode-type";
import { $AppStore, AppStoreKey } from "../../classes/stores/app-store-mv";
import { $ScrollFreezeTitleBarMV } from "../../components/scroll-freeze-title-bar/index-mv";
import { getUrlParam } from "../../classes/utils/UrlUtils";

const Item = List.Item;
const Brief = Item.Brief;
const RadioGroup = Radio.Group;
const CheckboxItem = Checkbox.CheckboxItem;
const now = new Date();
declare let WeixinJSBridge: any;
declare let window: any;

@withRouter
@observer
class Recharge extends React.Component<any, any> {
  @autowired($AppStore)
  public $AppStore: $AppStore;
  @autowired($RechargeMv)
  public $myMv: $RechargeMv;
  @autowired($ScrollFreezeTitleBarMV)
  public $ScrollFreezeTitleBarMV: $ScrollFreezeTitleBarMV;

  @autowired($SubmitOrderMv)
  public $SubmitOrderMv: $SubmitOrderMv;

  @autowired(WxUploadImgMv)
  public wxUploadImgMv: WxUploadImgMv;

  @autowired(ChooseBankCardMv)
  public chooseBankCardMv: ChooseBankCardMv;

  constructor(props) {
    super(props);
    this.state = {
      value: "",
      payCount: 230,
      isShowMabel: false,
      percent: 0,
      showProgress: false,
      isShowAccount: false,
      animating: false,
      date: null,
      selectCapitalAccountInfo: {
        bankName: "",
        accountCode: "",
        bankAccountName: "",
        capitalAccountId: "",
      },
      rechargeValue: 0,
      isChooseAccount: true,
      isDisabled: true,
      totalValue: 0,
      isChooseAllInPayBk: false,
      isShowChooseBankCardModal: false,
      selectedBankName: null,
      selectedBankCardType: null,
      selectedBankCardNo: null,
      selectedBankId: null,
      needBind: false,
      bindUrl: null,
      transferAmount: 0,
      selectBankCardCode: null,
      rateCode: "",
      showRate: false,
      selectRateList: null,
      bankInfo: {
        bankCardRate: 0,
      },
      isShowPaymentLine: false,   // 是否展示付款线路
      subMerchantList: [],  // 可选付款线路
      originSubMerchantList: [],  // 用于恢复初始状态
    };
  }  // 离开记录滚动高度
  public saveData = (cb?) => {
    const { paymentModeList, pics } = this.$myMv
    const { imgList } = this.wxUploadImgMv
    const { selectCapitalAccountInfo, date, rechargeValue, transferAmount, totalValue, selectedBankName, selectBankCardCode, selectedBankCardType, selectedBankCardNo, selectedBankId, isChooseAccount  } = this.state;
    const rechargeStorage = {
      rechargeValue,
      transferAmount,
      totalValue,
      mode: JSON.stringify(paymentModeList.filter((mode) => mode.isCheck)),
      selectedBankName,
      selectBankCardCode,
      selectedBankCardType,
      selectedBankCardNo,
      selectedBankId,
      isChooseAccount,
      date: date ? date.toString() : "",
      scrollHeight: $("#pageWrap").scrollTop(),
      scrollFreezeTitleBarMV: JSON.stringify(this.$ScrollFreezeTitleBarMV),
      imgList: JSON.stringify(imgList),
      pics: JSON.stringify(pics),
      selectCapitalAccountInfo: JSON.stringify(selectCapitalAccountInfo),
    }
    this.$AppStore.savePageMv(AppStoreKey.RECHARGESTORAGE, rechargeStorage);
    cb && cb();
  }

  public componentWillUnmount(): void {
    this.saveData();
  }

  public pageWindowSkip = (url) => {
    // 跳转到其他页面需要保存数据
    this.saveData();
    setTimeout(() => {
      window.location.href = url;
    }, 50);
  }

  public componentDidMount() {
    document.title = "充值";
    this.$myMv.clearPics();
    this.fetchRepayment();
    this.pageGoBack();
  }
  public fetchRepayment = () => {
    this.$myMv.showSpin();
    const params = {
      accountId: this.props.match.params.accountId,
    };
    const oldData = this.$AppStore.getPageMv(AppStoreKey.RECHARGESTORAGE);
    const rechargeStorage = oldData && JSON.parse(oldData);
    console.log("rechargeStorage", rechargeStorage);
    this.$AppStore.clearPageMv(AppStoreKey.RECHARGESTORAGE);
    this.$myMv.fetchRepayment(params).then(() => {
      console.log(this.$myMv.paymentModeList);
      if ((this.$myMv.paymentModeList && this.$myMv.paymentModeList.length === 0) || (this.$myMv.paymentModeList && this.$myMv.paymentModeList.length === 1 && this.$myMv.paymentModeList.filter((mode) => mode.code === $PaymentModeType.ALLINPAY).length === 1)) {
        this.$myMv.hideSpin();
        Toast.info("无可用的支付方式，请联系管理员");
      }
      if (this.$myMv.paymentModeList.filter((mode) => mode.code === $PaymentType.BANK_TRANSFER).length > 0) {
        this.loadCapitalAccount();
      }
      if (this.$myMv.paymentModeList.filter((mode) => mode.code === $PaymentType.ALLINPAY).length > 0) {
        this.chooseBankCardMv.loadPaymentTpBankList().then((data) => {
          this.$myMv.hideSpin();
          const { bankList } = data;
          if (rechargeStorage && rechargeStorage.transferAmount) {
            this.setState({
              selectedBankName: rechargeStorage.selectedBankName || (bankList[0] && bankList[0].bankName) || null,
              selectBankCardCode: rechargeStorage.selectBankCardCode || (bankList[0] && bankList[0].bankCardTypeCode) || null,
              selectedBankCardType: rechargeStorage.selectedBankCardType === "null" ? null : rechargeStorage.selectedBankCardType || (bankList[0] && bankList[0].bankCardTypeName) || null,
              selectedBankCardNo: rechargeStorage.selectedBankCardNo || (bankList[0] && bankList[0].bankCardNo) || null,
              selectedBankId: rechargeStorage.selectedBankId || (bankList[0] && bankList[0].id) || null,
              isDisabled: false,
            });
          }
        });
      }
      const wxPaymentModeFilter = this.$myMv.paymentModeList.filter((mode) => mode.code === $PaymentType.ALLINPAY_WECHAT);
      if (wxPaymentModeFilter.length > 0) {
        const wxPaymentMode: any = wxPaymentModeFilter[0];
        this.setState({
          originSubMerchantList: wxPaymentMode.subMerchantList,
          subMerchantList: this.handleSubMerchantList(wxPaymentMode.subMerchantList),
        });
      }
      if (this.$myMv.paymentModeList) {
        this.$myMv.paymentModeList.map((lis) => {
          if (lis.code === "wechat") {
            this.$SubmitOrderMv.fetchWxToken();
          }
        });
        this.setState({
          rechargeValue: (rechargeStorage && rechargeStorage.rechargeValue) || 0,
          transferAmount: (rechargeStorage && rechargeStorage.transferAmount) || 0,
          totalValue: (rechargeStorage && rechargeStorage.totalValue) || 0,
        }, () => {
          // mode
          if (rechargeStorage && rechargeStorage.mode) { // 重新支付
            const newArr = JSON.parse(rechargeStorage.mode);
            this.$myMv.paymentModeList.map((lis) => {
              if (lis.code === (newArr[0] && newArr[0].code)) {
                lis.isCheck = true;
              }
            });
          }
          if (this.$myMv.paymentModeList.filter((lis) => lis.isCheck).length > 0 && (this.state.transferAmount !== "" && this.state.transferAmount !== null && this.state.transferAmount !== undefined) && (this.state.transferAmount >= 0 || this.state.rechargeValue >= 0)) {
            this.setState({
              isDisabled: false,
            });
          }
        });
      }
      // 还原原始状态
      const comeBack = getUrlParam("comeBack");
      if (rechargeStorage && comeBack !== "true") {
        const { scrollHeight, scrollFreezeTitleBarMV, imgList, pics, selectCapitalAccountInfo, date, isChooseAccount } = rechargeStorage;
        console.log(rechargeStorage, JSON.parse(selectCapitalAccountInfo));
        this.$ScrollFreezeTitleBarMV.queryOldData(JSON.parse(scrollFreezeTitleBarMV));
        this.$myMv.pics = JSON.parse(pics);
        this.wxUploadImgMv.recordOldImgList(JSON.parse(imgList), JSON.parse(pics) ? JSON.parse(pics).length : 0);
        this.setState({
          selectCapitalAccountInfo: JSON.parse(selectCapitalAccountInfo),
          isChooseAccount,
          date: date ? new Date(date) : null,
        })
        setTimeout(() => {
          $("#pageWrap").scrollTop(scrollHeight);
        }, 50);
      }
    }).catch(() => {
      this.$myMv.hideSpin();
    });
  }
  public handleSubMerchantList = (subMerchantList) => {
    if (subMerchantList.length > 0) {
      subMerchantList.map((item) => {
        item.checked = item.defaulted === "Y";
      });
    }
    console.log("subMerchantList", subMerchantList);
    return subMerchantList;
  }
  public pageGoBack = () => {
    window.addEventListener("popstate", (res) => {
      // 触发函数
      console.log(res, this.props.history);
      this.setState({
        isShowMabel: false,
      });
    });
  }

  public loadCapitalAccount = () => {
    const { paymentModeList } = this.$myMv;
    let paymentModeId;
    if (paymentModeList) {
      paymentModeList.map((paymentMode) => {
        console.log(paymentMode.code);
        if (paymentMode.code === "bank_transfer") {
          paymentModeId = paymentMode.oid;
          const params = { paymentModeId, orderPartyId: this.$myMv.orderPartyId };
          this.$myMv.fetchCapitalAccount(params).then((data) => {
            const { capitalAccountList } = data;
            if (capitalAccountList.length === 1) {
              this.setState(
                {
                  selectCapitalAccountInfo: {
                    bankName: capitalAccountList[0].bankName,
                    accountCode: capitalAccountList[0].accountCode,
                    bankAccountName: capitalAccountList[0].bankAccountName,
                    capitalAccountId: capitalAccountList[0].capitalAccountId,
                  },
                  isChooseAccount: false,
                });
            }

          });
        }
      });
    }
  }

  public onChange = (e) => {
    this.setState({
      value: e.target.value,
    });
    console.log(e.target.value, this.$myMv.paymentModeList);
    const index = findIndex(this.$myMv.paymentModeList, { oid: e.target.value });
    this.$myMv.setIsShow(index);
    setTimeout(() => {
      if (document.querySelector(".ant-radio-wrapper-checked")) { // 线下转账
        const payType = document.querySelector(".ant-radio-wrapper-checked").nextSibling.nextSibling.value;
        document.querySelector(".big-input").value = null; // 清楚上一次留下的金额
        this.setState({ isShowAccount: payType !== "wechat" });
      }
    }, 0);
  }

  public setDefaultCheck = (v, index) => {
    const { capitalAccountList, capitalAccountInfo } = this.$myMv;
    capitalAccountList.forEach((item) => {
      item.isDefault = "N";
    });
    capitalAccountList[index].isDefault = "Y";
    this.props.history.goBack();
    this.setState(
      {
        isShowMabel: false,
        isChooseAccount: false,
        selectCapitalAccountInfo: {
          bankName: capitalAccountList[index].bankName,
          accountCode: capitalAccountList[index].accountCode,
          bankAccountName: capitalAccountList[index].bankAccountName,
          capitalAccountId: capitalAccountList[index].capitalAccountId,
        },
      },
    );
  }

  public showAccountMabel = () => {
    // 防止在选在账户页面点击浏览器返回返回到付款页面的上级页面
    this.props.history.push({
      pathname: window.location.pathname,
    });
    this.setState({
      isShowMabel: true,
    });
  }

  public getRandomNum = () => {
    let result = "";
    const date = new Date();
    const year = date.getFullYear();
    const month = this.twoNum(date.getMonth());
    const onedate = this.twoNum(date.getDate());
    const hour = this.twoNum(date.getHours());
    const min = this.twoNum(date.getMinutes());
    const sec = this.twoNum(date.getSeconds());
    const msec = this.threeNum(date.getMilliseconds());
    let randomStr = "";
    for (let i = 0; i < 6; i++) {
      randomStr += String(parseInt(Math.random() * 10));
    }
    result = year + month + onedate + hour + min + sec + msec + randomStr;
    return result;
  }

  public twoNum = (num) => {
    if (String(num).length < 2) {
      num = "0" + String(num);
    }
    return String(num);
  }

  public threeNum = (num) => {
    if (String(num).length < 2) {
      num = "00" + num;
    } else if (String(num).length < 3) {
      num = "0" + num;
    }
    return num;
  }

  public WeixinJSBridge = (wxInfo, params) => {
    if (typeof WeixinJSBridge == "undefined") {
      if (document.addEventListener) {
        document.addEventListener("WeixinJSBridgeReady", this.onBridgeReady, false);
      } else if (document.attachEvent) {
        document.attachEvent("WeixinJSBridgeReady", this.onBridgeReady);
        document.attachEvent("onWeixinJSBridgeReady", this.onBridgeReady);
      }
    } else {
      this.onBridgeReady(wxInfo, params);
    }
  }

  public onBridgeReady = (wxInfo, params) => {
    WeixinJSBridge.invoke(
      "getBrandWCPayRequest", {
        appId: wxInfo.appId,     // 公众号名称，由商户传入
        timeStamp: wxInfo.timeStamp,         // 时间戳，自1970年以来的秒数
        nonceStr: wxInfo.nonceStr, // 随机串
        package: wxInfo.package,
        signType: "MD5",         // 微信签名方式：
        paySign: wxInfo.paySign, // 微信签名
      },
      (res) => {
        if (res.err_msg === "get_brand_wcpay_request:ok") {
          // 使用以上方式判断前端返回,微信团队郑重提示：
          // res.err_msg将在用户支付成功后返回ok，但并不保证它绝对可靠。
          params.resultCode = "SUCCESS";
        } else {
          params.resultCode = "FAIL";
        }
        this.$myMv.submitRepay(params).then((data) => {
          if (data.result) {
            this.setState({ animating: false });
            Toast.info("支付成功", 3);
            setTimeout(() => {
              history.back();
            }, 3000);
          } else {
            Toast.info("支付失败", 3);
            this.setState({ animating: false });
          }
        }).catch(() => {
          this.setState({ animating: false });
        });
      });
  }

  public getSelectBankRate = (thridPartyKey) => {
    const { paymentModeList } = this.$myMv;
    const { rateList } = paymentModeList.filter((lis) => lis.code === thridPartyKey)[0];
    let rateCode = "";
    const { WECHATRATE, CREDITCARTRATE, STOREDVALUECARDRATE, CREDITBANKCARTCODE } = $PaymentModeType;
    const { selectBankCardCode, transferAmount } = this.state;
    switch (thridPartyKey) {
      case $PaymentType.ALLINPAY_WECHAT:
        rateCode = WECHATRATE;
        break;
      case $PaymentType.ALLINPAY_BANK_CARD:
        console.log(selectBankCardCode, CREDITBANKCARTCODE, selectBankCardCode === CREDITBANKCARTCODE);
        rateCode = selectBankCardCode === CREDITBANKCARTCODE ? CREDITCARTRATE : STOREDVALUECARDRATE;
        break;
      default:
        break;
    }
    let rateBookList = [];
    let tpRate = 0;
    if (rateList && rateList.length > 0 && rateList.filter((res) => res.rateCode === rateCode)) {
      console.log(rateList, "rateList");
      rateBookList = rateList.filter((res) => res.rateCode === rateCode)[0].rateBookList;
    }
    const expectedPaymentAmount = transferAmount;
    if (rateBookList.length > 0) {
      rateBookList.map((item) => {
        if (item.endValue === null || item.endValue === "" || item.endValue === undefined) {
          if (item.beginValue <= expectedPaymentAmount) {
            tpRate = toFixedOptimizing(expectedPaymentAmount * item.rate, 100);
          }
        } else {
          if ((item.beginValue <= expectedPaymentAmount) && (expectedPaymentAmount < item.endValue)) {
           tpRate = toFixedOptimizing(expectedPaymentAmount * item.rate, 100);
          } else if (expectedPaymentAmount >= item.endValue) {
           tpRate = toFixedOptimizing(expectedPaymentAmount * item.rate, 100);
          }
        }
      });
    }
    console.log(thridPartyKey, rateCode, selectBankCardCode);
    return Number(tpRate);
  }

  public onSubmit = () => {
    const { date, selectCapitalAccountInfo, rechargeValue, totalValue, selectedBankId, selectedBankCardNo, subMerchantList } = this.state; // 选中的
    const { paymentModeList } = this.$myMv; // 微信
    let result = 0;
    paymentModeList.map((mode) => {
      if (mode.isCheck) {
        result += 1;
      }
    });
    if (result === 0) {
      Toast.info("请选择支付方式");
      return;
    }
    if (rechargeValue <= 0) {
      Toast.info("请输入正数的数字");
      return;
    }
    if (!/^[0-9]+(.[0-9]{0,2})?$/.test(rechargeValue)) {
      Toast.info("充值金额最多有两位小数");
      return;
    }
    if (paymentModeList.filter((mode) => mode.code === $PaymentType.BANK_TRANSFER).length > 0 && paymentModeList.filter((mode) => mode.code === $PaymentType.BANK_TRANSFER)[0].isCheck && selectCapitalAccountInfo.accountCode === "") {
      Toast.info("请选择收款账户", 3);
      return;
    }
    if (paymentModeList.filter((mode) => mode.code === $PaymentType.BANK_TRANSFER).length > 0 && paymentModeList.filter((mode) => mode.code === $PaymentType.BANK_TRANSFER)[0].isCheck && date === null) {
      Toast.info("请选择正确的付款日期，财务将根据提交的日期核对银行流水", 5);
      return;
    }
    const { capitalAccountInfo, pics } = this.$myMv;
    let paymentModeId = null;
    if (paymentModeList.filter((mode) => mode.code === $PaymentType.BANK_TRANSFER).length > 0 && paymentModeList.filter((mode) => mode.code === $PaymentType.BANK_TRANSFER)[0].isCheck) {
      paymentModeId = paymentModeList.filter((mode) => mode.code === $PaymentType.BANK_TRANSFER)[0].oid;
    }
    if ((paymentModeList.filter((mode) => mode.code === $PaymentType.ALLINPAY_BANK_CARD).length > 0 && paymentModeList.filter((mode) => mode.code === $PaymentType.ALLINPAY_BANK_CARD)[0].isCheck) || (paymentModeList.filter((mode) => mode.code === $PaymentType.ALLINPAY_WECHAT).length > 0 && paymentModeList.filter((mode) => mode.code === $PaymentType.ALLINPAY_WECHAT)[0].isCheck)) {
      paymentModeId = paymentModeList.filter((mode) => mode.code === $PaymentType.ALLINPAY)[0].oid;
    }
    let paymentProductId = null;
    let paymentModeCode = null;
    let serviceCharge = null;
    let subMerchantCode = null;

    if (paymentModeList.filter((mode) => mode.code === $PaymentType.ALLINPAY_BANK_CARD).length > 0 && paymentModeList.filter((mode) => mode.code === $PaymentType.ALLINPAY_BANK_CARD)[0].isCheck) {
      paymentProductId = paymentModeList.filter((mode) => mode.code === $PaymentType.ALLINPAY_BANK_CARD)[0].oid;
      paymentModeCode = paymentModeList.filter((mode) => mode.code === $PaymentType.ALLINPAY_BANK_CARD)[0].code;
      serviceCharge = Number(Number(this.getSelectBankRate($PaymentType.ALLINPAY_BANK_CARD)).toFixed(2));
      console.log(serviceCharge);
    }
    if (paymentModeList.filter((mode) => mode.code === $PaymentType.ALLINPAY_WECHAT).length > 0 && paymentModeList.filter((mode) => mode.code === $PaymentType.ALLINPAY_WECHAT)[0].isCheck) {
      const paymentLineIndex = subMerchantList && subMerchantList.length > 0 ? subMerchantList.findIndex((line) => line.checked === true) : -1;
      paymentProductId = paymentModeList.filter((mode) => mode.code === $PaymentType.ALLINPAY_WECHAT)[0].oid;
      paymentModeCode = paymentModeList.filter((mode) => mode.code === $PaymentType.ALLINPAY_WECHAT)[0].code;
      serviceCharge = Number(Number(this.getSelectBankRate($PaymentType.ALLINPAY_WECHAT)).toFixed(2));
      subMerchantCode = paymentLineIndex > -1 ? subMerchantList[paymentLineIndex].code : "";
      console.log(serviceCharge);
      this.setState({
        animating: true,
      }, () => {
        this.chooseBankCardMv.paygatewayGetchanneluser().then((res) => {
          this.setState({
            needBind: res.needBind,
            bindUrl: res.bindUrl,
          }, () => {
            console.log(this.state.needBind);
            if (this.state.needBind) {
              this.pageWindowSkip(`${this.state.bindUrl}&redirectUrl=${encodeURIComponent(`${document.location.protocol}//${document.domain}:${window.location.port}/${SITE_PATH}/my-recharge/${this.props.match.params.accountId}?comeBack=true`)}`);
            } else {
              const params = {
                paymentModeId,
                paymentProductId, // 通联子项id
                paymentModeCode,
                amount: Number(rechargeValue),
                serviceCharge,
                subMerchantCode,
                accountId: Number(this.props.match.params.accountId),
                accountCode: paymentModeList.filter((mode) => mode.code === $PaymentType.BANK_TRANSFER).length > 0 && paymentModeList.filter((mode) => mode.code === $PaymentType.BANK_TRANSFER)[0].isCheck ? selectCapitalAccountInfo.accountCode : null,
                capitalAccountId: paymentModeList.filter((mode) => mode.code === $PaymentType.BANK_TRANSFER).length > 0 && paymentModeList.filter((mode) => mode.code === $PaymentType.BANK_TRANSFER)[0].isCheck ? selectCapitalAccountInfo.capitalAccountId : null,
                voucherImages: paymentModeList.filter((mode) => mode.code === $PaymentType.BANK_TRANSFER).length > 0 && paymentModeList.filter((mode) => mode.code === $PaymentType.BANK_TRANSFER)[0].isCheck && pics && pics.length === 0 ? this.wxUploadImgMv.imgList : pics,
                rechargeTime: paymentModeList.filter((mode) => mode.code === $PaymentType.BANK_TRANSFER).length > 0 && paymentModeList.filter((mode) => mode.code === $PaymentType.BANK_TRANSFER)[0].isCheck && date ? DateUtils.toStringFormat(date, "yyyy-MM-dd HH:mm") : null,
                bankCardId: selectedBankId,
              };
              this.$myMv.submitRepay(params).then((data) => {
                if (data.result) {
                  if (paymentModeList.filter((mode) => mode.code === $PaymentType.BANK_TRANSFER).length > 0 && paymentModeList.filter((mode) => mode.code === $PaymentType.BANK_TRANSFER)[0].isCheck) { // 非通联支付
                    localStorage.setItem("paymentMessage", JSON.stringify({
                      fromWhere: "recharge",
                      accountId: this.props.match.params.accountId,
                      rechargeTransferAmount: this.state.transferAmount,
                      rechargeValue: this.state.rechargeValue,
                      rechargeTotalValue: this.state.totalValue,
                      rechargeMode: JSON.stringify(paymentModeList.filter((lis) => lis.isCheck)),
                    }));
                    this.pageWindowSkip(`/${SITE_PATH}/payment-result-show/bank_transfer`);
                    // this.setState({
                    //   animating: false,
                    // });
                  } else { // 去网关进行通联支付
                    localStorage.setItem("paymentMessage", JSON.stringify({
                      fromWhere: "recharge",
                      accountId: this.props.match.params.accountId,
                      rechargeTransferAmount: this.state.transferAmount,
                      rechargeValue: this.state.rechargeValue,
                      rechargeTotalValue: this.state.totalValue,
                      rechargeMode: JSON.stringify(paymentModeList.filter((lis) => lis.isCheck)),
                    }));
                    this.pageWindowSkip(`${data.redirectUrl}&redirectUrl=${document.location.protocol}//${document.domain}:${window.location.port}/${SITE_PATH}/payment-result-show/allinpay`);
                  }
                } else {
                  Toast.info("支付失败", 3);
                  this.setState({ animating: false });
                }
              }).catch(() => {
                this.setState({ animating: false });
              });
            }
          });
        }).catch(() => {
          this.setState({
            animating: false,
          });
        });
      });
    } else { // 非通联微信
      const params = {
        paymentModeId,
        paymentProductId, // 通联子项id
        paymentModeCode,
        amount: Number(rechargeValue),
        serviceCharge,
        accountId: Number(this.props.match.params.accountId),
        accountCode: paymentModeList.filter((mode) => mode.code === $PaymentType.BANK_TRANSFER).length > 0 && paymentModeList.filter((mode) => mode.code === $PaymentType.BANK_TRANSFER)[0].isCheck ? selectCapitalAccountInfo.accountCode : null,
        capitalAccountId: paymentModeList.filter((mode) => mode.code === $PaymentType.BANK_TRANSFER).length > 0 && paymentModeList.filter((mode) => mode.code === $PaymentType.BANK_TRANSFER)[0].isCheck ? selectCapitalAccountInfo.capitalAccountId : null,
        voucherImages: paymentModeList.filter((mode) => mode.code === $PaymentType.BANK_TRANSFER).length > 0 && paymentModeList.filter((mode) => mode.code === $PaymentType.BANK_TRANSFER)[0].isCheck && pics && pics.length === 0 ? this.wxUploadImgMv.imgList : pics,
        rechargeTime: paymentModeList.filter((mode) => mode.code === $PaymentType.BANK_TRANSFER).length > 0 && paymentModeList.filter((mode) => mode.code === $PaymentType.BANK_TRANSFER)[0].isCheck && date ? DateUtils.toStringFormat(date, "yyyy-MM-dd HH:mm") : null,
        bankCardId: selectedBankId,
      };
      this.setState({ animating: true }, () => {
        this.$myMv.submitRepay(params).then((data) => {
          if (data.result) {
            if (paymentModeList.filter((mode) => mode.code === $PaymentType.BANK_TRANSFER).length > 0 && paymentModeList.filter((mode) => mode.code === $PaymentType.BANK_TRANSFER)[0].isCheck) { // 非通联支付
              localStorage.setItem("paymentMessage", JSON.stringify({
                fromWhere: "recharge",
                accountId: this.props.match.params.accountId,
                rechargeTransferAmount: this.state.transferAmount,
                rechargeValue: this.state.rechargeValue,
                rechargeTotalValue: this.state.totalValue,
                rechargeMode: JSON.stringify(paymentModeList.filter((lis) => lis.isCheck)),
              }));
              this.pageWindowSkip(`/${SITE_PATH}/payment-result-show/bank_transfer`);
              // this.setState({
              //   animating: false,
              // });
            } else { // 去网关进行通联支付
              localStorage.setItem("paymentMessage", JSON.stringify({
                fromWhere: "recharge",
                accountId: this.props.match.params.accountId,
                rechargeTransferAmount: this.state.transferAmount,
                rechargeValue: this.state.rechargeValue,
                rechargeTotalValue: this.state.totalValue,
                rechargeMode: JSON.stringify(paymentModeList.filter((lis) => lis.isCheck)),
              }));
              this.pageWindowSkip(`${data.redirectUrl}&redirectUrl=${document.location.protocol}//${document.domain}:${window.location.port}/${SITE_PATH}/payment-result-show/allinpay`);
            }
          } else {
            Toast.info("支付失败", 3);
            this.setState({ animating: false });
          }
        }).catch(() => {
          this.setState({ animating: false });
        });
      });
    }
  }

  public setPics = (files) => {
    this.$myMv.setPics(files);
  }

  public changePayment = (mode) => {
    console.log("changePayment", mode);
    if (mode.code === $PaymentType.ALLINPAY_BANK_CARD && !mode.isCheck) {
      this.setState({
        isShowChooseBankCardModal: true,
      });
    } else {
      this.thridPartyModeChangeNext(mode);
    }
  }

  public thridPartyModeChangeNext = (mode) => {
    const { paymentModeList } = this.$myMv;
    const { originSubMerchantList } = this.state;
    mode.isCheck = !mode.isCheck;
    console.log(this.state.transferAmount);
    this.handleSubMerchantList(originSubMerchantList);
    if (mode.isCheck && (this.state.transferAmount !== "" && this.state.transferAmount !== null && this.state.transferAmount !== undefined) && this.state.transferAmount >= 0) {
      this.setState({
        isDisabled: false,
      });
    } else {
      this.setState({
        isDisabled: true,
      });
    }

    if (mode.code === $PaymentType.ALLINPAY_BANK_CARD) { // 通联银行卡
      if (paymentModeList.filter((lis) => lis.code === $PaymentType.ALLINPAY_BANK_CARD).length > 0 && paymentModeList.filter((lis) => lis.code === $PaymentType.ALLINPAY_BANK_CARD)[0].isCheck) { // 如果选了通联的总额加上服务费
        // 如果通联银行卡选中，通联微信不选中
        if (paymentModeList.filter((lis) => lis.code === $PaymentType.ALLINPAY_WECHAT).length > 0) { // 微信勾选取消
          paymentModeList.filter((lis) => lis.code === $PaymentType.ALLINPAY_WECHAT)[0].isCheck = false;
        }
        this.setState({
          totalValue: Number(Number(this.state.rechargeValue) + this.getSelectBankRate($PaymentType.ALLINPAY_BANK_CARD)).toFixed(2),
        });
      } else {
        this.setState({
          totalValue: this.state.rechargeValue || 0,
        });
      }
    }

    if (mode.code === $PaymentType.ALLINPAY_WECHAT) { // 通联微信
      if (paymentModeList.filter((lis) => lis.code === $PaymentType.ALLINPAY_WECHAT).length > 0 && paymentModeList.filter((lis) => lis.code === $PaymentType.ALLINPAY_WECHAT)[0].isCheck) { // 如果微信选中

        if (paymentModeList.filter((lis) => lis.code === $PaymentType.ALLINPAY_BANK_CARD).length > 0) { // 通联银行卡勾选取消
          paymentModeList.filter((lis) => lis.code === $PaymentType.ALLINPAY_BANK_CARD)[0].isCheck = false;
        }
        this.setState({
          totalValue: Number(Number(this.state.rechargeValue) + this.getSelectBankRate($PaymentType.ALLINPAY_WECHAT)).toFixed(2),
        });
      } else {
        this.setState({
          totalValue: this.state.rechargeValue || 0,
        });
      }
    }
  }

  public changeRechargeValue = (value) => {
    console.log(value);
    const { paymentModeList } = this.$myMv;
    this.setState({
      rechargeValue: value,
      transferAmount: value,
      totalValue: value,
    }, () => {
      if (this.state.rechargeValue && this.state.rechargeValue >= 0) {
        paymentModeList.map((lis) => {
          if (lis.isCheck) {
            this.setState({
              isDisabled: false,
            });
          }
        });
      }
      if (value === null || value === undefined || value === "") {
        this.setState({
          isDisabled: true,
        });
      }
      if (paymentModeList.filter((mode) => mode.code === $PaymentType.ALLINPAY_BANK_CARD).length > 0 && paymentModeList.filter((mode) => mode.code === $PaymentType.ALLINPAY_BANK_CARD)[0].isCheck) { // 如果选了通联的总额加上服务费
        this.setState({
          totalValue: Number(Number(this.state.totalValue) + this.getSelectBankRate($PaymentType.ALLINPAY_BANK_CARD)).toFixed(2),
        });
      }
      if (paymentModeList.filter((mode) => mode.code === $PaymentType.ALLINPAY_WECHAT).length > 0 && paymentModeList.filter((mode) => mode.code === $PaymentType.ALLINPAY_WECHAT)[0].isCheck) { // 如果选了通联的总额加上服务费
        console.log(this.state.totalValue, this.getSelectBankRate($PaymentType.ALLINPAY_WECHAT), "jisuan");
        this.setState({
          totalValue: Number(Number(this.state.totalValue) + this.getSelectBankRate($PaymentType.ALLINPAY_WECHAT)).toFixed(2),
        });
      }
    });
  }

  public showChooseBankCardModal = () => {
    this.setState({
      isShowChooseBankCardModal: true,
    });
  }

  public goBack = () => {
    this.setState({
      isShowChooseBankCardModal: false,
    });
    if (this.state.selectedBankId === null) { // 当没有选中卡时
      if (this.$myMv.paymentModeList.filter((lis) => lis.code === $PaymentModeType.ALLINPAY_BANK_CARD).length > 0) {
        this.$myMv.paymentModeList.filter((lis) => lis.code === $PaymentModeType.ALLINPAY_BANK_CARD)[0].isCheck = false;
        this.setState({
          // isDisabled: true,
          totalValue: this.state.rechargeValue,
        });
      }
    }
  }

  public chooseBank = (bank) => {
    console.log(this.chooseBankCardMv.bankList);
    this.setState({
      isShowChooseBankCardModal: false,
      selectedBankName: bank.bankName,
      selectBankCardCode: bank.bankCardTypeCode,
      selectedBankCardType: bank.bankCardTypeName,
      selectedBankCardNo: bank.bankCardNo,
      selectedBankId: bank.id,
      bankInfo: bank,
    }, () => {
      if (this.state.selectedBankId) {
        bank.isCheck = true;
        this.chooseBankCardMv.bankList.filter((lis) => lis.id !== bank.id).map((card) => {
          card.isCheck = false;
        });
        const mode = this.$myMv.paymentModeList.filter((lis) => lis.code === $PaymentType.ALLINPAY_BANK_CARD)[0];
        mode.isCheck = false;
        this.thridPartyModeChangeNext(mode);
      }
    });
  }

  public showRateModal = (list) => {
    console.log(list);
    const { bankInfo } = this.state; // bankCardRate STORED_VALUE_CARD_RATE CREDIT_CARD_RATE
    if (list.paymentCode === $PaymentType.ALLINPAY_WECHAT) { // 微信
      this.setState({
        showRate: true,
        selectRateList: list && list.rateList && list.rateList[0] && list.rateList[0].rateBookList && list.rateList[0],
      }, () => {
        $("html").css("overflow", "hidden");
        $("body").css("overflow", "hidden");
      });
    } else { // 银行卡
      const data = list.rateList && list.rateList.filter((item) => item.rateCode === bankInfo.bankCardRate);
      this.setState({
        showRate: true,
        selectRateList: data && data[0],
      }, () => {
        $("html").css("overflow", "hidden");
        $("body").css("overflow", "hidden");
      });
    }
  }
  public showPaymentLine = () => {
    this.setState({isShowPaymentLine: true});
  }

  public hideRateModal = () => {
    this.setState({
      showRate: false,
    }, () => {
      $("html").css("overflow", "scroll");
      $("body").css("overflow", "scroll");
    });
  }

  public changePaymentLine = (line) => {
    const {subMerchantList} = this.state;
    if (subMerchantList.length > 0) {
      subMerchantList.map((item) => {
        item.checked = false;
        if (item.code === line.code) {
          item.checked = true;
        }
      });
    }
    this.setState({
      subMerchantList,
      isShowPaymentLine: false,
    });
  }

  public render() {
    const { paymentModeList, isSpin, pics, capitalAccountList, capitalAccountInfo } = this.$myMv;
    const { value, percent, showProgress, isShowMabel, isShowAccount, date, selectCapitalAccountInfo, transferAmount, rechargeValue, isChooseAccount, isDisabled, totalValue, isChooseAllInPayBk, isShowChooseBankCardModal, selectedBankName, selectedBankCardType, selectedBankCardNo, showRate, selectRateList, isShowPaymentLine, subMerchantList } = this.state;
    console.log("state", this.state);
    return (
      <RepaymentWrapper id="pageWrap">
        <List>
          <Item className="gray">充值金额</Item>
          <InputItemComponent
            type="money"
            value={rechargeValue}
            placeholder={"请输入充值金额"}
            clear={true}
            onChange={this.changeRechargeValue}
            label={"¥"}
          />
        </List>
        <Spin spinning={isSpin}>
          <PaymentListComponent
            paymentModeList={paymentModeList}
            onChange={this.changePayment}
            isShowAccount={isShowAccount}
            showAccountMabel={this.showAccountMabel}
            bankName={selectCapitalAccountInfo.bankName}
            accountCode={selectCapitalAccountInfo.accountCode}
            date={date}
            changeDate={(val) => this.setState({ date: val })}
            pics={pics}
            setPics={this.setPics}
            transferAmount={transferAmount}
            isChooseAccount={isChooseAccount}
            showChooseBankCardModal={this.showChooseBankCardModal}
            isShow={isShowChooseBankCardModal}
            goBack={this.goBack}
            getSelectBankRate={this.getSelectBankRate}
            chooseBank={this.chooseBank}
            selectedBankName={selectedBankName}
            selectedBankCardType={selectedBankCardType}
            selectedBankCardNo={selectedBankCardNo}
            skipToBindCard={this.saveData}
            bankRedirectUrl={encodeURIComponent(`${document.location.protocol}//${document.domain}:${window.location.port}/${SITE_PATH}/my-recharge/${this.props.match.params.accountId}?comeBack=true`)}
            showRate={showRate}
            showRateModal={this.showRateModal}
            showPaymentLine={this.showPaymentLine}
            hideRateModal={this.hideRateModal}
            selectRateList={selectRateList}
            subMerchantList={subMerchantList}
          />
          {
            find(paymentModeList, (mode) => mode.code === $PaymentType.BANK_TRANSFER)&& (!find(paymentModeList, (mode) => mode.code === $PaymentType.ALLINPAY)) && <ScrollFreezeTitleBar
              styled={{ paddingBottom: "90px" }}
              isHaveBankTranfer={true}
            />
          }
        </Spin>
        <ConfirmButton>
          <Button
            type="primary"
            onClick={this.onSubmit}
            disabled={isDisabled}
          >
            确认支付 ¥ {totalValue && Number(totalValue).toFixed(2)}
          </Button>
        </ConfirmButton>
        <ActivityIndicator
          toast={true}
          text="Loading..."
          animating={this.state.animating}
        />
        <CapitalAccountListMobel
          isShowMabel={isShowMabel}
          capitalAccountList={capitalAccountList}
          setDefaultCheck={this.setDefaultCheck}
        />
        {
          isShowPaymentLine &&
          <PaymentLine
            subMerchantList={subMerchantList}
            onChange={this.changePaymentLine}
            goBack={() => this.setState({isShowPaymentLine: false})}
          />
        }
      </RepaymentWrapper>
    );
  }
}

export default Recharge;

const RepaymentWrapper = styled.div`// styled
  & {
    width: 100%;
    height: ${document.documentElement.clientHeight}px;
    overflow-y: scroll;
    background: #F2F2F2;
    .hide {
      display: none;
    }
    .gray-bg {
      height: 10px;
      background: #f2f2f2;
    }
    .scm-icon-into {
      color: #307DCD;
      margin-right: 5px;
      position: relative;
      top: 1px;
    }
    .gray .am-list-content {
      color: #666666;
    }
    .am-list-item {
      min-height: 40px;
    }
    .am-list-item .am-list-line .am-list-content {
      font-size: 13px;
      font-family: SourceHanSansCN-Normal, SourceHanSansCN;
      font-weight: 400;
      color: rgba(48, 48, 48, 1);
      padding-bottom: 0;
      padding-top:0;
    }
    .ant-radio-group {
      width: 100%;
      > div {
        // border-bottom: 0.5px solid #ddd;
        position: relative;
        .right {
          position: absolute;
          right: 15px;
        }
      }
      .list-item {
        height: 42px;
        line-height: 42px;
        // border-bottom: 0.5px solid #ddd;
        padding-left: 15px;
      }
    }
    .am-list-item .am-input-control input {
      font-size: 24px !important;
      font-family: SourceHanSansCN-Normal, SourceHanSansCN;
      font-weight: 400;
      color: rgba(47, 47, 47, 1);
    }
    .am-list-item .am-input-label {
      font-size: 16px !important;
    }
    .am-list-body::after {
      display: none !important;
    }
    .am-list {
      margin-bottom: 10px;
    }
    .am-button-primary.am-button-disabled {
      background: #d9d9d9 !important;
    }
    .am-list-item .am-input-label.am-input-label-5 {
      width: unset !important;
    }
    .am-list-item .am-list-line {
      padding-right: 0 !important;
    }
    .am-list-item.am-list-item-middle .am-list-line {
      border: none !important;
    }
    @media (min-resolution: 2dppx) {
      html:not([data-scale]) .am-list-item:not(:last-child) .am-list-line {
        border: none !important;
      }
    }
    @media (min-resolution: 2dppx) {
      html:not([data-scale]) .am-list-body::after {
        display: none !important;
      }

      .am-list-body::after {
        display: none !important;
      }
    }
    .am-button-primary::before {
      display: none !important;
    }
    .am-button {
      border: none !important;
    }
  }
`;

const ConfirmButton = styled.div`
  &{
    padding: 15px;
    left:0;
    width:100%;
    position:fixed;
    bottom: 0px;
    background:#ffffff;
    .am-button{
      height:42px;
      line-height:42px;
      font-size:16px;
    }
    .am-button-primary{
      border-radius:3px;
    }
  }
`;
