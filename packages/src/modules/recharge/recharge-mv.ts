import { autowired, bean } from "@classes/ioc/ioc";
import { action, observable } from "mobx";
import { $MyInfoService } from "../../classes/service/$my-info-service";
import { $File } from "../../classes/entity/$file";
import { $Product } from "../../classes/entity/$product";
import { $OrderService } from "../../classes/service/$order-service";
import { $PaymentType } from "../../classes/const/$payment-type";
import { $PaymentModeItem } from "../../classes/entity/payment-mode-item";

@bean($RechargeMv)
export class $RechargeMv {
  @autowired($MyInfoService)
  public $myInfoService: $MyInfoService;

  @autowired($OrderService)
  public $orderService: $OrderService;

  @observable public paymentModeList: $PaymentModeItem[] = [];

  @observable public paymentModeId: number;

  @observable public balanceAmount: number;

  @observable public isSpin: boolean = false;

  @observable public pics: $File[] = [];

  @observable public capitalAccountList: any[] = [];

  @observable public capitalAccountInfo: object;

  @observable public orderPartyId: number;

  @action
  public showSpin() {
    this.isSpin = true;
  }

  @action
  public hideSpin() {
    this.isSpin = false;
  }

  @action
  public clearPics() {
    this.pics = [];
  }

  @action
  public fetchRepayment(params) {
    return this.$myInfoService.loadRechargePage(params).then((data) => {
      this.orderPartyId = data.orderPartyId;
      this.paymentModeId = data.paymentModeList && data.paymentModeList.length > 0 && data.paymentModeList[0].oid;
      data.paymentModeList.map((v, index) => {
        if (v.code === $PaymentType.ALLINPAY) {
          if (v.paymentProductList) {
            v.paymentProductList.map((product) => {
              product.code = product.paymentCode;
              product.name = product.paymentName;
              data.paymentModeList.push(product);
            });
          }
        }
      });
      console.log(data.paymentModeList);
      this.paymentModeList = data.paymentModeList.map((mode) => new $PaymentModeItem(mode));
      this.capitalAccountInfo = data.capitalAccountInfo;
      this.hideSpin();
    });
  }

  @action
  public setPics(pics: any[]) {
    this.pics = pics;
  }

  @action
  public submitRepay(params) {
    return this.$myInfoService.toRecharge(params);
  }

  @action
  public fetchCapitalAccount(params) {
    return this.$orderService.queryCapitalAccount(params).then((data) => {
      const { capitalAccountList } = data;
      this.capitalAccountList = capitalAccountList.map((capitalAccount) => new $Product(capitalAccount));
      return data;
    });
  }

  @action
  public setIsShow(index) {
    this.paymentModeList.map((v) => {
      v.isShow = false;
    });
    this.paymentModeList[index].isShow = true;
  }
}
