import { observer } from "mobx-react";
import React from "react";
import { withRouter } from "react-router";
import styled from "styled-components";

declare let window: any;
declare let require: any;

@observer
class NoAuthority extends React.Component<any, any> {
  public componentDidMount(): void {
    // document.title = "无权限";
  }

  public render() {
    return (
      <NoGoodsWrapper>
        <img src="https://order.fwh1988.cn:14501/static-img/scm/img_permissions.png" alt=""/>
        <Title>抱歉，您没有该页面权限</Title>
      </NoGoodsWrapper>
    );
  }
}

export default NoAuthority;

const Title = styled.h1`// styled
  & {
    font-size: 13px;
    line-height: 20px;
    color:rgba(102,102,102,1);
    margin: 0;
  }
`;
const NoGoodsWrapper = styled.div`// styled
  & {
    text-align:center;
    width:100%;
    height:calc(${document.documentElement.clientHeight}px);
    padding-top: 64px;
    background: #F2F2F2;
    img{
      //width:40%;
      min-width: 185px;
      min-height: 112px;
      margin-bottom:16px;
    }
  }
`;
