import { autowired, bean } from "@classes/ioc/ioc";
import { Spin } from "antd";
import { Modal,Toast } from "antd-mobile";
import { findIndex } from "lodash";
import { observable, transaction } from "mobx";
import { observer } from "mobx-react";
import React from "react";
import { withRouter } from "react-router";
import Slider from "react-slick";
import styled from "styled-components";
import { $ActiveType } from "../../classes/const/$active-type";
import { $Product } from "../../classes/entity/$product";
import { CountDown } from "../../components/count-down/count-down";
import { Footer } from "../../components/footer/footer";
import { TodayItem } from "../../components/table/today-item";
import { SITE_PATH } from "../app";
import { $CartMv } from "../shop-cart/cart-mv";
import { $HomeMV } from "./$home-mv";
import { $CartType } from "../../classes/const/$cart-type";
import { ScrollAbilityWrapComponent } from "../../components/scroll-ability/wrap";
import { LoadingTip } from "../../components/loading-marked-words";
import { $AppStore, AppStoreKey } from "../../classes/stores/app-store-mv";
import { $ReturnedSalesMv } from "../returned-sales/$returned-sales-mv";
const alert = Modal.alert;

@withRouter
@observer
class HomeWrap extends React.Component<any, any> {
  @autowired($AppStore)
  public $AppStore: $AppStore;
  @autowired($CartMv)
  public $CartMv: $CartMv;

  @autowired($ReturnedSalesMv)
  public $ReturnedSalesMv: $ReturnedSalesMv;

  @autowired($HomeMV)
  public $myMv: $HomeMV;

  constructor(props) {
    super(props);
    this.state = {
      showMore: false,
      pageSize: 10,
      finished: false,
      isShow: false,
      isMore: true,
      orderSchemeType: "",
    };
  }
  public saveMV = () => {
    const scrollHeight = $(".scroll-ability-wrap").scrollTop();
    const {
      productList,
      preferentialOrderPriceViewPermission,
      preferentialRetailPriceViewPermission,
      preferentialMemberPriceViewPermission,
    } = this.$myMv;
    const {
      orderSchemeType,
      isMore,
      finished,
    } = this.state;
    const params = {
      scrollHeight,
      productList,
      preferentialOrderPriceViewPermission,
      preferentialRetailPriceViewPermission,
      preferentialMemberPriceViewPermission,
      orderSchemeType,
      isMore,
      finished,
    }
    this.$AppStore.savePageMv(AppStoreKey.HOMEPRODUCTS, params);
  }
  public componentWillUnmount(): void {
    this.saveMV();
  }
  public pageWindowSkip = (url) => {
    this.saveMV()
    setTimeout(() => {
      window.location.href = url;
    }, 50);
  }
  public componentDidMount() {
    document.title = "首页";
    const params = {
      queryType: "INDEX",
    };
    const todayParams = {
      pageIndex: 0,
      pageSize: this.state.pageSize,
    };
    transaction(() => {
      this.$AppStore.queryShopOverdue("", () => {
        this.$myMv.showSpin();
        this.$CartMv.fetchShopcartproductnum().then((res) => {
          // console.log(res);
          if (res.errorCode === $CartType.NOSCHEME) {
            this.$myMv.hideSpin();
            this.$CartMv.setIsAgency();
            alert("暂未选择订货方案，是否继续进入订货方案开始订货", "", [
              {
                text: "取消", onPress: () => {
                  // window.location.href = document.referrer;
                  history.go(-1);
                },
              },
              {
                text: "继续", onPress: () => {
                  // console.log(909090);
                  this.$AppStore.clearPageMv(AppStoreKey.ORDERSCHEMELIST);
                  this.$ReturnedSalesMv.businesssControlCheck({orgId:res.orderPartyId,businessTypeControl:'SalesOrderControl'}).then(data=>{
                    if (data.errorCode == "-1") {
                      history.go(-1);
                      Toast.info(data.errorMessage)
                      
                    }else{
                      this.props.history.push({
                        pathname: `/${SITE_PATH}/select-scheme`,
                        state: { orderPartyId: `${res.orderPartyId}` },
                      });
                    }
                  })
                  
                },
              },
            ]);
          } else {
            // console.log(90909);
            this.$myMv.fetchHome();
            this.$myMv.fetchSuitList(params).then((data) => {
              this.$myMv.hideSpin();
              if (data.itemCount > 5) {
                this.setState({
                  showMore: true,
                });
              }
              this.$myMv.setSuitList(data.activitySuitList);
              this.$myMv.setOrderPriceViewPermission(data.orderPriceViewPermission, data.retailPriceViewPermission);
            });
            // 从缓存中获取老数据
            let oldData = this.$AppStore.getPageMv(AppStoreKey.HOMEPRODUCTS)
            if (oldData) {
              oldData  = JSON.parse(oldData);
              const {
                preferentialOrderPriceViewPermission,
                preferentialRetailPriceViewPermission,
                scrollHeight,
                orderSchemeType,
                productList,
                isMore,
                finished,
              } = oldData;
              this.setState({ orderSchemeType, isMore, finished });
              this.$myMv.setPreferentialPriceViewPermission(preferentialOrderPriceViewPermission, preferentialRetailPriceViewPermission);
              this.$myMv.setProducts(productList);
              $(".scroll-ability-wrap").scrollTop(scrollHeight);
              this.$AppStore.clearPageMv(AppStoreKey.HOMEPRODUCTS);
            } else {
              // 获取商品列表
              this.setState({ isShow: true });
              this.$myMv.fetchToday(todayParams).then((data) => {
                this.setState({ isShow: false, orderSchemeType: data.orderSchemeType });
                this.$myMv.setPreferentialPriceViewPermission(data.orderPriceViewPermission, data.retailPriceViewPermission);
                this.$myMv.setProducts(data.productList);
                if (data.productList.length < this.state.pageSize) {
                  this.setState({
                    isMore: false,
                  });
                }
              }).catch((err) => {
                this.setState({ isMore: false });
              });
            }
          }
        });
      }, true);
    });
  }
  public componentWillReceiveProps(nextProps: Readonly<any>, nextContext: any): void {
    const { isScrollEnd } = nextProps;
    if (isScrollEnd && isScrollEnd !== this.props.isScrollEnd) {
      this.loadData();
    }
  }
  public cartWrapper = (products: $Product[], cartProducts: $Product[]) => {
    products.forEach((product) => {
      const index = findIndex(cartProducts, { productSkuId: product.productSkuId });
      if (index > -1) {
        product.setQuantity(cartProducts[index].quantity);
      } else {
        product.setQuantity(0);
      }
    });
    return products;
  }

  public imgClickLink = (ad) => {
    if (ad.linkType === "URL_ADDRESS") {
      this.pageWindowSkip(ad.urlLink);
    } else if (ad.linkType === "PRODUCT_SKU") {
      this.pageWindowSkip(`/${SITE_PATH}/commodity-details/${ad.productSkuId}`);
      console.log(`跳商品详情${ad.productSkuId}`);
    } else if (ad.linkType === "ANNOUNCEMENT") {
      console.log(`跳公告详情${ad.announcementId}`);
      this.pageWindowSkip(`/${SITE_PATH}/message-company-detail/${ad.announcementId}`);
    }
  }

  public toMoreSuit = () => {
    this.props.history.push({ pathname: `/${SITE_PATH}/suit-list` });
  }

  public toSearch = () => {
    this.$AppStore.clearPageMv(AppStoreKey.SEARCHSHOPLIST);
    this.props.history.push({ pathname: `/${SITE_PATH}/SearchList/fromHome` });
  }

  public toSelect = () => {
    // this.props.history.push({ pathname: `/${SITE_PATH}/select` });
    sessionStorage.setItem("editOrderId", null);
    this.$AppStore.clearPageMv(AppStoreKey.ORDERPARTYSELECTION);
    this.pageWindowSkip(`/${SITE_PATH}/select`);
  }

  public loadData = () => {
    if (this.state.finished === false) {
      this.setState({
        isShow: true,
      });
      this.$myMv.addPageIndex();
      const todayParams = {
        pageIndex: this.$myMv.pageIndex,
        pageSize: this.state.pageSize,
      };
      this.$myMv.fetchToday(todayParams).then((data) => {
        const { productList, itemCount, orderSchemeType } = data;
        this.$myMv.pushProducts(productList).then((length) => {
          if (length >= itemCount) {
            this.setState({
              finished: true,
            });
          }
          const { loadingEnd } = this.props;
          loadingEnd && loadingEnd(length >= itemCount);
        });
        this.setState({
          isShow: false,
          orderSchemeType,
        });
      });
    }
  }

  public toSuitDetail = (id) => {
    this.props.history.push({ pathname: `/${SITE_PATH}/suit-detail/${id}` });
  }

  public render() {
    const { adList, suitList, productList, orderPartyName, orderSchemeName, isSpin, orderPriceViewPermission, retailPriceViewPermission, preferentialOrderPriceViewPermission, preferentialRetailPriceViewPermission } = this.$myMv;
    const { products, isAgency } = this.$CartMv;
    const { orderSchemeType } = this.state;
    const newProducts = this.cartWrapper(productList, products);
    // console.log(suitList);

    const adSettings = {
      autoplay: true,
      autoplaySpeed: 5000,
      centerMode: true,
      centerPadding: "0px",
      dots: true,
      infinite: true,
      slidesToScroll: 1,
      slidesToShow: 1,
      speed: 500,
    };

    return (
      <Wrapper className="indexPage">
        <Spin spinning={isSpin}>
          <header>
            <div className="left" onClick={this.toSelect}>
              <i className="scmIconfont scm-icon-shop"></i>
              {orderPartyName ? orderPartyName.length > 4 ? orderPartyName.slice(0, 5) + "..." : orderPartyName : null}－{orderSchemeName ? orderSchemeName.length > 14 ? orderSchemeName.slice(0, 15) + "..." : orderSchemeName : null}
              <i className="scmIconfont scm-icon-arrow-down"></i>
            </div>
            <div className="right" onClick={this.toSearch}><i className="scmIconfont scm-icon-search"></i></div>
          </header>
          {
            adList.length > 0 ? <Slider {...adSettings}>
              {
                adList.map((ad) => {
                  return <img className="slider-img" key={ad.adRotatorItemId}
                              src={ad.imageUrl ? ad.imageUrl : "https://order.fwh1988.cn:14501/static-img/scm/ico-banner-nopic.png"}
                              onClick={() => this.imgClickLink(ad)} alt=""/>;
                })
              }
            </Slider> : null}
          {
            suitList && suitList.length > 0 ? <SuitWrapper>
              <Title>
                <img className="left" src="https://order.fwh1988.cn:14501/static-img/scm/icon-title-line-left.png"
                     alt=""/>
                <img className="icon" src="https://order.fwh1988.cn:14501/static-img/scm/icon-title-discount.svg" alt=""/>
                套装特惠
                <img className="right" src="https://order.fwh1988.cn:14501/static-img/scm/icon-title-line-right.png"
                     alt=""/>
                {
                  this.state.showMore ? <span onClick={this.toMoreSuit}>更多 ></span> : null}
              </Title>
              {
                suitList.map((v, index) => {
                  return <SuitItem
                    key={v.activitySuitId}
                    onClick={() => this.toSuitDetail(v.activitySuitId)}
                    className={v.isActive ? "" : "hide"}
                  >
                    <img
                      src={v.imageUrl ? v.imageUrl : "https://order.fwh1988.cn:14501/static-img/scm/ico-banner-nopic.png"}
                      alt="" className="big-img"/>
                    <h5>{v.title && v.title.length > 16 ? v.title.slice(0, 16) + "..." : v.title}</h5>
                    {
                      v.subTitle && v.subTitle !== "" ? <div
                        className="suit-desc">{v.subTitle && v.subTitle.length > 30 ? v.subTitle.slice(0, 30) + "..." : v.subTitle}</div> : null}
                    <div className="suit-bottom">
                      {
                        (orderPriceViewPermission === $CartType.ORDERPRICEVIEWPERMISSION) && (v.orderPrice !== null) ?
                          <span className="red">￥{v.orderPrice}</span> : null
                      }
                      {
                        ((retailPriceViewPermission === $CartType.RETAILPRICEVIEWPERMISSION) && (v.originPrice !== null)) && (((orderPriceViewPermission === $CartType.ORDERPRICEVIEWPERMISSION) && (v.orderPrice !== null))) ?
                          <del>￥{v.originPrice}</del> :
                          (retailPriceViewPermission === $CartType.RETAILPRICEVIEWPERMISSION) && (v.originPrice !== null) ?
                            <span>￥{v.originPrice}</span> : null
                      }
                      <CountDown timeDiff={v.countDown} suitId={v.activitySuitId} type="index"></CountDown>
                    </div>
                  </SuitItem>;
                })
              }
            </SuitWrapper> : null
          }
          <TodayWrapper>
            <Title>
              <img className="left" src="https://order.fwh1988.cn:14501/static-img/scm/icon-title-line-left.png" alt=""/>
              <img className="icon" src="https://order.fwh1988.cn:14501/static-img/scm/icon-title-discount.svg" alt=""/>
              今日热销
              <img className="right" src="https://order.fwh1988.cn:14501/static-img/scm/icon-title-line-right.png"
                   alt=""/>
            </Title>
            <div>
              {
                newProducts ? newProducts.map((v) => {
                  return <TodayItem
                    key={v.productSkuId}
                    data={v}
                    noCart={true}
                    orderSchemeType={orderSchemeType}
                    pricePermission={preferentialOrderPriceViewPermission}
                    retailPriceViewPermission={preferentialRetailPriceViewPermission}
                    pageSkip={this.saveMV}
                  />;
                }) : null
              }
            </div>
            {
              newProducts &&
							<LoadingTip
								isFinished={this.state.finished}
								isLoad={this.state.isShow}
							/>
            }
          </TodayWrapper>
        </Spin>
        <Footer
          isAgency={isAgency}
          activeKey={$ActiveType.MAIN_KEY}
          count={this.$CartMv.totalCount}
          leaveCurrentPage={this.saveMV}
        />
      </Wrapper>
    );
  }
}
const Home = ScrollAbilityWrapComponent(HomeWrap);
export default Home;

const Wrapper = styled.div`// styled
  & {
    background:#f2f2f2;
    min-height:${document.documentElement.clientHeight}px;
    height: auto;
    padding:30px 0 50px;
    .slick-dots{
      bottom:5px;
    }
    .slick-slider.slick-initialized{
      margin-top:10px;
      overflow:hidden;
    }
    .slick-dots li button:before{
      color:white;
      opacity:1;
    }
    .slick-dots li{
      width:16px;
      height:16px;
      margin:0;
    }
    .slick-dots li.slick-active button:before{
      opacity:1;
      color:#307DCD;
    }
    .slider-img{
      height:${document.documentElement.clientWidth * 118 / 375}px;
    }
    header{
      height:30px;
      line-height:26px;
      color:#307DCD;
      font-size:12px;
      position:fixed;
      top:0;
      left:0;
      width:100%;
      padding:0 10px;
      background:white;
      z-index:999;
      border-bottom:1px solid #D8D8D8;
      .left{
        float:left;
        .scm-icon-shop{
          color:#999999;
          margin-right:5px;
          position:relative;
          top:1px;
        }
        .scm-icon-arrow-down{
          margin-left:5px;
          position:relative;
          top:1px;
        }
      }
      .right{
        float:right;
        padding:0px 5px 0 10px;
        .ico-search{
          color:#999999;
          position:relative;
          top:1px;
        }
      }
    }
  }
`;

const SuitWrapper = styled.div`// styled
  & {
    margin-top:10px;
    background:white;
    padding:10px 15px 0px;
    overflow:hidden;
    .hide{
      display:none;
    }
    img.big-img{
      height:${(document.documentElement.clientWidth - 30) * 118 / 343 }px;
    }
  }
`;

const Title = styled.div`
  & {
    height:36px;
      line-height:36px;
      font-size: 13px;
      color: #307DCD;
      text-align:center;
      position:relative;
      margin:0;
      span{
        position:absolute;
        right:0;
        top:1px;
        font-size:12px;
        color:#999999;
      }
      img{
        vertical-align:middle;
        position:relative;
        top:-2px;
        &.left{
          margin-right:17px;
          width:24px;
        }
        &.icon{
          margin-right:7px;
        }
        &.right{
          margin-left:17px;
          width:24px;
        }
      }
  }
`;

const SuitItem = styled.div`// styled
  & {
    margin-bottom: 25px;
    img {
      width: 100%;
      border-radius: 4px;
    }
    h5 {
      height: 28px;
      line-height: 28px;
      font-size: 12px;
      color: #333333;
      overflow: hidden;
      margin: 0;
    }
    .suit-desc {
      font-size: 10px;
      height: 28px;
      line-height: 28px;
      color: #999999;
    }
    .suit-bottom {
      height: 28px;
      line-height: 28px;
      .red {
        font-family: MicrosoftYaHei;
        font-size: 12px;
        color: #FF3030;
        margin-right: 15px;
      }
      del {
        color: #A0A0A0;
        font-size: 12px;
      }
      position: relative;
      > div {
        position: absolute;
        right: -72px;
        top: -6px;
        transform: scale(0.5);
      }
    }
  }
`;

const TodayWrapper = styled.div`// styled
  & {
    margin-top: 10px;
    background: white;
    padding: 10px 15px 40px;
    .common-bottomTotal {
      width: 100%;
      text-align: center;
    }
  }
`;
