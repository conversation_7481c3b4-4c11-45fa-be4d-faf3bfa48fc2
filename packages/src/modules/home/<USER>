import { autowired, bean } from "@classes/ioc/ioc";
import { action, computed, extendObservable, observable } from "mobx";
import { $Ad } from "../../classes/entity/$ad";
import { $Product } from "../../classes/entity/$product";
import { $ComponentService } from "../../classes/service/$component-service";
import { $ProductService } from "../../classes/service/$product-service";
import { findIndex } from "lodash";

@bean($HomeMV)
export class $HomeMV {
  @autowired($ComponentService)
  public $componentService: $ComponentService;

  @autowired($ProductService)
  public $productService: $ProductService;

  @observable public adList: $Ad[] = [];

  @observable public productList: $Product[] = [];

  @observable public suitList: any;

  @observable public suitAllList: any;

  @observable public pageIndex: number = 0;

  @observable public suitDetail: object;

  @observable public totalQuantity: number = 1;

  @observable public orderPartyName: string;

  @observable public orderSchemeName: string;

  @observable public isSpin: boolean = false;

  @observable public orderPriceViewPermission: string;

  @observable public retailPriceViewPermission: string;

  @observable public preferentialOrderPriceViewPermission: string;

  @observable public preferentialRetailPriceViewPermission: string;

  @observable public suitDetailOrderPriceViewPermission: string;

  @observable public suitDetailRetailPriceViewPermission: string;

  @action
  public showSpin() {
    this.isSpin = true;
  }

  @action
  public hideSpin() {
    this.isSpin = false;
  }

  @computed
  get autoPlay() {
    return this.adList.length > 1 ? true : false;
  }

  @action
  public fetchHome() {
    this.$componentService.queryHome().then((data) => {
      this.orderPartyName = data.orderPartyName;
      this.orderSchemeName = data.orderSchemeName;
      this.adList = data.adRotatorItemList.map((ad) => new $Ad(ad));
    });
  }

  @action
  public setTotalQuantity(val) {
    console.log(val);
    if (val === "") {
      this.totalQuantity = null;
    } else {
      this.totalQuantity = Number(val);
    }
  }

  @action
  public fetchSuitList(params) {
    return this.$componentService.querySuitList(params);
  }

  @action
  public fetchToday(params) {
    return this.$componentService.queryTodayList(params);
  }

  @action
  public setSuitList(list) {
    this.suitList = list;
    this.suitList.map((v) => {
      extendObservable(v, {
        isActive: true,
      });
    });
  }

  @action
  public setOrderPriceViewPermission(orderPriceViewPermission, retailPriceViewPermission) {
    this.orderPriceViewPermission = orderPriceViewPermission;
    this.retailPriceViewPermission = retailPriceViewPermission;
  }

  @action
  public setSuitAllList(list) {
    this.suitAllList = list;
    this.suitAllList.map((v) => {
      extendObservable(v, {
        isActive: true,
      });
    });
  }

  @action
  public changeSuitActive(suitId, type) {
    if (type === "index") {
      const index = findIndex(this.suitList, { activitySuitId: suitId });
      this.suitList[index].isActive = false;
    } else if (type === "all") {
      const index = findIndex(this.suitAllList, { activitySuitId: suitId });
      this.suitAllList[index].isActive = false;
    }
  }

  @action
  public pushProducts(list) {
    return new Promise((resolve) => {
      this.productList = this.productList.concat(list.map((product) => new $Product(product)));
      resolve(this.productList.length);
    });
  }

  @action
  public setProducts(list) {
    this.productList = list.map((product) => new $Product(product));
  }

  @action
  public setPreferentialPriceViewPermission(preferentialOrderPriceViewPermission, retailPriceViewPermission) {
    this.preferentialOrderPriceViewPermission = preferentialOrderPriceViewPermission;
    this.preferentialRetailPriceViewPermission = retailPriceViewPermission;
  }

  @action
  public addPageIndex() {
    this.pageIndex++;
  }

  @action
  public fetchSuitDetail(params) {
    this.$componentService.querySuitDetail(params).then((data) => {
      this.suitDetailOrderPriceViewPermission = data.orderPriceViewPermission;
      this.suitDetailRetailPriceViewPermission = data.retailPriceViewPermission;
      this.suitDetail = data;
      this.hideSpin();
    });
  }

  @action
  public saveSuit(params) {
    return this.$componentService.saveSuit(params);
  }
}
