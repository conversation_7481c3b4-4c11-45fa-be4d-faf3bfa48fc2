import { autowired } from "@classes/ioc/ioc";
import { Spin } from "antd";
import { Modal, Toast } from "antd-mobile";
import { observer } from "mobx-react";
import React from "react";
import { withRouter } from "react-router";
import styled from "styled-components";
import { $ActiveType } from "../../classes/const/$active-type";
import { CountDown } from "../../components/count-down/count-down";
import { Footer } from "../../components/footer/footer";
import { $CartMv } from "../shop-cart/cart-mv";
import { $HomeMV } from "./$home-mv";
import { $CartType } from "../../classes/const/$cart-type";
import { InputNumberItemComponent } from "../../components/input-number-item-component/input-number-item-component";

@withRouter
@observer
class SuitDetail extends React.Component<any, any> {
  @autowired($CartMv)
  public $CartMv: $CartMv;

  @autowired($HomeMV)
  public $mv: $HomeMV;

  constructor(props) {
    super(props);
    this.state = {
      showModal: false,
    };
  }

  public componentDidMount() {
    document.title = "套装详情";
    const params = {
      activitySuitId: this.props.match.params.activitySuitId,
    };
    this.$mv.showSpin();
    this.$mv.fetchSuitDetail(params);
    this.$CartMv.fetchShopcartproductnum();
  }

  public purchase = () => {
    this.setState({
      showModal: true,
    });
  }

  public setInputValue = (val) => {
    this.$mv.setTotalQuantity(val);
  }

  public closeModal = () => {
    this.setState({
      showModal: false,
    });
  }

  public onConfirm = () => {
    const params = {
      activitySuitId: Number(this.props.match.params.activitySuitId),
      quantity: this.$mv.totalQuantity && Number(this.$mv.totalQuantity),
      orderId: sessionStorage.getItem("editOrderId") ? sessionStorage.getItem("editOrderId") : null,
      operation: "add",
    };
    this.setState({
      showModal: false,
    });
    this.$mv.saveSuit(params).then((data) => {
      if (data.result) {
        Toast.info("加入购物车成功", 1);
        this.$CartMv.fetchShopcartproductnum();
      }
    });
  }

  public render() {
    const { suitDetail, totalQuantity, isSpin, suitDetailOrderPriceViewPermission, suitDetailRetailPriceViewPermission } = this.$mv;
    const { productList } = suitDetail || {};
    const { showModal } = this.state;

    return (
      <Wrapper>
        <Spin spinning={isSpin}>
          {
            suitDetail ? <SuitWrapper>
              <SuitItem>
                <img
                  src={suitDetail.imageUrl ? suitDetail.imageUrl : "https://order.fwh1988.cn:14501/static-img/scm/ico-banner-nopic.png"}
                  alt=""/>
                <h5>{suitDetail.title ? suitDetail.title.length > 18 ? suitDetail.title.slice(0, 18) + "..." : suitDetail.title : null}</h5>
                <div
                  className="suit-desc">{suitDetail.subTitle ? suitDetail.subTitle.length > 18 ? suitDetail.subTitle.slice(0, 18) + "..." : suitDetail.subTitle : null}</div>
                <div className="suit-bottom">
                  {
                    (suitDetail.suitOrderPrice) && (suitDetailOrderPriceViewPermission === $CartType.ORDERPRICEVIEWPERMISSION) ?
                      <span
                        className="red">￥{suitDetail.suitOrderPrice}</span> : <span/>
                  }
                  <CountDown timeDiff={suitDetail.countDown} suitId={suitDetail.activitySuitId}
                             type="detail"></CountDown>
                </div>
              </SuitItem>
              <Title>
                <img className="left" src="https://order.fwh1988.cn:14501/static-img/scm/icon-title-line-left.png"
                     alt=""/>
                <i className="scmIconfont scm-icon-title-content"></i>
                套装详情
                <img className="right" src="https://order.fwh1988.cn:14501/static-img/scm/icon-title-line-right.png"
                     alt=""/>
                {/*{
                productList ? productList.map((v, index) => {
                  return <SuitItem key={v.productSkuId}>
                    <img src={v.imageUrl} alt=""/>
                    <h5>{v.name}</h5>
                    <div className="suit-bottom">
                      <span className="red">￥{v.orderPrice}</span>
                      <del>￥{v.retailPrice}</del>
                    </div>
                  </SuitItem>;
                }) : null
              }*/}
              </Title>
              <SuitDetailList>
                {
                  productList ? productList.length > 0 ? productList.map((product, index) => {
                    return (
                      <SuitDetailItem key={index}>
                        <div>
                          <img
                            src={product.imageUrl ? product.imageUrl : "https://order.fwh1988.cn:14501/static-img/scm/ico-nopic.png"}
                            alt=""/>
                        </div>
                        <div>
                          <p>
                            <span>{product.name ? product.name.length > 12 ? product.name.slice(0, 12) + "..." : product.name : null}</span>
                            <span>x{product.quantity}</span>
                          </p>
                          <p>
                            {
                              (suitDetailOrderPriceViewPermission === $CartType.ORDERPRICEVIEWPERMISSION) && (product.orderPrice !== null) ?
                                <span className="redprice">
                                  <span style={{fontSize: "10px"}}>￥</span>
                                  {product.orderPrice}
                                  <span className="blackprice">{product.unitOfMeasure && `/${product.unitOfMeasure}`}</span>
                                  </span> : null
                            }
                            {
                              (suitDetailRetailPriceViewPermission === $CartType.RETAILPRICEVIEWPERMISSION) && (product.retailPrice !== null) ?
                                <span className="blackprice">
                  {(((suitDetailOrderPriceViewPermission === $CartType.ORDERPRICEVIEWPERMISSION) && (product.orderPrice !== null))) && (product.retailPrice !== null) ? " (" : null}零售 ￥{product.retailPrice}{(((suitDetailOrderPriceViewPermission === $CartType.ORDERPRICEVIEWPERMISSION) && (product.orderPrice !== null))) && (product.retailPrice !== null) ? ")" : null}{(((suitDetailOrderPriceViewPermission !== $CartType.ORDERPRICEVIEWPERMISSION) || (product.orderPrice === null))) && (product.retailPrice !== null) ? <span className="blackprice">{product.unitOfMeasure && `/${product.unitOfMeasure}`}</span> : null}
                  </span> : <span/>
                            }
                          </p>
                        </div>
                      </SuitDetailItem>
                    );
                  }) : null : null
                }
              </SuitDetailList>
            </SuitWrapper> : null
          }
          <div style={{ position: "fixed", width: "100%", height: 50, background: "#fff", bottom: 48 }}>
            <PurchaseBtn onClick={this.purchase}>
              一键下单
            </PurchaseBtn>
          </div>
          <Modal
            title={<div>请输入数量</div>}
            visible={showModal}
            transparent={true}
            maskClosable={false}
            className={"scm-input-modal"}
            footer={[
              { text: "取消", onPress: () => this.closeModal() },
              { text: "确定", onPress: this.onConfirm },
            ]}
          >
            {/*<InputItem*/}
            {/*style={{ textAlign: "center" }}*/}
            {/*type="number"*/}
            {/*defaultValue={totalQuantity ? String(totalQuantity) : "0"}*/}
            {/*onChange={(value) => this.setInputValue(Number(value))}*/}
            {/*/>*/}
            <InputNumberItemComponent
              style={{ textAlign: "center" }}
              type="money"
              defaultValue={totalQuantity ? String(totalQuantity) : "0"}
              value={totalQuantity && String(totalQuantity)}
              onChange={(value) => this.setInputValue(value)}
              moneyKeyboardAlign={"left"}
            />
          </Modal>
        </Spin>
        <Footer activeKey={$ActiveType.MAIN_KEY} count={this.$CartMv.totalCount}/>
      </Wrapper>
    );
  }
}

export default SuitDetail;

const Wrapper = styled.div`// styled
  & {
    background:#f2f2f2;
    min-height:${document.documentElement.clientHeight};
    .slick-slider.slick-initialized{
      overflow:hidden;
    }
  }
`;

const SuitWrapper = styled.div`// styled
  & {
    background: white;
    padding: 15px;
    margin-bottom: 100px;
  }
`;

const SuitItem = styled.div`// styled
  & {
    margin-bottom: 28px;
    img {
      width: 100%;
      height: calc(${document.documentElement.clientWidth * (118 / 343)}px);
      border-radius: 5px;
    }
    h5 {
      height: 28px;
      line-height: 28px;
      font-size: 12px;
      color: #333333;
      overflow: hidden;
      margin: 0;
    }
    .suit-desc {
      font-size: 10px;
      height: 28px;
      line-height: 28px;
      color: #999999;
    }
    .suit-bottom {
      height: 28px;
      line-height: 28px;
      .red {
        font-family: MicrosoftYaHei;
        font-size: 12px;
        color: #FF3030;
        margin-right: 15px;
      }
      del {
        color: #A0A0A0;
        font-size: 12px;
      }
      position: relative;
      > div {
        position: absolute;
        right: -55px;
        top: 0px;
        transform: scale(0.5);
      }
    }
  }
`;

const SuitDetailList = styled.div`// styled
  & {
    width: 100%;
    padding: 15px 15px 20px 15px;
    overflow: auto;
  }
`;

const SuitDetailItem = styled.div`// styled
  & {
    width: 100%;
    height: 80px;
    position: relative;
    margin-bottom: 10px;
    > div {
      display: inline-block;
    }
    > div:nth-of-type(1) {
      position: absolute;
      top: 0;
      left: 0;
      width: 80px;
      height: 78px;
      > img {
        width: 80px;
        height: 80px;
        border: 0;
        border-radius: 3px;
      }
    }
    > div:nth-of-type(2) {
      margin-left: 90px;
      > p:nth-of-type(1) {
        > span:nth-of-type(1) {
          font-size: 12px;
          color: #333;
        }
        > span:nth-of-type(2) {
          font-size: 12px;
          color: #999;
          position: absolute;
          top: 0;
          right: 0;
        }
      }
      > p:nth-of-type(2) {
        .redprice {
          font-size: 14px;
          color: #ff3030;
          font-family: SourceHanSansCN-Normal, SourceHanSansCN;
        }
        .blackprice {
          font-size: 10px;
          color: #999999;
          font-family: SourceHanSansCN-Normal, SourceHanSansCN;
        }
      }
    }
  }
`;

const PurchaseBtn = styled.div`// styled
  & {
    position: fixed;
    bottom: 50px;
    width: 92%;
    height: 42px;
    background: #307DCD;
    color: #fff;
    padding: 10px;
    border-radius: 3px;
    text-align: center;
    margin: 0 15px 12px 15px;
    font-size: 16px;
    box-sizing: border-box;
  }
`;

const Title = styled.div`
  & {
    height:36px;
      line-height:36px;
      font-size: 13px;
      color: #307DCD;
      text-align:center;
      position:relative;
      margin:0;
      span{
        position:absolute;
        right:0;
        top:1px;
        font-size:12px;
        color:#999999;
      }
      .scmIconfont{
        position:relative;
        margin-right:3px;
        top:2px;
      }
      img{
        vertical-align:middle;
        position:relative;
        top:-2px;
        &.left{
          margin-right:10px;
          width:24px;
        }
        &.icon{
          margin-right:3px;
        }
        &.right{
          margin-left:10px;
          width:24px;
        }
      }
  }
`;
