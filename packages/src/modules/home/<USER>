import { autowired } from "@classes/ioc/ioc";
import { Spin } from "antd";
import { observer } from "mobx-react";
import React from "react";
import { withRouter } from "react-router";
import styled from "styled-components";
import { CountDown } from "../../components/count-down/count-down";
import { SITE_PATH } from "../app";
import { $CartMv } from "../shop-cart/cart-mv";
import { $HomeMV } from "./$home-mv";
import { $CartType } from "../../classes/const/$cart-type";

@withRouter
@observer
class SuitList extends React.Component<any, any> {
  @autowired($CartMv)
  public $CartMv: $CartMv;

  @autowired($HomeMV)
  public $mv: $HomeMV;

  constructor(props) {
    super(props);
  }

  public componentDidMount() {
    document.title = "套装列表";
    const params = {
      queryType: "ALL",
    };
    this.$mv.showSpin();
    this.$mv.fetchSuitList(params).then((data) => {
      this.$mv.hideSpin();
      this.$mv.setSuitAllList(data.activitySuitList);
      this.$mv.setOrderPriceViewPermission(data.orderPriceViewPermission, data.retailPriceViewPermission);
    });
  }

  public toSuitDetail = (id) => {
    this.props.history.push({ pathname: `/${SITE_PATH}/suit-detail/${id}` });
  }

  public render() {
    const { suitAllList, isSpin, orderPriceViewPermission, retailPriceViewPermission } = this.$mv;

    return (
      <Wrapper>
        <Spin spinning={isSpin}>
          <SuitWrapper>
            {
              suitAllList ? suitAllList.map((v) => {
                return <SuitItem key={v.activitySuitId}
                                 onClick={() => this.toSuitDetail(v.activitySuitId)}
                                 className={v.isActive ? "" : "hide"}
                >
                  <img
                    src={v.imageUrl ? v.imageUrl : "https://order.fwh1988.cn:14501/static-img/scm/ico-banner-nopic.png"}
                    alt="" className="big-img"/>
                  <h5>{v.title && v.title.length > 16 ? v.title.slice(0, 16) + "..." : v.title}</h5>
                  <div className="suit-desc">
                    {v.subTitle && v.subTitle.length > 30 ? v.subTitle.slice(0, 30) + "..." : v.subTitle}
                  </div>
                  <div className="suit-bottom">
                    {
                      (orderPriceViewPermission === $CartType.ORDERPRICEVIEWPERMISSION) && (v.orderPrice !== null) ?
                        <span className="red">￥{v.orderPrice}</span> : null
                    }
                    {
                      ((retailPriceViewPermission === $CartType.RETAILPRICEVIEWPERMISSION) && (v.originPrice !== null)) && ((orderPriceViewPermission === $CartType.ORDERPRICEVIEWPERMISSION) && (v.orderPrice !== null)) ?
                        <del>￥{v.originPrice}</del> :
                        (retailPriceViewPermission === $CartType.RETAILPRICEVIEWPERMISSION) && (v.originPrice !== null) ?
                          <span>￥{v.originPrice}</span> : null
                    }
                    {/*<span className="red">￥{v.orderPrice}</span>*/}
                    {/*<del>￥{v.originPrice}</del>*/}
                    <CountDown timeDiff={v.countDown} suitId={v.activitySuitId} type="all"></CountDown>
                  </div>
                </SuitItem>;
              }) : null
            }
          </SuitWrapper>
        </Spin>
      </Wrapper>
    );
  }
}

export default SuitList;

const Wrapper = styled.div`// styled
  & {
    background:#f2f2f2;
    min-height:${document.documentElement.clientHeight}
  }
`;

const SuitWrapper = styled.div`// styled
    & {
    margin-top:5px;
    background:white;
    padding:10px 15px 0px;
    overflow:hidden;
    .hide{
      display:none;
    }
    img.big-img{
      height:${(document.documentElement.clientWidth - 30) * 118 / 343 }px;
      border-radius:4px;
    }
  }
`;

const SuitItem = styled.div`// styled
  & {
    margin-bottom:25px;
    img{
      width:100%;
    }
    img.big-img{
      height:${(document.documentElement.clientWidth - 30) * 118 / 343 }px;
    }
    h5{
      height:28px;
      line-height:28px;
      font-size:12px;
      color:#333333;
      overflow:hidden;
      margin:0;
    }
    .suit-desc{
      font-size:12px;
      height:28px;
      line-height:28px;
      color:#999999;
    }
    .suit-bottom{
      height:28px;
      line-height:28px;
      .red{
        font-family: MicrosoftYaHei;
        font-size: 12px;
        color: #FF3030;
        margin-right:15px;
      }
      del{
        color:#A0A0A0;
        font-size: 12px;
      }
      position:relative;
      >div{
          position: absolute;
          right: -72px;
          top: -6px;
          transform: scale(0.5);
      }
    }
  }
`;
