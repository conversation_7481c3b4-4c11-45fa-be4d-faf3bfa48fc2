import { autowired, bean } from "@classes/ioc/ioc";
import { action, observable } from "mobx";
import { $ReceiveService } from "../../classes/service/$receive-order-service";
import { beanMapper } from "../../helpers/bean-helpers";

@bean(ReceiveOrderListMv)
export class ReceiveOrderListMv {
  @autowired($ReceiveService)
  public $myService: $ReceiveService;
  @observable public pageIndex: number = 0;
  @observable public pageSize: number = 20;
  @observable public scrollHeight: number = 0;
  @observable public isSpin: boolean = false;
  @observable public finished: boolean = false;
  @observable public isLoad: boolean = false;
  @observable public key: string = "ALL";
  @observable public orderListInfo: any[] = [];
  @observable public orderStatus: any[] = [
    {
      activeKey: "ALL",
      name: "全部",
      value: "ALL",
    },
    {
      activeKey: "SEND_GOODS",
      name: "已发货",
      value: "SEND_GOODS",
    },
    {
      activeKey: "CONFIRM_GOODS",
      name: "已收货",
      value: "CONFIRM_GOODS",
    },
  ];
  @action
  public queryReceiveOrderList(params) {
    return this.$myService.queryReceiveOrderList(params);
  }
  @action
  public queryOldData(obj) {
    beanMapper(obj, this);
    console.log(this);
  }
  @action
  public clearMVData() {
    this.pageIndex = 0;
    this.pageSize = 20;
    this.scrollHeight = 0;
    this.isSpin = false;
    this.finished = false;
    this.isLoad = false;
    this.key = "ALL";
    this.orderListInfo = [];
    this.orderStatus = [
      {
        activeKey: "ALL",
        name: "全部",
        value: "ALL",
      },
      {
        activeKey: "SEND_GOODS",
        name: "已发货",
        value: "SEND_GOODS",
      },
      {
        activeKey: "CONFIRM_GOODS",
        name: "已收货",
        value: "CONFIRM_GOODS",
      },
    ];
  }
}
