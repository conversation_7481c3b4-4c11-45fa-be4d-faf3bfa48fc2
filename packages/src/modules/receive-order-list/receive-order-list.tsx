import { autowired, bean } from "@classes/ioc/ioc";
import { Spin } from "antd";
import { Modal } from "antd-mobile";
import { observer } from "mobx-react";
import React from "react";
import { withRouter } from "react-router";
import styled from "styled-components";
import { ReceiveOrderListMv } from "./receive-order-list-mv";
import NoAuthority from "../no-authority/no-authority";
import { ScrollAbilityWrapComponent } from "../../components/scroll-ability/wrap";
import { LoadingTip } from "../../components/loading-marked-words";
import { SITE_PATH } from "../app";
import { NoGoods } from "../../components/no-goods/no-goods";
import { gaEvent } from "../../classes/website-statistics/website-statistics";
import { $AppStore, AppStoreKey } from "../../classes/stores/app-store-mv";
const alert = Modal.alert;

@withRouter
@observer
class ReceiveOrderListWrap extends React.Component<any, any> {
  @autowired($AppStore)
  public $AppStore: $AppStore;
  @autowired(ReceiveOrderListMv)
  public $myMv: ReceiveOrderListMv;

  constructor(props) {
    super(props);
    this.state = {
    };
  }

  public componentWillReceiveProps(nextProps: Readonly<any>, nextContext: any): void {
    const { isScrollEnd } = nextProps;
    console.log(isScrollEnd);
    if (isScrollEnd && isScrollEnd !== this.props.isScrollEnd) {
      this.loadData();
    }
  }
  // 离开记录滚动高度
  public componentWillUnmount(): void {
    this.$myMv.scrollHeight = $(".scroll-ability-wrap").scrollTop();
    this.$AppStore.savePageMv(AppStoreKey.RECEIVEORDERLISTSTORE, this.$myMv);
  }
  public componentDidMount(): void {
    document.title = "收货单列表";
    gaEvent("收货单列表");
    setTimeout(() => {
      const oldData = this.$AppStore.getPageMv(AppStoreKey.RECEIVEORDERLISTSTORE)
      if (oldData) {
        this.$myMv.queryOldData(JSON.parse(oldData));
        this.$AppStore.clearPageMv(AppStoreKey.RECEIVEORDERLISTSTORE);
        $(".scroll-ability-wrap").scrollTop(this.$myMv.scrollHeight);
      } else {
        this.$myMv.clearMVData();
        this.searchOrder();
      }
    }, 50);
  }

  public loadData = () => {
    const { finished } = this.$myMv;
    if (!finished) {
      this.searchOrder();
    }
  }
  public searchOrder = () => {
    const { pageIndex, pageSize, key, orderListInfo } = this.$myMv;
    const { loadingEnd } = this.props;
    const params = {
      pageIndex,
      pageSize,
      status: key,
    };
    this.$myMv.isLoad = true;
    this.$myMv.isSpin = true;
    this.$myMv.queryReceiveOrderList(params).then((res) => {
      console.log("res", res);
      const { itemCount, deliveryOrderList} = res;
      const newOrderListInfo = pageIndex ? orderListInfo.concat(deliveryOrderList) : deliveryOrderList;
      this.$myMv.isLoad = false
      this.$myMv.isSpin = false
      this.$myMv.finished = newOrderListInfo.length >= itemCount
      this.$myMv.pageIndex = pageIndex + 1
      this.$myMv.orderListInfo = newOrderListInfo;
      loadingEnd && loadingEnd();
    }).catch(() => {
      this.$myMv.isSpin = false;
      loadingEnd && loadingEnd();
    });
  }
  public goToOrderDetail = (deliveryId) => {
    this.props.history.push({
      pathname: `/${SITE_PATH}/receive-order-detail/${deliveryId}`,
    });
  }
  public changeOrderStatus = (value) => {
    document.getElementsByClassName("wrapper")[0].style.position = "fixed";
    setTimeout(() => {
      document.getElementsByClassName("wrapper")[0].style.position = "relative";
    }, 10)
    this.$myMv.key = value;
    this.$myMv.pageIndex = 0;
    this.searchOrder();
  }
  public getStyleBgc = (code) => {
    let styleBgc = {};
    switch (code) {
      case "SEND_GOODS":
        styleBgc = { color: "#52C41A" };
        break;
      case "CONFIRM_GOODS":
        styleBgc = { color: "#FF3030" };
        break;
      default:
        styleBgc = { color: "#999999" };
        break;
    }
    return styleBgc;
  }
  public render() {
    const { isSpin, key, finished, isLoad, orderListInfo, orderStatus } = this.$myMv;
    console.log("this.$myMv", this.$myMv);
    return (
      <Wrapper className="wrapper">
        <SearchTypeBar>
          {
            orderStatus.map((status, index) => {
              return (
                <div
                  key={index}
                  onClick={() => { this.changeOrderStatus(status.value); }}
                >
                  <span className={status.value === key ? "active" : null}>
                    {status.name}
                  </span>
                </div>
              );
            })
          }
        </SearchTypeBar>
        <Spin spinning={isSpin} className="spining">
          <OrderList>
            {
              orderListInfo && orderListInfo.length > 0 && orderListInfo.map((item, index) => {
                const { oid, docNo, statusText, statusValue, orderOrg, warehouseOrg, totalQuantity, deliveryDate, boxingQuantity} = item;
                const styleBgc = this.getStyleBgc(statusValue);
                return (
                  <OrderInfo key={oid} onClick={() => this.goToOrderDetail(oid)}>
                    <div className="header">
                      <div className="text">发货单号：</div>
                      <div className="value">{docNo}</div>
                      <div className="status" style={styleBgc}>{statusText}</div>
                    </div>
                    <div className="content-list">
                      <div className="logistics-information">
                        <div className="text">发至</div>
                        <div className="value">{orderOrg}</div>
                      </div>
                      <div className="content-item">
                        <div className="text">发货仓库：</div>
                        <div className="value">{warehouseOrg}</div>
                      </div>
                      <div className="content-item">
                        <div className="text">发货总数量：</div>
                        <div className="value">{totalQuantity}</div>
                      </div>
                      {
                        typeof (boxingQuantity) === "number" &&
                          <div className="content-item">
                            <div className="text">装货总件数：</div>
                            <div className="value">{boxingQuantity}</div>
                          </div>
                      }
                      <div className="content-item">
                        <div className="text">发货时间：</div>
                        <div className="value">{deliveryDate}</div>
                      </div>
                    </div>
                  </OrderInfo>
                );
              })
            }

            {
              orderListInfo && orderListInfo.length === 0 ?
                <NoGoods
                  title="暂无收货单"
                  height={document.documentElement.clientHeight - 40}
                /> : null
            }
            {
              orderListInfo && orderListInfo.length > 0 ?
                <LoadingTip
                  isFinished={finished}
                  isLoad={isLoad}
                /> : null
            }
          </OrderList>
        </Spin>
      </Wrapper>
    );
  }
}

const ReceiveOrderList = ScrollAbilityWrapComponent(ReceiveOrderListWrap)
export default ReceiveOrderList;

const Wrapper = styled.div`// styled
  & {
    width: 100%;
    min-height: calc(${document.documentElement.clientHeight}px);
    height: auto;
    background-color: #F2F2F2;
    .ant-spin-nested-loading{
      width: 100%;
      height: 100%;
      padding-top: 40px;
    }
  }
`;
const OrderList = styled.div`// styled
  & {
    width: 100%;
    height: auto;
    background-color: #F2F2F2;
  }
`;
const SearchTypeBar = styled.div`// styled
  & {
    width: 100%;
    height: 40px;
    line-height: 20px;
    background-color: #fff;
    display: flex;
    flex-direction: row;
    text-align: center;
    position: fixed;
    color: #8a8a8a;
    z-index: 99;
    padding: 10px 16px;
    box-sizing: border-box;
    justify-content: space-around;
    > div {
      height: 30px;
      padding: 0 20px;
      //flex: 1;
      > span {
        display: inline-block;
        height: 30px;
        font-size: 14px;
        color: #666;
      }
      > .active {
        color: #307DCD;
        font-size: 14px;
        border-bottom: 2px solid #307DCD;
      }
    }
  }
`;
const OrderInfo = styled.div`// styled
  & {
    width: 100%;
    height: auto;
    background-color: #fff;
    margin-top: 10px;
    .header{
      color: #333333;
      font-size: 14px;
      height: 40px;
      line-height: 40px;
      padding: 0 12px;
      .text{
        width: 72px;
        float: left;
      }
      .value{
        float: left;
      }
      .status{
        float: right;
        font-size: 13px;
      }
    }
    .content-list{
      border-top: 1px solid #D8D8D8;
      padding: 12px;
      font-size: 13px;
      .logistics-information{
        color: #2F2F2F;
        font-size: 13px;
        font-weight: 500;
        overflow: hidden;
        line-height: 13px;
        .text,.value{
          float: left;
        }
      }
      .content-item{
        position: relative;
        margin-top: 14px;
        font-size: 12px;
        line-height: 12px;
        .text{
          position: absolute;
          width: 75px;
          top: 0;
          left: 0;
          color: #666666;
        }
        .value{
          padding-left: 75px;
          color: #333333;
        }
      }
    }
  }
`;
