import { autowired } from "@classes/ioc/ioc";
import { Spin } from "antd";
import { Modal, SearchBar, Toast } from "antd-mobile";
import { observer } from "mobx-react";
import * as React from "react";
import { withRouter } from "react-router";
import styled from "styled-components";
import { $OrderType } from "../../classes/const/$order-type";
import { NoGoods } from "../../components/no-goods/no-goods";
import { SITE_PATH } from "../app";
import { $CartMv } from "../shop-cart/cart-mv";
import { $AllShopOrderListMv } from "./all-shop-order-list-mv";
import { GoHome } from "../../components/go-home/go-home";
import { ScreeningStores } from "../../components/screening-stores/screening-stores";
import { map } from "lodash";
import { SingleOrderInfo } from "../../components/single-order-info/single-order-info";
import { ScrollAbilityWrapComponent } from "../../components/scroll-ability/wrap";
import { LoadingTip } from "../../components/loading-marked-words";
import disabledOprate from "../../components/noWX-disabled-operate";
import { gaEvent } from "../../classes/website-statistics/website-statistics";
import { $AppStore, AppStoreKey } from "../../classes/stores/app-store-mv";
import { toJS, transaction } from "mobx";

const alert = Modal.alert;
declare let window: any;

@withRouter
@observer
class AllShopOrderListWrap extends React.Component<any, any> {
  @autowired($CartMv)
  public $CartMv: $CartMv;
  @autowired($AppStore)
  public $AppStore: $AppStore;
  @autowired($AllShopOrderListMv)
  public $myMv: $AllShopOrderListMv;

  constructor(props) {
    super(props);
    this.state = ({
    });
  }
  // 离开记录滚动高度
  public saveMV = () => {
    this.$myMv.scrollHeight = $(".scroll-ability-wrap").scrollTop();
    this.$AppStore.savePageMv(AppStoreKey.ALLSHOPORDERLISTSTORE, this.$myMv);
  }
  public componentWillUnmount(): void {
    this.saveMV();
  }
  public componentDidMount() {
    document.title = "订单列表";
    gaEvent("订单列表");
    setTimeout(() => {
      const oldData = this.$AppStore.getPageMv(AppStoreKey.ALLSHOPORDERLISTSTORE);
      if (oldData) {
        this.$myMv.queryOldData(JSON.parse(oldData));
        this.$AppStore.clearPageMv(AppStoreKey.ALLSHOPORDERLISTSTORE);
        $(".scroll-ability-wrap").scrollTop(this.$myMv.scrollHeight);
      } else {
        this.$myMv.clearMVData();
        this.initPage();
      }
    }, 50);
  }

  public initPage = () => {
    transaction(() => {
      this.$myMv.orderpartyList({ pageIndex: 0, pageSize: 99999999 }).then(() => {
        this.$myMv.salesOrderStatus({isContainsN: false}).then(() => {
          const { searchContent } = this.$myMv;
          console.log(this.props.location);
          if (this.props.location.state) {
            const { status } = this.props.location.state;
            this.searchOrder(status, searchContent);
            this.$CartMv.setIsAgency();
          } else {
            this.searchOrder("ALL", searchContent);
          }
        });
      });
    });
  }
  public componentWillReceiveProps(nextProps: Readonly<any>, nextContext: any): void {
    const { isScrollEnd } = nextProps;
    if (isScrollEnd && isScrollEnd !== this.props.isScrollEnd) {
      this.loadData();
    }
  }

  public loadData = () => {
    const { key, searchContent, finished, showOrderPop } = this.$myMv;
    if (finished || showOrderPop) {
      return;
    }
    this.searchOrder(key, searchContent);
  }

  public searchOrder = (val, searchContent) => {
    const { pageIndex, pageSize } = this.$myMv;
    if ((val === $OrderType.AUDIT || val === $OrderType.DELIVER || val === $OrderType.DONE || val === $OrderType.PAYMENT || val === $OrderType.ALL || val === $OrderType.WAITDELIVER) && pageIndex === 0) {
      this.$myMv.orderListInfo = [];
    }
    console.log("orderPartyList", this.$myMv.orderPartyList);
    console.log("salesOrderStatusList", this.$myMv.salesOrderStatusList);
    const params = {
      orderPartyIdList: map(this.$myMv.orderPartyList.filter((party) => party.checked), "oid"),
      orderPartyRange: this.props.location.state ? this.props.location.state.orderPartyRange : "All",
      pageIndex,
      pageSize,
      searchText: searchContent,
      type: val,
      statusList: map(this.$myMv.salesOrderStatusList.filter((party) => party.checked), "value"),
    };
    this.$myMv.key = val;
    this.$myMv.showOrderPop = false;
    this.$myMv.showSpin();
    this.$myMv.getList(params).then((res) => {
      this.$myMv.hideSpin();
      const { itemCount, isBySkuType, salesOrderList, isShowMergeButton } = res;
      this.$myMv.setIsShowMergeButton(isShowMergeButton);
      // this.$myMv.setPriceViewPermission(freightViewPermission);
      this.$myMv.changeIsBySkuType(isBySkuType);
      if (isShowMergeButton) {
        document.getElementsByClassName("am-search")[0].style.width = "60%";
      } else {
        document.getElementsByClassName("am-search")[0].style.width = "79%";
      }
      const { loadingEnd } = this.props;
      if (salesOrderList) {
        this.$myMv.orderListInfo = pageIndex ? this.$myMv.orderListInfo.concat(salesOrderList) : salesOrderList;
        this.$myMv.changePage();
        this.$myMv.finished = this.$myMv.orderListInfo.length >= itemCount;
        this.$myMv.isLoad = false;
        loadingEnd && loadingEnd();
      } else {
        this.$myMv.finished = true;
        this.$myMv.isLoad = false;
        loadingEnd && loadingEnd();
      }
    }).catch(() => {
      this.$myMv.hideSpin();
    });
  }
  public pageWindowSkip = (url) => {
    this.saveMV();
    setTimeout(() => {
      window.location.href = url;
    }, 50);
  }
  public orderButton = (btnType, orderId, orderOrgId, orderSchemeId, payRange) => {
    switch (btnType) {
      case "cancel":
        this.$myMv.cancleOrder(orderId).then((data) => {
          if (data.result) {
            Toast.info("订单已取消", 3);
            setTimeout(() => {
              window.location.reload();
            }, 2500);
            /*const { key } = this.$myMv;
            this.searchOrder(key);*/
          }
        });
        break;
      case "reject":
        this.$myMv.cancleOrder(orderId).then((data) => {
          if (data.result) {
            window.location.reload();
            /*const { key } = this.$myMv;
            this.searchOrder(key);*/
          }
        });
        break;
      case "edit":
        this.$myMv.editOrder(orderId).then((data) => {
          if (data.result) {
            // this.$OrderMv.setEditOrderId(orderId);
            sessionStorage.setItem("isSetShopCartData", "true");
            sessionStorage.setItem("editOrderId", orderId);
            this.$AppStore.clearPageMv(AppStoreKey.SHOPCART);
            this.props.history.push({
              pathname: `/${SITE_PATH}/shop/cart`, state: {
                orderId,
                backSource: "all",
              },
            });
          }
        });
        break;
      case "auditPass":
        this.$myMv.auditOrder(orderId).then((data) => {
          if (data.result) {
            Toast.info("审核通过，请等待客服人员审核", 3);
            setTimeout(() => {
              window.location.reload();
            }, 2500);
          }
        });
        break;
      case "submitFinance":
        this.$AppStore.clearPageMv(AppStoreKey.SINGLEPAYMENTSTORAGE);
        this.pageWindowSkip(`/${SITE_PATH}/submit/order-payment/34657/${orderId}/notPay?backSource=all&payRange=${payRange}`);
        break;
      case "nowPay":
      case "goOnPay":
        this.$AppStore.clearPageMv(AppStoreKey.SINGLEPAYMENTSTORAGE);
        this.pageWindowSkip(`/${SITE_PATH}/submit/order-payment/34657/${orderId}/notPay?payRange=${payRange}`);
        break;
      case "deliveryOrder":
        this.pageWindowSkip(`/${SITE_PATH}/delivery-order/${orderId}`);
        break;
      case "capyOrder":
        console.log("capyOrder");
        sessionStorage.setItem("editOrderId", null);
        this.$myMv.copyOrder({ slaesOrderId: orderId }).then((res) => {
          const { errorMessage, isSkip } = res;
          if (errorMessage) {
            Toast.info(errorMessage);
          }
          if (isSkip) {
            const saveParams = { orderPartyId: orderOrgId, orderSchemeId };
            this.$myMv.saveShopAndScheme(saveParams).then((data) => {
              const message = data.errorMessage;
              if (message) {
                Toast.info(message);
              } else {
                sessionStorage.setItem("isSetShopCartData", "true");
                this.$AppStore.clearPageMv(AppStoreKey.SHOPCART);
                this.$CartMv.openDiscount();
                this.props.history.push({
                  pathname: `/${SITE_PATH}/shop/cart`,
                });
              }
            });
          }
        });
        break;
      default:
        break;
    }
  }

  public capyOrder = () => {
    const { goToShopCar } = this.props;
  }

  public goToOrderDetail = (orderId, orderSchemeName, paymentModeListLength) => {
    this.props.history.push({
      pathname: `/${SITE_PATH}/shop/order-detail/${orderId}/${orderSchemeName}`,
      state: {
        backSource: "all",
        paymentModeListLength,
      },
    });
  }

  public goToShopCar = (orderId, orderSchemeName) => {
    this.props.history.push({
      pathname: `/${SITE_PATH}/shop/order-detail/${orderId}/${orderSchemeName}`,
      state: {
        backSource: "all",
      },
    });
  }

  public onChangeSearch = (val) => {
    console.log(val);
    this.$myMv.searchContent = val;
  }

  public onSearch = () => {
    const { key, searchContent } = this.$myMv;
    this.$myMv.setPage();
    this.searchOrder(key, searchContent);
    // document.getElementsByClassName("orderPage")[0].style.position = "relative";
    $("html").css("overflow", "scroll");
    $("body").css("overflow", "scroll");
    $(".scroll-ability-wrap").css("overflow", "scroll");
  }

  public goToCombinedpayment = () => {
    if (localStorage.getItem("scanningLogin") === "Y") {
      disabledOprate();
    } else {
      const { searchContent } = this.$myMv;
      this.props.history.push({
        pathname: `/${SITE_PATH}/shop/combined-payment`,
        state: {
          searchText: searchContent,
        },
      });
    }
  }

  public showChooseShop = () => {
    this.$myMv.showOrderPop = true;
    $("html").css("overflow", "hidden");
    $("body").css("overflow", "hidden");
    $(".scroll-ability-wrap").css("overflow", "hidden");
    $(".scroll-ability-wrap").scrollTop(0);
    // document.getElementsByClassName("orderPage")[0].style.position = "fixed";
    // document.getElementsByClassName("orderList")[0].addEventListener("touchmove", function(e) {
    //   e.preventDefault();
    // }, {passive: false}); // passive防止阻止默认事件不生效
  }

  public canclePop = () => {
    this.$myMv.showOrderPop = false;
    $("html").css("overflow", "scroll");
    $("body").css("overflow", "scroll");
    $(".scroll-ability-wrap").css("overflow", "scroll");
  }

  public changeOrderStatus = (value, searchContent) => {
    this.$myMv.pageIndex = 0;
    // document.getElementsByClassName("orderPage")[0].style.position = "fixed";
    setTimeout(() => {
      // document.getElementsByClassName("orderPage")[0].style.position = "relative";
      $("html").css("overflow", "scroll");
      $("body").css("overflow", "scroll");
      $(".scroll-ability-wrap").css("overflow", "scroll");
    }, 10);
    this.searchOrder(value, searchContent);
  }
  public goHome = () => {
    this.saveMV();
  }
  public confirmSearch = (selectObj) => {
    const { orderPartyList, salesOrderStatusList } = selectObj;
    this.$myMv.orderPartyList = orderPartyList || [];
    this.$myMv.salesOrderStatusList = salesOrderStatusList || [];
    this.$myMv.showOrderPop = false;
    this.onSearch();
  }
  public render() {
    const { isBySkuType, orderStatus, orderListInfo, isSpin, isShowMergeButton, orderPartyList, isLoad, key, searchContent, showOrderPop, finished, salesOrderStatusList } = this.$myMv;
    const { isAgency } = this.$CartMv;
    return (
      <OrderPage className="orderPage">
        <SelectBar>
          <SearchBar
            value={searchContent}
            placeholder="订单号"
            onSubmit={this.onSearch}
            onChange={this.onChangeSearch}
          />
          <div className="right"
               style={{
                 borderLeft: isShowMergeButton ? "1px solid #D8D8D8" : "none",
                 width: isShowMergeButton ? "38%" : "21%",
               }}
          >
            {
              isShowMergeButton ? <span onClick={this.goToCombinedpayment}>合并付款</span> : null
            }
            <span onClick={this.showChooseShop}>筛选</span>
          </div>
        </SelectBar>
        <SearchTypeBar>
          {
            orderStatus.map((status, index) => {
              return (
                <div key={index} onClick={() => {
                  this.changeOrderStatus(status.value, searchContent);
                }}>
                  <span className={status.value === key ? "active" : null}>
                    {status.name}
                  </span>
                </div>
              );
            })
          }
        </SearchTypeBar>
        <Spin spinning={isSpin}>
          <OrderLists
            className={"orderList"}
            // style={{ marginBottom: orderListInfo ? orderListInfo.length > 2 ? 30 : 0 : 0 }}
          >
            {
              orderListInfo ? orderListInfo.length > 0 ?
                orderListInfo.map((order, index) => {
                  return <div key={index}>
                    <SingleOrderInfo
                      order={order}
                      goToOrderDetail={this.goToOrderDetail}
                      goToShopCar={this.goToShopCar}
                      componentAfreshDidMount={() => {
                        this.componentDidMount();
                      }}
                      isBySkuType={isBySkuType}
                      orderButton={this.orderButton}
                      showCheckboxItem={false}
                      chooseOne={null}
                    />
                    <MarginBottom/>
                  </div>;
                })
                : <NoGoods title="暂无订单" height={document.documentElement.clientHeight - 90}/> :
                <NoGoods title="暂无订单" height={document.documentElement.clientHeight - 90}/>
            }
            {
              orderListInfo && orderListInfo.length > 0 ?
                <LoadingTip
                  isFinished={finished}
                  isLoad={isLoad}
                /> : null
            }
          </OrderLists>
        </Spin>
        <GoHome
          goHome={this.goHome}
        />
        {
          showOrderPop &&
            <ScreeningStores
              confirmSearch={this.confirmSearch}
              canclePop={this.canclePop}
              isHideScheme={true}
              orderPartyList={toJS(orderPartyList)}
              salesOrderStatusList={toJS(salesOrderStatusList)}
            />
        }
      </OrderPage>
    );
  }
}

const AllShopOrderList = ScrollAbilityWrapComponent(AllShopOrderListWrap);
export default AllShopOrderList;

const OrderPage = styled.div`// styled
  & {
    width: 100%;
    min-height: calc(${document.documentElement.clientHeight}px);
    height: auto;
    background-color: #f5f5f5;
    // overflow-y: auto;
    color: #2a2a2a;
    // margin-bottom: 50px;
    > .orderPage span {
      text-align: center;
    }
  }
`;

const MarginBottom = styled.div`// styled
  & {
    width: 100%;
    height: 10px;
    background: #f5f5f5;
  }
`;

const SelectBar = styled.div`// styled
  & {
    width: 100%;
    height: 40px;
    background-color: #F2F2F2;
    position: fixed;
    // border-bottom: 1px solid #d8d8d8;
    z-index: 99;
    :after {
      content: '';
      position: absolute;
      background-color: #D8D8D8 !important;
      display: block;
      z-index: 1;
      top: auto;
      right: auto;
      bottom: 0;
      left: 0;
      width: 100%;
      height: 1px;
      transform-origin: 50% 100%;
      transform: scaleY(0.5);
    }
    .am-search {
      background-color: #F2F2F2;
      padding: 7px 10px 7px 16px;
      height: 39px;
      width: 62%;
      display: inline-block;
    }
    .am-search-input {
      border-radius: 13px;
      height: 26px;
    }
    .am-search-input .am-search-clear {
      padding: 5.5px;
    }
    .am-search-input .am-search-synthetic-ph {
      height: 26px;
      line-height: 26px;
      width: 55% !important;
    }
    .am-search-input input[type="search"] {
      height: 26px;
      padding-left: 56px !important;
    }
    .am-search-cancel {
      display: none;
    }
    .am-search-synthetic-ph-placeholder { /* WebKit, Blink, Edge */
      font-family: PingFangSC-Regular;
      font-size: 14px;
      color: #999999;
    }
    .right{
      float: right;
      width: 38%;
      border-left: 1px solid #D8D8D8;
      padding: 0 16px 0 7px;
      margin: 10px 0px;
      > span {
        float: right;
        font-size: 13px;
        font-family: "SourceHanSansCN-Normal";
        font-weight: 400;
        color: rgba(24, 144, 255, 1);
        width: 50%;
        text-align: center;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
      >span:first-of-type{
        //margin-right: 0;
      }
    }
  }
`;

const SearchTypeBar = styled.div`// styled
  & {
    width: 100%;
    height: 40px;
    line-height: 20px;
    background-color: #fff;
    display: flex;
    flex-direction: row;
    text-align: center;
    position: fixed;
    top: 40px;
    color: #8a8a8a;
    z-index: 99;
    padding: 10px 16px;
    box-sizing: border-box;
    > div {
      height: 30px;
      //flex: 1;
      > span {
        display: inline-block;
        height: 30px;
        font-size: 14px;
        color: #666;
      }
      > .active {
        color: #307DCD;
        font-size: 14px;
        border-bottom: 2px solid #307DCD;
      }
    }
    > div:nth-of-type(1) {
      text-align: left;
      width: 10%;
    }
    > div:nth-of-type(2) {
      width: 25%;
    }
    > div:nth-of-type(3) {
      width: 25%;
    }
    > div:nth-of-type(4) {
      width: 25%;
    }
    > div:last-child {
      text-align: right;
      width: 15%;
    }
  }
`;

const OrderLists = styled.div`// styled
  & {
    width: 100%;
    /*height: 600px;*/
    padding-top: 90px;
    // overflow-y: auto;
  }
`;
