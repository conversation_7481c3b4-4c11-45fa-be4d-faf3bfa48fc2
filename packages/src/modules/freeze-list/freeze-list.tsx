import { autowired } from "@classes/ioc/ioc";
import { observer } from "mobx-react";
import { transaction } from "mobx";
import React from "react";
import { withRouter } from "react-router";
import styled from "styled-components";
import { $FreezeListMv } from "./freeze-list-mv";
import { NoGoods } from "../../components/no-goods/no-goods";
import { SITE_PATH } from "../app";
import { Spin } from "antd";
import { ScrollAbilityWrapComponent } from "../../components/scroll-ability/wrap";
import { LoadingTip } from "../../components/loading-marked-words";
import { $AppStore, AppStoreKey } from "../../classes/stores/app-store-mv";
import { CustomMonthModal } from "../../components/custom-month-modal/custom-month-modal";
import { AccountBusinessModal } from "../../components/account-business-modal/account-business-modal";
import { AccountFlowItem } from "../../components/account-flow-item/account-flow-item";
import { $SearchTimeType } from "@classes/const/$search-time-type";

@withRouter
@observer
class FreezeListWrap extends React.Component<any, any> {

  @autowired($FreezeListMv)
  public $myMv: $FreezeListMv;

  @autowired($AppStore)
  public $AppStore: $AppStore;

  public constructor(props) {
    super(props);
    this.state = {
      showDateModal: false,
      showBusinessModal: false,
    };
  }

  public componentDidMount() {
    this.$myMv.accountFlowList = [];
    document.title = "冻结解冻";
    this.$myMv.clearMVData();
    this.initPage();
  }

  public componentWillReceiveProps(nextProps: Readonly<any>, nextContext: any): void {
    const { isScrollEnd } = nextProps;
    const { showDateModal, showHfpzModal } = this.state;
    if (isScrollEnd && isScrollEnd !== this.props.isScrollEnd && !showDateModal && !showHfpzModal) {
      this.loadData();
    }
  }

  public initPage = () => {
    const { accountId } = this.props.match.params || { accountId: "" };
    const { pageSize, pageIndex, key, status, businessType, startTime, endTime } = this.$myMv;
    this.$myMv.showSpin();
    this.$myMv.isLoading = true;
    const params = {
      accountId,
      condition: key,
      pageIndex,
      pageSize,
      status,
      businessType,
      start: startTime,
      end: endTime,
    };
    this.firstLoadFetchAccountInfo(params);
  }

  public firstLoadFetchAccountInfo = (params) => {
    transaction(() => {
      this.$myMv.fetchAccountInfo(params, false);
    });
  }

  public queryAccountInfo = (activeKey) => {
    this.$myMv.key = activeKey;
    this.$myMv.isFinished = false;
    this.$myMv.pageIndex = 0;
    this.initPage();
  }

  public loadData = () => {
    const { isFinished, isLoading, pageIndex } = this.$myMv;
    if (!isFinished) {
      this.$myMv.showSpin();
      if (!isLoading) {
        this.$myMv.pageIndex = pageIndex + 1;
        this.loadList();
      }
    }
  }

  public loadList = () => {
    const { pageSize, key, pageIndex } = this.$myMv;
    this.$myMv.showSpin();
    const params = { accountId: this.props.match.params.accountId, condition: key, pageIndex, pageSize };
    this.$myMv.isLoading = true;
    const { loadingEnd } = this.props;
    this.$myMv.fetchAccountInfo(params, true).then(() => {
      loadingEnd && loadingEnd();
    });
    // 发送请求改变 isFinished， isLoading状态
  }

  public onSearchDate = (startTime, endTime, dateType) => {
    this.$myMv.startTime = startTime;
    this.$myMv.endTime = endTime;
    this.$myMv.dateType = dateType;
    this.initPage();
  }

  public onSearchBusiness = (val) => {
    this.$myMv.businessType = val[0];
    this.initPage();
  }

  public goAccountDetail = (item) => {
    sessionStorage.setItem("detailParams", JSON.stringify(item));
    this.props.history.push({ pathname: `/${SITE_PATH}/new-account-info-detail/${this.props.match.params.accountId}` });
  }

  public render() {
    const { showDateModal, showBusinessModal } = this.state;
    const { accountFlowList, key, isLoading, isFinished, frozenAmount, startTime,
            endTime, inAmount, outAmount, businessType, dateType } = this.$myMv;
    const isFixed = showDateModal || showBusinessModal;
    return (
      <Spin spinning={isLoading} style={{ pointerEvents: isFixed ? "none" : "auto" }}>
        <Wrapper className="account-content-info-warp" >
          <SHeadBg />
          <SHeader>
            <div>冻结</div>
            <div>{frozenAmount}</div>
          </SHeader>
          <SSearch>
            <div className="search-key">
              <span className={key === "ALL" ? "active" : ""} onClick={() => this.queryAccountInfo("ALL")}>全部</span>
              <span className={key === "IN" ? "active" : ""} onClick={() => this.queryAccountInfo("IN")}>增加</span>
              <span className={key === "OUT" ? "active" : ""} onClick={() => this.queryAccountInfo("OUT")}>减少</span>
            </div>
            <div className="search-more">
              <div onClick={() => this.setState({ showDateModal: true })}>
                <span>
                  {
                    dateType === $SearchTimeType.MONTH ? startTime.slice(0, 7) : `${startTime}~${endTime}`
                  }
                </span>
                <img src="https://supplychainms-fwh.oss-cn-hangzhou.aliyuncs.com/static-img/bottom-arrow-icon.png" />
              </div>
              <div onClick={() => this.setState({ showBusinessModal: true })}>
                <span>{businessType || "业务类型"}</span>
                <img src="https://supplychainms-fwh.oss-cn-hangzhou.aliyuncs.com/static-img/bottom-arrow-icon.png" />
              </div>
            </div>
            {
              (inAmount > 0 || outAmount > 0) &&  <div className="search-board">
                {
                  outAmount > 0 && <div>
                    <div className="red">{outAmount}</div>
                    <div>冻结</div>
                  </div>
                }
                {
                  inAmount > 0 && <div>
                    <div>{inAmount}</div>
                    <div>解冻</div>
                  </div>
                }
              </div>
            }
          </SSearch>
          <SList>
            {
              dateType === $SearchTimeType.MONTH && <div className="date-title">{startTime.slice(0, 4)}年{startTime.slice(5, 7)}月</div>
            }
            {
              accountFlowList && accountFlowList.length > 0 ? accountFlowList.map((item, index) => {
                return <AccountFlowItem item={item} key={index} goAccountFlowDetail={() => this.goAccountDetail(item)}/>;
              }) : <NoGoods title="暂无数据" />
            }
            {
              accountFlowList && accountFlowList.length > 0 ?
                <LoadingTip
                  isFinished={isFinished}
                  isLoad={isLoading}
                /> : null
            }
          </SList>
          <CustomMonthModal
            visible={showDateModal}
            type={"range"}
            dateType={dateType}
            startDate={startTime}
            endDate={endTime}
            onCancel={() => this.setState({ showDateModal: false })}
            onConfirm={this.onSearchDate}
          />
          <AccountBusinessModal
            visible={showBusinessModal}
            onCancel={() => this.setState({ showBusinessModal: false })}
            onConfirm={this.onSearchBusiness}
          />
        </Wrapper>
      </Spin>
    );
  }
}

const FreezeList = ScrollAbilityWrapComponent(FreezeListWrap);

export default FreezeList;

const Wrapper = styled.div`// styled
  & {
    width: 100%;
    height: 100vh;
    padding: 15px;
    display: flex;
    flex-direction: column;
    align-items: center;
    background: #F7F7F7;

    .am-modal-content {
      z-index: 1000;

      .modal-title {
        padding: 12px;
      }
    }
  }
`;

const SHeadBg = styled.div`
  & {
    position: absolute;
    top: 0;
    width: 100vw;
    height: 32vw;
    background: #437DF0;
  }
`;

const SHeader = styled.div`
  & {
    width: 90vw;
    color: #FFF;
    z-index: 10;
    margin-top: 16px;
    display: flex;
    flex-direction: column;

    > div:nth-child(1) {
      font-size: 13px;
    }

    > div:nth-child(2) {
      font-size: 32px;
      font-weight: 500;
    }
  }
`;

const SList = styled.div`
  & {
    width: 100%;
    margin-top: 12px;
    border-radius: 8px;
    overflow: scroll;
    background: #FFF;

    .date-title {
      padding: 12px;
      font-size: 16px;
      font-weight: 500;
      color: #333;
    }
  }
`;

const SSearch = styled.div`// styled
  & {
    width: 100%;
    height: auto;
    background: #fff;
    margin-top: 12px;
    border-radius: 8px;
    z-index: 100;

    .search-key {
      width: 100%;
      height: 40px;
      line-height: 40px;
      display: flex;
      > span {
        display: inline-block;
        width: 100%;
        text-align: center;
        color: #666;
        margin: 0 30px;
        font-size: 14px;
      }
      .active {
        border-bottom: 2px solid #307DCD;
        color: #307DCD;
        font-size: 14px;
      }
    }

    .search-more {
      display: flex;
      justify-content: space-between;
      padding: 12px;
      background: #F7F7F7;

      > div {
        > span {
          color: #333;
          font-size: 14px;
          margin-right: 3px;
        }
        > img {
          width: 14px;
          height: 14px;
        }
      }
    }

    .search-board {
      padding: 12px 36px;
      display: flex;
      justify-content: space-around;
      background: linear-gradient(181deg, #F0F3FC 4%, #FFFFFF 64%);

      > div {
        display: flex;
        flex-direction: column;
        text-align: left;

        > div:nth-child(1) {
          font-size: 18px;
          font-weight: 500;
          color: #333;
        }

        > div:nth-child(2) {
          font-size: 14px;
          color: #666;
          margin-top: 5px;
        }

        .red {
          color: #FF3030 !important;
        }
      }
    }
  }
`;
