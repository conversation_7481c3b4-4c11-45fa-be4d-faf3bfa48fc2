import { autowired } from "@classes/ioc/ioc";
import { Spin } from "antd";
import { InputItem, List, ActionSheet, Toast } from "antd-mobile";
import { observer } from "mobx-react";
import React from "react";
import styled from "styled-components";
import { $FillInLogisticsMv } from "./fill-in-logistics-mv";
import { SITE_PATH } from "../app";
import { withRouter } from "react-router";

declare let window: any;
const Item = List.Item;
const logisticsType = {
  EXPRESSDELIVER: "EXPRESS_DELIVER", // 快递
  OFFLINEDELIVER: "OFFLINE_DELIVER", // 线下发货
}

@withRouter
@observer
class FillInLogistics extends React.Component<any, any> {

  @autowired($FillInLogisticsMv)
  public $myMv: $FillInLogisticsMv;

  constructor(props) {
    super(props);
    this.state = {
      expressNo: "",
      orderId: "",
      orderPartyName: "",
      orgId: "",
      selectLogisticsType: {},
      selectCompanyTarget: {},
    };
  }

  public componentDidMount() {
    document.title = "填写物流";
    const orderId = this.props.match.params.orderId;
    console.log("this.props.location.state", this.props.location.state);
    const { selectCompanyTarget, orderPartyName, orgId } = this.props.location.state || {};
    this.setState({ orderId, selectCompanyTarget: selectCompanyTarget ? selectCompanyTarget : {}, orderPartyName, orgId }, () => {
      this.getLogisticsInfo();
      this.getLogisticsTypeList();
    });
  }
  public getLogisticsInfo = () => {
    this.$myMv.showSpin();
    const { selectCompanyTarget } = this.state;
    this.$myMv.getLogisticsInfo({ refundOrderId: this.state.orderId }).then((data) => {
      console.log("getLogisticsInfo", data, selectCompanyTarget);
      const logisticsInfo = JSON.parse(localStorage.getItem("logisticsInfo"));
      const { logisticsTypeCode, logisticsTypeName, logisticsCompanyId, logisticsCompanyName, expressNo } = data;
      const newSelectCompanyTarget = selectCompanyTarget.oid ? selectCompanyTarget : { oid: logisticsCompanyId, name: logisticsCompanyName };
      const selectLogisticsType = logisticsInfo ? logisticsInfo.selectLogisticsType : { text: logisticsTypeName, value: logisticsTypeCode };
      const newExpressNo = logisticsInfo ? logisticsInfo.expressNo : expressNo;
      this.setState({ selectCompanyTarget: newSelectCompanyTarget, selectLogisticsType, expressNo: newExpressNo }, () => {
        if (logisticsInfo) {
          localStorage.removeItem("logisticsInfo");
        }
      });
      this.$myMv.setLogisticsInfo(data);
      this.$myMv.hideSpin();
    });
  }
  public getLogisticsTypeList = () => {
    this.$myMv.getLogisticsTypeList({}).then((data) => {
      console.log("getLogisticsType", data);
      this.$myMv.setLogisticsTypeList(data);
    });
  }
  public changeExpressageNum = (expressNo) => {
    console.log("changeExpressageNum", expressNo);
    this.setState({ expressNo });
  }
  public changeLogisticsType = (e) => {
    console.log("changeLogisticsType", e);
    const { logisticsTypeList } = this.$myMv;
    const { selectLogisticsType } = this.state;
    const optionList = [];
    logisticsTypeList.map((item) => {
      optionList.push(item.text);
    })
    this.showActionSheet(optionList, (buttonIndex) => {
      const newSelectLogisticsType = logisticsTypeList.find((item, index) => buttonIndex === index);
      this.setState({ selectLogisticsType: newSelectLogisticsType ? newSelectLogisticsType : selectLogisticsType });
    });
  }
  public changeLogisticsCompany = (e) => {
    console.log("changeLogisticsCompany", e);
    const { orderId, selectLogisticsType, expressNo, orgId, orderPartyName } = this.state;
    const logisticsInfo = {
      expressNo,
      selectLogisticsType,
    }
    localStorage.setItem("logisticsInfo", JSON.stringify(logisticsInfo))
    this.props.history.push({
      pathname: `/${SITE_PATH}/logistics-company-list/${orderId}`,
      state: {
        orderPartyName,
        orgId,
      },
    });
  }
  public saveLogisticsInfo = (e) => {
    const { orderId, selectLogisticsType, selectCompanyTarget, expressNo } = this.state;
    let params = {}
    if (selectLogisticsType.value === logisticsType.OFFLINEDELIVER) {
      params = {
        logisticsType: selectLogisticsType.value,
        oid: orderId,
      };
    } else if (selectLogisticsType.value === logisticsType.EXPRESSDELIVER && expressNo && selectCompanyTarget.oid) {
      params = {
        expressNo,
        logisticsCompany: selectCompanyTarget.oid,
        logisticsType: selectLogisticsType.value,
        oid: orderId,

      };
    } else {
      return;
    }
    this.$myMv.changeLogisticsInfo(params).then((data) => {
      console.log("getLogisticsType", data);
      const { errorCode, errorMessage } = data || {};
      if (errorCode && errorCode !== "0") {
        Toast.fail(errorMessage, 3);
      } else {
        const status = "success";
        const { orderPartyName, orgId } = this.state;
        this.props.history.push({
          pathname: `/${SITE_PATH}/confirm-result-show/${status}/fill-in-logistics`,
          state: {
            orgId,
            orderPartyName,
          },
        });
      }
    });
  }
  public showActionSheet = (optionList, cb) => {
    const isIPhone = new RegExp("\\biPhone\\b|\\biPod\\b", "i").test(window.navigator.userAgent);
    let wrapProps;
    if (isIPhone) {
      wrapProps = {
        onTouchStart: (e) => e.preventDefault(),
      };
    }
    optionList.push("取消")
    ActionSheet.showActionSheetWithOptions({
        "cancelButtonIndex": optionList.length - 1,
        "data-seed": "logId",
        "maskClosable": true,
        // "title": "物流方式",
        "message": "请选择物流方式",
        // "destructiveButtonIndex": optionList.length - 2,
        "options": optionList,
        "wrapProps": wrapProps,
      },
      (buttonIndex) => {
        cb && cb(buttonIndex);
      });
  }

  public render() {
    const { isSpin } = this.$myMv;
    const { selectLogisticsType, expressNo, selectCompanyTarget } = this.state;
    console.log("selectLogisticsType", selectLogisticsType);
    return (
      <PageWarp>
        <Spin spinning={isSpin}>
          <div className="pageContent">
            <List className={selectLogisticsType.value ? "logistics-type" : "logistics-type scm-placeholder"}>
              <Item
                extra={selectLogisticsType && selectLogisticsType.text}
                arrow="horizontal"
                onClick={this.changeLogisticsType}
              >物流方式</Item>
            </List>
            {
              (selectLogisticsType.value) !== logisticsType.OFFLINEDELIVER &&
                <List className={selectCompanyTarget.oid ? "logistics-company" : "logistics-company scm-placeholder"}>
                  <Item
                    extra={selectCompanyTarget.name}
                    arrow="horizontal"
                    onClick={this.changeLogisticsCompany}
                  >物流公司</Item>
                </List>
            }
            {
              (selectLogisticsType.value) !== logisticsType.OFFLINEDELIVER &&
                <div className="expressage-num-wrap">
                  <InputItem
                    className="expressage-num"
                    placeholder="请输入"
                    value={expressNo}
                    onChange={this.changeExpressageNum}
                  >
                    快递单号
                  </InputItem>
                </div>
            }
          </div>
        </Spin>
        <div className="footer">
            <span
              className={`confirm-button ${ ((selectLogisticsType.value) === logisticsType.OFFLINEDELIVER || selectCompanyTarget.oid && expressNo) ? "" : "disabled"}`}
              onClick={this.saveLogisticsInfo}
            >确定</span>
        </div>
      </PageWarp>
    );
  }
}

export default FillInLogistics;

const PageWarp = styled.div`// styled
  & {
    width: 100%;
    height: 100%;
    background-color: rgba(242, 242, 242, 1);
    .logistics-type{
      margin-bottom: 10px;
    }
    .scm-placeholder{
      .am-list-extra{
        position: relative;
        &:after{
          position: absolute;
          top: 50%;
          right: 0;
          transform: translateY(-50%);
          content: "请选择";
          font-size: 13px;
          color: rgba(153, 153, 153, 1);
        }
      }
    }
    .am-list-item{
      padding-left: 12px;
    }
    .am-list-item .am-list-line{
      padding-right: 12px;
    }
    .am-list-item .am-list-line .am-list-content{
      color: rgba(47, 47, 47, 1);
      font-size: 14px;
    }
    .am-list-item .am-list-line .am-list-extra{
      color: rgba(102, 102, 102, 1);
      font-size: 13px;
      flex-basis: 67%;
      white-space: inherit;
    }
    .expressage-num{
      padding-left: 12px;
      .am-list-line{
        >div:first-of-type{
          font-size: 14px;
          color: rgba(47, 47, 47, 1);
        }
        >.am-input-control{
          font-size: 13px;
          color: rgba(102, 102, 102, 1);
          input {
            font-size: 13px;
            color: rgba(153, 153, 153, 1);
            text-align: right;
          }
        }
      }
    }
    .footer{
      width: 100%;
      height: 66px;
      background-color: #fff;
      text-align: center;
      padding: 12px 16px;
      position: fixed;
      bottom: 0;
      .confirm-button{
        display: inline-block;
        width: 100%;
        height: 100%;
        background-color: rgba(48, 125, 205, 1);
        border-radius: 3px;
        text-align: center;
        font-size: 16px;
        color: #ffffff;
        line-height: 42px;
      }
      .disabled{
        background-color: rgba(217, 217, 217, 1);
      }
    }
  }
`;
