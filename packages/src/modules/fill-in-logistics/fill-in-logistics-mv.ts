import { autowired, bean } from "@classes/ioc/ioc";
import { action, observable } from "mobx";
import { $ComponentService } from "../../classes/service/$component-service";

@bean($FillInLogisticsMv)
export class $FillInLogisticsMv {

  @autowired($ComponentService)
  public $ComponentService: $ComponentService;

  @observable public isSpin: boolean = false
  @observable public logisticsTypeList: any[] = []
  @observable public logisticsInfo: any = {}
  @action
  public showSpin() {
    this.isSpin = true;
  }

  @action
  public hideSpin() {
    this.isSpin = false;
  }
  @action
  public setLogisticsTypeList(list) {
    this.logisticsTypeList = list;
  }
  @action
  public setLogisticsInfo(info) {
    this.logisticsInfo = info;
  }
  @action
  public getLogisticsTypeList(params) {
    return this.$ComponentService.getLogisticsTypeList(params);
  }
  @action
  public getLogisticsInfo(params) {
    return this.$ComponentService.getLogisticsInfo(params);
  }
  @action
  public changeLogisticsInfo(params) {
    return this.$ComponentService.changeLogisticsInfo(params);
  }
}
