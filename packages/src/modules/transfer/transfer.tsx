import { autowired } from "@classes/ioc/ioc";
import { observer } from "mobx-react";
import { transaction } from "mobx";
import { withRouter } from "react-router";
import * as React from "react";
import { $Transfer } from "./transfer-mv";
import styled from "styled-components";
import { ActivityIndicator, Button, List } from "antd-mobile";
import { Radio, Spin } from "antd";
import { Toast } from "antd-mobile/es";
import { SITE_PATH } from "../app";
import { $AppStore, AppStoreKey } from "@classes/stores/app-store-mv";

const Item = List.Item;
const RadioGroup = Radio.Group;
declare let window: any;

@withRouter
@observer
class Transfer extends React.Component<any, any> {
  @autowired($Transfer)
  public $mv: $Transfer;

  @autowired($AppStore)
  public $AppStore: $AppStore;

  constructor(props) {
    super(props);
    this.state = {
      isShowMabel: false,
      animating: false,
    };
  }

  public componentDidMount() {
    document.title = "转账";
    this.$mv.maxTransferAmount = 0;
    this.$mv.transferInAccountInfo = null;
    transaction(() => {
      this.$mv.showSpin();
      const params = { accountId: this.props.match.params.accountId, accountType: this.props.match.params.accountType };
      this.$mv.fetchTransfer(params);
      this.$mv.fetchTransferInAccountList(params);
      this.$mv.hideSpin();
    });
  }

  public onSubmit = () => {
    const { transferInOrgId, transferInAccountId } = this.$mv.transferInAccountInfo;
    const money = document.querySelector(".big-input").value;
    if (money === "") {
      Toast.info("请输入金额", 3);
      return;
    }
    if (money > this.$mv.maxTransferAmount) {
      Toast.info("转出金额超限", 3);
      return;
    }
    if (parseFloat(money) <= 0) {
      Toast.info("输入金额不可小于0", 3);
      return;
    }
    if (!/^[0-9]+(.[0-9]{0,2})?$/.test(money)) {
      Toast.info("支付金额最多有两位小数");
      return;
    }
    const params = {
      transferOutAccountId: this.props.match.params.accountId,
      transferInOrgId: transferInOrgId,
      transferInAccountId: transferInAccountId,
      amount: money,
      accountType: this.props.match.params.accountType,
    }
    this.setState({ animating: true }, () => {
      this.$mv.submitTransfer(params).then((data) => {
        if (data.result) {
          this.setState({ animating: false });
          Toast.info("转账成功", 3);
          this.$AppStore.clearPageMv(AppStoreKey.ACCOUNTINFO);
          window.location.href = `/${SITE_PATH}/my`;
        } else {
          Toast.info("转账失败", 3);
          this.setState({ animating: false });
        }
      }).catch(() => {
        this.setState({ animating: false });
      });
    });
  }

  public changeMax() {
    document.querySelector(".big-input").value = this.$mv.maxTransferAmount;
  }

  public changeInAccount(index) {
    this.$mv.setTransferInAccountInfo(index);
    this.setState({
      isShowMabel: false,
    });
  }

  public openMabel() {
    this.setState({
      isShowMabel: true,
    });
  }

  public render() {
    const { maxTransferAmount, transferInAccountInfo, transferInAccountList, isSpin } = this.$mv;
    const { isShowMabel } = this.state;
    return (
      <RepaymentWrapper>
        <List>
          <Item><i className="scmIconfont scm-icon-out"></i>转出金额</Item>
        </List>
        <Spin spinning={isSpin}>
          <RadioGroup>
            {
              transferInAccountInfo ? <div className="pay-list">
                <BigInput>
                  <input type="number" className="big-input" placeholder={`最多可转出${maxTransferAmount}`}/>
                  <span className="all" onClick={() => this.changeMax()}>全部</span>
                </BigInput>
                <div className="gray-bg"></div>
              </div> : null
            }
          </RadioGroup>
          <List>
            <Item><i className="scmIconfont scm-icon-into"></i>收款账户</Item>
          </List>
          {
            transferInAccountInfo ? <List>
              <Item arrow="horizontal" className="gray"
                    onClick={() => this.openMabel()}>{transferInAccountInfo.transferInAccountLabel}</Item>
            </List> : null
          }
        </Spin>
        <div className="gray-bg"></div>
        <ConfirmButton>
          <Button type="primary" onClick={this.onSubmit}>确认转出</Button>
        </ConfirmButton>
        <ActivityIndicator
          toast={true}
          text="Loading..."
          animating={this.state.animating}
        />
        <Mabel style={{ display: isShowMabel ? "block" : "none" }}>
          {
            transferInAccountList ? transferInAccountList.map((v, index) => {
              return <div className="mabel-item" key={v.transferInOrgId} onClick={() => this.changeInAccount(index)}>
                {v.transferInAccountLabel}
              </div>;
            }) : null
          }
        </Mabel>
      </RepaymentWrapper>
    );
  }
}

export default Transfer;

const RepaymentWrapper = styled.div`
  &{
  .am-list-item .am-list-line .am-list-content{
    font-size:14px;
    color:#666666;
  }
  .gray.am-list-item .am-list-line .am-list-content{
    font-size:14px;
    color:#999999;
  }
  .scm-icon-out,.scm-icon-into{
    color:#307DCD;
    margin-right:5px;
    position:relative;
    top:1px;
  }
  .gray-bg{
    height:10px;
    background:#f2f2f2;
  }
  .ant-radio-group {
      width: 100%;
      > div {
        border-bottom: 0.5px solid #ddd;
        position: relative;
        .right {
          position: absolute;
          right: 15px;
        }
      }
      .list-item{
        height:42px;
        line-height:42px;
        border-bottom: 0.5px solid #ddd;
        padding-left:15px;
      }
    }
  }
`;

const BigInput = styled.div`
  &{
  font-size:22px;
  padding-left:15px;
  height:70px;
  line-height:70px;
  positon:relative;
  color:#666666;
  .all{
    position:absolute;
    right:15px;
    color:#307DCD;
    font-size:14px;
    color:#307DCD;
  }
    input{
      height:36px;
      line-height:36px;
      font-size:14px;
      color:#999999;
      border:none;
      margin-left:20px;
      width:84%;
      position:relative;
      top:-2px;
    }
  }
`;

const ConfirmButton = styled.div`
  &{
    padding: 15px;
    left:0;
    width:100%;
    position:fixed;
    bottom: 0px;
    background:#ffffff;
    .am-button{
      height:42px;
      line-height:42px;
      font-size:16px;
    }
    .am-button-primary{
      border-radius:3px;
    }
  }
`;

const Mabel = styled.div`
  &{
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    min-height: 100%;
    background: #F2F2F2;
    z-index: 100;
    .mabel-item{
      height:64px;
      line-height:64px;
      background:#ffffff;
      font-size:14px;
      margin-bottom:10px;
      padding-left:20px;
      color:#333333;
    }
  }
`;
