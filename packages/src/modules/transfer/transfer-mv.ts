import { autowired, bean } from "@classes/ioc/ioc";
import { action, observable } from "mobx";
import { $MyInfoService } from "../../classes/service/$my-info-service";

@bean($Transfer)
export class $Transfer {
  @autowired($MyInfoService)
  public $myInfoService: $MyInfoService;

  @observable public transferInAccountInfo: any;

  @observable public maxTransferAmount: number;

  @observable public transferInAccountList: any;

  @observable public isSpin: boolean = false;

  @action
  public showSpin() {
    this.isSpin = true;
  }

  @action
  public hideSpin() {
    this.isSpin = false;
  }

  @action
  public fetchTransfer(params) {
    this.$myInfoService.loadTransferPage(params).then((data) => {
      this.transferInAccountInfo = data && data.transferInAccountInfo;
      this.maxTransferAmount = data.maxTransferAmount;
      this.hideSpin();
    });
  }

  @action
  public submitTransfer(params) {
    return this.$myInfoService.toTransfer(params);
  }

  @action
  public fetchTransferInAccountList(params) {
    this.$myInfoService.queryTransferInAccountList(params).then((data) => {
      this.transferInAccountList = data.transferInAccountList;
    });
  }

  @action
  public setTransferInAccountInfo(index) {
    this.transferInAccountInfo = this.transferInAccountList[index];
  }

}
