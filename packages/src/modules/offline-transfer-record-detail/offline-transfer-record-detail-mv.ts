import { autowired, bean } from "@classes/ioc/ioc";
import { action, observable } from "mobx";
import { $OfflineTransferRecordService } from "../../classes/service/$offline-transfer-record-service";
import { $OrderPartyService } from "../../classes/service/$order-party-service";

@bean($OfflineTransferRecordDetailMv)
export class $OfflineTransferRecordDetailMv {
  @autowired($OfflineTransferRecordService)
  public $offlineTransferRecordService: $OfflineTransferRecordService;
  @autowired($OrderPartyService)
  public $orderPartyService: $OrderPartyService;

  @observable public offlineTransferRecordDetail = null;

  @action
  public getOfflineTransferRecordDetail(params) {
    return this.$offlineTransferRecordService.getOfflineTransferRecordDetail(params);
  }
  @action
  public withdraw(params) {
    return this.$offlineTransferRecordService.withdraw(params);
  }
  @action
  public clearOfflineTransferRecordDetail() {
    this.offlineTransferRecordDetail = null;
  }
  @action
  public setOfflineTransferRecordDetail(obj) {
    this.offlineTransferRecordDetail = obj;
    const { accoutInfo } = obj;
    const { orderPartyId } = accoutInfo || {};
    if (orderPartyId) {
      const params = { orderPartyId }
      this.$orderPartyService.saveShopAndScheme(params);
    }
  }
}
