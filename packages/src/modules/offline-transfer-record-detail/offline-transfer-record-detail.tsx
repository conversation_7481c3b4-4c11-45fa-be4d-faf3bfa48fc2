import { autowired } from "@classes/ioc/ioc";
import { Spin } from "antd";
import { Button, Modal, SearchBar, Toast } from "antd-mobile";
import { observer } from "mobx-react";
import * as React from "react";
import { withRouter } from "react-router";
import styled from "styled-components";
import { $OfflineTransferRecordDetailMv } from "./offline-transfer-record-detail-mv";
import { GoHome } from "../../components/go-home/go-home";
import { LoadingTip } from "../../components/loading-marked-words";
import { findIndex } from "lodash";
import NoAuthority from "../no-authority/no-authority";

import {
  offlineTransferRecordOrderType,
  offlineTransferRecordOrderStatus,
  orderStatus,
  $OfflineTransferRecordOrderStatus,
  $OfflineTransferRecordOrderType,
} from "../../classes/const/$offline-transfer-record";
import { SITE_PATH } from "../app";
import { $InterfaceErrorCode } from "../../classes/const/$interface-error-code";
import { $AccountType } from "../../classes/const/$account-type";
import { gaEvent } from "../../classes/website-statistics/website-statistics";
import { $AppStore, AppStoreKey } from "../../classes/stores/app-store-mv";
import { SingleExpenseOrderInfo } from '../../components/single-expense-order-info/single-expense-order-info';
import DateUtils from '@classes/utils/DateUtils';

const alert = Modal.alert;
declare let window: any;

@withRouter
@observer
class OfflineTransferRecordDetail extends React.Component<any, any> {

  @autowired($AppStore)
  public $AppStore: $AppStore;
  @autowired($OfflineTransferRecordDetailMv)
  public $offlineTransferRecordDetailMv: $OfflineTransferRecordDetailMv;

  constructor(props) {
    super(props);
    this.state = ({
      isCurrentUser: false,
      isGetPermissions: false,
      isHavePermissions: false,
      isLoading: false,
      viewer: null,
    });
  }

  public componentDidMount() {
    document.title = "转账记录详情";
    gaEvent("转账记录详情");
    this.$offlineTransferRecordDetailMv.clearOfflineTransferRecordDetail();
    this.getOfflineTransferRecordDetail();
  }

  public componentWillUnmount(): void {
    const viewerImgList = document.getElementsByClassName("viewer-imgList");
    console.log(this.state.viewer, viewerImgList.length);
    if (viewerImgList && viewerImgList.length > 0) {
      for (let i = 0; i < viewerImgList.length; i++) {
        document.getElementsByClassName("viewer-imgList")[i].style.display = "none";
      }
    }
  }

  public getOfflineTransferRecordDetail = () => {
    const { docType, oid } = this.props.match.params;
    const params = {
      docType,
      oid,
    };
    this.setState({ isLoading: true });
    this.$offlineTransferRecordDetailMv.getOfflineTransferRecordDetail(params).then((res) => {
      this.setState({ isGetPermissions: true, isHavePermissions: res.errorCode !== $InterfaceErrorCode.NO_PAGE_VIEW_PERMISSIONS, isCurrentUser: res.isCurrentUser })
      if (res.errorCode === $InterfaceErrorCode.NO_PAGE_VIEW_PERMISSIONS) {
        return;
      }
      const { transferRecord } = res;
      this.$offlineTransferRecordDetailMv.setOfflineTransferRecordDetail(transferRecord);
      if (transferRecord.imageUrl && transferRecord.imageUrl.length > 0) {
        const _this = this;
        if (!_this.state.viewer) {
          const options = {
            toolbar: {
              zoomIn: false,
              zoomOut: false,
              oneToOne: false,
              reset: false,
              prev: false,
              play: false,
              next: false,
              rotateLeft: "large",
              rotateRight: "large",
              flipHorizontal: false,
              flipVertical: false,
            },
            button: false,
            className: "viewer-imgList",
            ready() {
              // 2 methods are available here: "show" and "destroy".
              console.log("show destroy", viewer);
            },
            shown() {
              console.log("9 methods are available here: hide", "view", "prev", "next", "play", "stop", "full", "exit and destroy", viewer);
            },
            viewed() {
              console.log("All methods are available here except", viewer);
              _this.setState({ viewer });
              // this.viewer.zoomTo(1).rotateTo(180);
            },
          }
          const viewer = new Viewer(document.getElementById("imgList"), options);
        }
      }
    }).catch((error) => {
      console.log("error", error);
    }).then(() => {
      this.setState({ isLoading: false });
    });
  }
  public withdraw = (oid, docType) => {
    alert("确认要撤回这个单据？", "", [
      {
        text: "取消", onPress: () => {
          console.log("取消");
        },
      },
      {
        text: "确认撤回", onPress: () => {
          const params = { oid, docType }
          this.$offlineTransferRecordDetailMv.withdraw(params).then((res) => {
            console.log(res);
            if (res.errorCode === "0") {
              console.log("撤销成功");
              this.getOfflineTransferRecordDetail();
            }
          });
        },
      },
    ]);
  }
  public searchTargetObj = (searchTarge, searchRange) => {
    const index = findIndex(searchRange, { key: searchTarge });
    return index >= 0 ? searchRange[index] : { text: "", color: "", key: "" };
  }
  public skipToAccount = (accountId, type) => {
    if (type === $AccountType.CREDIT) {
      this.$AppStore.clearPageMv(AppStoreKey.ACCOUNTINFOLIST);
      this.props.history.push({ pathname: `/${SITE_PATH}/account-info-list/${accountId}/${type}`});
    } else {
      this.$AppStore.clearPageMv(AppStoreKey.ACCOUNTINFO);
      this.props.history.push({ pathname: `/${SITE_PATH}/account-info/${accountId}/${type}`});
    }
  }
  public skipToOrderDetail = (orderId, orderSchemeName) => {
    this.props.history.push({
      pathname: `/${SITE_PATH}/shop/order-detail/${orderId}/${orderSchemeName}`,
      state: {
        backSource: "all",
        // paymentModeListLength,
      },
    });
  }
  public goToOrderDetail = (docId) => {
    console.log("orderId", docId);
    this.props.history.push({
      pathname: `/${SITE_PATH}/expense-node-detail/${docId}`,
    });
  }
  public render() {
    const { offlineTransferRecordDetail, offlineTransferRecordStatus } = this.$offlineTransferRecordDetailMv;
    const { isLoading, isHavePermissions, isGetPermissions, isCurrentUser } = this.state;
    console.log(offlineTransferRecordDetail);
    const { DEALWITH } = $OfflineTransferRecordOrderStatus;
    const { STOREDACCOUNTRECHARGE, ORDERPAYMENT, CREDITACCOUNTREPAYMENT } = $OfflineTransferRecordOrderType;
    const { oid, docStatus, amount, sellerBankName, sellerAccountName, sellerAccountCode, paymentTime, imageUrl, submitTime, orderPartyName, docNo, paymentMemo, memo, relateOrderList, docType, accoutInfo, relateFeeDocumentList,paymentCode,paymentType,paymentMode,paymentFlowNo,serviceCharge } = offlineTransferRecordDetail || {};
    const { hasPermission, accountId, condition } = accoutInfo || {};
    const offlineTransferStatus = docStatus && this.searchTargetObj(docStatus, offlineTransferRecordOrderStatus);
    const orderType = docType && this.searchTargetObj(docType, offlineTransferRecordOrderType);
    return (
      <Container>
        {
          isGetPermissions ? isHavePermissions ?
            <ContainerContent>
              <Spin spinning={isLoading}>
                {
                  offlineTransferRecordDetail &&
									<div className="offline-transfer-record-content">
										<div className="order-info">
											<div className="order-info-top">
												<span><i className="scmIconfont scm-icon-account"/></span>
												<span>
                      <div className="order-name">{orderType.text}</div>
                      <div className="order-money">￥{amount}</div>
                    </span>
											</div>
											<div className="order-info-buttom">
												<div className="order-status" style={{ color: offlineTransferStatus.color }}>{offlineTransferStatus.text}</div>
                        {
                          docStatus === DEALWITH && isCurrentUser && paymentCode != "allinpay" &&
													<Button className="withdraw-button" onClick={(e) => {
                            e.preventDefault();
                            e.stopPropagation();
                            this.withdraw(oid, docType);
                          }}>撤回</Button>
                        }
											</div>
										</div>
										<ul className="payment-info">
											{paymentCode == "bank_transfer" &&
                      <li>
												<div className="payment-info-name">收款银行</div>
												<div className="payment-info-value">{sellerBankName}</div>
											</li>}
											{paymentCode == "bank_transfer" &&
                      <li>
												<div className="payment-info-name">收款账户</div>
												<div className="payment-info-value">{sellerAccountName + `(${sellerAccountCode})`}</div>
											</li>}
											<li>
												<div className="payment-info-name">付款方式</div>
												<div className="payment-info-value">{paymentType}</div>
											</li>
											{paymentCode == "allinpay" &&
                      <li>
												<div className="payment-info-name">支付方式</div>
												<div className="payment-info-value">{paymentMode}</div>
											</li>}
											{paymentCode == "allinpay" &&
                      <li>
												<div className="payment-info-name">支付流水号</div>
												<div className="payment-info-value">{paymentFlowNo}</div>
											</li>}
											{paymentCode == "allinpay" &&
                      <li>
												<div className="payment-info-name">服务费</div>
												<div className="payment-info-value">{serviceCharge}</div>
											</li>}
											<li>
												<div className="payment-info-name">付款时间</div>
												<div className="payment-info-value">{paymentTime}</div>
											</li>
											{paymentCode == "bank_transfer" &&
                      <li>
												<div className="payment-info-name">付款凭证</div>
												<ul className="payment-info-value img-list" id="imgList">
                          {
                            // const { imageUrl } = offlineTransferRecordDetail;
                            imageUrl ? imageUrl.length > 0 ?
                              imageUrl.slice(0, 10).map((item) => {
                                return <li
                                  key={item}
                                >
                                  <img src={item} alt=""/>
                                </li>
                              })
                              :
                              <li><img src="https://order.fwh1988.cn:14501/static-img/scm/ico-nopic.png" alt=""/></li>
                              :
                              <li><img src="https://order.fwh1988.cn:14501/static-img/scm/ico-nopic.png" alt=""/></li>
                          }
												</ul>
											</li>}
											<li className="cut-off-rule"/>
											<li>
												<div className="payment-info-name">提交时间</div>
												<div className="payment-info-value">{submitTime}</div>
											</li>
                      {
                        docType === STOREDACCOUNTRECHARGE &&
												<li>
													<div className="payment-info-name">充值方</div>
													<div className="payment-info-value">{orderPartyName}</div>
												</li>
                      }
                      {
                        docType === CREDITACCOUNTREPAYMENT &&
												<li>
													<div className="payment-info-name">还款方</div>
													<div className="payment-info-value">{orderPartyName}</div>
												</li>
                      }
											<li>
												<div className="payment-info-name">单号</div>
												<div className="payment-info-value">{docNo}</div>
											</li>
                      {
                        paymentMemo &&
												<li>
													<div className="payment-info-name">付款备注</div>
													<div className="payment-info-value">{paymentMemo}</div>
												</li>
                      }
                      {
                        memo &&
												<li>
													<div className="payment-info-name">审单备注</div>
													<div className="payment-info-value">{memo}</div>
												</li>
                      }
										</ul>
                    {
                      docType === ORDERPAYMENT && relateOrderList && relateOrderList.length > 0 &&
                        <div className="relevant-order">
                          <div className="relevant-order-title">相关订货单</div>
                          <ul className="relevant-order-list">{
                            relateOrderList.slice(0, 20).map((item) => {
                              const { orderOrg, productSkuTotalCount, orderScheme, createdOn } = item;
                              const targetObj = this.searchTargetObj(item.docStatus, orderStatus);
                              return <li className="relevant-order-detail" key={item.oid} onClick={() => {
                                this.skipToOrderDetail(item.oid, orderScheme);
                              }}>
                                <div className="record-title">
                                  <div className="receipt-type">订单号：</div>
                                  <div className="receipt-number">{item.docNo}</div>
                                  <div className="receipt-status"
                                       style={{ color: targetObj.color }}>{targetObj.text}</div>
                                </div>
                                <ul className="relevant-order-info">
                                  <li><span>下单门店:</span><span>{orderOrg}</span></li>
                                  <li><span>商品数量:</span><span>{productSkuTotalCount}</span></li>
                                  <li><span>订货方案:</span><span>{orderScheme}</span></li>
                                  <li><span>下单时间:</span><span>{createdOn}</span></li>
                                </ul>
                              </li>
                            })
                          }
                          </ul>
                        </div>
                    }
                    {
                      docType === ORDERPAYMENT && relateFeeDocumentList && relateFeeDocumentList.length > 0 &&
                        <div className="expense-order">
                          <div className="expense-order-title">相关费用单</div>
                          <ul className="expense-order-list">{
                            relateFeeDocumentList.slice(0, 10).map((item) => {
                              return(
                                <li
                                  className="expense-order-detail"
                                  key={item.oid}
                                  onClick={() => {
                                    this.goToOrderDetail(item.docId);
                                  }}
                                >
                                  <div className="record-title">
                                    <div className="receipt-type">编码：</div>
                                    <div className="receipt-number">{item.code}</div>
                                    <div className="receipt-status" style={{ color: "#FF3030" }}>{item.docStatus}</div>
                                  </div>
                                  <ul className="relevant-order-info">
                                    <li><span>订货组织:</span><span>{item.orderOrgName}</span></li>
                                    <li><span>费用单类型:</span><span>{item.feeDocType}</span></li>
                                    <li><span>最后付款日期:</span><span>{DateUtils.toStringFormat(item.limitPaymentTime, "yyyy-MM-dd HH:mm:ss")}</span></li>
                                    <li><span>创建时间:</span><span>{DateUtils.toStringFormat(item.createTime, "yyyy-MM-dd HH:mm:ss")}</span></li>
                                    <li><span>审单时间:</span><span>{DateUtils.toStringFormat(item.confirmTime, "yyyy-MM-dd HH:mm:ss")}</span></li>
                                  </ul>
                                  {/*<SingleExpenseOrderInfo*/}
                                  {/*  order={{ ...item, isOverdue: false, isShowPaymentBtn: false}}*/}
                                  {/*  isHideAmountList={true}*/}
                                  {/*  isShowAmount={false}*/}
                                  {/*/>*/}
                                </li>
                              );
                            })
                          }
                          </ul>
                        </div>
                    }
                    {
                      docType === CREDITACCOUNTREPAYMENT && hasPermission &&
											<div className="skip-button" onClick={() => this.skipToAccount(accountId, "XYZH")}>
												<div className="skip-button-text">查看还款账户</div>
												<span className="skip-button-icon"><i className="scmIconfont scm-icon-jiantou-you"/></span>
											</div>
                    }
                    {
                      docType === STOREDACCOUNTRECHARGE && hasPermission &&
											<div className="skip-button" onClick={() => this.skipToAccount(accountId, "YECZ")}>
												<div className="skip-button-text">查看充值账户</div>
												<span className="skip-button-icon"><i className="scmIconfont scm-icon-jiantou-you"/></span>
											</div>
                    }
									</div>
                }
              </Spin>
              <GoHome/>
            </ContainerContent>
            : <NoAuthority /> : <div />
        }

      </Container>
    );
  }
}

export default OfflineTransferRecordDetail;

const Container = styled.div`// styled
  & {
    width: 100%;
    height: 100%;
    background-color: #f5f5f5;
    color: #2a2a2a;
    overflow: hidden;
  }
`;
const ContainerContent = styled.div`// styled
  & {
    width: 100%;
    height: 100%;
    background-color: rgba(236, 246, 255, 1);
    color: #2a2a2a;
    font-size: 12px;
    overflow: auto;
    -webkit-overflow-scrolling: touch;
    overflow-scrolling: touch;

    .offline-transfer-record-content {
      width: 100%;
      height: 100%;
      padding: 16px;
      >.order-info {
        padding: 16px 16px 12px;
        background-color: rgba(255, 255, 255, 1);
        box-shadow: 0px 0px 3px 0px rgba(0, 0, 0, 0.05);
        border-radius: 8px;
        background-image: url(https://order.fwh1988.cn:14501/static-img/scm/icon-card-bg.png);

        .order-info-top {
          width: 100%;
          height: 44px;
          position: relative;

          > span:first-of-type {
            display: inline-block;
            width: 44px;
            height: 44px;
            float: left;
            line-height: 44px;

            > i {
              width: 44px;
              height: 44px;
              font-size: 43px;
              color: #307DCD;
            }
          }

          > span:nth-of-type(2) {
            display: inline-block;
            float: left;
            margin-left: 12px;

            .order-name {
              color: #666666;
              line-height: 12px;
            }

            .order-money {
              color: #307DCD;
              font-size: 20px;
              line-height: 20px;
              margin-top: 8px;
            }
          }
        }

        .order-info-buttom {
          width: 100%;
          height: 30px;
          margin-top: 4px;
          padding-left: 52px;

          .order-status {
            height: 100%;
            line-height: 30px;
            display: inline-block;
          }

          .withdraw-button {
            height: 30px;
            width: 72px;
            color: #307DCD;
            text-align: center;
            line-height: 30px;
            float: right;
            font-size: 12px;
            border: none !important;
            border: 1px solid #307DCD !important;
            border-radius: 5px;
          }
        }
      }

      .payment-info {
        background: rgba(255, 255, 255, 1);
        box-shadow: 0px 0px 3px 0px rgba(0, 0, 0, 0.05);
        border-radius: 8px;
        list-style: none;
        margin: 16px 0 0 0;
        width: 100%;
        height: 100%;
        padding: 8px 16px;

        > li {
          height: auto;
          line-height: 28px;
          overflow: hidden;
          padding-left: 71px;
          position: relative;

          .payment-info-name {
            position: absolute;
            top: 0;
            left: 0;
            color: rgba(89, 89, 89, 1);
          }

          .payment-info-value {
            float: right;
            color: rgba(47, 47, 47, 1);
          }

          .img-list {
            margin: 0;
            padding: 0;
            list-style: none;

            > li {
              display: inline-block;
              float: right;
              width: 48px;
              height: 48px;
              margin: 0 0 16px 16px;

              > img {
                display: inline-block;
                width: 100%;
                height: 100%;
              }
            }
          }
        }
        .cut-off-rule{
          width: 100%;
          background-color: #D8D8D8;
          height: 1px;
          box-shadow: 0px 0px 0px 0px rgba(216,216,216);
          margin-bottom: 4px;
        }
      }

      .relevant-order, .expense-order {
        width: 100%;
        height: auto;
        margin-top: 16px;

        .relevant-order-title, .expense-order-title {
          width: 100%;
          font-size: 13px;
          line-height: 13px;
          color: #2F2F2F;
          margin-bottom: 8px;
          padding-left: 16px;
        }

        > .relevant-order-list, .expense-order-list {
          list-style: none;
          width: 100%;
          height: auto;
          margin: 0;
          padding: 0;
          .relevant-order-detail, .expense-order-detail {
            background: rgba(255, 255, 255, 1);
            box-shadow: 0px 0px 3px 0px rgba(0, 0, 0, 0.05);
            border-radius: 8px;
            padding: 0 16px;
            width: 100%;
            height: auto;
            margin-bottom: 16px;

            .record-title {
              width: 100%;
              height: 40px;
              padding: 0;

              .receipt-type, .receipt-number, .receipt-status {
                display: inline-block;
                float: left;
                line-height: 40px;
                font-size: 13px;
                color: #2F2F2F;
              }

              .receipt-status {
                float: right;
                font-size: 13px;
              }
            }

            .relevant-order-info {
              list-style: none;
              padding: 8px 0;
              border-top: 1px solid #D8D8D8;

              > li {
                line-height: 20px;
                position: relative;
                padding-left: 56px;
                height: auto;

                > span:first-of-type {
                  position: absolute;
                  left: 0;
                  top: 0;
                }
              }
            }
          }
          .expense-order-detail{
            .relevant-order-info{
              >li{
                padding-left: 82px;
              }
            }
          }
          .relevant-order-detail:last-of-type {
            margin-bottom: 0;
          }
        }
      }

      .skip-button {
        background: rgba(255, 255, 255, 1);
        box-shadow: 0px 0px 3px 0px rgba(0, 0, 0, 0.05);
        border-radius: 6px;
        padding: 0 16px;
        height: 40px;
        margin-top: 16px;

        .skip-button-text {
          font-size: 13px;
          line-height: 40px;
          color: rgba(47, 47, 47, 1);
          float: left;
        }

        .skip-button-icon {
          float: right;
          width: 16px;
          height: 40px;
          line-height: 40px;

          > i {
            font-size: 12px;
            color: #999999;
          }
        }
      }
    }
  }
`;
const ContainerBackGroud = styled.div`// styled
  & {
    width: 100%;
    height: 100%;
    background-color: rgba(236, 246, 255, 1);
    z-index: -1;

    .back-ground-header {
      width: 100%;
      height: 80px;
      background-color: rgba(81, 118, 172, 1);
    }
  }
`;
