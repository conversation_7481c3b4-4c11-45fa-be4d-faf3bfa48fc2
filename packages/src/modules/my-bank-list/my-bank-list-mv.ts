import { autowired, bean } from "@classes/ioc/ioc";
import { action, observable } from "mobx";
import { $MyInfoService } from "../../classes/service/$my-info-service";
import { $MessageCateList } from "../../classes/entity/$message-cate-list";

@bean(MyBankListMv)
export class MyBankListMv {

  @autowired($MyInfoService)
  public $myInfoService: $MyInfoService;

  @observable public isSpin: boolean = false;

  @observable public messageCateList: $MessageCateList[] = [];

  @action
  public showSpin() {
    this.isSpin = true;
  }

  @action
  public hideSpin() {
    this.isSpin = false;
  }
}
