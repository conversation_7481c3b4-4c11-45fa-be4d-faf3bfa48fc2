import * as React from "react";
import { observer } from "mobx-react";
import styled from "styled-components";
import { autowired } from "@classes/ioc/ioc";
import { ChooseBankCardMv } from "../../components/choose-bank-card-modal/choose-bank-card-mv";
import { Modal } from "antd-mobile";
import { Spin } from "antd";
import { $AppStore, AppStoreKey } from "../../classes/stores/app-store-mv";
import { getUrlParam } from "../../classes/utils/UrlUtils";

declare let window: any;
const alert = Modal.alert;

@observer
class MyBankList extends React.Component<any, any> {

  @autowired($AppStore)
  public $AppStore: $AppStore;
  @autowired(ChooseBankCardMv)
  public $myMv: ChooseBankCardMv;
  // 离开记录滚动高度
  public saveData = () => {
    this.$myMv.scrollHeight = $(".my-bank-list").scrollTop();
    this.$AppStore.savePageMv(AppStoreKey.MYBANKLIST, this.$myMv);
  }
  // public componentWillUnmount(): void {
  //   this.saveData();
  // }
  public componentDidMount() {
    document.title = "银行卡列表";
    const bindCardSuccess = getUrlParam("bindCardSuccess");
    if (bindCardSuccess === "true") {
      this.$AppStore.clearPageMv(AppStoreKey.MYBANKLIST);
    }
    setTimeout(() => {
      const oldData = this.$AppStore.getPageMv(AppStoreKey.MYBANKLIST)
      if (oldData) {
        this.$myMv.queryOldData(JSON.parse(oldData));
        this.$AppStore.clearPageMv(AppStoreKey.MYBANKLIST);
        $(".my-bank-list").scrollTop(this.$myMv.scrollHeight);
      } else {
        this.$myMv.clearMVData();
        this.initPage();
      }
    }, 50);
  }
  public initPage = () => {
    this.$myMv.showSpin();
    this.$myMv.loadPaymentTpBankList().then(() => {
      $(".my-bank-list").scrollTop(0);
    });
  }
  public windowSkipPage = (url) => {
    this.saveData();
    setTimeout(() => {
      window.location.href = url;
    }, 50);
  }
  public gatewayBindingCardAddress = () => {
    const { gatewayBindingCardAddress } = this.$myMv;
    this.windowSkipPage(`${gatewayBindingCardAddress}&redirectUrl=${document.location.protocol}//${document.domain}:${window.location.port}/scm/my-bank-list?bindCardSuccess=true`);
  }

  public unbindBank = (bank) => {
    alert("确定解除绑定该银行卡？", "", [
      {
        text: "取消", onPress: () => {
          console.log("取消");
        },
      },
      {
        text: "确定", onPress: () => {
          this.$myMv.showSpin();
          const params = {
            bankcardId: bank.id,
          };
          this.$myMv.unbindBankCard(params).then(() => {
            this.initPage();
          }).catch(() => {
            this.$myMv.hideSpin();
          });
        },
      },
    ]);
  }

  public render() {
    const { bankList, isSpin } = this.$myMv;
    return (
      <MyBankListWrapper className="my-bank-list">
        <Spin spinning={isSpin}>
          <BankList>
            {
              bankList && bankList.length > 0 && bankList.map((bank) => {
                return (
                  <BankItem key={bank.id}>
                    <div>
                      <p>
                        <i className={"scmIconfont scm-yinhangka"}/>
                        <span>{bank.bankName && bank.bankName.length > 15 ? bank.bankName.slice(0, 15) + "..." : bank.bankName}</span>
                        <br/>
                        <span className={"bankCardNo"}>{bank.bankCardTypeName}</span>
                      </p>
                      <p style={{ float: "right" }}>{"****" + bank.bankCardNo}</p>
                    </div>
                    <UnbindBtn onClick={() => this.unbindBank(bank)}>
                      <p>解除绑定</p>
                    </UnbindBtn>
                  </BankItem>
                );
              })
            }
          </BankList>
          <AddBtn onClick={this.gatewayBindingCardAddress}>
            <i className={"scmIconfont scm-qia"}/>
            添加银行卡
          </AddBtn>
        </Spin>
      </MyBankListWrapper>
    );
  }
}

export default MyBankList;

const MyBankListWrapper = styled.div`// styled
  & {
    width: 100%;
    height: ${document.documentElement.clientHeight}px;
    background: #F2F2F2;
    overflow-y: auto;
    padding: 12px;
    p {
      margin-bottom: 0;
    }
  }
`;

const AddBtn = styled.div`// styled
  & {
    width: 100%;
    height: 46px;
    background: #FFFFFF;
    padding: 11px 14px;
    box-shadow: 0px 2px 6px 0px rgba(0, 0, 0, 0.05);
    border-radius: 8px;
    font-size: 14px;
    font-family: SourceHanSansCN-Regular, SourceHanSansCN;
    font-weight: 400;
    color: rgba(47, 47, 47, 1);
    .scm-qia {
      color: #52C41A;
      margin-right: 8px;
    }
  }
`;

const BankList = styled.div`// styled
  & {
    width: 100%;
    height: auto;
  }
`;

const BankItem = styled.div`// styled
  & {
    width: 100%;
    height: 90px;
    background: #fff url(https://order.fwh1988.cn:14501/static-img/scm/img_bg_returned_status.png) no-repeat;
    background-size: contain;
    border-radius: 8px;
    box-shadow: 0px 2px 6px 0px rgba(0, 0, 0, 0.05);
    margin-bottom: 12px;
    padding: 12px;
    font-size: 14px;
    font-family: SourceHanSansCN-Medium, SourceHanSansCN;
    font-weight: 500;
    color: rgba(47, 47, 47, 1);
    .scm-yinhangka {
      margin-right: 8px;
      color: #999999;
      position: relative;
      top: 0.5px;
    }
    .bankCardNo {
      font-size: 13px;
      font-family: SourceHanSansCN-Regular, SourceHanSansCN;
      font-weight: 400;
      color: rgba(89, 89, 89, 1);
      margin-left: 24px;
    }
    > div {
      > p {
        display: inline-block;
      }
    }
  }
`;

const UnbindBtn = styled.div`// styled
  & {
    width: 72px;
    height: 30px;
    float: right;
    font-size: 12px;
    font-family: SourceHanSansCN-Normal, SourceHanSansCN;
    font-weight: 400;
    color: rgba(48, 125, 205, 1);
    text-align: center;
    line-height: 30px;
    position: relative;
    top: -8px;
    cursor: pointer;
    :after {
      content: "  ";
      position: absolute;
      left: 0;
      top: 0;
      z-index: 1;
      width: 200%;
      height: 200%;
      border: 1px solid #1890FF;
      border-radius: 3px;
      padding: 3px 6px;
      -webkit-transform-origin: 0 0;
      transform-origin: 0 0;
      -webkit-transform: scale(.5, .5);
      transform: scale(.5, .5);
    }
  }
`;
