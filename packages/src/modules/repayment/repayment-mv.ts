import { autowired, bean } from "@classes/ioc/ioc";
import { action, observable } from "mobx";
import { $MyInfoService } from "../../classes/service/$my-info-service";
import { $ComponentService } from "../../classes/service/$component-service";
import { $File } from "../../classes/entity/$file";
import { $Product } from "../../classes/entity/$product";
import { $PaymentModeItem } from "../../classes/entity/payment-mode-item";
import { $PaymentType } from "../../classes/const/$payment-type";

@bean($RepaymentMv)
export class $RepaymentMv {
  @autowired($MyInfoService)
  public $myInfoService: $MyInfoService;
  @autowired($ComponentService)
  public $ComponentService: $ComponentService;

  @observable public paymentModeList: any[];

  @observable public accountId: number;

  @observable public balanceAmount: number;

  @observable public isSpin: boolean = false;

  @observable public orderPartyId: number;

  @observable public pics: $File[] = [];

  @observable public capitalAccountList: any[] = [];

  @observable public selectCapitalAccountInfo: object = {
    bankName: "",
    accountCode: "",
    bankAccountName: "",
    capitalAccountId: "",
  };

  @action
  public showSpin() {
    this.isSpin = true;
  }

  @action
  public hideSpin() {
    this.isSpin = false;
  }

  @action
  public clearPics() {
    this.pics = [];
  }

  @action
  public fetchRepayment() {
    return this.$myInfoService.loadRepaymentPage().then((data) => {
      this.accountId = data.accountId;
      this.orderPartyId = data.orderPartyId;
      data.paymentModeList.map((v, index) => {
        if (v.code === $PaymentType.ALLINPAY) {
          if (v.paymentProductList) {
            v.paymentProductList.map((product) => {
              product.code = product.paymentCode;
              product.name = product.paymentName;
              data.paymentModeList.push(product);
            });
          }
        }
      });
      console.log(data.paymentModeList);
      this.paymentModeList = data.paymentModeList.map((mode) => new $PaymentModeItem(mode));
      this.hideSpin();
    });
  }

  @action
  public fetchCapitalAccount(params) {
    return this.$myInfoService.queryCapitalAccount(params).then((data) => {
      const { capitalAccountList } = data;
      this.capitalAccountList = capitalAccountList.map((capitalAccount) => new $Product(capitalAccount));
      return data;
    });
  }

  @action
  public setPics(pics: any[]) {
    this.pics = pics;
  }

  @action
  public submitRepay(params) {
    return this.$myInfoService.toRepayment(params);
  }
  @action
  public getCreditAccountRepayment(params) {
    return this.$myInfoService.getCreditAccountRepayment(params);
  }

  @action
  public setIsShow(index) {
    this.paymentModeList.map((v) => {
      v.isShow = false;
    });
    this.paymentModeList[index].isShow = true;
  }
  @action
  public checkTongLianPaymentStatus(params) {
    return this.$ComponentService.checkTongLianPaymentStatus(params);
  }
  @action
  public checkContinuePay(params) {
    return this.$ComponentService.checkContinuePay(params);
  }
  @action
  public cancelTongLianPay(params) {
    return this.$ComponentService.cancelTongLianPay(params);
  }
}
