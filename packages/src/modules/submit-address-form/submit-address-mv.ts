import {bean} from "@classes/ioc/ioc";
import {action, observable} from "mobx";

@bean($AddressMv)
export class $AddressMv {
  @observable public contactPerson: string = "";
  @observable public phoneNumber: string = "";
  @observable public location: string = "";
  @observable public addressId: any;
  @observable public oid: any;
  @observable public postalCode: any;
  @observable public contactPersonId: any;
  @observable public onlySelect: boolean = false;
  @observable public tag: string = "";
  @observable public type: string = "";
  @observable public isSelectedOnce: boolean = false;
  @observable public isFormPay: boolean = false;
  @observable public selectedAddress: object = {
    contactPerson: "",
    phoneNumber: "",
    location: "",
    addressId: "",
    isDefault: "",
    postalCode: "",
  };

  @action
  public setFormData(receiveUserName, receiveUserphoneNumber, detailAddress, origin, contactPersonId, postalCode) {
    this.contactPerson = receiveUserName;
    this.phoneNumber = receiveUserphoneNumber;
    this.location = detailAddress;
    this.addressId = origin;
    this.contactPersonId = contactPersonId;
    this.postalCode = postalCode;
  }

  @action
  public getFormData() {
    return this;
  }

  @action
  public setContactPerson(PersonName) {
    this.contactPerson = PersonName;
  }

  @action
  public setPostalCode(postalCode) {
    this.postalCode = postalCode;
  }

  @action
  public setIsSelectedOnce(isSelectedOnce) {
    this.isSelectedOnce = isSelectedOnce;
  }

  @action
  public setSelectedAddress(selectedAddress) {
    this.selectedAddress = selectedAddress;
  }

  @action
  public setPhoneNumber(phoneNumber) {
    this.phoneNumber = phoneNumber;
  }

  @action
  public setAddress(detailAddress) {
    this.location = detailAddress;
  }

  @action
  public setAddressId(addressId) {
    this.addressId = addressId;
  }

  @action
  public setTag(tag) {
    this.tag = tag;
  }

  @action
  public setOid(oid) {
    this.oid = oid;
  }

  @action
  public setCustomerEmployee(name, phoneNumber, contactPersonId) {
    this.contactPerson = name;
    this.phoneNumber = phoneNumber;
    this.contactPersonId = contactPersonId;
  }

  @action
  public setOnlySelect(onlySelect) {
    this.onlySelect = onlySelect;
  }

  @action
  public setNewAddress() {
    this.contactPerson = null;
    this.phoneNumber = null;
    this.location = null;
    this.addressId = null;
    this.contactPersonId = null;
    this.oid = null;
    this.tag = null;
    this.type = "create";
  }

  @action
  public setType(type) {
    this.type = type;
  }
}
