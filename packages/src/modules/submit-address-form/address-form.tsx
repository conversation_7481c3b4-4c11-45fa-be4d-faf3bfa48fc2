import { autowired } from "@classes/ioc/ioc";
import { Button, InputItem, List, Picker, TextareaItem } from "antd-mobile";
import { observer } from "mobx-react";
import React from "react";
import { withRouter } from "react-router";
import styled from "styled-components";
import { $CartMv } from "../shop-cart/cart-mv";
import { $AddressMv } from "./submit-address-mv";

declare let window: any;
const Item = List.Item;

@withRouter
@observer
class AddressForm extends React.Component<any, any> {
  @autowired($CartMv)
  public $CartMv: $CartMv;

  @autowired($AddressMv)
  public $AddressMv: $AddressMv;

  constructor(props) {
    super(props);
    this.state = {};

  }

  public componentDidMount() {
    document.title = "地址表单";
  }

  public initPage() {
  }

  public onChange = (receiveUserName, receiveUserMobile, detailAddress, origin) => {
    this.$AddressMv.setFormData(receiveUserName, receiveUserMobile, detailAddress, origin);
  }

  public onSubmit = () => {
    this.props.history.push({ pathname: `${window.ENV.sitePath}/submit/order` });
  }

  public render() {
    const { contactPersonName, mobile, address, addressId } = this.$AddressMv;
    return (
      <Wrapper>
        <List>
          <InputItem
            value={contactPersonName}
            onChange={(v) => this.onChange(v, mobile, address, addressId)}>收货人</InputItem>
          <InputItem type="phone"
                     value={mobile}
                     onChange={(v) => this.onChange(contactPersonName, v, address, addressId)}>手机号</InputItem>
          <TextareaItem value={address}
                        title="详细地址"
                        autoHeight={true}
                        labelNumber={7}
                        onChange={(v) => this.onChange(contactPersonName, mobile, v, addressId)}/>
          <Picker
            title="选择地区"
            extra=""
            data={[]}
            value={addressId}
            onChange={(v) => this.onChange(contactPersonName, mobile, address, v)}
            onOk={(v) => this.onChange(contactPersonName, mobile, address, v)}>
            <Item>地区</Item>
          </Picker>
          <Item>
            <Button type="primary" onClick={this.onSubmit}>保存</Button>
          </Item>
        </List>

      </Wrapper>
    );
  }
}

export default AddressForm;

const Wrapper = styled.div`// styled
  & {

  }
`;
