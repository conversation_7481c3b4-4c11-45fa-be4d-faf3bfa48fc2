import * as React from "react";
import styled from "styled-components";
import { observer } from "mobx-react";
import { withRouter } from "react-router";
import { $PaymentModeType } from "../../classes/const/$payment-mode-type";
import { SITE_PATH } from "../app";
import { $paymentRedirectType } from "../../classes/const/$payment-redirect-type";
import { getQueryString, getUrlParam } from "../../classes/utils/UrlUtils";
import { post } from "../../helpers/ajax-helpers";

import "./style.less";
import { autowired } from "@classes/ioc/ioc";
import { $AppStore, AppStoreKey } from "../../classes/stores/app-store-mv";

declare let window: any;

@withRouter
@observer
export default class PaymentResultShow extends React.Component<any, any> {
  @autowired($AppStore)
  public $AppStore: $AppStore;
  constructor(props) {
    super(props);
    this.state = {
      fromWhere: "",
      feeDocumentId: null,
      canPartialPay: false,
      continueShoppingButton: {
        cb: this.continueShopping,
        name: "继续购物",
        buttonStyle: "blue",
      },
      seeOrderButton: {
        cb: this.seeOrder,
        name: "查看订单",
        buttonStyle: "white",
      },
      goBackBalanceAccountButton: {
        cb: this.goBackBalanceAccount,
        name: "回到余额账户",
        buttonStyle: "blue",
      },
      goBackRepaymentAccountButton: {
        cb: this.goBackRepaymentAccount,
        name: "回到我的页面",
        buttonStyle: "blue",
      },
      payAgainButton: {
        cb: this.payAgain,
        name: "重新支付",
        buttonStyle: "blue",
      },
      refreshPaymentResultButton: {
        cb: this.refreshPaymentResult,
        name: "刷新支付结果",
        buttonStyle: "white",
      },
      paymentResultInfo: {
        code: "", // "success", "failure", "waiting"
        introduce: null,
        imgUrl: "",
        operationButtonList: [],
      },
    };
  }

  public componentDidMount(): void {
    document.title = "支付结果页";
    // const { result } = this.props.match.params;
    // console.log("999999----look", this.props.match.params.paymentMode);
    // console.log("transStatus", window.location, window.location.href);
    const { paymentMode } = this.props.match.params;
    const paymentMessage = JSON.parse(localStorage.getItem("paymentMessage"));
    console.log("paymentMessage",paymentMessage);
    const { fromWhere, feeDocumentId, canPartialPay } = paymentMessage || {};
    console.log("feeDocumentId", feeDocumentId);
    if (feeDocumentId) {
      const { seeOrderButton, continueShoppingButton } = this.state;
      this.setState({
        seeOrderButton: { ...seeOrderButton, name: "查看费用单" },
        continueShoppingButton: { ...continueShoppingButton, name: "返回工作台" },
      });
    }
    this.setState({ fromWhere, feeDocumentId, canPartialPay }, () => {
      console.log(fromWhere);
      this.showDifferentResoult(paymentMode);
    });
  }

  public showDifferentResoult = (paymentMode) => {
    switch (paymentMode) {
      case $PaymentModeType.BANKTRANSFER:
        if (this.state.fromWhere === "all-order-payment" || this.state.fromWhere === "single-order-payment") {
          this.setState({
            paymentResultInfo: {
              code: "",
              imgUrl: "https://order.fwh1988.cn:14501/static-img/scm/img_successful.png",
              introduce: `<div><span>提交成功</span><span>请等待财务的审核结果</span></div>`,
              operationButtonList: [
                this.state.seeOrderButton,
                this.state.continueShoppingButton,
              ],
            },
          });
        } else if (this.state.fromWhere === "recharge") {
          this.setState({
            paymentResultInfo: {
              code: "",
              imgUrl: "https://order.fwh1988.cn:14501/static-img/scm/img_successful.png",
              introduce: `<div><span>提交成功</span><span>请等待财务的审核结果</span></div>`,
              operationButtonList: [
                this.state.goBackBalanceAccountButton,
              ],
            },
          });
        } else if (this.state.fromWhere === "repayment") {
          this.setState({
            paymentResultInfo: {
              code: "",
              imgUrl: "https://order.fwh1988.cn:14501/static-img/scm/img_successful.png",
              introduce: `<div><span>提交成功</span><span>请等待财务的审核结果</span></div>`,
              operationButtonList: [
                this.state.goBackRepaymentAccountButton,
              ],
            },
          });
        }else if(this.state.fromWhere === "expense-node-list"){
          this.setState({
            paymentResultInfo: {
              code: "success",
              imgUrl: "https://order.fwh1988.cn:14501/static-img/scm/img_successful.png",
              introduce: `<div><span>支付成功</span></div>`,
              operationButtonList: [
                this.state.seeOrderButton,
                this.state.continueShoppingButton,
              ],
            },
          });
        }
        break;
      case $PaymentModeType.ALLINPAY:
        const result = getUrlParam("transStatus");
        if (this.state.fromWhere === "recharge") { // 通联充值
          this.allInPayRecharge(result);
        } else if (this.state.fromWhere === "repayment") { // 通联还款
          this.allInPayRepayment(result);
        } else if (this.state.fromWhere === "single-order-payment") { // 通联单笔
          this.allInPaySinglePayment(result);
        } else if (this.state.fromWhere === "all-order-payment") { // 通联合并
          this.allInPayCombinePayment(result);
        }else if(this.state.fromWhere === "expense-node-list"){//费用单合并
          this.allInPayCombinePayment(result);
        }
        break;
      case $PaymentModeType.STOREDVALUE:
        if (this.state.fromWhere === "repayment") {
          this.setState({
            paymentResultInfo: {
              code: "",
              imgUrl: "https://order.fwh1988.cn:14501/static-img/scm/img_successful.png",
              introduce: `<div><span>支付成功</span></div>`,
              operationButtonList: [
                this.state.goBackRepaymentAccountButton,
              ],
            },
          });
        } else if (this.state.fromWhere === "all-order-payment" || this.state.fromWhere === "single-order-payment") {
          this.setState({
            paymentResultInfo: {
              code: "",
              imgUrl: "https://order.fwh1988.cn:14501/static-img/scm/img_successful.png",
              introduce: `<div><span>支付成功</span></div>`,
              operationButtonList: [
                this.state.seeOrderButton,
                this.state.continueShoppingButton,
              ],
            },
          });
        }
        break;
      default:
        this.setState({
          paymentResultInfo: {
            code: "success",
            imgUrl: "https://order.fwh1988.cn:14501/static-img/scm/img_successful.png",
            introduce: `<div><span>支付成功</span></div>`,
            operationButtonList: [
              this.state.seeOrderButton,
              this.state.continueShoppingButton,
            ],
          },
        });
        break;
    }
  }

  public removeCombinePaymentSession = () => {
    const { paymentMode } = this.props.match.params;
    if (paymentMode === $PaymentModeType.ALLINPAY) {
      sessionStorage.removeItem("$myMv");
      sessionStorage.removeItem("state");
    }
    const paymentMessage = {
      destination: "",
      fromWhere: "all-order-payment",
    }
    localStorage.setItem("paymentMessage", JSON.stringify(paymentMessage));
  }

  public seeOrder = () => {
    const { fromWhere, feeDocumentId } = this.state;
    console.log("fromWhere", fromWhere);
    if (fromWhere === "all-order-payment") {
      this.removeCombinePaymentSession();
      this.$AppStore.clearPageMv(AppStoreKey.ALLSHOPORDERLISTSTORE);
      this.props.history.push({
        pathname: `/${SITE_PATH}/shop/all-shop-order-list`,
        state: { status: "ALL", orderPartyRange: "All" },
      });
    }
    if(fromWhere === "expense-node-list"){
      this.props.history.push({
        pathname: `/${SITE_PATH}/expense-node-list/Payment/null`,
      });
    }
    if (fromWhere === "single-order-payment") {
      const paymentMessage = {
        destination: "",
        fromWhere: "single-order-payment",
        feeDocumentId,
      }
      localStorage.setItem("paymentMessage", JSON.stringify(paymentMessage));
      this.$AppStore.clearPageMv(AppStoreKey.SINGLEPAYMENTSTORAGE);
      this.$AppStore.clearPageMv(AppStoreKey.SINGLESHOPORDERLISTSTORE);
      if (feeDocumentId) {
        this.props.history.push({
          pathname: `/${SITE_PATH}/expense-node-detail/${feeDocumentId}`,
        });
      } else {
        this.props.history.push({
          pathname: `/${SITE_PATH}/shop/order-list`,
          state: { status: "ALL" },
        });
      }
    }
  }

  public continueShopping = () => {
    console.log("continueShopping");
    const { fromWhere, feeDocumentId } = this.state;
    if (fromWhere === "all-order-payment" || feeDocumentId) {
      if (!feeDocumentId) {
        this.removeCombinePaymentSession();
      }
      this.$AppStore.clearPageMv(AppStoreKey.ORDERPARTYSELECTION);
      this.props.history.push({ pathname: `/${SITE_PATH}/select` });
    } else if (fromWhere === "single-order-payment") {
      this.$AppStore.queryShopOverdue("", () => {
        const paymentMessage = {
          destination: "",
          fromWhere: "single-order-payment",
        }
        localStorage.setItem("paymentMessage", JSON.stringify(paymentMessage));
        this.$AppStore.clearPageMv(AppStoreKey.SHOPLIST);
        this.props.history.push({ pathname: `/${SITE_PATH}/shop/list` });
      });
    }
  }

  public goBackBalanceAccount = () => {
    console.log("goBackBalanceAccount");
    const paymentMessage = JSON.parse(localStorage.getItem("paymentMessage"));
    console.log(paymentMessage);
    const { accountId } = paymentMessage;
    // localStorage.removeItem("paymentMessage");
    const paymentMessage1 = {
      destination: "",
      fromWhere: "recharge",
      accountId,
    }
    localStorage.setItem("paymentMessage", JSON.stringify(paymentMessage1));
    this.$AppStore.clearPageMv(AppStoreKey.ACCOUNTINFO);
    window.location.href = `/${SITE_PATH}/account-info/${accountId}/YECZ`;
  }

  public goBackRepaymentAccount = () => {
    this.$AppStore.clearPageMv(AppStoreKey.ACCOUNTINFOLIST);
    window.location.href = `/${SITE_PATH}/my?agencyParams=agency`;
  }

  public payAgain = () => {
    console.log("payAgain");
    const paymentMessage = JSON.parse(localStorage.getItem("paymentMessage"));
    console.log(paymentMessage);
    const { fromWhere } = this.state;
    const { accountId, orderId, backSource, surplusAmount } = paymentMessage;
    if (fromWhere === "recharge") {
      window.location.href = `/${SITE_PATH}/my-recharge/${accountId ? accountId : ""}?comeBack=true`;
    } else if (fromWhere === "repayment") {
      window.location.href = `/${SITE_PATH}/my-repayment/${surplusAmount ? surplusAmount : ""}?comeBack=true`;
    } else if (fromWhere === "single-order-payment") {
      const { canPartialPay, feeDocumentId } = this.state;
      if (feeDocumentId > 0) {
        window.location.href = `/${SITE_PATH}/submit/order-payment/:record/${orderId ? orderId : ""}/:showPaymentBtn?feeDocumentId=${orderId ? orderId : ""}&canPartialPay=${canPartialPay}&backSource=${backSource ? backSource : ""}&comeBack=true`;
      } else {
        window.location.href = `/${SITE_PATH}/submit/order-payment/34657/${orderId ? orderId : ""}/notPay?backSource=${backSource ? backSource : ""}&comeBack=true`;
      }
    } else if (fromWhere === "all-order-payment") {
      window.location.href = `/${SITE_PATH}/shop/all-order-payment?comeBack=true&reLoad=true`;
    } else if (fromWhere === "expense-node-list"){
      window.location.href = `/${SITE_PATH}/shop/all-order-payment?payRange=All&comeBack=true&docType=FeeDocument&reLoad=true`;
    }
  }

  public refreshPaymentResult = () => {
    const orderNo = getUrlParam("orderNo");
    const bizOrderNo = getUrlParam("bizOrderNo");
    const params = {
      bizOrderNo,
      orderNo,
    }
    post("/integration/scm/paygateway/transstatusrefresh", params).then((res) => {
      const { transStatus, message } = res;
      const href = window.location.href.replace(`transStatus=${getUrlParam("transStatus")}`, `transStatus=${transStatus}`).replace(`retMsg=${getUrlParam("retMsg")}}`, `retMsg=${message}`);
      console.log("refreshPaymentResult", href);
      window.location.href = href;
    });
  }

  public allInPayCombinePayment = (result) => {
    console.log(result);
    // 这里根据跳转返回结果生成条件
    if (result === $paymentRedirectType.SUCCESS) {
      this.setState({
        paymentResultInfo: {
          code: result,
          imgUrl: "https://order.fwh1988.cn:14501/static-img/scm/img_successful.png",
          introduce: `<div><span>支付成功</span></div>`,
          operationButtonList: [
            this.state.seeOrderButton,
            this.state.continueShoppingButton,
          ],
        },
      });
    } else if (result === $paymentRedirectType.FAIL || result === $paymentRedirectType.CLOSE) {
      this.setState({
        paymentResultInfo: {
          code: result,
          imgUrl: "https://order.fwh1988.cn:14501/static-img/scm/img_failure.png",
          introduce: this.renderFailComponent(),
          operationButtonList: [
            this.state.payAgainButton,
            this.state.seeOrderButton,
          ],
        },
      });
    } else if (result === $paymentRedirectType.APPLIED || result === $paymentRedirectType.INIT || result === $paymentRedirectType.PENDING) {
      this.setState({
        paymentResultInfo: {
          code: result,
          imgUrl: "https://order.fwh1988.cn:14501/static-img/scm/img_waiting.png",
          introduce: `<div><span>请耐心等待支付结果</span><span>请勿重复支付</span></div>`,
          operationButtonList: [
            this.state.refreshPaymentResultButton,
            this.state.seeOrderButton,
            this.state.continueShoppingButton,
          ],
        },
      });
    }
  }

  public renderFailComponent = () => {
    const retMsg = getQueryString("retMsg");
    console.log(retMsg);
    return `<div>
        <span>支付失败</span>
        <span>支付遇到问题，请重新尝试</span>
        <span class='retMsg'>失败原因：${retMsg && retMsg.length > 49 ? retMsg.slice(0, 49) + "..." : retMsg}</span>
      </div>`;
  }

  public allInPayRecharge = (result) => {
    console.log(result);
    // 这里根据跳转返回结果生成条件
    if (result === $paymentRedirectType.SUCCESS) {
      this.setState({
        paymentResultInfo: {
          code: result,
          imgUrl: "https://order.fwh1988.cn:14501/static-img/scm/img_successful.png",
          introduce: `<div><span>支付成功</span></div>`,
          operationButtonList: [
            this.state.goBackBalanceAccountButton,
          ],
        },
      });
    } else if (result === $paymentRedirectType.FAIL || result === $paymentRedirectType.CLOSE) {
      this.setState({
        paymentResultInfo: {
          code: result,
          imgUrl: "https://order.fwh1988.cn:14501/static-img/scm/img_failure.png",
          introduce: this.renderFailComponent(),
          operationButtonList: [
            this.state.payAgainButton,
            this.state.goBackBalanceAccountButton,
          ],
        },
      });
    } else if (result === $paymentRedirectType.APPLIED || result === $paymentRedirectType.INIT || result === $paymentRedirectType.PENDING) {
      this.setState({
        paymentResultInfo: {
          code: result,
          imgUrl: "https://order.fwh1988.cn:14501/static-img/scm/img_waiting.png",
          introduce: `<div><span>请耐心等待支付结果</span><span>请勿重复支付</span></div>`,
          operationButtonList: [
            this.state.refreshPaymentResultButton,
            this.state.goBackBalanceAccountButton,
          ],
        },
      });
    }
  }

  public allInPayRepayment = (result) => {
    console.log(result);
    // 这里根据跳转返回结果生成条件
    if (result === $paymentRedirectType.SUCCESS) {
      this.setState({
        paymentResultInfo: {
          code: result,
          imgUrl: "https://order.fwh1988.cn:14501/static-img/scm/img_successful.png",
          introduce: `<div><span>支付成功</span></div>`,
          operationButtonList: [
            this.state.goBackRepaymentAccountButton,
          ],
        },
      });
    } else if (result === $paymentRedirectType.FAIL || result === $paymentRedirectType.CLOSE) {
      this.setState({
        paymentResultInfo: {
          code: result,
          imgUrl: "https://order.fwh1988.cn:14501/static-img/scm/img_failure.png",
          introduce: this.renderFailComponent(),
          operationButtonList: [
            this.state.payAgainButton,
            this.state.goBackRepaymentAccountButton,
          ],
        },
      });
    } else if (result === $paymentRedirectType.APPLIED || result === $paymentRedirectType.INIT || result === $paymentRedirectType.PENDING) {
      this.setState({
        paymentResultInfo: {
          code: result,
          imgUrl: "https://order.fwh1988.cn:14501/static-img/scm/img_waiting.png",
          introduce: `<div><span>请耐心等待支付结果</span><span>请勿重复支付</span></div>`,
          operationButtonList: [
            this.state.refreshPaymentResultButton,
            this.state.goBackRepaymentAccountButton,
          ],
        },
      });
    }
  }

  public allInPaySinglePayment = (result) => {
    console.log(result);
    // 这里根据跳转返回结果生成条件
    if (result === $paymentRedirectType.SUCCESS) {
      this.setState({
        paymentResultInfo: {
          code: result,
          imgUrl: "https://order.fwh1988.cn:14501/static-img/scm/img_successful.png",
          introduce: `<div><span>支付成功</span></div>`,
          operationButtonList: [
            this.state.seeOrderButton,
            this.state.continueShoppingButton,
          ],
        },
      });
    } else if (result === $paymentRedirectType.FAIL || result === $paymentRedirectType.CLOSE) {
      this.setState({
        paymentResultInfo: {
          code: result,
          imgUrl: "https://order.fwh1988.cn:14501/static-img/scm/img_failure.png",
          introduce: this.renderFailComponent(),
          operationButtonList: [
            this.state.payAgainButton,
            this.state.seeOrderButton,
          ],
        },
      });
    } else if (result === $paymentRedirectType.APPLIED || result === $paymentRedirectType.INIT || result === $paymentRedirectType.PENDING) {
      this.setState({
        paymentResultInfo: {
          code: result,
          imgUrl: "https://order.fwh1988.cn:14501/static-img/scm/img_waiting.png",
          introduce: `<div><span>请耐心等待支付结果</span><span>请勿重复支付</span></div>`,
          operationButtonList: [
            this.state.refreshPaymentResultButton,
            this.state.seeOrderButton,
            this.state.continueShoppingButton,
          ],
        },
      });
    }
  }

  public render() {
    // const { goods } = this.props;
    const { code, imgUrl, introduce, operationButtonList } = this.state.paymentResultInfo;
    return (
      <PaymentResultShowWrap>
        <div className="result-icon">
          <img src={imgUrl} alt=""/>
        </div>
        <div className="result-detail" dangerouslySetInnerHTML={{ __html: introduce }}/>
        <div className="result-operation">
          {
            operationButtonList.length > 0 && operationButtonList.map((item) => {
              console.log("burto", item);
              const { cb, name, buttonStyle } = item;
              return <div
                onClick={cb}
                className={`operate-button ${buttonStyle}`}
                key={name}
              >
                {name}
              </div>;
            })
          }
        </div>
      </PaymentResultShowWrap>
    );
  }
}

const PaymentResultShowWrap = styled.div`// styled
  & {
    position: relative;
    padding-top: 56px;
    .result-icon, .result-detail, .result-operation {
    }
    .result-icon {
      width: 96px;
      height: 73px;
      margin: 0 auto;
    }
    .result-detail {
      min-height: 67px;
      > div {
        text-align: center;
        margin-top: 11px;
        span:first-of-type {
          color: #2F2F2F;
          font-size: 14px;
          display: block;
        }
        span:nth-of-type(2) {
          color: #595959;
          font-size: 12px;
          margin-top: 6px;
          display: block;
        }
      }
    }
    .result-operation {
      text-align: center;
      .operate-button {
        width: 180px;
        height: 42px;
        line-height: 40px;
        text-align: center;
        margin: 0 auto 12px;
        font-size: 14px;
        border-radius: 3px;
      }
      .blue {
        background-color: #307DCD;
        color: #ffffff;
      }
      .white {
        background-color: #ffffff;
        color: #595959;
        border: 1px solid #D9D9D9;
      }
    }
  }
`;
