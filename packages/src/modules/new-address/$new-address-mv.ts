import { autowired, bean } from "@classes/ioc/ioc";
import { action, observable } from "mobx";
import { $ComponentService } from "../../classes/service/$component-service";
import { $ShippingAddressService } from "../../classes/service/$shipping-address-service";
import { $AddressMv } from "../submit-address-form/submit-address-mv";

@bean($NewAddressMv)
export class $NewAddressMv {
  @autowired($ShippingAddressService)
  public $shippingAddressService: $ShippingAddressService;

  @autowired($ComponentService)
  public $componentService: $ComponentService;

  @autowired($AddressMv)
  public $AddressMv: $AddressMv;

  @observable public addressTree: any;

  @observable public tagTypeList: any;

  @observable public addressInfo: any;

  @observable public isSpin: boolean = false;

  @action
  public showSpin() {
    this.isSpin = true;
  }

  @action
  public hideSpin() {
    this.isSpin = false;
  }

  @action
  public fetchNewAddress(params) {
    return this.$shippingAddressService.queryAddressById(params).then((data) => {
      this.addressInfo = data;
    });
  }

  @action
  public fetchAddressTree() {
    return this.$shippingAddressService.queryAddressList({}).then((data) => {
      const { regionTree } = data;
      this.addressTree = regionTree;
      this.hideSpin();
    });
  }

  @action
  public loadAddress(oid) {
    return this.$shippingAddressService.loadAddress(oid).then((data) => {
      this.$AddressMv.setTag(data.tag);
      this.$AddressMv.setFormData(data.contactPersonName,
        data.mobile, data.address,
        data.addressId, data.contactPersonId, null);
    });
  }

  @action
  public saveAddress(formData, type) {
    if (type === "edit") {
      return this.$shippingAddressService.editAddress(formData);
    } else {
      return this.$shippingAddressService.saveAddress(formData);
    }
  }

}
