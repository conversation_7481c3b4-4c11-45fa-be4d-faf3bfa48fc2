import { autowired } from "@classes/ioc/ioc";
import { <PERSON><PERSON>, InputItem, List, Picker, TextareaItem, WingBlank } from "antd-mobile";
import { transaction } from "mobx";
import { observer } from "mobx-react";
import React from "react";
import { SITE_PATH } from "../app";
import { $MyInfoMv } from "../my-info/$my-info-mv";
import { $AddressMv } from "../submit-address-form/submit-address-mv";
import { $NewAddressMv } from "./$new-address-mv";
import { $AppStore, AppStoreKey } from "../../classes/stores/app-store-mv";

declare let window: any;

@observer
export class NewAddress extends React.Component<any, any> {
  @autowired($AppStore)
  public $AppStore: $AppStore;
  @autowired($AddressMv)
  public $AddressMv: $AddressMv;

  @autowired($MyInfoMv)
  public $myInfoMv: $MyInfoMv;

  @autowired($NewAddressMv)
  public $newAddressMv: $NewAddressMv;

  public componentDidMount() {
    const params = {
      addressId: this.props.match.params.addressId,
    };
    transaction(() => {
      /*this.$newAddressMv.loadAddress(this.$AddressMv.oid);*/
      this.$newAddressMv.fetchNewAddress(params);
      this.$newAddressMv.fetchAddressTree();
    });
  }

  public saveAddress = () => {
    const formData = this.$AddressMv.getFormData();
    this.$newAddressMv.saveAddress({
      ...formData,
      orderPartyId: this.$myInfoMv.orderPartyId,
    }, this.$AddressMv.type).then(() => {
      this.$AppStore.clearPageMv(AppStoreKey.ADDRESSLIST);
      this.props.history.push({ pathname: `/${SITE_PATH}/shippingAddress` });
    });
  }

  public render() {
    const { addressTree } = this.$newAddressMv;
    return (
      <div>
        <List style={{ position: "relative" }}>
          <InputItem
            className="address_showInput"
            placeholder="收货人"
            value={this.$AddressMv.contactPerson}
            onChange={(v) => this.$AddressMv.setContactPerson(v)}
          >
            收货人
          </InputItem>
          <InputItem
            className="address_showInput"
            type="phone"
            placeholder="联系方式"
            value={this.$AddressMv.phoneNumber}
            onChange={(v) => this.$AddressMv.setPhoneNumber(v)}
          >
            联系方式
          </InputItem>
          <InputItem
            className="address_showInput"
            type="digit"
            placeholder="邮政编码"
            value={this.$AddressMv.postalCode}
            onChange={(v) => this.$AddressMv.setPostalCode(v)}
          >
            邮政编码
          </InputItem>
          <Picker
            cols={3}
            data={addressTree}
            // data={addressTree2}
            extra="请选择(可选)"
            onOk={(e) => console.log("ok", e)}
            title="选择地区"
            value={this.$AddressMv.addressId}
            onChange={(v) => this.$AddressMv.setAddressId(v)}
          >
            <List.Item arrow="horizontal">地区</List.Item>
          </Picker>
          <TextareaItem
            autoHeight={true}
            title="详细街道"
            placeholder="街道、门牌号"
            value={this.$AddressMv.location}
            onChange={(v) => this.$AddressMv.setAddress(v)}
          />
        </List>
        <WingBlank className="bottom_btn">
          <Button type="primary" onClick={this.saveAddress}>
            保存
          </Button>
        </WingBlank>
      </div>
    );
  }
}
