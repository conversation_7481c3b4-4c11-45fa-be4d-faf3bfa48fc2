import * as React from "react";
import { observer } from "mobx-react";
import styled from "styled-components";
import { Button } from "antd-mobile";
import { $ResponseType } from "../../classes/const/response-type";
import { SITE_PATH } from "../app";
import { autowired } from "@classes/ioc/ioc";
import { InitiatingReturnsMv } from "./initiating-returns-mv";
import { ReturnProductItem } from "./return-product-item";
import { remove } from "lodash";
import { msgError } from "../../helpers/msg-helper";

@observer
class ReturnsProductErrorList extends React.Component<any, any> {

  @autowired(InitiatingReturnsMv)
  public initiatingReturnsMv: InitiatingReturnsMv;

  public constructor(props) {
    super(props);
    this.state = {
      returnReason: null,
      orderPartyName: null,
      orgId: null,
      disabled: true,
      isChoose: false,
      animating: false,
    };
  }

  public componentDidMount() {
    document.title = "退货商品异常列表";
    this.initiatingReturnsMv.errorProductSkuList = [];
    console.log(this.props.location.state);
    if (this.props.location.state) {
      if (this.props.location.state.orgId && this.props.location.state.orderPartyName && this.props.location.state.errorProductSkuList) {
        this.setState({
          returnReason: this.props.location.state.memo,
          orgId: this.props.location.state.orgId,
          orderPartyName: this.props.location.state.orderPartyName,
          fromWhere: this.props.location.state.fromWhere,
        });
        this.initiatingReturnsMv.errorProductSkuList = this.props.location.state.errorProductSkuList;
        if (this.initiatingReturnsMv.errorProductSkuList.filter((sku) => sku.quantity > sku.inventoryQuantity).length > 0) {
          this.initiatingReturnsMv.errorProductSkuList.filter((sku) => sku.quantity > sku.inventoryQuantity).map((lis) => {
            lis.quantity = lis.inventoryQuantity;
          });
        }
      } else {
        this.setState({
          fromWhere: this.props.location.state.fromWhere,
        });
      }
    }
  }

  // public componentWillReceiveProps(nextProps) {
  //   console.log(898989);
  //   this.initiatingReturnsMv.errorProductSkuList = [];
  //   console.log(this.props.location.state);
  //   if (this.props.location.state) {
  //     if (this.props.location.state.orgId && this.props.location.state.orderPartyName && this.props.location.state.errorProductSkuList) {
  //       this.setState({
  //         returnReason: this.props.location.state.memo,
  //         orgId: this.props.location.state.orgId,
  //         orderPartyName: this.props.location.state.orderPartyName,
  //       });
  //       console.log(this.initiatingReturnsMv.errorProductSkuList);
  //       this.initiatingReturnsMv.errorProductSkuList = this.props.location.state.errorProductSkuList;
  //       console.log(this.props.location.state.errorProductSkuList);
  //     }
  //   }
  // }

  public refundOrderConfirmRefund = () => {
    const { errorProductSkuList } = this.initiatingReturnsMv;
    if (errorProductSkuList.filter((sku) => sku.inventoryQuantity === 0).length > 0) {
      errorProductSkuList.filter((sku) => sku.inventoryQuantity === 0).map((lis) => {
        lis.quantity = 0;
      });
    }
    if (errorProductSkuList.filter((sku) => sku.isInventoryExist === false).length > 0) {
      errorProductSkuList.filter((sku) => sku.isInventoryExist === false).map((lis) => {
        lis.quantity = 0;
      });
    }
    if (errorProductSkuList.filter((sku) => sku.quantity > sku.inventoryQuantity).length > 0) {
      errorProductSkuList.filter((sku) => sku.quantity > sku.inventoryQuantity).map((lis) => {
        lis.quantity = lis.inventoryQuantity;
      });
    }
    const params = {
      orgId: this.state.orgId,
      memo: this.state.returnReason,
      productSkuList: errorProductSkuList.filter((sku) => sku.quantity !== 0),
    };
    this.setState({
      animating: true,
    });
    this.initiatingReturnsMv.refundOrderConfirmRefund(params).then((data) => {
      this.setState({
        animating: false,
      });
      if (data.errorCode === $ResponseType.SUCCESS) {
        const { fromWhere, orderPartyName, orgId } = this.state;
        this.props.history.push({
          pathname: `/${SITE_PATH}/confirm-result-show/success/initiating-returns`,
          state: {
            orderPartyName: fromWhere === "my" ? orderPartyName : "",
            orgId: fromWhere === "my" ? orgId : "",
          },
        });
      } else if (data.errorCode === $ResponseType.ERRORLIST) {
        this.props.history.push({
          state: {
            errorProductSkuList: data.errorProductSkuList,
            orderPartyName: this.state.orderPartyName,
            orgId: this.state.orgId,
            memo: this.state.returnReason,
            fromWhere: this.state.fromWhere,
          },
        });
        this.initiatingReturnsMv.errorProductSkuList = data.errorProductSkuList;
      } else {
        msgError(data.errorMessage);
      }
    }).catch(() => {
      this.setState({
        animating: false,
      });
    });
  }

  public add = (item) => {
    console.log(8989898989);
    console.log(item);
    if (this.initiatingReturnsMv.errorProductSkuList.filter((sku) => sku.oid === item.oid).length > 0) {
      this.initiatingReturnsMv.errorProductSkuList.filter((sku) => sku.oid === item.oid)[0].quantity++;
    }
  }

  public reduce = (item) => {
    if (this.initiatingReturnsMv.errorProductSkuList.filter((sku) => sku.oid === item.oid).length > 0) {
      this.initiatingReturnsMv.errorProductSkuList.filter((sku) => sku.oid === item.oid)[0].quantity--;
      if (this.initiatingReturnsMv.errorProductSkuList.filter((sku) => sku.oid === item.oid)[0].quantity === 0) {
        remove(this.initiatingReturnsMv.errorProductSkuList, (sku) => sku.oid === item.oid);
      }
    }
  }

  public render() {
    const { errorProductSkuList } = this.initiatingReturnsMv;
    return (
      <ReturnsProductErrorListPage>
        <ReturnsProductErrorListContent>
          <ErrorHeader>
            <i className={"scmIconfont scm-modal-tishi"}/>
            <span>以下商品数据异常，请调整后重新提交</span>
          </ErrorHeader>
          <ErrorContent>
            <div>退货商品</div>
            {
              errorProductSkuList && errorProductSkuList.length > 0 && errorProductSkuList.filter((item) => item.isSuccess === false).map((item) => {
                return (
                  <ReturnProductItem
                    key={item.oid}
                    item={item}
                    add={this.add}
                    reduce={this.reduce}
                    isErrorList={true}
                  />
                );
              })
            }
          </ErrorContent>
        </ReturnsProductErrorListContent>
        <ConfirmBtn>
          <Button onClick={() => this.refundOrderConfirmRefund()} type={"primary"}>提交</Button>
        </ConfirmBtn>
      </ReturnsProductErrorListPage>
    );
  }
}

export default ReturnsProductErrorList;

const ReturnsProductErrorListPage = styled.div`// styled
  & {
    width: 100%;
    height: ${document.documentElement.clientHeight}px;
    background-color: #F2F2F2;
  }
`;

const ErrorHeader = styled.div`// styled
  & {
    width: 100%;
    height: 44px;
    background-color: rgba(48, 125, 205, 0.1);
    padding: 12px 15px;
    .scm-modal-tishi {
      font-size: 15px;
      color: #307DCD;
      margin-right: 8px;
    }
    > span {
      font-size: 14px;
      font-family: SourceHanSansCN-Regular, SourceHanSansCN;
      font-weight: 400;
      color: rgba(47, 47, 47, 1);
    }
  }
`;

const ReturnsProductErrorListContent = styled.div`// styled
  & {
    width: 100%;
    height: auto;
    padding-bottom: 80px;
  }
`;

const ConfirmBtn = styled.div`// styled
  & {
    width: 100%;
    height: 66px;
    padding: 12px 16px;
    position: fixed;
    bottom: 0;
    background: #fff;
    z-index: 99;
    .am-button-primary.am-button-disabled {
      background: #D9D9D9;
    }
    .am-button::before {
      display: none;
    }
  }
`;

const ErrorContent = styled.div`// styled
  & {
    width: 100%;
    height: auto;
    background-color: #fff;
    > div:first-child {
      width: 100%;
      height: 44px;
      padding: 12px 15px;
      background-color: #fff;
      position: relative;
      font-size: 14px;
      font-family: SourceHanSansCN-Regular, SourceHanSansCN;
      font-weight: 400;
      color: rgba(51, 51, 51, 1);
      :after {
        content: '';
        position: absolute;
        background-color: #D8D8D8 !important;
        display: block;
        z-index: 1;
        top: auto;
        right: auto;
        bottom: 0;
        left: 0;
        width: 100%;
        height: 1.2px;
        transform-origin: 50% 100%;
        transform: scaleY(0.5);
      }
    }
  }
`;
