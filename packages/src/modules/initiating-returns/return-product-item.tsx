import * as React from "react";
import { observer } from "mobx-react";
import styled from "styled-components";
import { SingleCount } from "../../components/single-count/single-count";

@observer
export class ReturnProductItem extends React.Component<any, any> {
  public render() {
    const { item, add, reduce, isErrorList, bottom } = this.props;
    return (
      <ReturnProductItemWrapper>
        <div>
          <img src={item.imgUrl || "https://order.fwh1988.cn:14501/static-img/scm/ico-nopic.png"} alt=""/>
        </div>
        <div className={"info"}>
          <span>{item.name && item.name.length > 32 ? item.name.slice(0, 32) + "..." : item.name}</span>
          <span>货号：{item.code}</span>
        </div>
        {
          isErrorList ? item.isInventoryExist && item.inventoryQuantity > 0 &&
            <div className={"count"} style={{ bottom: 50 }}>
              <span>退货数量</span>
              <SingleCount
                item={item}
                add={add}
                reduce={reduce}
                isErrorList={isErrorList}
              />
            </div> : <div className={"count"} style={{ bottom: 12 }}>
            <span>退货数量</span>
            <SingleCount
              item={item}
              add={add}
              reduce={reduce}
              isErrorList={isErrorList}
            />
          </div>
        }
        {
          isErrorList && item.reason && <ErrorReason>
            <span>错误原因：</span>
            <span>{item.reason}</span>
          </ErrorReason>
        }
      </ReturnProductItemWrapper>
    );
  }
}

const ReturnProductItemWrapper = styled.div`// styled
  & {
    width: 100%;
    height: auto;
    padding: 12px;
    position: relative;
    :after {
      content: '';
      position: absolute;
      background-color: #D8D8D8 !important;
      display: block;
      z-index: 1;
      top: auto;
      right: auto;
      bottom: 0;
      left: 0;
      width: 100%;
      height: 1.2px;
      transform-origin: 50% 100%;
      transform: scaleY(0.5);
    }
    > div {
      display: inline-block;
      vertical-align: text-top;
      img {
        width: 72px;
        height: 72px;
        margin-right: 8px;
      }
    }
    .count {
      position: absolute;
      bottom: 12px;
      right: 12px;
      text-align: center;
      > span:first-child {
        font-size: 10px;
        font-family: SourceHanSansCN-Regular, SourceHanSansCN;
        font-weight: 400;
        color: rgba(89, 89, 89, 1);
      }
      .am-list-item .am-list-line {
        padding-right: 0;
      }
    }
    .info {
      width: calc(100% - 80px);
      > span {
        display: block;
        word-break: break-all;
      }
      > span:first-child {
        margin-bottom: 8px;
        font-size: 13px;
        font-family: SourceHanSansCN-Regular, SourceHanSansCN;
        font-weight: 400;
        color: rgba(47, 47, 47, 1);
      }
      > span:last-child {
        font-size: 12px;
        font-family: SourceHanSansCN-Regular, SourceHanSansCN;
        font-weight: 400;
        color: rgba(89, 89, 89, 1);
      }
    }
  }
`;

const ErrorReason = styled.div`// styled
  & {
    display: block;
    width: 100%;
    min-height: 28px;
    background-color: #F2F2F2;
    border-radius: 2px;
    padding: 6px 8px;
    margin-top: 10px;
    > span {
      font-size: 13px;
      font-family: SourceHanSansCN-Regular, SourceHanSansCN;
      font-weight: 400;
    }
    > span:first-child {
      color: rgba(255, 48, 48, 1);
    }
    > span:last-child {
      color: #2F2F2F;
    }
  }
`;
