import * as React from "react";
import { observer } from "mobx-react";
import styled from "styled-components";
import { ActivityIndicator, Button, List, Modal } from "antd-mobile";
import { ReturnsProduct } from "./returns-product";
import { TextareaItemComponent } from "../../components/textarea-item/textarea-item";
import { SITE_PATH } from "../app";
import { autowired } from "@classes/ioc/ioc";
import { InitiatingReturnsMv } from "./initiating-returns-mv";
import { remove, uniqBy } from "lodash";
import { $ResponseType } from "../../classes/const/response-type";
import { msgError } from "../../helpers/msg-helper";
import { $AppStore, AppStoreKey } from "../../classes/stores/app-store-mv";

const Item = List.Item;
const Brief = Item.Brief;
const alert = Modal.alert;

declare let window: any;

@observer
class InitiatingReturns extends React.Component<any, any> {
  @autowired($AppStore)
  public $AppStore: $AppStore;

  @autowired(InitiatingReturnsMv)
  public $myMv: InitiatingReturnsMv;

  public constructor(props) {
    super(props);
    this.state = {};
  }

  // 离开记录滚动高度
  public saveMV = () => {
    this.$myMv.scrollHeight = $(".page-wrap").scrollTop();
    this.$AppStore.savePageMv(AppStoreKey.INITIATINGRETURNS, this.$myMv);
  }

  public componentWillUnmount(): void {
    this.saveMV();
  }

  public componentDidMount() {
    document.title = "发起退货";
    setTimeout(() => {
      const oldData = this.$AppStore.getPageMv(AppStoreKey.INITIATINGRETURNS)
      if (oldData) {
        this.$myMv.queryOldData(JSON.parse(oldData));
        this.$AppStore.clearPageMv(AppStoreKey.INITIATINGRETURNS);
        $(".page-wrap").scrollTop(this.$myMv.scrollHeight);
      } else {
        this.$myMv.clearInitiatingMVData();
        this.initPage();
      }
    }, 50);
  }

  public initPage = () => {
    if (this.props.location.state) {
      if ((this.props.location.state.orderPartyName && this.props.location.state.orgId) || (this.props.location.state.refundOrderTypeName && this.props.location.state.refundOrderTypeId)) {
        this.$myMv.orderPartyName = this.props.location.state.orderPartyName;
        this.$myMv.orgId = this.props.location.state.orgId;
        this.$myMv.isChoose = this.props.location.state.isChoose || false;
        this.$myMv.isChooseType = this.props.location.state.isChooseType || false;
        this.$myMv.refundOrderTypeName = this.props.location.state.refundOrderTypeName;
        this.$myMv.refundOrderTypeId = this.props.location.state.refundOrderTypeId;
        this.$myMv.fromWhere = this.props.location.state.fromWhere;
        this.$myMv.returnReason = this.props.location.state.memo || "";
        if (this.props.location.state.selectProductSkuList) {
          console.log(this.$myMv.selectProductSkuList);
          this.$myMv.selectProductSkuList = uniqBy(this.props.location.state.selectProductSkuList, "oid");
        }
        console.log(this.state.isChoose);
      } else {
        this.$myMv.fromWhere = this.props.location.state.fromWhere;
        this.$myMv.isChoose = true;
        this.$myMv.isChooseType = true;
      }
    } else {
      this.$myMv.isChoose = true;
      this.$myMv.isChooseType = true;
    }
    this.isSubmit();
    this.click();
  }

  public click = () => {
    window.addEventListener("popstate", (res) => {
      // 触发函数
      console.log(res);
      if (window.location.href && (window.location.href.indexOf("choose-item") > -1 || window.location.href.indexOf("choose-returns-product") > -1)) {
        // this.$AppStore.clearPageMv(AppStoreKey.RETURNEDSALES);
        window.location.href = `/${SITE_PATH}/returned-sales`;
      }
    });
  }

  public changeReturnReason = (value) => {
    this.$myMv.returnReason = value;
    this.isSubmit();
  }

  public submit = () => {
    console.log(89898989);
    const { selectProductSkuList } = this.$myMv;
    const isFilter = selectProductSkuList.every((sku) => sku.quantity !== 0);
    if (!isFilter) {
      alert("检测到包含退货数量为0的商品，提交后将过滤掉该商品，确认提交？", "", [
        { text: "我再想想", onPress: () => console.log("ok") },
        { text: "确认提交", onPress: () => this.refundOrderConfirmRefund() },
      ]);
    } else {
      this.refundOrderConfirmRefund();
    }
  }

  public refundOrderConfirmRefund = () => {
    const { selectProductSkuList, orgId, returnReason, fromWhere, orderPartyName, refundOrderTypeName, refundOrderTypeId } = this.$myMv;
    const params = {
      memo: returnReason,
      orgId,
      productSkuList: selectProductSkuList.filter((sku) => sku.quantity !== 0),
      refundOrderTypeId,
    };
    this.$myMv.animating = true;
    this.$myMv.refundOrderConfirmRefund(params).then((data) => {
      this.$myMv.animating = false;
      if (data.errorCode === $ResponseType.SUCCESS) {
        this.props.history.push({
          pathname: `/${SITE_PATH}/confirm-result-show/success/initiating-returns`,
          state: {
            fromWhere,
            orderPartyName: fromWhere === "my" ? orderPartyName : "",
            orgId: fromWhere === "my" ? orgId : "",
            refundOrderTypeName: fromWhere === "my" ? refundOrderTypeName : "",
            refundOrderTypeId: fromWhere === "my" ? refundOrderTypeId : "",
          },
        });
      } else if (data.errorCode === $ResponseType.ERRORLIST) {
        this.props.history.push({
          pathname: `/${SITE_PATH}/returns-product-errorList`,
          state: {
            errorProductSkuList: data.errorProductSkuList,
            fromWhere,
            memo: returnReason,
            orderPartyName,
            orgId,
            refundOrderTypeName,
            refundOrderTypeId,
          },
        });
      } else {
        msgError(data.errorMessage);
      }
    }).catch(() => {
      this.$myMv.animating = false;
    });
  }

  public goToChooseItem = () => {
    const { fromWhere, returnReason } = this.$myMv;
    this.props.history.push({
      pathname: `/${SITE_PATH}/choose-item`,
      state: {
        url: "/integration/scm/orderParty/list/formal",
        isChoose: true,
        selectProductSkuList: uniqBy(this.$myMv.selectProductSkuList, "oid"),
        orgId: this.props.location.state && this.props.location.state.orgId,
        fromWhere,
        memo: returnReason,
        refundOrderTypeId: this.props.location.state && this.props.location.state.refundOrderTypeId,
        refundOrderTypeName: this.props.location.state && this.props.location.state.refundOrderTypeName,
      },
    });
  }

  public goToChooseReturnType = () => {
    const { fromWhere, returnReason } = this.$myMv;
    this.props.history.push({
      pathname: `/${SITE_PATH}/choose-returnType`,
      state: {
        url: "/integration/scm/ordermall/refundordertype/select",
        isChooseType: true,
        selectProductSkuList: uniqBy(this.$myMv.selectProductSkuList, "oid"),
        refundOrderTypeId: this.props.location.state && this.props.location.state.refundOrderTypeId,
        fromWhere,
        memo: returnReason,
        orderPartyName: this.props.location.state && this.props.location.state.orderPartyName,
        orgId: this.props.location.state && this.props.location.state.orgId,
      },
    });
  }

  public addProduct = () => {
    console.log(this.$myMv.selectProductSkuList);
    const { orderPartyName, orgId, isChoose, fromWhere, returnReason, selectProductSkuList, refundOrderTypeName, refundOrderTypeId, isChooseType } = this.$myMv;
    if (orderPartyName === null || orderPartyName === undefined || orderPartyName === "") {
      alert("请先选择门店", "", [
        { text: "我知道了", onPress: () => console.log("ok") },
      ]);
      return;
    }
    if (refundOrderTypeName === null || refundOrderTypeName === undefined || refundOrderTypeName === "") {
      alert("请先选择退货类型", "", [
        { text: "我知道了", onPress: () => console.log("ok") },
      ]);
      return;
    }
    this.props.history.push({
      pathname: `/${SITE_PATH}/choose-returns-product`,
      state: {
        fromWhere,
        isChoose,
        orderPartyName,
        memo: returnReason,
        orgId,
        selectProductSkuList: selectProductSkuList || [],
        refundOrderTypeName,
        refundOrderTypeId,
        isChooseType,
      },
    });
    console.log(this.$myMv.selectProductSkuList);
  }

  public add = (item) => {
    console.log(8989898989);
    console.log(item);
    if (this.$myMv.selectProductSkuList.filter((sku) => sku.oid === item.oid).length > 0) {
      this.$myMv.selectProductSkuList.filter((sku) => sku.oid === item.oid)[0].quantity++;
    }
  }

  public reduce = (item) => {
    if (this.$myMv.selectProductSkuList.filter((sku) => sku.oid === item.oid).length > 0) {
      this.$myMv.selectProductSkuList.filter((sku) => sku.oid === item.oid)[0].quantity--;
      if (this.$myMv.selectProductSkuList.filter((sku) => sku.oid === item.oid)[0].quantity === 0) {
        remove(this.$myMv.selectProductSkuList, (sku) => sku.oid === item.oid);
        if (this.$myMv.selectProductSkuList.length === 0) {
          this.$myMv.disabled = true;
        }
      }
    } else {
      this.$myMv.disabled = true;
    }
  }

  public isSubmit() {
    const { selectProductSkuList } = this.$myMv;
    if (this.props.location.state && this.props.location.state.orgId && this.props.location.state.refundOrderTypeId && selectProductSkuList.length > 0) {
      this.$myMv.disabled = false;
    } else {
      this.$myMv.disabled = true;
    }
  }

  public render() {
    const { selectProductSkuList, returnReason, orderPartyName, disabled, isChoose, animating, isChooseType, refundOrderTypeName } = this.$myMv;
    console.log(selectProductSkuList);
    return (
      <InitiatingReturnsPage className="page-wrap">
        <InitiatingReturnsContent>
          <List>
            <Item
              extra={orderPartyName || "请选择"}
              arrow={isChoose && "horizontal"}
              onClick={isChoose && this.goToChooseItem}
            >
              退货门店
            </Item>
          </List>
          <List>
            <Item
              extra={refundOrderTypeName || "请选择"}
              arrow={isChooseType && "horizontal"}
              onClick={isChooseType && this.goToChooseReturnType}
            >
              退货类型
            </Item>
          </List>
          <ReturnsProduct
            addProduct={this.addProduct}
            data={uniqBy(selectProductSkuList, "oid")}
            add={(item) => this.add(item)}
            reduce={(item) => this.reduce(item)}
          />
          <ReturnReason>
            <div>退货原因</div>
            <TextareaItemComponent
              value={returnReason}
              placeholder={"请输入"}
              rows={5}
              count={120}
              onChange={this.changeReturnReason}
            />
          </ReturnReason>
        </InitiatingReturnsContent>
        <ConfirmBtn>
          <Button onClick={() => this.submit()} type={"primary"} disabled={disabled}>提交</Button>
        </ConfirmBtn>
        <ActivityIndicator toast={true} text="loading" animating={animating}/>
      </InitiatingReturnsPage>
    );
  }
}

export default InitiatingReturns;

const InitiatingReturnsPage = styled.div`// styled
  & {
    width: 100%;
    height: 100%;
    background: #F2F2F2;
    overflow: scroll;
    .am-list-item .am-list-line .am-list-content {
      font-size: 14px;
      font-family: SourceHanSansCN-Regular, SourceHanSansCN;
      font-weight: 400;
      color: rgba(47, 47, 47, 1);
    }
    .am-list-item .am-list-line .am-list-extra {
      font-size: 13px;
      font-family: SourceHanSansCN-Regular, SourceHanSansCN;
      font-weight: 400;
      color: rgba(102, 102, 102, 1);
      flex-basis: 75%;
    }
    .am-list-item {
      padding-left: 12px;
    }
    .am-list-item .am-list-line {
      padding-right: 12px;
    }
    @media (min-resolution: 2dppx) {
      html:not([data-scale]) .am-list-item:not(:last-child) .am-list-line {
        border: none !important;
      }
    }
    @media (min-resolution: 2dppx) {
      html:not([data-scale]) .am-list-body::after {
        display: none !important;
      }

      .am-list-body::after {
        display: none !important;
      }
    }
    .am-button-primary::before {
      display: none !important;
    }
    .am-button {
      border: none !important;
    }
  }
`;

const InitiatingReturnsContent = styled.div`// styled
  & {
    width: 100%;
    height: auto;
    padding-bottom: 70px;
  }
`;

const ReturnReason = styled.div`// styled
  & {
    width: 100%;
    height: auto;
    margin-top: 10px;
    background: #fff;
    padding-bottom: 12px;
    > div:first-child {
      width: 100%;
      height: 40px;
      padding: 12px;
    }
    .am-textarea-item {
      margin: 0 12px 0px 12px;
      background: #F2F2F2;
      border-radius: 4px;
      padding: 10px;
    }
    .am-textarea-control textarea {
      font-size: 13px;
      font-family: SourceHanSansCN-Regular, SourceHanSansCN;
      font-weight: 400;
      color: rgba(102, 102, 102, 1);
    }
    .am-textarea-control {
      padding: 0;
    }
  }
`;

const ConfirmBtn = styled.div`// styled
  & {
    width: 100%;
    height: 66px;
    padding: 12px 16px;
    position: fixed;
    bottom: 0;
    background: #fff;
    z-index: 99;
    .am-button-primary.am-button-disabled {
      background: #D9D9D9;
    }
  }
`;
