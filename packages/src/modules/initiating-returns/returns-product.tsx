import * as React from "react";
import { observer } from "mobx-react";
import styled from "styled-components";
import { ReturnProductItem } from "./return-product-item";

@observer
export class ReturnsProduct extends React.Component<any, any> {
  public render() {
    const { addProduct, data, add, reduce } = this.props;
    return (
      <ReturnsProductPage>
        <Header>
          <span>退货商品</span>
          <span onClick={addProduct}>
            <i className={"scmIconfont scm-jiahao"}/>
            添加
          </span>
        </Header>
        <Content>
          {
            data && data.length > 0 ? data.map((item) => {
              return (
                <ReturnProductItem
                  key={item.oid}
                  item={item}
                  add={add}
                  reduce={reduce}
                  isErrorList={false}
                />
              );
            }) : <div className={"noData"}>
              <div>
                <img src="https://order.fwh1988.cn:14501/static-img/scm/no-data.png" alt=""/>
              </div>
              <div>退货商品为空</div>
            </div>
          }
        </Content>
      </ReturnsProductPage>
    );
  }
}

const ReturnsProductPage = styled.div`// styled
  & {
    width: 100%;
    height: auto;
    margin-top: 10px;
    background: #fff;
  }
`;

const Header = styled.div`// styled
  & {
    width: 100%;
    height: 44px;
    position: relative;
    padding: 14px 12px;
    :after {
      content: '';
      position: absolute;
      background-color: #D8D8D8 !important;
      display: block;
      z-index: 1;
      top: auto;
      right: auto;
      bottom: 0;
      left: 0;
      width: 100%;
      height: 1.2px;
      transform-origin: 50% 100%;
      transform: scaleY(0.5);
    }
    > span:first-child {
      font-size: 14px;
      font-family: SourceHanSansCN-Regular, SourceHanSansCN;
      font-weight: 400;
      color: rgba(51, 51, 51, 1);
    }
    > span:last-child {
      float: right;
      font-size: 13px;
      font-family: SourceHanSansCN-Regular, SourceHanSansCN;
      font-weight: 400;
      color: rgba(48, 125, 205, 1);
    }
    .scm-jiahao {
      font-size: 12px;
      margin-right: 4px;
    }
  }
`;

const Content = styled.div`// styled
  & {
    width: 100%;
    height: auto;
    .noData {
      height: 122px;
      text-align: center;
      font-size: 12px;
      font-family: SourceHanSansCN-Regular, SourceHanSansCN;
      font-weight: 400;
      color: rgba(153, 153, 153, 1);
      padding-top: 24px;
      img {
        width: 56px;
        height: 51px;
      }
      > div:last-child {
        margin-top: 10px;
      }
    }
  }
`;
