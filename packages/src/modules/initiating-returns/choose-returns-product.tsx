import * as React from "react";
import { observer } from "mobx-react";
import styled from "styled-components";
import { Spin } from "antd";
import { <PERSON><PERSON>, SearchBar } from "antd-mobile";
import { autowired } from "@classes/ioc/ioc";
import { InitiatingReturnsMv } from "./initiating-returns-mv";
import { ScrollAbilityWrapComponent } from "../../components/scroll-ability/wrap";
import { ChooseReturnsProductList } from "./choose-returns-product-list";
import { SITE_PATH } from "../app";
import { isEmpty, uniqBy } from "lodash";
import { NoGoods } from "../../components/no-goods/no-goods";
import "./common.less";
import { $AppStore, AppStoreKey } from "../../classes/stores/app-store-mv";

@observer
class ChooseReturnsProductWrapper extends React.Component<any, any> {

  @autowired(InitiatingReturnsMv)
  public initiatingReturnsMv: InitiatingReturnsMv;
  @autowired($AppStore)
  public $AppStore: $AppStore;

  public constructor(props) {
    super(props);
    this.state = {
      finished: false,
      isShow: false,
      isPosActive: false,
      searchProduct: null,
      isSearch: false,
    };
    this.myRef = React.createRef();
  }

  public componentDidMount() {
    document.title = "退货商品列表";
    this.initiatingReturnsMv.page = 0;
    this.initiatingReturnsMv.productSkuList = [];
    this.initiatingReturnsMv.copySelectProductSkuList = [];
    if (this.props.location.state) {
      if (!isEmpty(this.props.location.state.selectProductSkuList)) {
        console.log(typeof this.props.location.state.selectProductSkuList);
        this.initiatingReturnsMv.copySelectProductSkuList = this.props.location.state.selectProductSkuList;
      }
    }
    console.log(this.myRef);
    this.myRef.current.focus();
    // this.loadData();
  }

  public componentWillReceiveProps(nextProps: Readonly<any>, nextContext: any): void {
    const { isScrollEnd } = nextProps;
    console.log(isScrollEnd);
    if (isScrollEnd && isScrollEnd !== this.props.isScrollEnd) {
      this.loadData();
    }
  }

  public loadData = () => {
    const { finished } = this.state;
    if (finished) {
      return;
    }
    this.search();
  }

  public search = () => {
    const { page } = this.initiatingReturnsMv;
    console.log(this.props.location.state);
    if (this.props.location.state) {
      if (this.props.location.state.orgId && this.state.searchProduct) {
        const params = {
          pageSize: 10,
          pageIndex: page,
          keyword: this.state.searchProduct,
          orderId: this.props.location.state.orgId,
        };
        this.initiatingReturnsMv.showSpin();
        this.initiatingReturnsMv.loadData(params).then((data) => {
          this.initiatingReturnsMv.hideSpin();
          data.productSkuList.map((item) => {
            item.checked = false;
            item.disabled = false;
            item.quantity = 0;
          });
          console.log(data);
          const { loadingEnd } = this.props;
          if (data.productSkuList && data.productSkuList.length > 0) {
            this.initiatingReturnsMv.productSkuList = this.initiatingReturnsMv.productSkuList.concat(data.productSkuList);
            this.initiatingReturnsMv.copySelectProductSkuList.map((sku) => {
              if (this.initiatingReturnsMv.productSkuList.filter((item) => item.oid === sku.oid).length > 0) {
                this.initiatingReturnsMv.productSkuList.filter((item) => item.oid === sku.oid)[0].checked = true;
              }
            });
            this.initiatingReturnsMv.changePage();
            this.setState({
              finished: this.initiatingReturnsMv.productSkuList.length >= data.itemCount,
              isShow: false,
              isPosActive: data.isPosActive,
            });
            loadingEnd && loadingEnd(this.initiatingReturnsMv.productSkuList.length >= data.itemCount);
          } else {
            this.setState({
              finished: true,
              isShow: false,
            });
            loadingEnd && loadingEnd(true);
          }
        }).catch(() => {
          this.initiatingReturnsMv.hideSpin();
        });
      }
    }
  }

  public changeSearchProduct = (value) => {
    console.log(value === "");
    if (value === "") {
      this.initiatingReturnsMv.productSkuList = [];
      this.setState({
        isSearch: false,
      });
    }
    this.setState({
      searchProduct: value,
    });
  }

  public submitSearch = () => {
    this.initiatingReturnsMv.page = 0;
    this.initiatingReturnsMv.productSkuList = [];
    this.search();
    this.setState({
      isSearch: true,
    });
  }

  public confirmAdd = () => {
    this.$AppStore.clearPageMv(AppStoreKey.INITIATINGRETURNS);
    this.props.history.push({
      pathname: `/${SITE_PATH}/initiating-returns`,
      state: {
        selectProductSkuList: uniqBy(this.initiatingReturnsMv.copySelectProductSkuList, "oid"),
        orderPartyName: this.props.location.state.orderPartyName,
        orgId: this.props.location.state.orgId,
        isChoose: this.props.location.state.isChoose,
        fromWhere: this.props.location.state.fromWhere,
        memo: this.props.location.state.memo,
        refundOrderTypeName: this.props.location.state.refundOrderTypeName,
        refundOrderTypeId: this.props.location.state.refundOrderTypeId,
        isChooseType: this.props.location.state.isChooseType,
      },
    });
  }

  public render() {
    const { productSkuList, selectProductSkuList, copySelectProductSkuList, isSpin } = this.initiatingReturnsMv;
    console.log(copySelectProductSkuList);
    const { finished, isShow, isPosActive, searchProduct, isSearch } = this.state;
    return (
      <ChooseReturnsProductPage>
        <Spin spinning={isSpin}>
          <SearchBar
            placeholder="输入商品货号、名称"
            value={searchProduct}
            onChange={this.changeSearchProduct}
            onSubmit={this.submitSearch}
            ref={this.myRef}
          />
          {
            productSkuList.length > 0 ? <ChooseReturnsProductList
              finished={finished}
              isShow={isShow}
              chooseItemList={productSkuList}
              isPosActive={isPosActive}
            /> : isSearch ? <NoGoods title={"没有找到相关的商品"} height={document.documentElement.clientHeight - 50 - 46}/> :
              <div style={{ height: document.documentElement.clientHeight - 50 - 46 }}/>
          }
        </Spin>
        <ConfirmBtn>
          <span>已选择：{copySelectProductSkuList.length > 0 ? copySelectProductSkuList.filter((sku) => sku && sku.checked).length : 0}</span>
          <Button
            type={"primary"}
            onClick={this.confirmAdd}
            disabled={copySelectProductSkuList.filter((sku) => sku && sku.checked).length === 0}
          >
            确定
          </Button>
        </ConfirmBtn>
      </ChooseReturnsProductPage>
    );
  }
}

const ChooseReturnsProduct = ScrollAbilityWrapComponent(ChooseReturnsProductWrapper);

export default ChooseReturnsProduct;

const ChooseReturnsProductPage = styled.div`// styled
  & {
    width: 100%;
    min-height: calc(${document.documentElement.clientHeight}px);
    height: auto;
    background: #F2F2F2;
    padding-bottom: 50px;
    .am-search {
      height: 46px;
      background-color: #fff;
      padding: 8px 12px;
      position: fixed;
      top: 0;
      width: 100%;
      z-index: 99;
      :after {
        content: '';
        position: absolute;
        background-color: #D8D8D8 !important;
        display: block;
        z-index: 1;
        top: auto;
        right: auto;
        bottom: 0;
        left: 0;
        width: 100%;
        height: 1.2px;
        transform-origin: 50% 100%;
        transform: scaleY(0.5);
      }
    }
    .noGoods{
      margin-top: 46px;
    }
    .am-search-input {
      height: 30px;
      background-color: #F2F2F2;
      border-radius: 15px;
    }
    .am-search-input input[type="search"] {
      font-size: 14px;
      font-family: SourceHanSansCN-Regular, SourceHanSansCN;
      font-weight: 400;
      color: #2F2F2F;
      height: 30px;
    }
    .am-search-input .am-search-clear {
      top: 25%;
      right: 12px;
    }
    .am-search.am-search-start .am-search-input .am-search-synthetic-ph {
      width: auto !important;
    }
    .noGoods {
      img {
        width: 48px;
        height: 50px;
        margin-top: 64px;
      }
    }
    .am-list-line {
      border: none !important;
    }
    @media (min-resolution: 2dppx) {
      html:not([data-scale]) .am-list-item:not(:last-child) .am-list-line {
        border: none !important;
      }

      html:not([data-scale]) .am-checkbox-item:not(:last-child) .am-list-line {
        border: none !important;
      }
    }
    @media (min-resolution: 2dppx) {
      html:not([data-scale]) .am-list-body::after {
        display: none !important;
      }
    }
    .am-button-primary::before {
      display: none !important;
    }
    .am-button {
      border: none !important;
    }
  }
`;

const ConfirmBtn = styled.div`// styled
  & {
    width: 100%;
    height: 48px;
    background: #fff;
    padding: 6px 12px;
    position: fixed;
    bottom: 0;
    z-index: 99;
    > span:first-child {
      font-size: 12px;
      font-family: SourceHanSansCN-Normal, SourceHanSansCN;
      font-weight: 400;
      color: rgba(51, 51, 51, 1);
      display: inline-block;
      height: 36px;
      line-height: 36px;
    }
    .am-button {
      display: inline-block;
      float: right;
      padding: 0px 30px;
      border-radius: 20px;
      height: 36px;
      line-height: 36px;
      font-size: 14px;
      font-family: SourceHanSansCN-Normal, SourceHanSansCN;
      font-weight: 400;
      color: rgba(255, 255, 255, 1);
    }
    .am-button::before {
      border: none !important;
    }
    .am-button.am-button-disabled {
      background-color: #D9D9D9;
      color: #fff;
    }
  }
`;
