import { autowired, bean } from "@classes/ioc/ioc";
import { action, observable } from "mobx";
import { InitiatingReturnsService } from "./initiating-returns-service";
import { beanMapper } from '../../helpers/bean-helpers';

@bean(InitiatingReturnsMv)
export class InitiatingReturnsMv {

  @autowired(InitiatingReturnsService)
  public initiatingReturnsService: InitiatingReturnsService;

  @observable public productSkuList: any[] = [];

  @observable public isSpin: boolean = false;

  @observable public page: number = 0;
  @observable public scrollHeight: number = 0;

  @observable public selectProductSkuList: any[] = [];

  @observable public copySelectProductSkuList: any[] = [];

  @observable public errorProductSkuList: any[] = [];
  @observable public returnReason = null;
  @observable public orderPartyName = null;
  @observable public orgId = null;
  @observable public disabled: boolean = true;
  @observable public isChoose: boolean = false;
  @observable public isChooseType: boolean = false;
  @observable public animating: boolean = false;
  @observable public fromWhere: string = "";
  @observable public refundOrderTypeId = null;
  @observable public refundOrderTypeName = null;
  @action
  public showSpin() {
    this.isSpin = true;
  }

  @action
  public hideSpin() {
    this.isSpin = false;
  }

  @action
  public changePage() {
    this.page++;
  }

  @action
  public loadData(params) {
    return this.initiatingReturnsService.refundOrderProductSkuQuery(params);
  }

  @action
  public refundOrderConfirmRefund(params) {
    return this.initiatingReturnsService.refundOrderConfirmRefund(params);
  }
  @action
  public queryOldData(obj) {
    beanMapper(obj, this);
    console.log(this);
  }
  @action
  public clearInitiatingMVData() {
    this.selectProductSkuList = [];
    this.returnReason = "";
    this.orderPartyName = "";
    this.refundOrderTypeName = "";
    this.disabled = true;
    this.isChoose = false;
    this.isChooseType = false;
    this.animating = false;
    this.scrollHeight = 0;
  }
}
