import * as React from "react";
import { observer } from "mobx-react";
import { Checkbox } from "antd-mobile";
import styled from "styled-components";
import { LoadingTip } from "../../components/loading-marked-words";
import { InitiatingReturnsMv } from "./initiating-returns-mv";
import { autowired } from "@classes/ioc/ioc";
import { remove } from "lodash";

const CheckboxItem = Checkbox.CheckboxItem;

@observer
export class ChooseReturnsProductList extends React.Component<any, any> {

  @autowired(InitiatingReturnsMv)
  public initiatingReturnsMv: InitiatingReturnsMv;

  public onChange = (e, item) => {
    console.log(item);
    console.log(e.target.checked);
    if (this.initiatingReturnsMv.productSkuList.filter((sku) => sku.oid === item.oid).length > 0) {
      this.initiatingReturnsMv.productSkuList.filter((sku) => sku.oid === item.oid)[0].checked = e.target.checked;
      if (e.target.checked) {
        this.initiatingReturnsMv.productSkuList.filter((sku) => sku.oid === item.oid)[0].quantity = 1;
        this.initiatingReturnsMv.copySelectProductSkuList.push(item);
      } else {
        this.initiatingReturnsMv.productSkuList.filter((sku) => sku.oid === item.oid)[0].quantity = 0;
        remove(this.initiatingReturnsMv.copySelectProductSkuList, (sku) => sku.oid === item.oid);
      }
      console.log(this.initiatingReturnsMv.copySelectProductSkuList);
    }
  }

  public render() {
    const { isSpin } = this.initiatingReturnsMv;
    const { finished, isShow, chooseItemList, isPosActive } = this.props;
    return (
      <ChooseItemPage>
        {
          chooseItemList && chooseItemList.length > 0 && chooseItemList.map((item) => {
            return (
              <ChooseItem key={item.oid}>
                <CheckboxItem
                  checked={item.checked}
                  disabled={item.disabled}
                  onChange={(e) => this.onChange(e, item)}
                  {...this.props}
                />
                <div className={"img"}>
                  <img src={item.imgUrl || "https://order.fwh1988.cn:14501/static-img/scm/ico-nopic.png"} alt=""/>
                </div>
                <div>
                  <span>{item.name && item.name.length > 32 ? item.name.slice(0, 32) + "..." : item.name}</span>
                  <span>货号：{item.code}</span>
                </div>
              </ChooseItem>
            );
          })
        }
        {
          chooseItemList && chooseItemList.length > 0 &&
          <LoadingTip
            isFinished={finished}
            isLoad={isShow}
          />
        }
      </ChooseItemPage>
    );
  }
}

const ChooseItemPage = styled.div`// styled
  & {
    width: 100%;
    height: auto;
    margin-top: 46px;
    .am-list-item {
     padding-left: 0;
    }
    @media (min-resolution: 2dppx) {
      html:not([data-scale]) .am-list-item:not(:last-child) .am-list-line {
        border: none !important;
      }

      html:not([data-scale]) .am-checkbox-item:not(:last-child) .am-list-line {
        border: none !important;
      }
    }
    @media (min-resolution: 2dppx) {
      html:not([data-scale]) .am-list-body::after {
        display: none !important;
      }
    }
    .am-button-primary::before {
      display: none !important;
    }
    .am-button {
      border: none !important;
    }
  }
`;

const ChooseItem = styled.div`// styled
  & {
    width: 100%;
    height: auto;
    padding: 12px;
    position: relative;
    background: #fff;
    :after {
      content: '';
      position: absolute;
      background-color: #D8D8D8 !important;
      display: block;
      z-index: 1;
      top: auto;
      right: auto;
      bottom: 0;
      left: 0;
      width: 100%;
      height: 1.2px;
      transform-origin: 50% 100%;
      transform: scaleY(0.5);
    }
    > div {
      display: inline-block;
      vertical-align: text-top;
      img {
        width: 72px;
        height: 72px;
        margin-right: 8px;
      }
      > span {
        display: block;
      }
    }
    .img {
      vertical-align: initial;
    }
    > div:last-child {
      width: calc(100% - 12px - 21px - 12px - 80px);
      vertical-align: top;
      > span:first-child {
        word-break: break-all;
        font-size: 13px;
        font-family: SourceHanSansCN-Regular, SourceHanSansCN;
        font-weight: 400;
        color: rgba(47, 47, 47, 1);
        margin-bottom: 8px;
      }
      > span:last-child {
        font-size: 12px;
        font-family: SourceHanSansCN-Regular, SourceHanSansCN;
        font-weight: 400;
        color: rgba(89, 89, 89, 1);
      }
    }
  }
`;

const ConfirmButton = styled.div`// styled
  & {
    width: 100%;
    height: auto;
    position: fixed;
    bottom: 0;
  }
`;
