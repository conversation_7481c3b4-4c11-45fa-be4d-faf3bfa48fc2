import { autowired, bean } from "@classes/ioc/ioc";
import { action, observable } from "mobx";
import { $ComponentService } from "../../classes/service/$component-service";
import { $MyInfoService } from "../../classes/service/$my-info-service";
import { $CreditDetail } from "../../classes/entity/$credit-detail";

@bean($CreditDetailMv)
export class $CreditDetailMv {
  @autowired($ComponentService)
  public $componentService: $ComponentService;

  @autowired($MyInfoService)
  public $myInfoService: $MyInfoService;

  @observable public buttonList: any[];

  @observable public accountFlowList: any[];

  @observable public balanceAmount: number;

  @observable public allSurplusToReturnAmount: number;

  @observable public isSpin: boolean = false;

  @observable public automaticType: string;

  @observable public effectiveCreditAccount: number;

  @observable public creditAccountList: any[] = [];

  @observable public creditDetail: $CreditDetail = new $CreditDetail();

  @action
  public showSpin() {
    this.isSpin = true;
  }

  @action
  public hideSpin() {
    this.isSpin = false;
  }

  @action
  public fetchAccountInfo(params) {
    return this.$componentService.queryAccountInfo(params).then((data) => {
      const { buttonList, balanceAmount, accountFlowList } = data;
      this.buttonList = buttonList;
      this.accountFlowList = accountFlowList;
      this.balanceAmount = balanceAmount;
      this.hideSpin();
    });
  }

  @action
  public fetchCreditDetail(params) {
    this.$componentService.queryCreditDetail(params).then((data) => {
      this.creditDetail = data;
    });
  }
}
