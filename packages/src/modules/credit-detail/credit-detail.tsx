import { autowired } from "@classes/ioc/ioc";
import { List } from "antd-mobile";
import { observer } from "mobx-react";
import React from "react";
import { withRouter } from "react-router";
import styled from "styled-components";
// import "../common.less";
import { $AccountType } from "../../classes/const/$account-type";
import { NoGoods } from "../../components/no-goods/no-goods";
import { SITE_PATH } from "../app";
import { Spin } from "antd";
import { $CreditDetailMv } from "./credit-detail-mv";

const Item = List.Item;
const Brief = Item.Brief;
declare let window: any;

@withRouter
@observer
class CreditDetail extends React.Component<any, any> {
  @autowired($CreditDetailMv)
  public $creditDetailMv: $CreditDetailMv;

  public constructor(props) {
    super(props);
    this.state = {
      key: "ALL",
      pageIndex: 0,
      pageSize: *********,
    };
  }

  public componentDidMount() {
    document.title = "信用账户详情";
    this.initPage("ALL");
  }

  public initPage = (activeKey) => {
    const { creditId } = this.props.location.state;
    const { pageSize, pageIndex } = this.state;
    const params = { accountId: creditId, condition: activeKey, pageSize, pageIndex };
    this.setState({ key: activeKey });
    this.$creditDetailMv.showSpin();
    this.$creditDetailMv.fetchAccountInfo(params);
    const paramsDetail = { accountId: creditId };
    this.$creditDetailMv.fetchCreditDetail(paramsDetail);
  }

  public queryAccountInfo = (activeKey) => {
    this.initPage(activeKey);
  }

  public accountFlowDetail = (accountFlowId) => {
    this.props.history.push({ pathname: `/${SITE_PATH}/account-info-detail/${accountFlowId}` });
  }

  public render() {
    const { accountFlowList, isSpin, creditDetail } = this.$creditDetailMv;
    const { key } = this.state;
    return (
      <Wrapper style={{ overflow: accountFlowList ? accountFlowList.length < 1 ? "hidden" : "auto" : null }}>
        <Spin spinning={isSpin}>
          <CreditAccountWrapper>
            <p>
              <i className="scmIconfont scm-icon-account"/>
            </p>
            <div>
              {/*<div>
                  <p>￥{balanceAmount}</p>
                  <p>可用额度（元）</p>
                </div>*/}
              <div>
                <p>￥{creditDetail.surplusToReturnAmount}</p>
                <p>欠款（元）</p>
              </div>
            </div>
            <p>
              {/*<span onClick={() => this.goToRecharge("repayment", allSurplusToReturnAmount)}>还款</span>*/}
            </p>
          </CreditAccountWrapper>
          <CreditAccountInfo>
            <CreditAccountInfoHeader>
              <span>信用明细</span>
            </CreditAccountInfoHeader>
            <CreditAccountInfoContent>
              <p>
                <span>账户编码</span>
                <span>{creditDetail.code}</span>
              </p>
              <p>
                <span>信用额度</span>
                <span>{Number(creditDetail.creditAmount).toFixed(2)}</span>
              </p>
              <p>
                <span>信用额度有效期</span>
                <span>{`${creditDetail.creditAmountExpiryDateFrom} 至 ${creditDetail.creditAmountExpiryDateTo}`}</span>
              </p>
              <p>
                <span>待还款金额</span>
                <span>{Number(creditDetail.surplusToReturnAmount).toFixed(2)}</span>
              </p>
              <p>
                <span>还款日期</span>
                <span>{creditDetail.repaymentTime}</span>
              </p>
              <p>
                <span>使用范围</span>
                <span>{creditDetail.accountRuleLimit ? creditDetail.accountRuleLimit.length > 15 ? creditDetail.accountRuleLimit.slice(0, 15) + "..." : creditDetail.accountRuleLimit : null}</span>
              </p>
            </CreditAccountInfoContent>
          </CreditAccountInfo>
          <AccountInfoWrapper>
            <AccountInfoHeader>
              <span className={key === "ALL" ? "active" : ""} onClick={() => this.queryAccountInfo("ALL")}>明细</span>
              <span className={key === "IN" ? "active" : ""} onClick={() => this.queryAccountInfo("IN")}>收入</span>
              <span className={key === "OUT" ? "active" : ""} onClick={() => this.queryAccountInfo("OUT")}>支出</span>
            </AccountInfoHeader>
            {
              accountFlowList ? accountFlowList.length > 0 ? accountFlowList.map((accountFlow, index) => {
                return (
                  <AccountInfoContent key={index} onClick={() => this.accountFlowDetail(accountFlow.accountFlowId)}>
                    <div>
                      <p>{accountFlow.accountUse}</p>
                      <p>{accountFlow.docDate}</p>
                    </div>
                    <div style={{
                      color: accountFlow.inOutType === $AccountType.ADD
                        ? "#FF4242" : "#333"
                    }}>
                      {accountFlow.inOutType === $AccountType.ADD ? "+" : "-"}{accountFlow.amount}
                    </div>
                  </AccountInfoContent>
                );
              }) : <NoGoods title="暂无账户信息"/> : null
            }
          </AccountInfoWrapper>
        </Spin>
      </Wrapper>
    );
  }
}

export default CreditDetail;

const Wrapper = styled.div`// styled
  & {
    width: 100%;
    height: calc(${document.documentElement.clientHeight}px);
    background: #ECF6FF;
    padding: 15px;
    overflow:auto;
    overflow-scrolling: touch;
    -webkit-overflow-scrolling: touch;
  }
`;

const CreditAccountWrapper = styled.div`// styled
  & {
    width: 100%;
    height: auto;
    text-align: center;
    padding: 15px 15px 0 15px;
    background: url("https://order.fwh1988.cn:14501/static-img/scm/icon-card-bg.png");
    border-radius: 5px;
    > p:nth-of-type(1) i {
      color: #307DCD;
      font-size: 44px;
    }
    > div {
      display: flex;
      > div {
        width: 100%;
        text-align: center;
      }
      > div:nth-of-type(1) {
        border-right: 1px solid #F2F2F2;
        > p:nth-of-type(1) {
          font-size: 20px;
          color: #307DCD;
          margin-bottom: 8px;
        }
        > p:nth-of-type(2) {
          font-size: 12px;
          color: #666;
          margin-bottom: 0;
        }
      }
      > div:nth-of-type(2) {
        > p:nth-of-type(1) {
          font-size: 20px;
          color: #FF3030;
          margin-bottom: 8px;
        }
        > p:nth-of-type(2) {
          font-size: 12px;
          color: #666;
          margin-bottom: 0;
        }
      }
    }
    > p:nth-of-type(2) {
      display: flex;
      margin-bottom: 0;
      > span {
        width: 100%;
        height: 28px;
        line-height: 28px;
        background: #307DCD;
        color: #fff;
        border: 1px solid transparent;
        border-radius: 14px;
        text-align: center;
        margin: 15px 90px;
      }
    }
  }
`;

const AccountInfoWrapper = styled.div`// styled
  & {
    width: 100%;
    height: auto;
    background: #fff;
    margin-top: 15px;
    border: 1px solid transparent;
    border-radius: 8px;
    padding-bottom: 30px;
  }
`;

const AccountInfoHeader = styled.div`// styled
  & {
    width: 100%;
    height: 40px;
    line-height: 40px;
    border-bottom: 1px solid #D8D8D8;
    display: flex;
    > span {
      display: inline-block;
      width: 100%;
      text-align: center;
      color: #666;
      margin: 0 30px;
      font-size: 14px;
    }
    .active {
      border-bottom: 2px solid #307DCD;
      color: #307DCD;
      font-size: 14px;
    }
  }
`;

const AccountInfoContent = styled.div`// styled
  & {
    width: 100%;
    height: 64px;
    border-bottom: 1px solid #D8D8D8;
    padding: 10px 15px;
    position: relative;
    > div:nth-of-type(1) {
      position: absolute;
      > p:nth-of-type(1) {
        color: #666;
        font-size: 12px;
        margin-bottom: 12px;
      }
      > p:nth-of-type(2) {
        color: #999;
        font-size: 12px;
        margin-bottom: 0;
      }
    }
    > div:nth-of-type(2) {
      position: absolute;
      top: 24px;
      right: 15px;
      color: #FF4242;
      font-size: 14px;
    }
  }
`;

const CreditAccountInfo = styled.div`// styled
  & {
    width: 100%;
    height: auto;
    background: #fff;
    margin-top: 15px;
    border: 1px solid transparent;
    border-radius: 8px;
  }
`;

const CreditAccountInfoHeader = styled.div`// styled
  & {
    width: 100%;
    height: 40px;
    line-height: 40px;
    background: #fff;
    border-bottom: 1px solid #D8D8D8;
    padding: 0px 15px;
    box-sizing: border-box;
    > span:nth-of-type(1) {
      font-family: MicrosoftYaHei;
      font-size: 14px;
      color: #333333;
    }
    > span:nth-of-type(2) {
      float: right;
      font-family: MicrosoftYaHei;
      font-size: 14px;
      color: #307DCD;
    }
  }
`;

const CreditAccountInfoContent = styled.div`// styled
  & {
    width: 100%;
    height: 194px;
    padding: 15px;
    > p {
      margin-bottom: 8px;
      > span:nth-of-type(1) {
        font-family: MicrosoftYaHei;
        font-size: 13px;
        color: #333333;
      }
      > span:nth-of-type(2) {
        font-family: MicrosoftYaHei;
        font-size: 13px;
        color: #666666;
        text-align: right;
        float: right;
      }
    }
  }
`;
