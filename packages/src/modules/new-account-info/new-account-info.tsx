import { autowired } from "@classes/ioc/ioc";
import { observer } from "mobx-react";
import { transaction } from "mobx";
import React from "react";
import { withRouter } from "react-router";
import styled from "styled-components";
import { $AccountInfoMv } from "./new-account-info-mv";
import { $AccountType } from "../../classes/const/$account-type";
import { NoGoods } from "../../components/no-goods/no-goods";
import { SITE_PATH } from "../app";
import { Spin } from "antd";
import { ScrollAbilityWrapComponent } from "../../components/scroll-ability/wrap";
import { LoadingTip } from "../../components/loading-marked-words";
import { $AppStore } from "../../classes/stores/app-store-mv";
import { CustomMonthModal } from "../../components/custom-month-modal/custom-month-modal";
import { AccountBusinessModal } from "../../components/account-business-modal/account-business-modal";
import { AccountFlowItem } from "../../components/account-flow-item/account-flow-item";
import { AccountStatusModal } from "../../components/account-status-modal/account-status-modal";
import { Modal, Toast } from "antd-mobile";
import moment from "moment";
import { $SearchDateType } from "@classes/const/$search-date-type";
import { $SearchTimeType } from "@classes/const/$search-time-type";
import { $SearchKey, SearchKeyName } from "@classes/const/$search-key";

const SearchKeyList = [
  {
    key: $SearchKey.ALL,
    name: SearchKeyName[$SearchKey.ALL],
  },
  {
    key: $SearchKey.IN,
    name: SearchKeyName[$SearchKey.IN],
  },
  {
    key: $SearchKey.OUT,
    name: SearchKeyName[$SearchKey.OUT],
  },
];

@withRouter
@observer
class NewAccountInfoWrap extends React.Component<any, any> {

  @autowired($AccountInfoMv)
  public $myMv: $AccountInfoMv;

  @autowired($AppStore)
  public $AppStore: $AppStore;

  public constructor(props) {
    super(props);
    this.state = {
      showDateModal: false,
      showBusinessModal: false,
      showStatusModal: false,
    };
  }

  public componentDidMount() {
    const { accountTypeCode } = this.props.match.params || { accountTypeCode: "" };
    this.$myMv.clearMVData();
    this.$myMv.dateType = $SearchTimeType.MONTH;
    this.$myMv.accountFlowList = [];
    if (accountTypeCode === $AccountType.RETURN_FREE_DISTRIBUTION) {
      document.title = "可用配赠账户";
      if (moment().isBefore(moment("2025-02-01"))) {
        this.$myMv.startTime = moment().format("YYYY-01-16");
      } else {
        this.$myMv.startTime = $SearchDateType.FirstDayOfMonth;
      }
    } else if (accountTypeCode === $AccountType.DEDUCTION_LIMIT) {
      document.title = "返利账户";
      this.$myMv.startTime = $SearchDateType.FirstDayOfMonth;
    }
    this.$myMv.endTime = $SearchDateType.LastDayOfMonth;
    this.initPage();
  }

  public componentWillUnmount() {
    this.$myMv.dateType = $SearchTimeType.MONTH;
    this.$myMv.startTime = $SearchDateType.FirstDayOfMonth;
    this.$myMv.endTime = $SearchDateType.LastDayOfMonth;
    this.$myMv.status = "";
    this.$myMv.businessType = "";
  }

  public componentWillReceiveProps(nextProps: Readonly<any>, nextContext: any): void {
    const { isScrollEnd } = nextProps;
    const { showDateModal, showBusinessModal, showStatusModal } = this.state;
    // isScrollEnd && isScrollEnd !== this.props.isScrollEnd &&
    if (isScrollEnd && isScrollEnd !== this.props.isScrollEnd && !showDateModal && !showBusinessModal && !showStatusModal) {
      this.loadData();
    }
  }

  public initPage = () => {
    const { accountTypeCode, accountId } = this.props.match.params || { accountTypeCode: "", accountId: "" };
    const { pageSize, pageIndex, key, businessType, startTime, endTime, status } = this.$myMv;
    this.$myMv.showSpin();
    this.$myMv.isLoading = true;
    const params = {
      accountId,
      condition: key,
      pageIndex,
      pageSize,
      businessType,
      status,
      start: startTime,
      end: endTime,
    };
    const { loadingEnd } = this.props;
    loadingEnd && loadingEnd(false);
    this.firstLoadFetchAccountInfo(params);
  }

  public firstLoadFetchAccountInfo = (params) => {
    transaction(() => {
      this.$myMv.fetchAccountInfo(params, false);
    });
  }

  public queryAccountInfo = (activeKey) => {
    this.$myMv.key = activeKey;
    this.$myMv.isFinished = false;
    this.$myMv.pageIndex = 0;
    this.initPage();
  }

  public loadData = () => {
    const { isFinished, isLoading, pageIndex } = this.$myMv;
    if (!isFinished) {
      this.$myMv.showSpin();
      if (!isLoading) {
        this.$myMv.pageIndex = pageIndex + 1;
        this.loadList();
      }
    }
  }

  public loadList = () => {
    const { pageSize, key, startTime, status, endTime, businessType, pageIndex } = this.$myMv;
    this.$myMv.showSpin();
    const params = {
      accountId: this.props.match.params.accountId,
      pageIndex,
      pageSize,
      businessType,
      condition: key,
      status,
      start: startTime,
      end: endTime,
    };
    this.$myMv.isLoading = true;
    const { loadingEnd } = this.props;
    this.$myMv.fetchAccountInfo(params, true).then((res) => {
      loadingEnd && loadingEnd(res);
    });
  }

  public onSearchDate = (startTime, endTime, dateType) => {
    if (moment(endTime).isBefore(startTime)) {
      Toast.info("结束时间不得大于开始时间");
      return;
    }
    this.$myMv.pageIndex = 0;
    this.$myMv.startTime = startTime;
    this.$myMv.endTime = endTime;
    this.$myMv.dateType = dateType;
    this.initPage();
  }

  public onSearchBusiness = (val) => {
    this.$myMv.pageIndex = 0;
    this.$myMv.businessType = val[0];
    this.initPage();
  }

  public onSearchType = (val) => {
    this.$myMv.pageIndex = 0;
    this.$myMv.status = val[0];
    this.initPage();
  }

  public goAccountDetail = (item) => {
    sessionStorage.setItem("detailParams", JSON.stringify(item));
    sessionStorage.setItem("newAccountBusinessType", item.businessType);
    this.props.history.push({ pathname: `/${SITE_PATH}/new-account-info-detail/${this.props.match.params.accountId}` });
  }

  public goGiftsLimitList = () => {
    this.props.history.push({ pathname: `/${SITE_PATH}/gifts-limit-list/${this.props.match.params.accountId}` });
  }

  public goFreezeList = () => {
    this.props.history.push({ pathname: `/${SITE_PATH}/freeze-list/${this.props.match.params.accountId}` });
  }

  public goTransfer = () => {
    this.props.history.push({ pathname: `/${SITE_PATH}/my-transfer/${this.props.match.params.accountId}/${this.props.match.params.accountTypeCode}` });
  }

  public getShowStatus = (status) => {
    if (status === "N") {
      return "未结算";
    } else if (status === "Y") {
      return "已结算";
    } else {
      return "状态";
    }
  }

  public onShowModal = (showTipsModal: boolean) => {
    this.setState({ showTipsModal });
  }

  public renderHfpzHeader = () => {
    const { balanceAmount, totalAmount, orderQuotaLimit, buttonList } = this.$myMv;
    return <SHfpzHeader>
      <div>
        <div >
          <div>可用配赠额度</div>
          <div>{balanceAmount}</div>
        </div>
        <div>
          <div>总配赠额度</div>
          <div onClick={() => this.goGiftsLimitList()}>{orderQuotaLimit || 0} <i style={{color: "gray"}} className="scmIconfont scm-icon-jiantou-you" /></div>
          <div >
            配赠上限
            <span>{totalAmount}</span>
          </div>
        </div>
      </div>
      {/* <div>
      {
        // TODO 转账按钮需要注释
        buttonList && buttonList.filter((item) => item.key === "transfer").length > 0 && <div onClick={this.goTransfer}>余额转店</div>
      }
      </div> */}
    </SHfpzHeader>;
  }

  public renderSyfzHeader = () => {
    const { balanceAmount, buttonList, totalAmount, noSettlementAmount } = this.$myMv
    return <SSyfzHeader>
      <div>
        <div>
          <div>返利额度</div>
          <div>{totalAmount}</div>
        </div>
        {
          buttonList && buttonList.filter((item) => item.key === "transfer").length > 0 && <div onClick={this.goTransfer}>余额转店</div>
        }
      </div>
      <div>
        <div>
          <div>可用返利额度</div>
          <div>{balanceAmount}</div>
        </div>
        <div>
          <div>未结算额度 <i style={{ marginLeft: 4 }} onClick={() => this.onShowModal(true)} className="scmIconfont scm-icon-yiwenkongxin" /></div>
          <div>{noSettlementAmount}</div>
        </div>
      </div>
    </SSyfzHeader>;
  }

  public render() {
    const { accountTypeCode } = this.props.match.params || { accountTypeCode: "" };
    const { showDateModal, showBusinessModal, showStatusModal, showTipsModal } = this.state;
    const { accountFlowList, key, isLoading, isFinished, startTime, endTime, after, inAmount, outAmount, dateType } = this.$myMv;
    const isFixed = showDateModal || showBusinessModal || showStatusModal || showTipsModal;
    return (
      <Spin spinning={isLoading} style={{ pointerEvents: isFixed ? "none" : "auto" }}>
        <Wrapper className="account-content-info-warp">
          <SHeadBg/>
          {
            accountTypeCode === $AccountType.RETURN_FREE_DISTRIBUTION && this.renderHfpzHeader()
          }
          {
            accountTypeCode === $AccountType.DEDUCTION_LIMIT && this.renderSyfzHeader()
          }
          <SSearch>
            <div className="search-more">
              <div onClick={() => this.setState({ showDateModal: true })}>
                <span>
                  {
                    dateType === $SearchTimeType.MONTH ? startTime.slice(0, 7) : `${startTime}~${endTime}`
                  }
                </span>
                <i className="scmIconfont scm-icon-xiajiantou" />
              </div>
              <div onClick={() => this.setState({ showBusinessModal: true })}>
                <span>业务类型</span>
                <i className="scmIconfont scm-icon-xiajiantou" />
              </div>
              {
                accountTypeCode === $AccountType.DEDUCTION_LIMIT &&
                <div onClick={() => this.setState({ showStatusModal: true })}>
                  <span>状态</span>
                  <i className="scmIconfont scm-icon-xiajiantou" />
                </div>
              }
            </div>
            <div className="search-key">
              {
                SearchKeyList.map((item, index) => {
                  return <span key={index} className={key === item.key && "active"} onClick={() => this.queryAccountInfo(item.key)}>
                    {item.name}
                  </span>;
                })
              }
            </div>
            {
              accountTypeCode === $AccountType.DEDUCTION_LIMIT && key === $SearchKey.ALL && (after > 0 || inAmount > 0 || outAmount > 0) && <div className="search-board">
                {
                  after > 0 && <div>
                    <div>{after}</div>
                    <div>{ accountTypeCode === $AccountType.DEDUCTION_LIMIT ? "结余" : "可用额度" }</div>
                  </div>
                }
                {
                  inAmount > 0 && <div>
                    <div className="red">{inAmount}</div>
                    <div>{accountTypeCode === $AccountType.DEDUCTION_LIMIT ? "增加" : "可用增加"}</div>
                  </div>
                }
                {
                  outAmount > 0 && <div>
                    <div>{outAmount}</div>
                    <div>{accountTypeCode === $AccountType.DEDUCTION_LIMIT ? "减少" : "可用减少"}</div>
                  </div>
                }
              </div>
            }
            {
              accountTypeCode === $AccountType.DEDUCTION_LIMIT && (key === $SearchKey.IN || key === $SearchKey.OUT) && <div className="single-board">
                {
                  key === $SearchKey.IN && <div className="red">增加 <span>{inAmount}</span></div>
                }
                {
                  key === $SearchKey.OUT && <div className="gray">减少 <span>{outAmount}</span></div>
                }
              </div>
            }
          </SSearch>
          <SList>
            {
              accountFlowList && accountFlowList.length > 0 ? accountFlowList.map((item, index) => {
                return <AccountFlowItem item={item} key={index} accountTypeCode={accountTypeCode} goAccountFlowDetail={() => this.goAccountDetail(item)}/>;
              }) : <NoGoods title="暂无数据"/>
            }
            {
              accountFlowList && accountFlowList.length > 0 ?
                <LoadingTip
                  isFinished={isFinished}
                  isLoad={isLoading}
                /> : null
            }
          </SList>
          <CustomMonthModal
            visible={showDateModal}
            type={"range"}
            dateType={dateType}
            startDate={startTime}
            endDate={endTime}
            pageType={accountTypeCode}
            onCancel={() => this.setState({ showDateModal: false })}
            onConfirm={this.onSearchDate}
          />
          <AccountBusinessModal
            visible={showBusinessModal}
            accountTypeCode={accountTypeCode}
            onCancel={() => this.setState({ showBusinessModal: false })}
            onConfirm={this.onSearchBusiness}
          />
          <AccountStatusModal
            visible={showStatusModal}
            onCancel={() => this.setState({ showStatusModal: false })}
            onConfirm={this.onSearchType}
          />
          <Modal visible={showTipsModal} transparent={true} maskClosable={false}>
            <STips>
              <div className="title">结算规则</div>
              <div className="content">每月1号，结算上月应返利</div>
              <div className="btn" onClick={() => this.onShowModal(false)}>关闭</div>
            </STips>
          </Modal>
        </Wrapper>
      </Spin>
    );
  }
}

const NewAccountInfo = ScrollAbilityWrapComponent(NewAccountInfoWrap);

export default NewAccountInfo;

const SHfpzHeader = styled.div`
  & {
    background: #FFF;
    width: 92vw;
    z-index: 10;
    border-radius: 8px;
    padding: 16px;
    display: flex;
    flex-direction: column;
    box-shadow: 0px 0px 10px 0px rgba(45, 45, 45, 0.0694);

    > div:nth-child(1) {
      display: flex;
      justify-content: space-between;

      > div {
        width: 45%;

        > div:nth-child(1) {
          font-size: 13px;
          font-weight: 500;
          color: #333;
        }

        > div:nth-child(2) {
          font-size: 28px;
          font-weight: 500;
          color: #FF3030;
        }

        > div:nth-child(3) {
          font-size: 12px;
          color: #333;
          display: flex;
          align-items: center;

          > span {
            margin-left: 5px;
            font-weight: 500;
            color: #437DF0;
          }

          > img {
            width: 14px;
            height: 14px;
          }
        }
      }
    }

    > div:nth-child(2) {
      margin: 0 30px;
      background: #437DF0;
      font-size: 14px;
      color: #FFF;
      font-weight: 500;
      padding: 8px 40px;
      border-radius: 30px;
      margin-top: 10px;
      text-align: center;
    }
  }
`;

const STips = styled.div`
  & {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;

    .title {
      font-size: 20px;
      font-weight: 500;
      color: #333;
    }

    .content {
      font-size: 14px;
      margin-top: 12px;
    }

    .btn {
      margin-top: 25px;
      background: #437DF0;
      color: #fff;
      font-size: 14px;
      font-weight: 500;
      padding: 9px 45px;
      border-radius: 25px;
      margin-bottom: 10px;
    }
  }
`;

const Wrapper = styled.div`// styled
  & {
    width: 100%;
    height: 100vh;
    padding: 15px;
    display: flex;
    flex-direction: column;
    align-items: center;
    background: #F7F7F7;
  }
`;

const SHeadBg = styled.div`
  & {
    position: absolute;
    top: 0;
    width: 100vw;
    height: 32vw;
    background: #437DF0;
  }
`;

const SSyfzHeader = styled.div`
  & {
    background: #FFF;
    width: 92vw;
    z-index: 10;
    border-radius: 8px;
    padding: 16px;
    display: flex;
    flex-direction: column;
    box-shadow: 0 0 10px 0 rgba(45, 45, 45, 0.0694);

    > div:nth-child(1) {
      display: flex;
      justify-content: space-between;

      > div:nth-child(1) {
        display: flex;
        flex-direction: column;

        > div:nth-child(1) {
          font-size: 13px;
          font-weight: 500;
          color: #333;
        }

        > div:nth-child(2) {
          font-size: 32px;
          font-weight: 500;
          color: #FF3030;
        }
      }

      > div:nth-child(2) {
        background: #437DF0;
        border-radius: 16px;
        font-size: 14px;
        color: #FFF;
        text-align: center;
        line-height: 36px;
        height: 36px;
        width: 106px;
      }
    }

    > div:nth-child(2) {
      display: flex;
      justify-content: space-between;

      > div:nth-child(1) {
        display: flex;
        flex-direction: column;

        > div:nth-child(1) {
          font-size: 12px;
          color: #666;
        }

        > div:nth-child(2) {
          font-size: 18px;
          font-weight: 500;
          color: #333;

          > span {
            color: #437DF0;
            font-size: 12px;
          }
        }
      }

      > div:nth-child(2) {
        display: flex;
        flex-direction: column;
        margin-right: 20vw;

        > div:nth-child(1) {
          font-size: 12px;
          color: #666;
          display: flex;
          align-items: center;
        }

        > div:nth-child(2) {
          font-size: 18px;
          font-weight: 500;
          color: #333;
        }
      }
    }
  }
`;

const SList = styled.div`
  & {
    width: 100%;
    margin-top: 12px;
    border-radius: 8px;
    overflow: scroll;
    background: #FFF;

    .date-title {
      padding: 12px;
      font-size: 16px;
      font-weight: 500;
      color: #333;
    }
  }
`;

const SSearch = styled.div`// styled

  & {
    width: 100%;
    height: auto;
    background: #fff;
    margin-top: 12px;
    border-radius: 8px;
    box-shadow: 0px 0px 10px 0px rgba(45, 45, 45, 0.0694);

    .search-key {
      width: 100%;
      height: 40px;
      line-height: 40px;
      display: flex;
      background: #F7F7F7;

      > span {
        display: inline-block;
        width: 100%;
        text-align: center;
        color: #666;
        margin: 0 30px;
        font-size: 14px;
      }

      .active {
        border-bottom: 2px solid #307DCD;
        color: #307DCD;
        font-size: 14px;
      }
    }

    .search-more {
      display: flex;
      justify-content: space-between;
      padding: 12px;
      background: #FFF;

      > div {
        > span {
          color: #333;
          font-size: 14px;
          margin-right: 3px;
        }

        > img {
          width: 14px;
          height: 14px;
        }
      }
    }

    .single-board {
      padding: 12px 24px;
      background: #FFF;
      font-size: 14px;

      .red {
        display: flex;
        align-items: center;

        > span {
          margin-left: 5px;
          color: #FF3030;
          font-size: 18px;
          font-weight: 500;
        }
      }

      .gray {
        display: flex;
        align-items: center;

        > span {
          margin-left: 5px;
          color: #333333;
          font-size: 18px;
          font-weight: 500;
        }
      }
    }

    .search-board {
      padding: 12px 36px;
      display: flex;
      justify-content: space-between;
      background: linear-gradient(181deg, #F0F3FC 4%, #FFFFFF 64%);

      > div {
        display: flex;
        flex-direction: column;
        text-align: left;

        > div:nth-child(1) {
          font-size: 18px;
          font-weight: 500;
          color: #333;
        }

        > div:nth-child(2) {
          font-size: 14px;
          color: #666;
          margin-top: 5px;
        }

        .red {
          color: #FF3030 !important;
        }
      }
    }
  }
`;
