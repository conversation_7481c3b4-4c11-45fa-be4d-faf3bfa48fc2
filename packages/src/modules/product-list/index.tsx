import { autowired } from "@classes/ioc/ioc";
import { Spin } from "antd";
import { Modal,Toast } from "antd-mobile";
import { findIndex } from "lodash";
import { transaction } from "mobx";
import { observer } from "mobx-react";
import * as React from "react";
import ReactDOM from "react-dom";
import { withRouter } from "react-router";
import styled from "styled-components";
import { $ActiveType } from "../../classes/const/$active-type";
import { $Product } from "../../classes/entity/$product";
import { $ProductService } from "../../classes/service/$product-service";
import { Footer } from "../../components/footer/footer";
import { NoGoods } from "../../components/no-goods/no-goods";
import { SearchMenuBar } from "../../components/search-menu-bar/search-menu-bar";
import { SITE_PATH } from "../app";
import { $CartMv } from "../shop-cart/cart-mv";
import { $SubmitOrderMv } from "../submit-order/submit-order-mv";
import { $ProductListMv } from "./$product-list-mv";
import { $CartType } from "../../classes/const/$cart-type";
import ProBox from "./pro-box";
import { $AppStore, AppStoreKey } from "../../classes/stores/app-store-mv";
import { $ReturnedSalesMv } from "../returned-sales/$returned-sales-mv";
import  AnnouncementModel from "../../components/announcement-model/announcement-model"
import { $EnterType } from "../../classes/const/$enter-Type"
import { getUrlParam } from "../../classes/utils/UrlUtils";
import { $ExceedQuotaStatus } from "@classes/const/$exceed-quota-status";
import { $AccountType } from "@classes/const/$account-type";

const querystring = require("querystring");

declare let require: any;
const alert = Modal.alert;

@withRouter
@observer
class ProductList extends React.Component<any, any> {
  @autowired($AppStore)
  public $AppStore: $AppStore;

  @autowired($ProductListMv)
  public $mv: $ProductListMv;

  @autowired($CartMv)
  public $CartMv: $CartMv;

  @autowired($SubmitOrderMv)
  public $SubmitOrderMv: $SubmitOrderMv;

  @autowired($ProductService)
  public $ProductService: $ProductService;
  @autowired($ReturnedSalesMv)
  public $ReturnedSalesMv: $ReturnedSalesMv;

  constructor(props) {
    super(props);
    this.state = {
      refreshing: false,
      pageSize: 10,
      lastDataLength: 0,
      hasGoods: true,
      finished: false,
      isLoad: false,
      isRequest: true,
      itemCount: null,
      orderSchemeType: "",
      isShowAnnouncementModel:false,
      isShowQuotaTip: false, // 是否显示额度提示
      isShowExceedQuota: false, // 是否显示超出额度提示


    };
  }
  // 离开记录滚动高度
  public saveData = () => {
    const {
      pageIndex,
      productClassificationId,
      products,
    } = this.$mv;
    const shopListStore = {
      pageIndex,
      productClassificationId,
      productsLength: products.length,
      scrollTop: $(".module-scroll-ability-wrap") && $(".module-scroll-ability-wrap").scrollTop(),
    };
    this.$AppStore.savePageMv(AppStoreKey.SHOPLIST, shopListStore);
  }
  public componentWillUnmount(): void {
    this.saveData();
  }
  public componentDidMount() {
    document.title = "商品分类列表";
    this.$AppStore.queryShopOverdue("", this.initPage, true);
  }
  public initPage = async () => {
    this.$mv.clearClassesList();
    this.$mv.clearProducts();
    this.$mv.clearClassesActiveName();
    // this.$SubmitOrderMv.fetchQueryRole();
    this.$mv.classesActive = undefined;
    this.$mv.products = [];
    this.setState({ isLoad: true });
    this.$mv.showSpin();
    await this.$CartMv.fetchShopSchemeTips();
    this.$CartMv.fetchShopcartproductnum().then((res) => {
      if (res.errorCode === $CartType.NOSCHEME) {
        this.$mv.hideSpin();
        this.$CartMv.setIsAgency();
        alert("暂未选择订货方案，是否继续进入订货方案开始订货", "", [
          {
            text: "取消", onPress: () => {
              // window.location.href = document.referrer;
              history.go(-1);
            },
          },
          {
            text: "继续", onPress: () => {
              // console.log(909090);
              this.$AppStore.clearPageMv(AppStoreKey.ORDERSCHEMELIST);
              this.$ReturnedSalesMv.businesssControlCheck({orgId:res.orderPartyId,businessTypeControl:'SalesOrderControl'}).then(data=>{
                if (data.errorCode == "-1") {
                  history.go(-1);
                  Toast.info(data.errorMessage)

                }else{
                  this.props.history.push({
                    pathname: `/${SITE_PATH}/select-scheme`,
                    state: { orderPartyId: `${res.orderPartyId}` },
                  });
                }
              })

            },
          },
        ]);
      } else {
        this.$mv.fetchClasses()
          .then((data) => {
            this.$mv.hideSpin();
            if (data.errorCode === $CartType.TIMELIMIT) {
              alert(`${data.errorMessage}`, "", [
                {
                  text: "确认", onPress: () => {

                  },
                },
              ]);
            }
            this.$mv.setClassesList(data.productClassificationList);
            const { classesActive, classesList } = this.$mv;
            if (classesActive === undefined) {
              let oldData = this.$AppStore.getPageMv(AppStoreKey.SHOPLIST);
              if (oldData) {
                oldData = JSON.parse(oldData);
                const {
                  scrollTop,
                  pageIndex,
                  productClassificationId,
                  productsLength,
                } = oldData;
                this.setState({ isLoad: true });
                transaction(() => {
                  let index = findIndex(classesList, { oid: productClassificationId });
                  index = index > -1 ? index : 0;
                  const { pageSize } = this.state;
                  this.setState({
                    pageSize: productsLength,
                  }, () => {
                    this.changeClasses(index, classesList[index].oid, false, () => {
                      this.$mv.pageIndex = Number(pageIndex);
                      $(".module-scroll-ability-wrap").scrollTop(scrollTop);
                      this.setState({ pageSize });
                      this.$AppStore.clearPageMv(AppStoreKey.SHOPLIST);
                    });
                  });
                });
              } else {
                this.changeClasses(0, classesList[0].oid, false);
              }
            }
          }).catch((err) => {
          this.setState({ finished: true });
        });
      }
      if (this.$CartMv.selectedAmount > this.$CartMv.totalQuotaAmount) {
        this.setState({ isShowExceedQuota: true })
      }
      this.setState({ isLoad: false });
    });
  }
  public componentWillReceiveProps(nextProps: Readonly<any>, nextContext: any): void {
    const { isScrollEnd } = nextProps;
    if (isScrollEnd && isScrollEnd !== this.props.isScrollEnd) {
      this.loadData();
    }
  }

  public changeClasses = (index, id, cancat: boolean, cb ?) => {
    this.setState({ isLoad: true });
    this.$mv.showSpin();
    const { classesList, pageIndex } = this.$mv;
    const params = {
      pageIndex: cancat ? pageIndex : 0,
      pageSize: this.state.pageSize,
      productClassificationId: id,
    };
    this.$mv.setProductClassificationId(id);
    if (cancat === false) {
      this.$mv.setProducts([]);
      this.$mv.setClassesActive(index);
    }
    this.setState({ finished: false, isRequest: false });
    this.$mv.fetchProducts(params)
      .then((data) => {
        if(data.productList && data.productList.length>0 && getUrlParam("orderSchemeId")){
          this.setState({isShowAnnouncementModel:true})
        }else{
          //如果商品列表加载存在异常，公告弹窗不展示，并且建立临时会话禁止公告弹窗再次弹出
          getUrlParam("orderSchemeId") && sessionStorage.setItem($EnterType.ENTER_ORDER_SCHEME,"enterType_" + $EnterType.ENTER_ORDER_SCHEME)
        }


        const { itemCount, coverCount, productList, orderPriceViewPermission, retailPriceViewPermission, orderSchemeType } = data;

        transaction(() => {
          if (cancat === false) {
            this.$mv.setProducts(productList);
            this.$mv.setOrderPriceViewPermission(orderPriceViewPermission, retailPriceViewPermission);
            this.$mv.setClassesActiveName(classesList[index].name);
            this.$mv.setPageIndex(1);
            // @ts-ignore
            // ReactDOM.findDOMNode(this.refs.pullToFresh).scrollTop = 0;
            this.setState({ hasGoods: productList.length !== 0 });
          } else {
            this.$mv.pushProducts(productList);
            this.$mv.setPageIndex(pageIndex + 1);
          }
          this.$mv.hideSpin();
          const count = cancat ? this.state.itemCount - coverCount : itemCount - coverCount;
          this.setState({
            finished: this.$mv.products.length >= count,
            isRequest: true,
            itemCount: count,
            isLoad: false,
            orderSchemeType,
          });
          cb && cb();
        });
      });
  }
  public cartWrapper = (products: $Product[], cartProducts: $Product[]) => {
    products.forEach((product) => {
      const index = findIndex(cartProducts, { productSkuId: product.productSkuId });
      if (index > -1) {
        product.setQuantity(cartProducts[index].quantity);
      } else {
        product.setQuantity(0);
      }
    });
    return products;
  }

  public loadData = () => {
    if (this.state.finished === false) {
      this.$mv.showSpin();
      if (this.state.isRequest) {
        this.changeClasses(null, this.$mv.productClassificationId, true);
      }
    }
  }

  public onShowQuotaTip = (isShow: boolean) => {
    this.setState({ isShowQuotaTip: isShow })
  }

  public toDetail = (data) => {
    this.$mv.setDetailObject(data);
    if (data.skuType === $CartType.VIRTUALSUIT) {
      this.props.history.push({ pathname: `/${SITE_PATH}/virtualsuit-detail/${data.productSkuId}` });
    } else {
      this.props.history.push({ pathname: `/${SITE_PATH}/commodity-details/${data.productSkuId}` });
    }
  }

  public renderTips = () => {
    const { isAgency, quotaAmount, totalQuotaAmount, itemList, selectedAmount, accountTypeCode, totalCount } = this.$CartMv;
    if (totalCount > 0 && accountTypeCode !== "") {
      if (accountTypeCode === $AccountType.RETURN_FREE_DISTRIBUTION) {
        return <STips className="pz">
          <div className="select">已选 <span style={{ color: selectedAmount > totalQuotaAmount ? "#FF3030" : "#333333" }}>{selectedAmount}</span> 额度产品</div>
          <div className="quota">
            当前门店可用 {quotaAmount}，当前用户权限下转账最大可使用 {totalQuotaAmount}
            <i className={"scmIconfont scm-icon-yiwenkongxin"}
               onClick={() => this.onShowQuotaTip(true)}
               style={{ position: "relative", marginLeft: 5, fontSize: 13 }} />
          </div>
        </STips>
      } else {
        return <STips>
          <div className="select">已选 <span style={{ color: selectedAmount > totalQuotaAmount ? "#FF3030" : "#333333" }}>{selectedAmount}</span> 额度产品</div>
          <div className="quota">
            总可用额度{totalQuotaAmount}
            <i className={"scmIconfont scm-icon-yiwenkongxin"}
               onClick={() => this.onShowQuotaTip(true)}
               style={{ position: "relative", marginLeft: 5, fontSize: 13 }} />
          </div>
        </STips>
      }
    } else {
      return null;
    }
  }

  renderTipsModalContent = () => {
    const { totalPzAbleAmount, totalHFPZAbleAmount, totalQuotaAmount, itemList, selectedAmount, accountTypeCode, totalCount } = this.$CartMv;
    if (accountTypeCode === $AccountType.RETURN_FREE_DISTRIBUTION) {
      return <SQuotaModal>
        <div className="total">
          <span>最大可使用额度：</span>
          <span>{totalQuotaAmount}</span>
        </div>
        <div className="item-wrapper pz">
          <div className="item">
            <div>
              各店汇总
            </div>
            <div>{totalPzAbleAmount}额度</div>
            <div>{totalHFPZAbleAmount}上限</div>
          </div>
          <div className="tips">
            <span>当前用户有权限门店</span>
            <span>额度和上限二者取其小为可用</span>
          </div>
          {
            itemList && itemList.length > 0 && itemList.map((item, index) => {
              return <div className="item" key={index}>
                <div>{item.orgName}{index === 0 && <span>本门店</span>}</div>
                <div>{item.pz_accountBalance}额度</div>
                <div>{item.hfpz_accountBalance}上限</div>
              </div>;
            })
          }
        </div>
      </SQuotaModal>;
    } else {
      return <SQuotaModal>
        <div className="total">
          <span>总可用额度：</span>
          <span>{totalQuotaAmount}</span>
        </div>
        <div className="item-wrapper">
          {
            itemList && itemList.length > 0 && itemList.map((item, index) => {
              return <div className="item" key={index}>
                <div>{item.orgName}{index === 0 && <span>本门店</span>}</div>
                <div>{item.accountBalance}</div>
              </div>;
            })
          }
        </div>
      </SQuotaModal>
    }
  }

  public render() {
    const { classesActive, classesActiveName, classesList, products, isSpin, orderPriceViewPermission, retailPriceViewPermission } = this.$mv;
    const  orderSchemeId = getUrlParam("orderSchemeId")||'';
    const  orgIdList = [getUrlParam("orderPartyId")]|| [];
    const newProducts = this.cartWrapper(products, this.$CartMv.products);
    const { isAgency, totalQuotaAmount, itemList, selectedAmount, accountTypeCode, totalCount } = this.$CartMv;
    const { finished, isLoad, orderSchemeType,isShowAnnouncementModel, isShowQuotaTip } = this.state;

    return (
      <Wrapper className="indexPage">
        <SearchMenuBar showMenu={true}/>
        <ClassesBox>
          <div className="classes-list">
            {
              classesList.map((value, index) => {
                return <div
                  key={value.oid}
                  onClick={() => this.changeClasses(index, value.oid, false)}
                  className={index === classesActive ? "active" : null}
                >
                  {value.name.substr(0, 4)}<br/>{value.name.substr(4, 4)}
                </div>;
              })
            }
          </div>
          <div className="product-list">
            <h4>{classesActiveName}</h4>
            {
              this.state.hasGoods ? null : <NoGoods title="暂无任何商品~" height={document.documentElement.clientHeight / 2}/>
            }
            {
              newProducts && newProducts.length > 0 &&
                <div className="products">
									<ProBox
										newProducts={newProducts}
										loadData={this.loadData}
										orderPriceViewPermission={orderPriceViewPermission}
										retailPriceViewPermission={retailPriceViewPermission}
										finished={finished}
										isLoad={isLoad}
										leaveCurrentPage={this.saveData}
										orderSchemeType={orderSchemeType}
									/>
                </div>
            }
          </div>
        </ClassesBox>
        {this.renderTips()}
        <Footer
          isAgency={isAgency}
          activeKey={$ActiveType.CATEGORY_KEY}
          count={this.$CartMv.totalCount}
          leaveCurrentPage={this.saveData}
        />
               {isShowAnnouncementModel && <AnnouncementModel
                enterType={$EnterType.ENTER_ORDER_SCHEME}
                isAutoNotice="Y"//'Y'自动弹框提示
                orderSchemeId={ orderSchemeId }
                orgIdList={ orgIdList }
              />}
        <Modal
          visible={isShowQuotaTip}
          transparent={true}
          maskClosable={false}
          popup={true}
          animationType="slide-up"
          onClose={() => this.onShowQuotaTip(false)}
          title="详情"
          closable={true}
        >
          {this.renderTipsModalContent()}
        </Modal>
      </Wrapper>
    );
  }
}

export default ProductList;

const SQuotaModal = styled.div`
  & {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding-bottom: 30px;

    .text {
      color: #666;
      font-size: 12px;
      border-top: 1px solid #E6E6E6;
      margin-top: 8px;
      padding-top: 8px;
    }

    .total {
      border-radius: 4px;
      background: #F8F8F8;
      padding: 8px;
      width: 100%;
      margin-top: 8px;
      text-align: left;

      > span:nth-child(1) {
        color: #333;
        font-weight: 500;
        font-size: 16px;
      }

      > span:nth-child(2) {
        color: #FF3030;
        font-weight: 500;
        font-size: 18px;
      }
    }

    .item-wrapper {
      max-height: 400px;
      overflow-y: scroll;
      width: 100%;
      margin-top: 12px;

      &.pz{
        .item{
          >div{
            width: 30%;
            text-align: right;
          }
          > div:nth-child(1){
            width: 40%;
            text-align: left;
          }
        }
      }

      .tips{
        display: flex;
        justify-content: space-between;
        font-size: 12px;
        color: #999999;
        padding-bottom: 8px;
      }
    }

    .item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      width: 100%;
      border-top: 1px solid #E8E8E8;
      padding: 8px 0px;
      color: #333;
      font-size: 13px;
      &:first-child{
        border-top: none;
      }
      &:last-child{
        border-bottom: 1px solid #E8E8E8;
      }

      > div:nth-child(1) {
        text-align: left;
        padding-right: 12px;
        display: flex;
        align-items: center;

        > span {
          color: #FFF;
          background: #417BEE;
          border-radius: 2px;
          padding: 2px 4px;
          line-height: 12px;
          margin-left: 2px;
          font-size: 10px;
          white-space: nowrap;
        }
      }
    }

    .title {
      color: #333;
      font-size: 13px;
    }

    .text {
      color: #999;
      font-size: 13px;
    }
  }
`

const STips = styled.div`
  & {
    position: fixed;
    bottom: 50px;
    width: 100%;
    height: 40px;
    background: #FBF1E8;
    display: flex;
    justify-content: space-between;
    padding: 0 20px;
    align-items: center;
    font-size: 13px;

    &.pz{
      display: block;
      padding: 5px;
      height: auto;
      .quota{
        font-size: 12px;
      }
    }

    .select {
      color: #333333;

      > span {
        font-size: 15px;
        font-weight: 500;
      }
    }

    .quota {
      color: #666666;
    }
  }
`

const Wrapper = styled.div`
  & {
    height: 100%;
  }
`;

const ClassesBox = styled.div`
    &{
      //padding-left:80px;
      height: calc(100% - 49px - 44px);
      .classes-list{
        display: inline-block;
        width:80px;
        //position:fixed;
        //left:0;
        //top:44px;
        //bottom:49px;
        background: #F2F2F2;
        overflow-y: auto;
        height: 100%;
        vertical-align: top;
        -webkit-overflow-scrolling: touch;
        div{
          font-size: 12px;
          color: #666666;
          letter-spacing: -0.13px;
          padding: 11px 0;
          text-align:center;
          position:relative;
          &:before{
            content:'';
            display:block;
            width:3px;
            height:10px;
            position:absolute;
            left:0;
            top:50%;
            margin-top:-5px;
            background:#F2F2F2;
          }
          &.active{
            background:#ffffff;
            color:#333333;
            &:before{
            background:#307DCD;
            }
          }
        }
      }
      .product-list{
        display: inline-block;
        width: calc(100% - 80px);
        height: 100%;
        padding-left:15px;
        padding-bottom: 90px;
        vertical-align: top;
        h4{
          height:40px;
          line-height:40px;
          font-size: 12px;
          color:#666666;
          margin:0;
        }
        .products{
          height:${document.documentElement.clientHeight - 44 - 40 - 49}px;
          overflow-y: scroll;
          //overflow: auto;
          -webkit-overflow-scrolling: touch;
          overflow-scrolling: touch;
        }
      }
      .common-bottomTotal{
        width:100%;
        text-align:center;
        min-height:54px;
        .pt20{
          position:relative;
          top:15px;
        }
      }
    }
`;
