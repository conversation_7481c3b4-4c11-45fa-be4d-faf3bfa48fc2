import { autowired, bean } from "@classes/ioc/ioc";
import { action, observable } from "mobx";
import { $Page } from "../../classes/entity/$pagination";
import { $Product } from "../../classes/entity/$product";
import { $ProductService } from "../../classes/service/$product-service";

// import {$ActiveType} from "../../classes/const/$active-type";

@bean($ProductListMv)
export class $ProductListMv {
  @autowired($ProductService)
  public $productService: $ProductService;

  @observable public products: $Product[] = [];

  @observable public page: $Page = new $Page();

  @observable public loaded: boolean = false;

  @observable public keyword: string;

  @observable public productClassificationId: number;

  @observable public productCategoryId: number;

  @observable public classesList: any = [];

  @observable public classesActive: number;

  @observable public classesActiveName: string;

  @observable public searchList: $Product[] = [];

  @observable public pageIndex: number = 0;

  @observable public searchIndex: number = 0;

  @observable public detailObject: object;

  @observable public isSpin: boolean = true;

  @observable public orderPriceViewPermission: string;

  @observable public retailPriceViewPermission: string;

  @action
  public showSpin() {
    this.isSpin = true;
  }

  @action
  public hideSpin() {
    this.isSpin = false;
  }

  @action
  public removeById(id) {
    // do nothing
  }

  @action
  public fetchClasses() {
    return this.$productService.queryProductsClasses();
  }

  @action
  public fetchProducts(params: any) {
    return this.$productService.queryProducts(params);
  }

  @action
  public pushProducts(products: $Product[]) {
    this.products = this.products.concat(products.map((product) => new $Product(product)));
  }

  @action
  public setProducts(products: any[]) {
    this.products = products.map((product) => new $Product(product));
  }

  @action
  public setOrderPriceViewPermission(permission, retailPriceViewPermission) {
    this.orderPriceViewPermission = permission;
    this.retailPriceViewPermission = retailPriceViewPermission;
  }

  @action
  public clearProducts() {
    this.products = [];
    console.log(this.products);
  }

  @action
  public pushSearchList(products: $Product[]) {
    return new Promise((resolve) => {
      this.searchList = this.searchList.concat(products.map((product) => new $Product(product)));
      console.log("this.searchList1", this.searchList);
      resolve(this.searchList.length);
    });
  }

  @action
  public setSearchList(products: $Product[]) {
    return new Promise((resolve) => {
      this.searchList = products.map((product) => new $Product(product));
      console.log("this.searchList2", this.searchList);
      resolve(this.searchList.length);
    });
  }

  @action
  public setLoaded(loaded: boolean) {
    this.loaded = loaded;
  }

  @action
  public setKeyword(keyword: string) {
    this.keyword = keyword;
  }

  @action
  public setProductClassificationId(productClassificationId: number) {
    this.productClassificationId = productClassificationId;
  }

  @action
  public setProductCategoryId(productCategoryId: number) {
    this.productCategoryId = productCategoryId;
  }

  @action
  public setClassesActive(index: number) {
    this.classesActive = index;
  }

  @action
  public setClassesActiveName(name: string) {
    this.classesActiveName = name;
  }

  @action
  public clearClassesActiveName() {
    this.classesActiveName = null;
  }

  @action
  public setClassesList(list: any[]) {
    this.classesList = list;
  }

  @action
  public clearClassesList() {
    this.classesList = [];
    console.log(this.classesList);
  }

  @action
  public setPageIndex(index: number) {
    this.pageIndex = index;
  }

  @action
  public setSearchIndex(index: number) {
    this.searchIndex = index;
  }

  @action
  public setDetailObject(obj) {
    this.detailObject = obj;
  }
}
