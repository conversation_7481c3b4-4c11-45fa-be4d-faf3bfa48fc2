import { autowired } from "@classes/ioc/ioc";
import { findIndex } from "lodash";
import { transaction } from "mobx";
import { observer } from "mobx-react";
import * as React from "react";
import { withRouter } from "react-router";
import styled from "styled-components";
import { $Product } from "../../classes/entity/$product";
import { Footer } from "../../components/footer/footer";
import { NoGoods } from "../../components/no-goods/no-goods";
import { SearchMenuBar } from "../../components/search-menu-bar/search-menu-bar";
import { $CartMv } from "../shop-cart/cart-mv";
import { $ProductListMv } from "./$product-list-mv";
import ProBox from "./pro-box";
import { $AppStore, AppStoreKey } from "../../classes/stores/app-store-mv";

const querystring = require("querystring");

declare let require: any;

@withRouter
@observer
class SearchList extends React.Component<any, any> {
  @autowired($ProductListMv)
  public $mv: $ProductListMv;
  @autowired($AppStore)
  public $AppStore: $AppStore;

  @autowired($CartMv)
  public $CartMv: $CartMv;

  constructor(props) {
    super(props);
    this.state = {
      categoryId: null,
      refreshing: false,
      pageSize: 10,
      hasGoods: true,
      lastDataLength: 0,
      finished: false,
      isLoad: false,
      itemCount: null,
      orderSchemeType: "",
      hei: document.documentElement.clientHeight - 44 - 49,
    };
  }
  // 离开记录滚动高度
  public saveData = () => {
    const {
      searchIndex,
      searchList,
    } = this.$mv;
    const store = {
      searchIndex,
      searchListLength: searchList.length,
      scrollTop: $(".module-scroll-ability-wrap").scrollTop(),
    };
    this.$AppStore.savePageMv(AppStoreKey.SEARCHSHOPLIST, store);
  }
  public componentWillUnmount(): void {
    this.saveData();
  }
  public componentDidMount() {
    document.title = "商品搜索列表";
    this.initPage();
  }
  public initPage = () => {
    this.$CartMv.fetchShopcartproductnum();
    const { keyword } = this.props.match.params;
    let oldData = this.$AppStore.getPageMv(AppStoreKey.SEARCHSHOPLIST);
    if (oldData) {
      oldData = JSON.parse(oldData);
      const {
        scrollTop,
        searchIndex,
        searchListLength,
      } = oldData;
      this.setState({ isLoad: true });
      transaction(() => {
        const { pageSize } = this.state;
        this.setState({
          pageSize: searchListLength,
        }, () => {
          this.$mv.setKeyword(keyword);
          this.loadSearchList(keyword, false, () => {
            this.$mv.searchIndex = Number(searchIndex);
            $(".module-scroll-ability-wrap").scrollTop(scrollTop);
            this.setState({ pageSize });
            this.$AppStore.clearPageMv(AppStoreKey.SEARCHSHOPLIST);
          });
        });
      });
    } else {
      if (keyword !== "fromHome") {
        this.loadSearchList(keyword, false);
        this.$mv.setKeyword(keyword);
      }
    }
    const hei = document.documentElement.clientHeight - 44 - 49;
    this.setState({
      hei,
    });
  }

  public loadSearchList = (kw, cancat: boolean, cb?) => {
    const { searchIndex } = this.$mv;
    const params = {
      pageIndex: cancat ? searchIndex : 0,
      pageSize: this.state.pageSize,
      keyword: kw,
    };
    this.setState({ isLoad: true });
    this.$mv.fetchProducts(params)
      .then((data) => {
        const { itemCount, productList, orderPriceViewPermission, retailPriceViewPermission, orderSchemeType } = data;
        const count = cancat ? this.state.itemCount : itemCount;
        transaction(() => {
          if (cancat === false) {
            this.$mv.setSearchList(productList).then((length) => {
              this.setState({ finished: length >= count });
            });
            this.$mv.setSearchIndex(1);
            this.$mv.setOrderPriceViewPermission(orderPriceViewPermission, retailPriceViewPermission);
            // ReactDOM.findDOMNode(this.refs.pullToFresh2).scrollTop = 0;
            this.setState({ hasGoods: productList.length !== 0 });
          } else {
            this.$mv.pushSearchList(productList).then((length) => {
              this.setState({ finished: length >= count });
            });
            this.$mv.setSearchIndex(searchIndex + 1);
          }
          const hei = document.documentElement.clientHeight - 44 - 49;
          this.setState({
            hei,
            isLoad: false,
            itemCount: count,
            orderSchemeType,
          });
          cb && cb();
        });
      });
  }

  public cartWrapper = (products: $Product[], cartProducts: $Product[]) => {
    products.forEach((product) => {
      const index = findIndex(cartProducts, { productSkuId: product.productSkuId });
      if (index > -1) {
        product.setQuantity(cartProducts[index].quantity);
      } else {
        product.setQuantity(0);
      }
    });
    return products;
  }

  public loadData = () => {
    if (this.state.finished === false) {
      this.loadSearchList(this.$mv.keyword, true);
    }
  }

  public render() {
    const { searchList, orderPriceViewPermission, retailPriceViewPermission } = this.$mv;
    const { keyword } = this.props.match.params;
    const newProducts = this.cartWrapper(searchList, this.$CartMv.products);
    const { finished, isLoad, orderSchemeType } = this.state;
    return (
      <SearchWraper className="indexPage" style={{ height: `${this.state.hei} + 44 + 49` }}>
        <SearchMenuBar showMenu={true} searchKeyword={keyword}/>
        <div className="products" ref="pullToFresh2" style={{ height: `${this.state.hei}` }}>
          {
            this.state.hasGoods ? null : <NoGoods title="暂无任何商品~" height={document.documentElement.clientHeight / 2}/>
          }
          {
            newProducts && newProducts.length > 0 &&
            <ProBox
              newProducts={newProducts}
              loadData={this.loadData}
              orderPriceViewPermission={orderPriceViewPermission}
              retailPriceViewPermission={retailPriceViewPermission}
              finished={finished}
              isLoad={isLoad}
              orderSchemeType={orderSchemeType}
              leaveCurrentPage={this.saveData}
            />
          }
        </div>
        <Footer
          count={this.$CartMv.totalCount}
          leaveCurrentPage={this.saveData}
        />
      </SearchWraper>
    );
  }
}

export default SearchList;

const SearchWraper = styled.div`
  &{
    .products{
      padding: 15px 0px 0 15px;
      height:${document.documentElement.clientHeight - 44 - 49}px;
      overflow:auto;
      -webkit-overflow-scrolling: touch;
    }
    .loading{
      text-align:center;
    }
     .common-bottomTotal{
        width:100%;
        text-align:center;
        min-height:54px;
        .pt20{
          position:relative;
          top:15px;
        }
      }
  }
`;
