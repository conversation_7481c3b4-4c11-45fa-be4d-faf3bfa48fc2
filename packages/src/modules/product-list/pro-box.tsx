import { autowired } from "@classes/ioc/ioc";
import { Spin } from "antd";
import { findIndex } from "lodash";
import { transaction } from "mobx";
import { observer } from "mobx-react";
import * as React from "react";
import ReactDOM from "react-dom";
import { withRouter } from "react-router";
import styled from "styled-components";
import { ScrollAbilityModuleComponent } from "../../components/scroll-ability/module";
import { ProductItem } from "../../components/table/product-item";
import { LoadingTip } from '../../components/loading-marked-words';

declare let require: any;

@withRouter
@observer
class ProBoxWrapItem extends React.Component<any, any> {
  constructor(props) {
    super(props);
    this.state = {
    };
  }

  public render() {
    const { newProducts, orderPriceViewPermission, retailPriceViewPermission, finished, isLoad, orderSchemeType, leaveCurrentPage } = this.props;
    return (
      <ProBoxWrap>
        <div ref="pullChild2" className="pro-box">
          {
            newProducts.map((value) => {
              return (<ProductItem
                key={value.productSkuId}
                data={value}
                pricePermission={orderPriceViewPermission}
                retailPriceViewPermission={retailPriceViewPermission}
                orderSchemeType={orderSchemeType}
                newProducts={newProducts}
                leaveCurrentPage={leaveCurrentPage}
                showOrder={true}
              />);
            })
          }
        </div>
        <LoadingTip
          isFinished={finished}
          isLoad={isLoad}
        />
      </ProBoxWrap>
    );
  }
}

const ProBox = ScrollAbilityModuleComponent(ProBoxWrapItem);

export default ProBox;
const ProBoxWrap = styled.div`// styled
  & {
    position: relative;
  }
`;
