import { $AccountType, $GiftAccountTabType } from "@classes/const/$account-type";
import { observer } from "mobx-react";
import React from "react";
import { withRouter } from "react-router";
import styled from "styled-components";


@withRouter
@observer
export class GiftAccountItem extends React.Component<any, any> {

  public render() {
    const { item, goAccountFlowDetail } = this.props;
    return (
      <SAccountFlowItem onClick={() => item.IsSpecial ? null : goAccountFlowDetail(item)}>
        <div className="tag">
          <span className={item.accountTypeCode === $GiftAccountTabType.PZED ? "pzed" : ""}>{item.typeName}</span>
        </div>
        <div className="account">
          <div>{item.itemType}</div>
          <div style={{ color: item.inOutType === $AccountType.ADD ? "#FF4242" : "#333" }}>
            { item.IsSpecial ? "" : (item.inOutType === $AccountType.ADD ? "+" : "-")}{item.amount}
          </div>
        </div>
        <div className="date">
          <div>{item.docDate}</div>
          <div className="total">{item.typeName} {item.afterAmount}</div>
        </div>
      </SAccountFlowItem>
    );
  }
}

const SAccountFlowItem = styled.div`// styled
  & {
    display: flex;
    flex-direction: column;
    padding: 12px;
    border-bottom: 0.5px solid #D8D8D8;

    .tag {
      margin-bottom: 4px;
      > span {
        background: #F0F4FC;
        border-radius: 2px;
        color: #437DF0;
        font-size: 12px;
        padding: 4px 8px;
        width: fit-content;
        &.pzed{
          background: #FDEFE0;
          color: #F27B00;
        }
      }
    }

    .account {
      display: flex;
      justify-content: space-between;
      color: #333;
      align-items: center;

      > div:nth-child(1) {
        font-size: 14px;
        font-weight: 500;
      }

      > div:nth-child(2) {
        font-size: 18px;
        font-weight: 500;
      }
    }

    .date {
      display: flex;
      justify-content: space-between;
      align-items: center;
      color: #999;
      font-size: 12px;
    }
  }
`;
