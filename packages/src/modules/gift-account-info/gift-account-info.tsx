import { autowired } from "@classes/ioc/ioc";
import { observer } from "mobx-react";
import { observable, transaction } from "mobx";
import React from "react";
import { withRouter } from "react-router";
import styled from "styled-components";
import { $AccountInfoMv } from "./gift-account-info-mv";
import { $AccountType, $GiftAccountTabType } from "../../classes/const/$account-type";
import { NoGoods } from "../../components/no-goods/no-goods";
import { SITE_PATH } from "../app";
import { Spin } from "antd";
import { ScrollAbilityWrapComponent } from "../../components/scroll-ability/wrap";
import { LoadingTip } from "../../components/loading-marked-words";
import { $AppStore } from "../../classes/stores/app-store-mv";
import { CustomMonthModal } from "../../components/custom-month-modal/custom-month-modal";
import { AccountBusinessModal } from "../../components/account-business-modal/account-business-modal";
import { Modal, Toast } from "antd-mobile";
import moment from "moment";
import { $SearchDateType } from "@classes/const/$search-date-type";
import { $SearchTimeType } from "@classes/const/$search-time-type";
import { $SearchKey, SearchKeyName } from "@classes/const/$search-key";
import { GiftAccountItem } from "./gift-account-item";

const IconTop = require("../../components/svg/icon_top.svg");

const SearchKeyList = [
  {
    key: $SearchKey.ALL,
    name: SearchKeyName[$SearchKey.ALL],
  },
  {
    key: $SearchKey.IN,
    name: SearchKeyName[$SearchKey.IN],
  },
  {
    key: $SearchKey.OUT,
    name: SearchKeyName[$SearchKey.OUT],
  },
];

@withRouter
@observer
class GiftAccountInfoWrap extends React.Component<any, any> {

  @autowired($AccountInfoMv)
  public $myMv: $AccountInfoMv;

  @autowired($AppStore)
  public $AppStore: $AppStore;

  public constructor(props) {
    super(props);
    this.state = {
      showDateModal: false,
      showBusinessModal: false,
    };
  }

  public componentDidMount() {
    document.title = "返配赠账户";
    if (sessionStorage.getItem("goDetail")) {
      document.querySelector(".pz-list").scrollTop = Number(sessionStorage.getItem("goDetail"));
      sessionStorage.removeItem("goDetail");
      return;
    }
    this.$myMv.clearMVData();
    this.$myMv.dateType = $SearchTimeType.MONTH;
    this.$myMv.startTime = $SearchDateType.FirstDayOfMonth;
    this.$myMv.endTime = $SearchDateType.LastDayOfMonth;
    this.initPage();
  }

  public componentWillReceiveProps(nextProps: Readonly<any>, nextContext: any): void {
    const { isScrollEnd } = nextProps;
    const { showDateModal, showBusinessModal } = this.state;
    if (isScrollEnd && isScrollEnd !== this.props.isScrollEnd && !showDateModal && !showBusinessModal) {
      this.loadData();
    }
  }

  public initPage = () => {
    const {orderPartyId } = this.props.match.params || {orderPartyId: "" };
    const { pageSize, pageIndex, key, businessType, startTime, endTime, type } = this.$myMv;
    this.$myMv.showSpin();
    this.$myMv.isLoading = true;
    const params = {
      orderPartyId,
      condition: key,
      pageIndex,
      pageSize,
      businessType,
      start: startTime,
      end: endTime,
      type,
    };
    const { loadingEnd } = this.props;
    loadingEnd && loadingEnd(false);
    this.firstLoadFetchAccountInfo(params);
  }

  public firstLoadFetchAccountInfo = (params) => {
    transaction(() => {
      this.$myMv.fetchAccountInfo(params, false);
    });
  }

  public queryAccountInfo = (activeKey) => {
    this.$myMv.key = activeKey;
    this.$myMv.isFinished = false;
    this.$myMv.pageIndex = 0;
    this.initPage();
  }

  public loadData = () => {
    const { isFinished, isLoading, pageIndex } = this.$myMv;
    if (!isFinished) {
      this.$myMv.showSpin();
      if (!isLoading) {
        this.$myMv.pageIndex = pageIndex + 1;
        this.loadList();
      }
    }
  }

  public loadList = () => {
    const {orderPartyId } = this.props.match.params || {orderPartyId: "" };
    const { pageSize, key, startTime, endTime, businessType, pageIndex, type } = this.$myMv;
    this.$myMv.showSpin();
    const params = {
      orderPartyId,
      pageIndex,
      pageSize,
      businessType,
      condition: key,
      start: startTime,
      end: endTime,
      type,
    };
    this.$myMv.isLoading = true;
    const { loadingEnd } = this.props;
    this.$myMv.fetchAccountInfo(params, true).then((res) => {
      loadingEnd && loadingEnd(res);
    });
  }

  public onSearchDate = (startTime, endTime, dateType) => {
    if (moment(endTime).isBefore(startTime)) {
      Toast.info("结束时间不得大于开始时间");
      return;
    }
    this.$myMv.pageIndex = 0;
    this.$myMv.startTime = startTime;
    this.$myMv.endTime = endTime;
    this.$myMv.dateType = dateType;
    this.initPage();
  }

  public onSearchBusiness = (val) => {
    this.$myMv.pageIndex = 0;
    this.$myMv.businessType = val;
    this.initPage();
  }

  public changeTab = (value) => {
    this.$myMv.type = value;
    this.$myMv.pageIndex = 0;
    this.$myMv.businessType = [];
    this.initPage();
  }

  public goAccountDetail = (item) => {
    sessionStorage.setItem("detailParams", JSON.stringify(item));
    sessionStorage.setItem("newAccountBusinessType", item.typeName + "流水");
    sessionStorage.setItem("goDetail", String(document.querySelector(".pz-list").scrollTop));
    this.props.history.push({ pathname: `/${SITE_PATH}/new-account-info-detail/null` });
  }

  public goTransfer = () => {
    this.props.history.push({ pathname: `/${SITE_PATH}/gift-transfer-store/${this.props.match.params.orderPartyId}` });
  }

  public renderHfpzHeader = () => {
    const {totalData } = this.$myMv;
    const {pzAmount, quotaAmount, minAmount, transferToken} = totalData;
    return <SHfpzHeader>
      <div className="amount">
        <div className="amount-left">
          <div>当前可用额度</div>
          <div className="value">{minAmount}</div>
        </div>
        <div className="amount-right">
          <div><span className="value">{pzAmount}</span>配赠额度 <span className="tag">订货</span></div>
          <div><span className="value">{quotaAmount}</span>配赠上限 <span className="tag sale">销售</span></div>
        </div>
      </div>
      <div className="tips">可用额度=配赠额度、配赠上限 二者取其小</div>
      <div className="btn-wrap">
      {
        transferToken && <div className="transfer-btn" onClick={this.goTransfer}>配赠转店</div>
      }
      </div>
    </SHfpzHeader>;
  }

  public render() {
    const { showDateModal, showBusinessModal } = this.state;
    const { accountFlowList, key, isLoading, isFinished, startTime, endTime, tabList, type, totalData, dateType } = this.$myMv;
    const {lastPzAmount, lastQuotaAmount, lastMinAmount, initAmount, changeAmount, finalAmount, addAmount, reduceAmount} = totalData;
    const isFixed = showDateModal || showBusinessModal;
    return (
      <Spin spinning={isLoading} style={{ pointerEvents: isFixed ? "none" : "auto" }}>
        <Wrapper className="account-content-info-warp">
          <SHeadBg/>
          {
            this.renderHfpzHeader()
          }
          <SSearch>
            <div className="search-tabs">
              {
                tabList.map(v => <div onClick={() => this.changeTab(v.value)} key={v.value} className={`search-tabs-item ${type === v.value ? "active" : ""}`}>{v.label}</div>)
              }
            </div>
            <div className="search-more">
              <div onClick={() => this.setState({ showDateModal: true })}>
                <span>
                  {
                    dateType === $SearchTimeType.MONTH ? startTime.slice(0, 7) : `${startTime}~${endTime}`
                  }
                </span>
                <i className="scmIconfont scm-icon-xiajiantou" />
              </div>
              <div onClick={() => this.setState({ showBusinessModal: true })}>
                <span>业务类型</span>
                <i className="scmIconfont scm-icon-xiajiantou" />
              </div>
            </div>
            <div className="search-key">
              {
                SearchKeyList.map((item, index) => {
                  return <span key={index} className={key === item.key ? "active" : ""} onClick={() => this.queryAccountInfo(item.key)}>
                    {item.name}
                  </span>;
                })
              }
            </div>
          </SSearch>
          {
            type === $GiftAccountTabType.ALL && <TotalBox>
              <div className="amount">
                <div>
                  <div>可用额度</div>
                  <div className="value">{lastMinAmount || 0}</div>
                </div>
                <div className="icon1">=</div>
                <div>
                  <div>配赠额度</div>
                  <div className="value black">{lastPzAmount || 0}</div>
                </div>
                <div className="icon2">:</div>
                <div>
                  <div>配赠上限</div>
                  <div className="value black">{lastQuotaAmount || 0}</div>
                </div>
              </div>
              <div className="tips">可用额度=配赠额度、配赠上限 二者取其小</div>
            </TotalBox>
          }
          {
            type === $GiftAccountTabType.PZED && <TotalBox>
              <div className="amount">
                <div>
                  <div>额度结余</div>
                  <div className="value">{finalAmount || 0}</div>
                </div>
                <div className="icon1">=</div>
                <div>
                  <div>期初额度</div>
                  <div className="value black">{initAmount || 0}</div>
                </div>
                <div className="icon1">+</div>
                <div>
                  <div>额度变动</div>
                  <div className="value black">{changeAmount || 0}</div>
                </div>
              </div>
              <div className="tips">
                额度变动 {changeAmount || 0} = 增加额度 {addAmount || 0} - 减少额度 {reduceAmount || 0}
                <div className="triangle"><IconTop/></div>
              </div>
            </TotalBox>
          }
          {
            type === $GiftAccountTabType.HFPZ && <TotalBox>
              <div className="amount">
                <div>
                  <div>上限结余</div>
                  <div className="value">{finalAmount || 0}</div>
                </div>
                <div className="icon1">=</div>
                <div>
                  <div>期初上限</div>
                  <div className="value black">{initAmount || 0}</div>
                </div>
                <div className="icon1">+</div>
                <div>
                  <div>上限变动</div>
                  <div className="value black">{changeAmount || 0}</div>
                </div>
              </div>
              <div className="tips">
                上限变动 {changeAmount || 0} = 增加额度 {addAmount || 0} - 减少额度 {reduceAmount || 0}
                <div className="triangle"><IconTop/></div>
              </div>
            </TotalBox>
          }
          <SList className="pz-list">
            {
              accountFlowList && accountFlowList.length > 0 ? accountFlowList.map((item, index) => {
                return <GiftAccountItem item={item} key={index} goAccountFlowDetail={() => this.goAccountDetail(item)}/>;
              }) : <NoGoods title="暂无数据"/>
            }
            {
              accountFlowList && accountFlowList.length > 0 ?
                <LoadingTip
                  isFinished={isFinished}
                  isLoad={isLoading}
                /> : null
            }
          </SList>
          <CustomMonthModal
            visible={showDateModal}
            type={"range"}
            dateType={dateType}
            startDate={startTime}
            endDate={endTime}
            pageType={$AccountType.RETURN_FREE_DISTRIBUTION}
            onCancel={() => this.setState({ showDateModal: false })}
            onConfirm={this.onSearchDate}
          />
          <AccountBusinessModal
            visible={showBusinessModal}
            accountTypeCode={$AccountType.RETURN_FREE_DISTRIBUTION}
            giftAccountType={type}
            businessType={this.$myMv.businessType}
            onCancel={() => this.setState({ showBusinessModal: false })}
            onConfirm={this.onSearchBusiness}
          />
        </Wrapper>
      </Spin>
    );
  }
}

const GiftAccountInfo = ScrollAbilityWrapComponent(GiftAccountInfoWrap);

export default GiftAccountInfo;

const SHfpzHeader = styled.div`
  & {
    background: #FFF;
    width: 100%;
    z-index: 10;
    border-radius: 8px;
    padding: 10px;
    display: flex;
    flex-direction: column;
    box-shadow: 0px 0px 10px 0px rgba(45, 45, 45, 0.0694);

    .amount{
      display: flex;
      justify-content: space-between;
      .amount-left{
        .value{
          color: #FF3030;
          font-size: 32px;
          line-height: 36px;
          font-weight: 500;
        }
      }
      .amount-right{
        >div{
          margin-top: 3px;
          font-size: 12px;
          display: flex;
          align-items: center;
          justify-content: flex-end;
          .value{
            color: #FF3030;
            font-weight: 500;
            margin-right: 5px;
            font-size: 16px;
          }
          .tag{
            border-radius: 2px;
            padding: 0px 8px;
            font-size: 12px;
            border: 0.5px solid #F27B00;
            color: #F27B00;
            background: #FDEFE0;
            margin-left: 5px;
            &.sale{
              background: #F0F4FC;
              border: 0.5px solid #437DF0;
              color: #437DF0;
            }
          }
        }
      }
    }
    .tips{
      color: #999999;
      font-size: 12px;
    }
    .btn-wrap{
      display: flex;
      justify-content: center;
      .transfer-btn{
        margin-top: 5px;
        width: 135px;
        height: 32px;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        padding: 7px 25px;
        border-radius: 18px;
        border: 1px solid #437DF0;
        color: #437DF0;
        font-weight: 500;
      }
    }
  }
`;

const Wrapper = styled.div`// styled
  & {
    width: 100%;
    height: 100vh;
    padding: 10px;
    display: flex;
    flex-direction: column;
    align-items: center;
    background: #F7F7F7;
  }
`;

const SHeadBg = styled.div`
  & {
    position: absolute;
    top: 0;
    width: 100vw;
    height: 24vw;
    background: #437DF0;
  }
`;

const SList = styled.div`
  & {
    width: 100%;
    margin-top: 10px;
    border-radius: 8px;
    overflow: scroll;
    background: #FFF;

    .date-title {
      padding: 12px;
      font-size: 16px;
      font-weight: 500;
      color: #333;
    }
  }
`;

const SSearch = styled.div`// styled

  & {
    width: 100%;
    height: auto;
    margin-top: 10px;
    border-radius: 8px;

    .search-key {
      width: 100%;
      height: 36px;
      line-height: 36px;
      display: flex;
      border-radius: 8px 8px 0 0;
      background: linear-gradient(182deg, #F0F3FC 12%, #FFFFFF 64%);

      > span {
        display: inline-block;
        width: 100%;
        text-align: center;
        color: #666;
        margin: 0 30px;
        font-size: 14px;
      }

      .active {
        border-bottom: 1px solid #437DF0;
        color: #437DF0;
        font-size: 14px;
      }
    }

    .search-tabs{
      display: flex;
      background: #fff;
      padding: 4px;
      border-radius: 20px;
      .search-tabs-item{
        flex: 1;
        height: 28px;
        line-height: 28px;
        text-align: center;
        &.active{
          background: #437DF0;
          color: #fff;
          border-radius: 15px;
        }
      }
    }

    .search-more {
      display: flex;
      justify-content: space-between;
      padding: 6px 12px;

      > div {
        > span {
          color: #333;
          font-size: 14px;
          margin-right: 3px;
        }

        > img {
          width: 14px;
          height: 14px;
        }
      }
    }
  }
`;

const TotalBox = styled.div`
  & {
    width: 100%;
    height: auto;
    border-radius: 0 0 8px 8px;
    background: #FFF;
    padding: 10px 12px;
    display: flex;
    flex-direction: column;
    .amount{
      display: flex;
      justify-content: space-between;
      align-items: center;
      font-size: 12px;
      .icon1{
        color: #999;
        border: 1px solid #999;
        height: 20px;
        width: 20px;
        border-radius: 10px;
        display: flex;
        align-items: center;
        justify-content: center;
      }
      .value{
        color: #437DF0;
        font-size: 16px;
        height: 24px;
        line-height: 24px;
        font-weight: 500;
        &.black{
          color: #333;
        }
      }
    }
    .tips{
      color: #999999;
      margin-top: 5px;
      font-size: 12px;
      background: #F8F8F8;
      padding: 4px 6px;
      position: relative;
      .triangle{
        position: absolute;
        right: 20px;
        top: -12px;
        width: 40px;
        height: 20px;
        background-color: #F8F8F8;
        clip-path: polygon(50% 0%, 0% 100%, 100% 100%);
        text-align: center;
        svg{
          width: 18px;
          height: 18px;
          margin-top: 2px;
        }
      }
    }
  }
`;
