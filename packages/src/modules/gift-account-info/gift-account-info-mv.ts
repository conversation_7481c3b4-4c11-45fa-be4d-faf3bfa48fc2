import { autowired, bean } from "@classes/ioc/ioc";
import { action, observable } from "mobx";
import { $ComponentService } from "../../classes/service/$component-service";
import { $OrderPartyService } from "../../classes/service/$order-party-service";
import { $InterfaceErrorCode } from "../../classes/const/$interface-error-code";
import { beanMapper } from '../../helpers/bean-helpers';
import { $SearchDateType } from "@classes/const/$search-date-type";
import { $GiftAccountTabType } from "@classes/const/$account-type";
import { $SearchTimeType } from "@classes/const/$search-time-type";

@bean($AccountInfoMv)
export class $AccountInfoMv {

  @autowired($ComponentService)
  public $componentService: $ComponentService;

  @autowired($OrderPartyService)
  public $orderPartyService: $OrderPartyService;

  @observable public isSpin: boolean = false;
  @observable public isFinished: boolean = false;
  @observable public isLoading: boolean = false;
  @observable public isShowLoading: boolean = true;
  @observable public pageIndex: number = 0;
  @observable public scrollHeight: number = 0;
  @observable public pageSize: number = 20;
  @observable public key: string = "ALL";
  @observable public accountFlowList: any[];
  @observable public businessType: string[] = [];
  @observable public startTime: string = $SearchDateType.FirstDayOfMonth;
  @observable public endTime: string = $SearchDateType.LastDayOfMonth;
  @observable public dateType: $SearchTimeType = $SearchTimeType.MONTH;
  @observable public totalData: any = {};
  @observable public type: $GiftAccountTabType = $GiftAccountTabType.ALL;
  @observable public tabList: any[] = [
    { label: "全部明细", value: $GiftAccountTabType.ALL },
    { label: "额度明细", value: $GiftAccountTabType.PZED },
    { label: "上限明细", value: $GiftAccountTabType.HFPZ },
  ];

  @action
  public showSpin() {
    this.isSpin = true;
  }

  @action
  public hideSpin() {
    this.isSpin = false;
  }

  @action
  public fetchAccountInfo(params, blean) {
    return new Promise((resolve, reject) => {
      this.$componentService.queryGiftAccountInfo(params).then((data) => {
        this.isLoading = false;
        if (data.errorCode === $InterfaceErrorCode.NO_PAGE_VIEW_PERMISSIONS) {
          resolve(null);
          return;
        }
        const {accountFlowList, count, ...other} = data;
        this.totalData = other;
        this.accountFlowList = blean ? this.accountFlowList.concat(accountFlowList) : accountFlowList;
        this.isFinished = this.accountFlowList.length >= count;
        this.hideSpin();
        data.isFinished = this.accountFlowList.length >= count;
        resolve(this.isFinished);
      }).catch((err) => {
        reject(err);
      });
    });
  }

  @action
  public queryOldData(obj) {
    beanMapper(obj, this);
    console.log(this);
  }

  @action
  public clearMVData() {
    this.isSpin = false;
    this.isFinished = false;
    this.isLoading = false;
    this.isShowLoading = true;
    this.scrollHeight = 0;
    this.pageIndex = 0;
    this.pageSize = 20;
    this.key = "ALL";
    this.accountFlowList = [];
    this.businessType = [];
  }
}
