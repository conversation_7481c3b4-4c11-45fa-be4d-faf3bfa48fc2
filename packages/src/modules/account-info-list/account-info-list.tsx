import { autowired } from "@classes/ioc/ioc";
import { Checkbox, List, Modal, Switch } from "antd-mobile";
import { observer } from "mobx-react";
import React from "react";
import { withRouter } from "react-router";
import styled from "styled-components";
import { $AccountInfoListMv } from "./account-info-list-mv";
import { $AccountType } from "../../classes/const/$account-type";
import { NoGoods } from "../../components/no-goods/no-goods";
import { SITE_PATH } from "../app";
import { Toast } from "antd-mobile/es";
import { message, Spin } from "antd";
import { $CreditType } from "../../classes/const/$credit-type";
import { findIndex, remove } from "lodash";
import { getUrlParam } from "../../classes/utils/UrlUtils";
import { ScrollAbilityWrapComponent } from "../../components/scroll-ability/wrap";
import { LoadingTip } from "../../components/loading-marked-words";
import NoAuthority from "../no-authority/no-authority";
import { $OrderType } from "../../classes/const/$order-type";
import { AmountShowComponent } from "../../components/account-flow-amount-component";
import { gaEvent } from "../../classes/website-statistics/website-statistics";
import { $AppStore, AppStoreKey } from "../../classes/stores/app-store-mv";
import { $ValidityType } from "@classes/const/$validity-type";
const alert = Modal.alert;
const Item = List.Item;
const Brief = Item.Brief;
const CheckboxItem = Checkbox.CheckboxItem;
declare let window: any;

@withRouter
@observer
class AccountInfoListWrap extends React.Component<any, any> {
  @autowired($AppStore)
  public $AppStore: $AppStore;
  @autowired($AccountInfoListMv)
  public $myMv: $AccountInfoListMv;

  public constructor(props) {
    super(props);
    this.state = {
    };
  }
  // 离开记录滚动高度
  public saveMV = () => {
    this.$myMv.scrollHeight = $(".scroll-ability-wrap").scrollTop();
    this.$AppStore.savePageMv(AppStoreKey.ACCOUNTINFOLIST, this.$myMv);
  }
  public componentWillUnmount(): void {
    this.saveMV();
  }

  public componentDidMount() {
    document.title = "欠款账户";
    gaEvent("欠款账户");
    this.$myMv.clearMVData();
    this.initPage();
  }

  public initPage = () => {
    const orderPartyId = getUrlParam("orderPartyId");
    const { accountTypeCode, accountId } = this.props.match.params || { accountTypeCode: "", accountId: "" };
    this.$myMv.accountTypeCode = accountTypeCode;
    this.$myMv.accountId = accountId;
    this.$myMv.isLoading = true;
    this.$myMv.showSpin();
    if (orderPartyId) {
      this.$myMv.saveShopAndScheme({ orderPartyId }).then((res) => {
        const message = res.errorMessage;
        if (message) {
          Toast.fail(message);
        } else {
         this.loadData(true);
        }
      });
    } else {
      this.loadData(true);
    }
  }
  public componentWillReceiveProps(nextProps: Readonly<any>, nextContext: any): void {
    const { isScrollEnd } = nextProps;
    if (isScrollEnd && isScrollEnd !== this.props.isScrollEnd) {
      this.isLoadData();
    }
  }
  public isLoadData = () => {
    const { isFinished, isLoading} = this.$myMv
    if (!isFinished) {
      this.$myMv.showSpin();
      if (!isLoading) {
        this.$myMv.isLoading = true
        this.loadData();
      }
    }
  }

  public loadData = (isFirst ?) => {
    const oldData = this.$AppStore.getPageMv(AppStoreKey.ACCOUNTINFOLIST);
    this.$myMv.fetchDebtAccountInfo(isFirst).then((restWaitRepayAmount) => {
      const { loadingEnd } = this.props;
      loadingEnd && loadingEnd();
      if (isFirst && (!restWaitRepayAmount || restWaitRepayAmount <= 0) && !oldData) {
        this.changeSearchType(false);
      }
      if (isFirst) {
        const { buttonType } = this.$myMv;
        this.$myMv.buttonList = [
          {
            // cb: this.goToRepaymentRecord,
            code: "button-item",
            text: "还款记录",
            key: buttonType.REPAYMENTRECORD,
          },
          {
            // cb: this.showModel,
            code: `button-item ${this.$myMv.totalWaitRepayAmount > 0 ? "active" : "disable"}`,
            text: "立即还款",
            key: buttonType.IMMEDIATELYPAYMENT,
          },
        ];
      }
      if (oldData) {
        this.$myMv.queryOldData(JSON.parse(oldData));
        this.$AppStore.clearPageMv(AppStoreKey.ACCOUNTINFOLIST);
        $(".scroll-ability-wrap").scrollTop(this.$myMv.scrollHeight);
      }
    });
    // 发送请求改变 isFinished， isLoading状态
  }
  public goToAvailableCredit = () => {
    this.props.history.push({ pathname: `/${SITE_PATH}/credit-amount-list`});
  }
  public accountFlowDetail = (accountFlowId) => {
    this.props.history.push({ pathname: `/${SITE_PATH}/account-info-detail/${accountFlowId}` });
  }
  public goToRepaymentRecord = () => {
    console.log("goToRepaymentRecord");
    sessionStorage.setItem("validityType", $ValidityType.REGULAR_CREDIT);
    this.props.history.push({ pathname: `/${SITE_PATH}/repayment-record`});
  }
  public changeSearchType = (blon) => {
    this.$myMv.isOverdue = blon;
    this.$myMv.pageIndex = 0;
    this.loadData();
  }
  public getHeaderHtml = (type) => {
    const { isOverdue, totalWaitRepayAmount, totalAvailableAmount, warning, restWaitRepayAmount, nearOverdueList } = this.$myMv
    let headerHtml;
    switch (type) {
      case $AccountType.CREDIT:
        headerHtml =
          <CreditHeader>
            <div className="header-top">
              <div className="amount-info">
                <div className="amount-info-item">
                  <div className="label">总计待还：</div>
                  <div className="value">￥{Number(totalWaitRepayAmount).toFixed(2)}</div>
                </div>
                <div
                  className="amount-info-item"
                  onClick={this.goToAvailableCredit}
                >
                  <div className="label">总可用额度：</div>
                  <div className="value">￥{Number(totalAvailableAmount).toFixed(2)}</div>
                  <i className="scmIconfont scm-icon-jiantou-you"/>
                </div>
              </div>
              <div className="status-info">
                <div
                  className={isOverdue ? "status-info-item active" : "status-info-item"}
                  onClick={() => this.changeSearchType(true)}
                >已逾期</div>
                <div
                  className={isOverdue ? "status-info-item" : "status-info-item active"}
                  onClick={() => this.changeSearchType(false)}
                >未逾期</div>
              </div>
            </div>
            {
              warning &&
                <div className="header-middle">
                  <i className="scmIconfont  scm-modal-tishi"/>
                  {warning}
                </div>
            }
            {
              nearOverdueList && nearOverdueList.length > 0 &&
                <div className="nearly-over-due-list">
                  <i className="scmIconfont  scm-modal-tishi"/>
                  {
                    nearOverdueList.map((item) => {
                      const { amount, lastRePayDate, accountId } = item;
                      return(
                        <div className="over-due-list-item" key={accountId}>
                          {`¥${typeof(amount) === "number" ? Number(amount).toFixed(2) : amount}即将逾期，最后还款日：${lastRePayDate}`}
                        </div>
                      );
                    })
                  }
                </div>
            }
            <AmountShowComponent
              label={"剩余待还"}
              value={restWaitRepayAmount}
            />
          </CreditHeader>
        break;
      default:
        break;

    }
    return headerHtml;
  }
  public hideModel = () => {
    this.$myMv.showCreditMedal = "none";
  }
  public showModel = () => {
    if (this.$myMv.totalWaitRepayAmount > 0) {
      this.$myMv.showCreditMedal = "block";
    }
  }
  public buttonOprate = (key) => {
    const { buttonType } = this.$myMv;
    if (key === buttonType.REPAYMENTRECORD) {
      this.goToRepaymentRecord();
    } else if (key === buttonType.IMMEDIATELYPAYMENT) {
      this.showModel();
    }
  }
  public pageWindowSkip = (url) => {
    this.saveMV()
    setTimeout(() => {
      window.location.href = url;
    }, 50);
  }
  public onCheckedCredit = (e, credit, surplusToReturnAmount) => {
    // const { selectCreditAccountList } = this.$myMv;
    // console.log(selectCreditAccountList);
    if (e.target.checked) {
      const index = findIndex(this.$myMv.creditAccountList, { planId: credit.planId });
      this.$myMv.creditAccountList[index].checkFlag = true;
      this.$myMv.selectCreditAccountList.push({
        accountId: credit.accountId,
        isMulti: credit.isMulti,
        seqNo: credit.seqNo,
        planId: credit.planId,
      });
      this.$myMv.selectSurplusToReturnAmount = this.$myMv.selectSurplusToReturnAmount + surplusToReturnAmount;
      // console.log(this.$myMv.selectSurplusToReturnAmount);
    } else {
      const index = findIndex(this.$myMv.creditAccountList, { planId: credit.planId });
      this.$myMv.creditAccountList[index].checkFlag = false;
      remove(this.$myMv.selectCreditAccountList, (val) => val.planId === credit.planId);
      this.$myMv.selectSurplusToReturnAmount = parseFloat(Number(this.$myMv.selectSurplusToReturnAmount).toFixed(2)) - surplusToReturnAmount;
    }
    console.log("selectCreditAccountList", this.$myMv.selectCreditAccountList);
  }
  public checkTongLianPaymentStatus = () => {
    const { selectCreditAccountList, selectSurplusToReturnAmount } = this.$myMv;
    console.log("入参", selectCreditAccountList);
    if (selectSurplusToReturnAmount <= 0) {
      return;
    }
    const params = {
      creditAccountList: selectCreditAccountList,
      docType: $OrderType.CREDITACCOUNTREPAYMENT,
    };
    this.$myMv.showSpin();
    this.$myMv.checkTongLianPaymentStatus(params).then((data) => {
      console.log("data", data);
      const { errorCode, errorMsg, docInfo } = data;
      this.$myMv.hideSpin();
      if (errorCode && errorCode !== "0") {
        Toast.fail(errorMsg);
      } else if (docInfo && docInfo.oid) {
        const { oid, code, totalAmount } = docInfo
        alert(`提示`, `你有${totalAmount.toFixed(2)}元正在付款`, [
          {
            text: "不付款了", onPress: () => {
              this.cancelTongLianPay(oid);
            },
          },
          {
            text: "去付款", onPress: () => {
              this.checkContinuePay(oid);
            },
          },
        ]);
      } else {
        // 继续主流程操作
        this.goToRepayment();
      }
    }).catch((err) => {
      console.log("checkTongLianPaymentStatus", err);
      this.$myMv.hideSpin();
    });
  }
  public checkContinuePay = (oid) => {
    this.$myMv.showSpin();
    const params = {
      docType: $OrderType.CREDITACCOUNTREPAYMENT,
      oid,
    }
    this.$myMv.checkContinuePay(params).then((data) => {
      this.$myMv.hideSpin();
      console.log("checkContinuePay", data);
      const { errorCode, errorMessage, redirectUrl } = data;
      if (errorCode && errorCode !== "0") {
        Toast.fail(errorMessage);
      } else if (redirectUrl) {
        this.pageWindowSkip(`${redirectUrl}&redirectUrl=${document.location.protocol}//${document.domain}:${window.location.port}/scm/payment-result-show/allinpay`);
      } else {
        console.log(data);
      }
    }).catch(() => {
      this.$myMv.hideSpin();
    });
  }
  public cancelTongLianPay = (oid) => {
    const params = {
      docType: $OrderType.CREDITACCOUNTREPAYMENT,
      oid,
    }
    this.$myMv.showSpin();
    this.$myMv.cancelTongLianPay(params).then((data) => {
      this.$myMv.hideSpin();
      console.log("cancelTongLianPay", data);
      const { errorCode, errorMessage } = data;
      if (errorCode && errorCode !== "0") {
        alert(`提示`, "显示接口里的错误信息", [
          {
            text: "我知道了", onPress: () => {
              window.location.reload();
            },
          },
        ]);
      } else {
        window.location.reload();
      }
    }).catch(() => {
      this.$myMv.hideSpin();
    });
  }
  public goToRepayment = () => {
    const { selectSurplusToReturnAmount, selectCreditAccountList, isSpin } = this.$myMv;
    const { accountId } = this.props.match.params;
    if (isSpin) {
      return;
    }
    if (selectSurplusToReturnAmount > 0) {
      this.$myMv.showSpin();
      const params = {
        creditAccountList: selectCreditAccountList,
      };
      this.$myMv.queryCreditrepaymentAuditedAmount(params)
        .then((data) => {
          this.$myMv.hideSpin();
          if (data.message) {
            if (Number((selectSurplusToReturnAmount - data.amount).toFixed(2)) <= 0) { // 为了不跳转
              Toast.info(`${data.message}`, 5);
            } else {
              this.$myMv.selectSurplusToReturnAmount -= data.amount;
              Toast.info(`${data.message}`, 5, () => {
                localStorage.setItem("selectCreditAccountList", JSON.stringify(selectCreditAccountList));
                this.$AppStore.clearPageMv(AppStoreKey.REPAYMENTSTORAGE);
                this.pageWindowSkip(`/${SITE_PATH}/my-repayment/${this.$myMv.selectSurplusToReturnAmount && Number(this.$myMv.selectSurplusToReturnAmount).toFixed(2) }?accountId=${accountId}`);
              });
            }
          } else {
            localStorage.setItem("selectCreditAccountList", JSON.stringify(selectCreditAccountList));
            this.$AppStore.clearPageMv(AppStoreKey.REPAYMENTSTORAGE);
            this.pageWindowSkip(`/${SITE_PATH}/my-repayment/${this.$myMv.selectSurplusToReturnAmount && Number(this.$myMv.selectSurplusToReturnAmount).toFixed(2) }?accountId=${accountId}`);
          }
        }).catch(() => {
        this.$myMv.hideSpin();
      });
    } else {
      Toast.info("请选择还款金额", 3);
    }
  }
  public render() {
    const { accountTypeCode, accountFlowList, creditAccountList, selectSurplusToReturnAmount, isGetPermissions, isHavePermissions, showCreditMedal, isLoading, isFinished, accountFlowListLength, buttonList } = this.$myMv;
    return (
      <PageWrap>
        <Spin spinning={isLoading}>
          {
            isGetPermissions ? isHavePermissions ?
              <div className="wrap">
                <Content>
                  <div className="page-top">
                    {this.getHeaderHtml(accountTypeCode)}
                  </div>
                  {
                    accountFlowListLength > 0 ?
                      <div className="page-middle">
                        <div className="middle-title">明细</div>
                        <div className="middle-list">
                          {
                            accountFlowList.map((item) => {
                              const { flowId, label, date, amount } = item || { };
                              return (
                                <div
                                  className="middle-list-item"
                                  key={flowId}
                                  onClick={() => this.accountFlowDetail(flowId)}
                                >
                                  <div className="item-left">
                                    <div className="item-top">{label}</div>
                                    <div className="item-bottom">{date}</div>
                                  </div>
                                  <div className="item-right">
                                    {Number(amount).toFixed(2)}
                                    <i className="scmIconfont scm-icon-jiantou-you"/>
                                  </div>
                                </div>);
                            })
                          }
                        </div>
                      </div>
                      :
                      <NoGoods title="暂无记录" height={document.documentElement.clientHeight - 290}/>
                  }
                  <div className="page-bottom">
                    {
                      accountFlowListLength > 0 ?
                        <LoadingTip
                          isFinished={isFinished}
                          isLoad={isLoading}
                        /> : null
                    }
                  </div>
                </Content>
                <Model style={{ display: showCreditMedal }}>
                  <LayerStyle onClick={this.hideModel}/>
                  <EffectiveCreditAccountInfos>
                    <EffectiveCreditAccountInfoHeader>
                      <span>还款</span>
                    </EffectiveCreditAccountInfoHeader>
                    <EffectiveCreditAccountInfoContents>
                      {
                        creditAccountList.length > 0 ? creditAccountList.filter((list) => list.surplusToReturnAmount !== 0).map((credit, index) => {
                          return (
                            <div key={index}>
                              <CheckboxItem
                                checked={credit.checkFlag}
                                onChange={(e) => this.onCheckedCredit(e, credit, credit.surplusToReturnAmount)}/>
                              <EffectiveCreditAccountInfoContent>
                                <p>
                                  {credit.code}（{credit.ruleType}）
                                </p>
                                <div className={"info"}>
                                  <p>
                                    <span className="range">￥{Number(credit.creditAmount).toFixed(2)}</span>
                                    <br/>
                                    <span>信用额度</span>
                                  </p>
                                  <p>
                                    <span className="value">￥{Number(credit.surplusToReturnAmount).toFixed(2)}</span>
                                    <br/>
                                    <span>欠款金额</span>
                                  </p>
                                  <p>
                          <span className="value"
                                style={{ color: credit.creditStatus === $CreditType.USEING ? "#7ED321" : credit.creditStatus === $CreditType.OVERDUE ? "#FF3030" : "#307DCD" }}
                          >
                            {credit.creditStatusDesc}
                          </span>
                                    <br/>
                                    <span>
                                      {credit.isMulti === $CreditType.ISMULTI ? `第${credit.seqNo}期` : null}
                                      还款日：{credit.repaymentTime}
                                    </span>
                                  </p>
                                </div>
                                <p>
                                  有效期：{credit.creditAmountExpiryDateFrom}至{credit.creditAmountExpiryDateTo}
                                </p>
                              </EffectiveCreditAccountInfoContent>
                            </div>
                          );
                        }) : <NoGoods title="暂无可用的信用~"/>
                      }
                    </EffectiveCreditAccountInfoContents>
                    <EffectiveCreditAccountInfoButton onClick={this.checkTongLianPaymentStatus} theme={{ selectSurplusToReturnAmount }}>
                      立即还款 ￥{Number(selectSurplusToReturnAmount).toFixed(2)}
                    </EffectiveCreditAccountInfoButton>
                  </EffectiveCreditAccountInfos>
                </Model>
              </div>
              : <NoAuthority/> : <div/>
          }
        </Spin>
        <Footer>
          {
            buttonList && buttonList.length > 0 && buttonList.map((item) => {
              const { code, key, text } = item;
              return(
                <div
                  className={code}
                  onClick={() => this.buttonOprate(key)}
                >
                  {text}
                </div>
              );
            })
          }
        </Footer>
      </PageWrap>
    );
  }
}

const AccountInfoList = ScrollAbilityWrapComponent(AccountInfoListWrap);
export default AccountInfoList;

const PageWrap = styled.div`// styled
  & {
    width: 100%;
    height: auto;
    .wrap{
      height: auto;
    }
    .ant-spin-container{
      height: 100%;
    }
  }
`;
const CreditHeader = styled.div`// styled
  & {
    width: 100%;
    height: auto;
    color: rgba(47, 47, 47, 1);
    font-size: 13px;
    .header-top{
      width: 100%;
      height: 80px;
      background-color: #fff;
      .amount-info{
        width: 100%;
        height: 40px;
        padding: 0 12px;
        display: flex;
        justify-content: space-between;
        .amount-info-item{
          >div{
            display: inline-block;
            float: left;
            line-height: 40px;
          }
          .scm-icon-jiantou-you{
            font-size: 12px;
            color: rgba(153, 153, 153, 1);
            line-height: 40px;
            margin-left: 4px;
          }
        }
      }
      .status-info{
        width: 100%;
        height: 40px;
        padding: 0 59px;
        display: flex;
        justify-content: space-between;
        .status-info-item{
          font-size:14px;
          color:rgba(102,102,102,1);
          line-height:40px;
          text-align: center;
          padding: 0 8px;
        }
        .active{
          color: rgba(48, 125, 205, 1);
          border-bottom: 2px solid rgba(48, 125, 205, 1);
        }
      }
    }
    .header-middle, .nearly-over-due-list{
      margin: 12px 12px 0px;
      height:auto;
      background-color:rgba(255,238,238,1);
      border-radius:4px;
      border:1px solid rgba(255,93,93,1);
      font-size: 13px;
      color: rgba(255, 93, 93, 1);
      position: relative;
      line-height: 18px;
      padding: 10px 15px 10px 28px;
      .scm-modal-tishi{
        position: absolute;
        left: 6px;
        top: 0;
        line-height: 38px;
        font-size: 15px;
      }
      .over-due-list-item{
        margin-bottom: 8px;
      }
      .over-due-list-item:last-of-type{
        margin-bottom: 0;
      }
    }
  }
`;
const Content = styled.div`// styled
  & {
    width: 100%;
    height: auto;
    background-color: rgba(242, 242, 242, 1);
    min-height: calc(${document.documentElement.clientHeight}px);
    padding-bottom: 66px;
    .page-middle{
      margin: 12px 12px 0px;
      background-color: #fff;
      border-radius: 8px 8px 0px 0px;
      .middle-title{
        width: 100%;
        height: 40px;
        line-height: 40px;
        padding-left: 12px;
        color: rgba(47, 47, 47, 1);
      }
      .middle-list{
        width: 100%;
        height: auto;
        .middle-list-item{
          width: 100%;
          height: 56px;
          padding: 12px 13px;
          color: rgba(51, 51, 51, 1);
          border-top: 1px solid rgba(216, 216, 216, 1);
          .item-left{
            float: left;
            width: calc(100% - 95px);
            .item-top{
              font-size: 13px;
              line-height: 13px;
              margin-bottom: 6px;
              overflow: hidden;
              white-space: nowrap;
              text-overflow: ellipsis;
            }
            .item-bottom{
              font-size: 12px;
              line-height: 12px;
              color: rgba(102, 102, 102, 1);
            }
          }
          .item-right{
            float: right;
            width: 95px;
            height: 30px;
            line-height: 30px;
            font-size: 14px;
            text-align: right;
            >.scm-icon-jiantou-you{
              font-size: 12px;
              color: rgba(153, 153, 153, 1);
              margin-left: 8px;
            }
          }
        }
      }
    }
  }
`;
const Footer = styled.div`// styled
  & {
    position: fixed;
    bottom: 0;
    right: 0;
    width: 100%;
    height: 66px;
    padding: 12px 48px;
    background-color: #fff;
    display: flex;
    justify-content: space-between;
    border-top: 1px solid #D8D8D8;
    .button-item{
      padding: 0 28px;
      border-radius:3px;
      border:1px solid rgba(217,217,217,1);
      line-height: 40px;
      text-align: center;
      font-size: 16px;
      color:rgba(102,102,102,1);
    }
    .button-item.active{
      border: none;
      background-color:rgba(48,125,205,1);
      color:rgba(255,255,255,1);
      line-height: 42px;
    }
    .button-item.disable{
      border: none;
      background-color:rgba(217,217,217,1);
      color:rgba(255,255,255,1);
      line-height: 42px;
    }
  }
`;

const Model = styled.div`// styled
  & {
    width: 100%;
    height: calc(${document.documentElement.clientHeight}px);
    position: fixed;
    top: 0;
    left:0;
    background: rgba(0,0,0,0.5);
    z-index: 99;
    -webkit-transition: all 4s ease-in;
	  -moz-transition: all 4s ease-in;
	  -o-transition: all 4s ease-in;
	  transition: all 4s ease-in;
  }
`;

const LayerStyle = styled.div`// styled
  & {
    width: 100%;
    height: calc(${document.documentElement.clientHeight / 2}px);;
  }
`;

const EffectiveCreditAccountInfos = styled.div`// styled
  & {
    & {
      width: 100%;
      height: auto;
      background: #fff;
      border: 1px solid transparent;
    }
  }
`;

const EffectiveCreditAccountInfoHeader = styled.div`// styled
  & {
    width: 100%;
    height: 46px;
    line-height: 46px;
    background: #fff;
    padding: 0px 15px;
    box-sizing: border-box;
    > span:nth-of-type(1) {
      font-family: MicrosoftYaHei;
      font-size: 14px;
      color: #333333;
    }
    > span:nth-of-type(2) {
      float: right;
      font-family: MicrosoftYaHei;
      font-size: 14px;
      color: #333333;
      > .am-switch {
        padding-left: 12px;
      }
    }
  }
`;

const EffectiveCreditAccountInfoContents = styled.div`// styled
  & {
    width: 100%;
    height: 400px;
    padding: 15px 15px 180px 15px;
    overflow-y: auto;
    > div {
      > .am-list-item {
        width: 10%;
        padding-left: 0;
        //vertical-align: unset;
        display: inline-block;
      }
    }
    .noGoods {
      img {
        width: 30%;
      }
    }
  }
`;

const EffectiveCreditAccountInfoContent = styled.div`// styled
  & {
    display: inline-block;
    width: 90%;
    height: 110px;
    padding: 8px 12px;
    border: 0.5px solid #D8D8D8;
    border-radius: 4px;
    margin-bottom: 15px;
    vertical-align: middle;
    > p {
      margin-bottom: 5px;
      color: #999999;
      font-family: MicrosoftYaHei;
      font-size: 12px;
    }
    > div {
      > p {
        display: inline-block;
        margin-bottom: 5px;
        width: 30%;
        > span {
          font-family: MicrosoftYaHei;
          font-size: 12px;
          color: #999999;
        }
        > .range {
          font-family: MicrosoftYaHei;
          font-size: 13px;
          color: #333333;
        }
        > .value {
          font-family: MicrosoftYaHei;
          font-size: 13px;
          color: #FF3030;
        }
      }
      > p:nth-of-type(4) {
        width: 15px;
        height: 15px;
        position: relative;
        float: right;
        .right {
          width: 15px;
          height: 15px;
          position: absolute;
          left: 0;
          top: 12px;
        }
        .right-arrow1, .right-arrow2 {
          width: 0;
          height: 0;
          display: block;
          position: absolute;
          left: 0;
          top: 0;
          border-top: 10px transparent dashed;
          border-right: 10px transparent dashed;
          border-bottom: 10px transparent dashed;
          border-left: 10px white solid;
          overflow: hidden;
        }
        .right-arrow1 {
          left: 1px; /*重要*/
          border-left: 10px #999999 solid;
        }
        .right-arrow2 {
          border-left: 10px white solid;
        }
      }
    }
    .info {
      > p:last-child {
        width: calc(100% - 30% - 30%);
      }
    }
  }
`;

const EffectiveCreditAccountInfoButton = styled.div`// styled
  & {
    width: 100%;
    height: 42px;
    line-height: 42px;
    background-color: ${(props) => props.theme.selectSurplusToReturnAmount > 0 ? "#307DCD" : "#D9D9D9"};
    font-family: PingFangSC-Regular;
    font-size: 14px;
    color: #FFFFFF;
    text-align: center;
    position: fixed;
    bottom: 0;
    z-index: 99;
  }
`;
