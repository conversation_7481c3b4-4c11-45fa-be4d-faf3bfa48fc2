import { autowired, bean } from "@classes/ioc/ioc";
import { action, observable, reaction } from "mobx";
import { $ComponentService } from "../../classes/service/$component-service";
import { $OrderPartyService } from "../../classes/service/$order-party-service";
import { $MyInfoService } from "../../classes/service/$my-info-service";
import { $CreditAccount } from "../../classes/entity/$credit-account";
import { $InterfaceErrorCode } from "../../classes/const/$interface-error-code";
import { beanMapper } from "../../helpers/bean-helpers";
import { $ValidityType } from "@classes/const/$validity-type";

@bean($AccountInfoListMv)
export class $AccountInfoListMv {
  @autowired($ComponentService)
  public $componentService: $ComponentService;

  @autowired($MyInfoService)
  public $myInfoService: $MyInfoService;

  @autowired($OrderPartyService)
  public $orderPartyService: $OrderPartyService;

  @observable public buttonList: any[];

  @observable public accountFlowList: any[];

  @observable public balanceAmount: number;

  @observable public accountFlowListSize: number;

  @observable public allSurplusToReturnAmount: number;

  @observable public isSpin: boolean = false;

  @observable public automaticType: string;

  @observable public effectiveCreditAccount: number;

  @observable public creditAccountList: any[] = [];

  @observable public selectCreditAccountList: any[] = [];
  @observable public nearOverdueList: any[] = [];

  @observable public selectSurplusToReturnAmount: number = 0;

  @observable public configId: string;
  @observable public accountTypeCode: string;
  @observable public accountId: string;
  @observable public warning: string;
  @observable public showCreditMedal: string = "none";
  @observable public pageIndex: number = 0;
  @observable public accountFlowListLength: number = 0;
  @observable public pageSize: number = 20;
  @observable public totalWaitRepayAmount: number;
  @observable public totalAvailableAmount: number;
  @observable public restWaitRepayAmount: number;
  @observable public scrollHeight: number;
  @observable public isFinished: boolean = false;
  @observable public isLoading: boolean = false;
  @observable public isHavePermissions: boolean = false;
  @observable public isGetPermissions: boolean = false;
  @observable public isOverdue: boolean = true;
  @observable public isCanGoToPayment: boolean = false;
  @observable public buttonType: any = {
    REPAYMENTRECORD: "REPAYMENTRECORD",
    IMMEDIATELYPAYMENT: "IMMEDIATELYPAYMENT",
  };
  constructor() {
    reaction(() => this.selectSurplusToReturnAmount, (selectSurplusToReturnAmount) => {
      if (selectSurplusToReturnAmount) {
        this.isCanGoToPayment = true;
      } else {
        this.isCanGoToPayment = false;
      }
    });
  }
  @action
  public showSpin() {
    this.isSpin = true;
  }

  @action
  public hideSpin() {
    this.isSpin = false;
  }

  @action
  public saveAccountId(params) {
    return this.$componentService.saveAccountId(params);
  }

  @action
  public saveShopAndScheme(params) {
    return this.$orderPartyService.saveShopAndScheme(params);
  }

  @action
  public fetchDebtAccountInfo(isFirst) {
    const { pageIndex, pageSize, isOverdue } = this;
    const params = { pageIndex, pageSize, isOverdue }
    return new Promise((resolve, reject) => {
      this.$componentService.fetchDebtAccountInfo(params).then((data) => {
        const { totalWaitRepayAmount, totalAvailableAmount, restWaitRepayAmount, warning, itemCount, itemList, errorCode, nearOverdueList } = data;
        this.isGetPermissions = true;
        this.isLoading = false;
        this.isHavePermissions = errorCode !== $InterfaceErrorCode.NO_PAGE_VIEW_PERMISSIONS;
        if (data.errorCode === $InterfaceErrorCode.NO_PAGE_VIEW_PERMISSIONS) {
          resolve(this.accountFlowList.length >= itemCount);
          return;
        }
        this.totalWaitRepayAmount = totalWaitRepayAmount ? totalWaitRepayAmount : 0;
        this.warning = warning;
        this.totalAvailableAmount = totalAvailableAmount ? totalAvailableAmount : 0;
        this.accountFlowList = this.pageIndex ? this.accountFlowList.concat(itemList) : itemList ? itemList : [];
        this.accountFlowListLength = this.accountFlowList.length;
        this.nearOverdueList = nearOverdueList ? nearOverdueList : [];
        this.restWaitRepayAmount = restWaitRepayAmount ? restWaitRepayAmount : 0;
        this.isFinished = this.accountFlowListLength >= itemCount
        this.pageIndex ++;
        if (isFirst) {
          const paramsCredit = { creditType: "Effective", validityType: $ValidityType.REGULAR_CREDIT };
          this.fetchCreditaccountListLoad(paramsCredit);
        }
        this.hideSpin();
        resolve(this.restWaitRepayAmount);
      }).catch((err) => {
        reject(err);
      });
    });
  }
  @action
  public fetchCreditaccountListLoad(params) {
    this.$componentService.queryCreditaccountListLoad(params).then((data) => {
      this.allSurplusToReturnAmount = data.allSurplusToReturnAmount;
      this.automaticType = data.automaticType;
      this.configId = data.configId;
      this.effectiveCreditAccount = data.effectiveCreditAccount;
      this.creditAccountList = data.creditAccountList.map((credit) => new $CreditAccount(credit));
      data.creditAccountList.filter((account) => account.surplusToReturnAmount !== 0).map((val) => {
        this.selectCreditAccountList.push({
          accountId: val.accountId,
          isMulti: val.isMulti,
          seqNo: val.seqNo,
          planId: val.planId,
        });
        this.selectSurplusToReturnAmount += val.surplusToReturnAmount;
      });
    });
  }
  @action
  public accountAutomaticUpdate(params) {
    return this.$componentService.accountAutomaticUpdate(params);
  }

  @action
  public queryCreditrepaymentAuditedAmount(params) {
    return this.$componentService.queryCreditrepaymentAuditedAmount(params);
  }
  @action
  public checkTongLianPaymentStatus(params) {
    return this.$componentService.checkTongLianPaymentStatus(params);
  }
  @action
  public checkContinuePay(params) {
    return this.$componentService.checkContinuePay(params);
  }
  @action
  public cancelTongLianPay(params) {
    return this.$componentService.cancelTongLianPay(params);
  }
  @action
  public queryOldData(obj) {
    console.log("obj" , obj);
    // beanMapper(obj, this);
    // console.log(this);
    const { isOverdue, scrollHeight, accountFlowList, warning, pageIndex, restWaitRepayAmount, nearOverdueList } = obj;
    this.isOverdue = isOverdue;
    this.scrollHeight = scrollHeight;
    this.accountFlowList = accountFlowList;
    this.warning = warning;
    this.pageIndex = pageIndex;
    this.restWaitRepayAmount = restWaitRepayAmount;
    this.nearOverdueList = nearOverdueList;
    this.accountFlowListLength = accountFlowList.length;
  }
  @action
  public clearMVData() {
    this.buttonList = [];
    this.accountFlowList = [];
    this.nearOverdueList = [];
    this.buttonType = {
      REPAYMENTRECORD: "REPAYMENTRECORD",
      IMMEDIATELYPAYMENT: "IMMEDIATELYPAYMENT",
    };
    this.balanceAmount = 0;
    this.accountFlowListSize = 0;
    this.allSurplusToReturnAmount = 0;
    this.isSpin = false;
    this.automaticType = "";
    this.effectiveCreditAccount = 0;
    this.creditAccountList = [];
    this.selectCreditAccountList = [];
    this.selectSurplusToReturnAmount = 0;
    this.configId = "";
    this.accountTypeCode = "";
    this.accountId = "";
    this.warning = "";
    this.showCreditMedal = "none";
    this.pageIndex = 0;
    this.accountFlowListLength = 0;
    this.pageSize = 20;
    this.totalWaitRepayAmount = 0;
    this.totalAvailableAmount = 0;
    this.restWaitRepayAmount = 0;
    this.scrollHeight = 0;
    this.isFinished = false;
    this.isLoading = false;
    this.isHavePermissions = false;
    this.isGetPermissions = false;
    this.isOverdue = true;
    this.isCanGoToPayment = false;
  }
}
