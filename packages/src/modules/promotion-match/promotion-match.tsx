import { autowired } from "@classes/ioc/ioc";
import { Spin } from "antd";
import { Button, Checkbox, Modal } from "antd-mobile";
import { toJS } from "mobx";
import { observer } from "mobx-react";
import React from "react";
import { withRouter } from "react-router";
import styled from "styled-components";
import { $ActiveType } from "../../classes/const/$active-type";
import { $PromotionMatchService } from "../../classes/service/$promotion-match-service";
import { Footer } from "../../components/footer/footer";
import { PromotionMatchTable } from "../../components/table/promotion-match-table";
import { SITE_PATH } from "../app";
import { $CartCouponMv } from "../cart-coupon/cart-coupon-mv";
import { $proChooseGiftMv } from "../promotion-choose-products/pro-choose-gift-mv";
import { $PromotionMatchMv } from "./promotion-match-mv";
import { $PromotionType } from "../../classes/const/$promotion-type";
import { $CartType } from "../../classes/const/$cart-type";
import { $AppStore, AppStoreKey } from "../../classes/stores/app-store-mv";

declare let window: any;
const CheckboxItem = Checkbox.CheckboxItem;
const alert = Modal.alert;

@withRouter
@observer
class PromotionMatch extends React.Component<any, any> {
  @autowired($PromotionMatchMv)
  public $myMv: $PromotionMatchMv;
  @autowired($AppStore)
  public $AppStore: $AppStore;

  @autowired($proChooseGiftMv)
  public $proChooseGiftMv: $proChooseGiftMv;

  @autowired($CartCouponMv)
  public $cartCouponMv: $CartCouponMv;

  @autowired($PromotionMatchService)
  public $promotionMatchService: $PromotionMatchService;

  public constructor(props) {
    super(props);
    this.state = {
    };
  }
  // 离开记录滚动高度
  public saveMV = () => {
    // this.$myMv.scrollHeight = $(".promotion-list-wrap").scrollTop();
    // this.$AppStore.savePageMv(AppStoreKey.PROMOTIONMATCH, this.$myMv);
  }
  public componentWillUnmount(): void {
    this.saveMV();
  }
  public componentDidMount() {
    document.title = "促销匹配";
    setTimeout(() => {
      const oldData = this.$AppStore.getPageMv(AppStoreKey.PROMOTIONMATCH)
      if (oldData) {
        this.$myMv.queryOldData(JSON.parse(oldData));
        this.$AppStore.clearPageMv(AppStoreKey.PROMOTIONMATCH);
        $(".promotion-list-wrap").scrollTop(this.$myMv.scrollHeight);
      } else {
        // this.$myMv.clearMVData(); // 因为其他页面（赠品列表页面选择完成后跳转回来）对这个数据里的页面有依赖展示不清楚数据
        this.initPage();
      }
    }, 50);
  }
  public initPage = () => {
    this.$proChooseGiftMv.clearSelectDonationAmount();
    if (this.$proChooseGiftMv.isSelectGift) { // 选择赠品返回
      this.$proChooseGiftMv.changeIsSelectGift();
      const { saveGiftObj } = this.$proChooseGiftMv;
      this.$myMv.changeAdjustItemList(saveGiftObj);
      this.$myMv.isShowBtn = true;
    } else if (this.$cartCouponMv.isSelectCoupon) { // 选择优惠卷返回，已经没有入口暂时没用
      const { couponCheckedData } = this.$cartCouponMv;
      this.$myMv.changeUsedCouponList(couponCheckedData);
      const { promotionList } = this.$myMv;
      const params = { promotionList: toJS(promotionList), shopCartItemList: JSON.parse(sessionStorage.getItem("productsParams")) };
      this.$myMv.countPromotion(params);
      this.$cartCouponMv.isSelectCoupon = false;
      this.$myMv.isShowBtn = true;
    } else if (this.props.location.state !== undefined) { // 提交订单返回上一页 已经没有入口暂时没用
      if (this.props.location.state.prePage) {
        this.$myMv.setPromotionList(this.props.location.state.prePromotionList);
        this.$myMv.isShowBtn = true;
      } else {
        this.loadPromotion();
      }
    } else {
      this.loadPromotion();
    }
  }

  public loadPromotion = () => {
    this.$myMv.showSpin();
    this.$myMv.fetchPromotionMatch({ shopCartItemList: JSON.parse(sessionStorage.getItem("productsParams")) }).then((data) => {
      this.$myMv.hideSpin();
      this.$myMv.isShowBtn = true;
      this.$proChooseGiftMv.clearGift();
      if (data === "") {
        this.props.history.push({
          pathname: `/${SITE_PATH}/submit/order`,
          state: {
            backSource: this.props.location.state ? this.props.location.state.backSource : null,
            prePageSource: this.props.location.state ? this.props.location.state.prePageSource : "promotion",
            hasPromotion: this.props.location.state ? this.props.location.state.hasPromotion : null,
          },
        });
      } else {
        const { orderTotalAmount, promotionList } = data;
        this.$myMv.setPromotionList(promotionList);
        this.$myMv.setOrderTotalAmount(orderTotalAmount);
      }
    });
  }

  public selectAll = (all: boolean) => {
    /*this.$CartMv.selectAll(all);*/
  }

  public gotoSubmitOrder = () => {
    const { promotionList } = this.$myMv;
    let isChooseGift = 0;
    promotionList.filter((promotion) => promotion.checkFlag === $PromotionType.CHECKFLAG).map((promotion) => {
      if (promotion.forceGiveType === $PromotionType.FORCEGIFTTYPE && promotion.adjustItemList.length === 0) {
        isChooseGift += 1;
      }
    });
    if (isChooseGift) {
      alert("您有赠品未选择，请点击选择赠品选购商品", "", [
        {
          text: "确认", onPress: () => {
            // window.location.href = document.referrer;
          },
        },
      ]);
    } else {
      const params = { promotionList: toJS(this.$myMv.promotionList) };
      this.$myMv.forceGive(params).then((data) => {
        const frontErrorCodeList = [$PromotionType.ERRORCODEFORCEGIVE, $PromotionType.ERRORPROMOTIONCHOOSEDONATIONAMOUNT, $PromotionType.SALESPROMOTIONINVALID, $PromotionType.NOSTANDARD, $PromotionType.CHOOSE_DONATIONQUANTITY, $PromotionType.CHOOSE_DONATIONSKU_REPEAT]
        if (frontErrorCodeList.indexOf(data.errorCode) >= 0) {
          alert(data.errorMessage, "", [
            {
              text: "确认", onPress: () => {
                this.$myMv.setPromotionList(data.promotionList);
                // window.location.href = document.referrer;
              },
            },
          ]);
        } else if (data.errorCode === $CartType.HASPROMOTION) {
          alert(`${data.errorMessage}`, "", [
            {
              text: "重试", onPress: () => {
                this.$AppStore.clearPageMv(AppStoreKey.SHOPCART);
                this.props.history.push({
                  pathname: `/${SITE_PATH}/shop/cart`,
                  state: {
                    backSource: this.props.location.state ? this.props.location.state.backSource : null,
                  },
                });
              },
            },
          ]);
        } else {
          // console.log(this.props.location.state.hasPromotion);
          this.props.history.push({
            pathname: `/${SITE_PATH}/submit/order`, state: {
              promotionList: toJS(this.$myMv.promotionList),
              orderId: sessionStorage.getItem("editOrderId"),
              shopCartTotalAmount: this.props.location.state && this.props.location.state.shopCartTotalAmount,
              backSource: this.props.location.state ? this.props.location.state.backSource : null,
              prePageSource: this.props.location.state ? this.props.location.state.prePageSource : "promotion",
              hasPromotion: this.props.location.state ? this.props.location.state.hasPromotion : null,
            },
          });
        }
      }).catch((err) => {
        console.log(err);
      });
    }
  }

  public render() {
    const { countOrderTotalAmount, orderDiscountAmount, orderTotalAmount, promotionList, isSpin, calculatePromotionCount, isShowBtn } = this.$myMv;

    return (
      <div>
        <Spin spinning={isSpin}>
          <TableWrapper>
            <PromotionMatchTable
              checkable={true}
              isCart={true}
              data={promotionList}
              suppressSearchMenuBar={true}
            />
          </TableWrapper>
        </Spin>
        <ToPay>
          <span>
            {/*<div>
              <span style={{ marginLeft: 8, fontSize: 12, color: "#333" }}>应付：
                <span style={{ color: "#FF3030", fontSize: 14 }}>
                  {Number(subtract(sum([countOrderTotalAmount, orderTotalAmount]), orderDiscountAmount)).toFixed(2)}
                </span>
              </span>
            </div>
            <div>
              <span className="amountName">总额：</span>
              <span className="amount">{Number(sum([countOrderTotalAmount,
                orderTotalAmount])).toFixed(2)}</span>
              <span className="amountName">优惠：</span>
              <span className="amount">{Number(orderDiscountAmount).toFixed(2)}</span>
            </div>*/}
            <p>
              <span>享受促销活动：</span>
              <span>{calculatePromotionCount}</span>
            </p>
          </span>
          {
            isShowBtn && <Button
              loading={isSpin}
              className="pay"
              onClick={this.gotoSubmitOrder}
            >
              去确认
            </Button>
          }
        </ToPay>
        <Footer
          activeKey={$ActiveType.CAR_KEY}
          leaveCurrentPage={this.saveMV}
        />
      </div>
    );
  }
}

export default PromotionMatch;

const TableWrapper = styled.div`// styled
  & {
    display: inline-block;
    height: calc(${document.documentElement.clientHeight}px - 44px - 49px);
    width: 100%;
    background: #F2F2F2;
    .am-list .am-list-body{
    border: 0;
    }
    .am-checkbox-inner:after{
      position: absolute;
      top: 2.5px;
      right: 7px;
    }
  }
`;

const ToPay = styled.div`// styled
  & {
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: fixed;
    width: 100%;
    bottom: 49px;
    height: 48px;
    background: #FFFFFF;
    border-top: 1px solid #D8D8D8;
    padding: 6px 12px 6px 16px;
    > span {
      .am-list-item .am-list-line {
        padding-right: 0;
      }
      .am-list-item .am-list-thumb:first-child {
        margin-right: 5px;
      }
    }
    .pay {
      width: 88px;
      text-align: center;
      height: 36px;
      line-height: 36px;
      color: #ffffff;
      font-size: 14px;
      background: #307DCD;
      margin-left: 10px;
      border-radius: 20px;
    }
    > span:first-child {
      > div:last-child {
        > .amountName {
          color: #999;
          margin-left: 8px;
          font-size: 10px;
        }
        > .amount {
          color: #FF3030;
          font-size: 10px;
        }
      }
      > p {
        margin-bottom: 0;
        > span {
          font-size: 12px;
          font-family: "SourceHanSansCN-Normal";
          font-weight: 400;
          color: #333333;
        }
        > span:last-child {
          color: #FF3030;
        }
      }
    }
    .am-list .am-list-body {
      border: 0;
    }
  }
`;
