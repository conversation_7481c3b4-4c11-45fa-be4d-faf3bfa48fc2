import { autowired, bean } from "@classes/ioc/ioc";
import { Toast } from "antd-mobile";
import { findIndex, sum } from "lodash";
import { action, computed, observable } from "mobx";
import { $PromotionType } from "../../classes/const/$promotion-type";
import { $Promotion } from "../../classes/entity/$promotion";
import { $PromotionMatchService } from "../../classes/service/$promotion-match-service";
import { beanMapper } from '../../helpers/bean-helpers';

@bean($PromotionMatchMv)
export class $PromotionMatchMv {
  @autowired($PromotionMatchService)
  public $promotionMatchService: $PromotionMatchService;

  @observable public promotionList: $Promotion[] = []; // 获取到的促销列表

  @observable public promotionId: string; // 促销ID

  @observable public actionType: string; // 促销类型

  @observable public actionId: string; // 促销类型

  @observable public name: string;

  @observable public times: number; // 促销次数
  @observable public isShowBtn: boolean = false; // 是否展示可以进入下一步的button
  @observable public scrollHeight: number = 0; // 是否展示可以进入下一步的button

  @observable public orderTotalAmount: number; // 总额

  @observable public promotionInfo: object;

  @observable public couponData: object; // 选择促销赠品后的数据

  @observable public isSpin: boolean = false;

  @action
  public showSpin() {
    this.isSpin = true;
  }

  @action
  public hideSpin() {
    this.isSpin = false;
  }

  @action
  public fetchPromotionMatch(params) {
    return this.$promotionMatchService.queryPromotionMatch(params);
  }

  @action
  public setOrderTotalAmount(orderTotalAmount) {
    this.orderTotalAmount = orderTotalAmount;
  }

  @action
  public setPromotionList(promotionList) {
    if (promotionList) {
      this.promotionList = promotionList.map((promotion) => new $Promotion(promotion));
    }
  }

  @action
  public countPromotion(params) {
    this.$promotionMatchService.countPromotion(params).then((data) => {
      this.setPromotionList(data.promotionList);
    });
  }

  @computed
  get calculatePromotionCount() {
    return this.promotionList.filter((promotion) => promotion.checkFlag === $PromotionType.CHECKFLAG).length;
  }

  @computed
  get countOrderTotalAmount() { // 当选择好赠品或者加价购等时将金额累加到总额上
    return sum(this.promotionList
      .filter((promotion) => promotion.checkFlag === $PromotionType.CHECKFLAG).map((promotion) => {
        if (promotion.interactFlag === $PromotionType.INTERACTFLAG) {
          if (promotion.adjustItemList) {
            return sum(promotion.adjustItemList.map((adjustItem) => {
              if (adjustItem.isActive !== $PromotionType.ISACTIVE) {
                return adjustItem.quantity * adjustItem.orderPrice;
              }
            }));
          }
        }
      }));
  }

  @computed
  get orderDiscountAmount() { // 促销优惠计算
    return sum(this.promotionList
      .filter((promotion) => promotion.checkFlag === $PromotionType.CHECKFLAG)
      .map((promotion) => {
        if (promotion.interactFlag === $PromotionType.INTERACTFLAG) {
          if (promotion.adjustItemList) {
            return sum(promotion.adjustItemList.map((adjustItem) => {
              if (adjustItem.isActive !== $PromotionType.ISACTIVE) {
                return adjustItem.quantity * ((adjustItem.orderPrice - adjustItem.promotionPrice) * 100 / 100);
              }
            }));
          }
        } else {
          if (promotion) {
            return promotion.amount;
          }
        }
      }));
  }

  @action
  public setCountPromotion(promotionList, orderTotalAmount) {
    this.promotionList = promotionList.map((promotion) => new $Promotion(promotion));
    this.orderTotalAmount = orderTotalAmount;
  }

  @action
  public addQuantityByProduct(promotion: $Promotion, count?) {
    const index = findIndex(this.promotionList, { promotionId: promotion.promotionId });
    let newPromotion = null;
    if (index > -1) {
      if (count !== undefined) {
        /*if (count === 0) {
          const removedProduct = this.products[index];
          this.removeById(this.products[index].productSkuId);
          return removedProduct;
        }*/
        this.promotionList[index].times++;
      }
    } else {
      if (count !== undefined) {
        newPromotion = new $Promotion({ ...promotion, quantity: count, checked: true });
        this.promotionList.push(newPromotion);
      } else {
        newPromotion = new $Promotion({ ...promotion, quantity: 1, checked: true });
        this.promotionList.push(newPromotion);
      }
    }
    return newPromotion ? newPromotion : this.getPromotionById(promotion);
  }

  @action
  public getPromotionById(promotion) {
    const index = findIndex(this.promotionList, { promotionId: promotion.promotionId });
    if (index > -1) {
      return this.promotionList[index];
    } else {
      return null;
    }
  }

  @action
  public minusQuantityByProduct(promotion: $Promotion) {
    const index = findIndex(this.promotionList, { promotionId: promotion.promotionId });
    if (index > -1) {
      if (this.promotionList[index].times > 1) {
        this.promotionList[index].times--;
        /*const removedProduct = this.promotionList[index];
        this.removeById(this.promotionList[index].promotionId);
        return removedProduct;*/
      } else if (this.promotionList[index].times === 1) {
        Toast.info("至少参与1次，如不想参加可取消选择");
      }
    }
    return this.getPromotionById(promotion);
  }

  @action
  public setPromotionIdInfo(promotionId, actionType, name, times, data, actionId) {
    this.promotionId = promotionId;
    this.actionType = actionType;
    this.name = name;
    this.times = times;
    this.promotionInfo = data;
    this.actionId = actionId;
  }

  @action
  public setCouponData(data) {
    this.couponData = data;
  }

  @action
  public changeAdjustItemList(saveGiftObj) {
    this.promotionList.forEach((promotion, index) => {
      if (promotion.promotionId === saveGiftObj.promotionId) {
        promotion.actionInfoList.forEach((item) => {
          if (item.actionId === saveGiftObj.actionId) {
            // promotion.adjustItemList.setAdjustItemList(saveGiftObj.saveGiftList);
            item.adjustItemList = saveGiftObj.saveGiftList;
          }
        });
      }
    });
    // todo: force render
    this.promotionList = [...this.promotionList];
  }

  @action
  public changeUsedCouponList(couponCheckedData) {
    if (couponCheckedData) {
      this.promotionList.forEach((promotion, index) => {
        if (couponCheckedData.promotionId) {
          if (promotion.promotionId === couponCheckedData.promotionId) {
            if (couponCheckedData.couponCheckedList) {
              promotion.setUsedCouponList(couponCheckedData.couponCheckedList);
              promotion.setSingleCouponTimes(couponCheckedData.couponCheckedList.length);
            }
          }
        }
      });
      // todo: force render
      this.promotionList = [...this.promotionList];
    }
  }

  @action
  public forceGive(params) {
    return this.$promotionMatchService.forceGive(params);
  }
  @action
  public queryOldData(obj) {
    beanMapper(obj, this);
    console.log(this);
  }
  @action
  public clearMVData() {

  }
}
