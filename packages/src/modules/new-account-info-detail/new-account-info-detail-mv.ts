import { autowired, bean } from "@classes/ioc/ioc";
import { action, observable } from "mobx";
import { $ComponentService } from "../../classes/service/$component-service";
import { $OrderPartyService } from "../../classes/service/$order-party-service";
import { $AcountFlowDetail } from "../../classes/entity/$acount-flow-detail";

@bean($NewAccountInfoDetailMv)
export class $NewAccountInfoDetailMv {

  @autowired($ComponentService)
  public $componentService: $ComponentService;

  @autowired($OrderPartyService)
  public $orderPartyService: $OrderPartyService;

  @observable public accountFlowInfo = new $AcountFlowDetail({});

  @observable public localFlowInfo: any = {};

  @observable public isSpin: boolean = false;

  @action
  public showSpin() {
    this.isSpin = true;
  }

  @action
  public hideSpin() {
    this.isSpin = false;
  }

  @action
  public fetchAccountFlowInfo(params) {
    return this.$componentService.queryNewAccountDetail(params).then((data) => {
      this.accountFlowInfo = new $AcountFlowDetail(data);
      this.hideSpin();
    });
  }
}
