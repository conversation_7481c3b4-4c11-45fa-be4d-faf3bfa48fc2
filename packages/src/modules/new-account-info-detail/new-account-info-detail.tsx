import { autowired } from "@classes/ioc/ioc";
import { observer } from "mobx-react";
import React from "react";
import { withRouter } from "react-router";
import styled from "styled-components";
import { $NewAccountInfoDetailMv } from "./new-account-info-detail-mv";
import { Spin } from "antd";
import { SITE_PATH } from "../app";
import { $AccountType, $GiftAccountTabType } from "../../classes/const/$account-type";

@withRouter
@observer
class AccountFlowDetail extends React.Component<any, any> {

  @autowired($NewAccountInfoDetailMv)
  public $accountFlowDetailMv: $NewAccountInfoDetailMv;

  public constructor(props) {
    super(props);
  }

  public componentDidMount() {
    document.title = `${sessionStorage.getItem("newAccountBusinessType")}`;
    this.initPage();
  }

  public initPage = () => {
    this.$accountFlowDetailMv.showSpin();
    const params = sessionStorage.getItem("detailParams");
    this.$accountFlowDetailMv.localFlowInfo = JSON.parse(params || "{}");
    this.$accountFlowDetailMv.fetchAccountFlowInfo(params).then();
  }

  public goToOrderDetail = (type, params?) => {
    switch (type) {
      case "orderDetail":
        const { relatedDocId, orderScheme } = params;
        this.props.history.push({
          pathname: `/${SITE_PATH}/shop/order-detail/${relatedDocId}/${orderScheme}`,
          state: {
            backSource: "single",
          },
        });
        break;
      case "returnedDetail":
        const { refundOrderId, orgName, orgId } = params;
        this.props.history.push({
          pathname: `/${SITE_PATH}/returned-detail/${refundOrderId}`,
          state: {
            orderPartyName: orgName,
            orgId,
          },
        });
        break;
      case "FeeDocument":
        const { feeDocumentId } = params;
        this.props.history.push({
          pathname: `/${SITE_PATH}/expense-node-detail/${feeDocumentId}`,
        });
        break;
      default:
        break;
    }
  }

  public goDeliveryInfo = (deliveryOrderId) => {
    this.props.history.push({
      pathname: `/${SITE_PATH}/receive-order-detail/${deliveryOrderId}`,
    });
  }

  public goRefundInfo = (refundOrderId) => {
    this.props.history.push({
      pathname: `/${SITE_PATH}/returned-detail/${refundOrderId}`,
    });
  }

  public render() {
    const { accountFlowInfo, isSpin, localFlowInfo } = this.$accountFlowDetailMv;
    const { accountTypeCode } = localFlowInfo;
    const { accountFlowDocTime, frozenCode, frozenAmount, frozenType, orderScheme, inOutOrder_memo,
            posOrderNo, deliveryOrder, accountFlowCode, accountFlowInOutType,
            accountFlowAmount, frozenDocTime, activityDesc, refundOrderId, accountType, operatorUser,
            inOutOrderCode, saleOrderId, inOutOrderType, relatedSaleOrder, transferInOrg, transferOutOrg,
            relatedStoreOrder, deliveryOrderId, relatedList, inOutAmount, totalAmount,
            availableAmount, accountFrozenAmount, refundOrder, relatedStoredAccountTransfer , itemType, accountTypeLabel} = accountFlowInfo;
    const isShowQuote = accountType === "fl";
    let accountFlowName = "账户流水号";
    const isFPZ = accountTypeCode === $GiftAccountTabType.PZED || accountTypeCode === $GiftAccountTabType.HFPZ;
    if (isFPZ) {
      accountFlowName = accountTypeLabel + "流水号";
    }
    return (
      <Wrapper>
        <Spin spinning={isSpin}>
          {
            (itemType || accountFlowCode || accountFlowDocTime || accountFlowInOutType) && <SBlock>
              {
                isFPZ && itemType && <SItem>
                  <div>业务明细</div>
                  <div>{itemType}</div>
                </SItem>
              }
              {
                accountFlowCode && <SItem>
                  <div>{accountFlowName}</div>
                  <div>{accountFlowCode}</div>
                </SItem>
              }
              {
                accountFlowDocTime && <SItem>
                  <div>交易时间</div>
                  <div>{accountFlowDocTime}</div>
                </SItem>
              }
              {
                accountFlowInOutType && <SItem>
                  <div>{accountFlowInOutType === $AccountType.ADD ? "收入" : "支出"}</div>
                  <div className={"bold"} style={{ color: accountFlowInOutType === $AccountType.ADD ? "#FF3030" : "#333" }}>
                    {accountFlowInOutType === $AccountType.ADD ? "+" : "-"}{accountFlowAmount}
                  </div>
                </SItem>
              }
            </SBlock>
          }
          {
            frozenCode && <SBlock>
              <SItem>
                <div>冻结/解冻流水</div>
                <div>{frozenCode}</div>
              </SItem>
              <SItem>
                <div>交易时间</div>
                <div>{frozenDocTime}</div>
              </SItem>
              <SItem>
                <div>{frozenType === "frozen" ? "冻结" : "解冻"}</div>
                <div className="bold">{frozenAmount}</div>
              </SItem>
            </SBlock>
          }
          {
            (inOutOrderCode || inOutAmount || inOutOrderType || relatedSaleOrder || relatedStoreOrder || posOrderNo || activityDesc
              || (deliveryOrder && deliveryOrderId) || (refundOrder && refundOrderId) || relatedStoredAccountTransfer || transferOutOrg
              || transferInOrg || operatorUser
            ) && <STitle>关联单据</STitle>
          }
          {
            (inOutOrderCode || inOutAmount || inOutOrderType || relatedSaleOrder || relatedStoreOrder || posOrderNo || activityDesc
              || (deliveryOrder && deliveryOrderId) || (refundOrder && refundOrderId) || relatedStoredAccountTransfer || transferOutOrg
              || transferInOrg || operatorUser
            ) && <SBlock>
              {
                inOutOrderCode && <SItem>
                  <div>额度/余额收支单</div>
                  <div>{inOutOrderCode}</div>
                </SItem>
              }
              {
                inOutAmount && <SItem>
                  <div>收支额度</div>
                  <div>{inOutAmount}</div>
                </SItem>
              }
              {
                inOutOrderType && <SItem>
                  <div>收支方式</div>
                  <div>{inOutOrderType}</div>
                </SItem>
              }
              {
                relatedStoredAccountTransfer && <SItem>
                  <div>转账单号</div>
                  <div>{relatedStoredAccountTransfer}</div>
                </SItem>
              }
              {
                transferOutOrg && <SItem>
                  <div>转出门店</div>
                  <div>{transferOutOrg}</div>
                </SItem>
              }
              {
                transferInOrg && <SItem>
                  <div>转入门店</div>
                  <div>{transferInOrg}</div>
                </SItem>
              }
              {
                operatorUser && <SItem>
                  <div>操作人</div>
                  <div>{operatorUser}</div>
                </SItem>
              }
              {
                (deliveryOrder && deliveryOrderId) &&  <SItem>
                  <div>发货单号</div>
                  <div onClick={() => this.goDeliveryInfo(deliveryOrderId)}>
                    {deliveryOrder}
                    <div className="payment-info-right">
                      <i className="scmIconfont scm-icon-jiantou-you" />
                    </div>
                  </div>
                </SItem>
              }
              {
                (refundOrder && refundOrderId) && <SItem>
                  <div>退货单号</div>
                  <div onClick={() => this.goRefundInfo(refundOrderId)}>
                    {refundOrder}
                    <div className="payment-info-right">
                      <i className="scmIconfont scm-icon-jiantou-you" />
                    </div>
                  </div>
                </SItem>
              }
              {
                relatedSaleOrder && <SItem>
                  <div>关联订货单</div>
                  <div onClick={() => this.goToOrderDetail("orderDetail", { relatedDocId: saleOrderId, orderScheme })}>{relatedSaleOrder}
                    <div className="payment-info-right">
                      <i className="scmIconfont scm-icon-jiantou-you" />
                    </div>
                  </div>
                </SItem>
              }
              {
                relatedStoreOrder && <SItem>
                  <div>关联充值单</div>
                  <div>{relatedStoreOrder}</div>
                </SItem>
              }
              {
                inOutOrder_memo && <SItem>
                  <div>备注</div>
                  <div>{inOutOrder_memo}</div>
                </SItem>
              }
              {
                posOrderNo && <SItem>
                  <div>POS订单单号</div>
                  <div>{posOrderNo}</div>
                </SItem>
              }
              {
                activityDesc && <SItem>
                  <div>活动力度说明</div>
                  <div>{activityDesc}</div>
                </SItem>
              }
            </SBlock>
          }
          {
            relatedList && relatedList.length > 0 && <STitle>其他关联单据</STitle>
          }
          {
            relatedList && relatedList.length > 0 && relatedList.map((item, index) => {
              return <SBlock>
                <SItem>
                  <div>额度/余额收支单</div>
                  <div>{item.docNo}</div>
                </SItem>
                <SItem>
                  <div>业务类型</div>
                  <div>{item.businessTypeLabel}</div>
                </SItem>
                <SItem>
                  <div>收支类型</div>
                  <div>{item.inOutType === $AccountType.ADD ? "增加" : "减少"}</div>
                </SItem>
                <SItem>
                  <div>收支金额</div>
                  <div>{item.amount}</div>
                </SItem>
              </SBlock>;
            })
          }
          <STitle>账户明细</STitle>
          <SBlock>
            <SItem>
              <div>{isFPZ ? accountTypeLabel : "总额度"}</div>
              <div>{totalAmount}</div>
            </SItem>
            {
              !isFPZ &&
              <SItem>
                <div>{isShowQuote ? '可用额度' : '可用余额' }</div>
                <div>{availableAmount}</div>
              </SItem>
            }
            {
              !isFPZ && accountFrozenAmount > 0 && <SItem>
                <div>{isShowQuote ? '冻结额度' : '冻结余额'}</div>
                <div>{accountFrozenAmount}</div>
              </SItem>
            }
          </SBlock>
        </Spin>
      </Wrapper>
    );
  }
}

export default AccountFlowDetail;

const STitle = styled.div`
  & {
    font-size: 14px;
    font-weight: 500;
    margin: 15px;
    color: #333;
    text-align: left;
  }
`;

const SItem = styled.div`
  & {
    display: flex;
    justify-content: space-between;
    margin-top: 10px;

    > div:nth-child(1) {
      font-size: 14px;
      color: #666;
      white-space: nowrap;
      padding-right: 5px;
    }

    > div:nth-child(2) {
      font-size: 14px;
      color: #333;
      display: flex;
      align-items: center;
    }

    .bold {
      font-weight: 500 !important;
      font-size: 18px !important;
    }
  }

  :nth-child(1) {
    margin-top: 0;
  }
`;

const SBlock = styled.div`
  & {
    background: #FFF;
    border-radius: 4px;
    margin: 12px;
    padding: 12px 10px;
  }
`;

const Wrapper = styled.div`// styled
  & {
    width: 100%;
    min-height: calc(${document.documentElement.clientHeight}px);
    height: auto;
    background: #F2F2F2;
  }
`;
