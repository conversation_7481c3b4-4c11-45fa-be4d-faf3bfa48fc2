import { autowired, bean } from "@classes/ioc/ioc";
import { Spin, message, Popconfirm } from "antd";
import { Modal, Toast } from "antd-mobile";
import { observer } from "mobx-react";
import React from "react";
import { withRouter } from "react-router";
import styled from "styled-components";
import { ReceiveOrderDetailMv } from "./receive-order-detail-mv";
import { SITE_PATH } from "../app";
import { gaEvent } from "../../classes/website-statistics/website-statistics";
const alert = Modal.alert;

@withRouter
@observer
class ReceiveOrderDetail extends React.Component<any, any> {
  @autowired(ReceiveOrderDetailMv)
  public $myMv: ReceiveOrderDetailMv;

  constructor(props) {
    super(props);
    this.state = {
      deliveryId: "",
      isSpinning: false,
    };
  }
  public componentDidMount(): void {
    document.title = "收货单详情";
    gaEvent("收货单详情");
    const { deliveryId } = this.props.match.params || { deliveryId: "" };
    this.setState({ deliveryId }, () => {
      this.loadData();
    });
  }
  public loadData = () => {
    const { deliveryId } = this.state;
    const params = {
      deliveryId,
    };
    this.setState({ isSpinning: true })
    this.$myMv.getReceiveOrderDetail(params).then(() => {
      this.setState({ isSpinning: false });
    }).catch((res) => {
      this.setState({ isSpinning: false });
    });
  }
  public confirmReceive() {
    alert("是否确认收货？", "", [
      {
        text: "确认收货", onPress: () => {
          const { deliveryId } = this.state;
          const params = {
            deliveryId,
          };
          this.$myMv.confirmReceive(params).then((res) => {
            console.log(res);
            const { errorCode, errorMsg } = res;
            if (errorCode !== "0") {
              message.error(errorMsg);
            } else {
              this.loadData();
            }
          });
        },
      },
      {
        text: "我再想想",
      },
    ]);
    console.log(this);

  }
  public goBinningDetailList() {
    const { deliveryId } = this.state;
    this.props.history.push({
      pathname: `/${SITE_PATH}/binning-detail-list/${deliveryId}`,
      state:{goodsList:this.$myMv.receiveOrderDetail.productSkuList}
    });
  }
  public cancelConfirmReceive() {
    console.log("cancelConfirmReceive");
  }
  public getReturnedStatusIcon(status) {
    let iconClass = "";
    switch (status) {
      case "CONFIRM_GOODS": // 已收货
        iconClass = "scm-icon-shouhuo";
        break;
      case "SEND_GOODS": // 已发货
        iconClass = "scm-icon-fahuo";
        break;
      default:
        iconClass = "scm-icon-zuofei";
        break;
    }
    return iconClass;
  }
  public copyRefundOrderDocNo() {
    const range = document.createRange();
    range.selectNode(document.getElementById("refundOrderDocNo"));
    const selection = window.getSelection();
    if (selection.rangeCount > 0) {
      selection.removeAllRanges();
    }
    selection.addRange(range);
    document.execCommand("copy");
    Toast.info("复制成功", .5);
  }
  public goTologisticsInfoList = () => {
    const { deliveryId } = this.state;
    this.props.history.push({
      pathname: `/${SITE_PATH}/logistics-information-list/${deliveryId}`,
    });
  }
  public render() {
    const { isSpinning } = this.state;
    const { receiveOrderDetail } = this.$myMv;
    const { statusText, statusValue, deliveryDate, receivePerson, receivePersonTel, receiveAddress, companyName, logisticsDocNo, hasLogistics, productSkuList, productSkuTotalQuantity, docNo, receiveDate, receiveOrg, deliveryWarehouseOrg, memo, isShowConfirmReceiptBtn, boxingQuantity } = receiveOrderDetail || {};
    console.log("receiveOrderDetail", receiveOrderDetail);
    return (
      <Wrapper className="wrapper" theme={{ isShowConfirmReceiptBtn }}>
        <Spin spinning={isSpinning} className="spinning">
          <PageWrapHeader>
            <div className="order-status">
              <i className={`scmIconfont ${this.getReturnedStatusIcon(statusValue)}`} />
              <div>{statusText}</div>
              <div>发货时间：{deliveryDate}</div>
            </div>
            <div className="logistics-information">
              <div className="receive-address">
                <i className={`scmIconfont scm-icon-dizhi`} />
                <div className="receive-address-info">
                  <p><span className="name">{receivePerson}</span><span className="phone">{receivePersonTel}</span></p>
                  <p><span className="text">收货地址：</span><span className="value">{receiveAddress}</span></p>
                </div>
              </div>
              <div className="logistics-info" onClick={this.goTologisticsInfoList}>
                <i className={`scmIconfont scm-icon-wuliu`} />
                <div className="receive-logistics-info">
                  <p>物流信息</p>
                  {
                    hasLogistics ?
                      <p>
                        {`${companyName ? companyName : "未知物流"}  快递单号：${logisticsDocNo ? logisticsDocNo : "暂无"}`}
                      </p>
                      :
                      <p>暂无</p>
                  }
                </div>
                <i className={`scmIconfont scm-icon-jiantou-you`} />
              </div>
            </div>
          </PageWrapHeader>
          <PageWrapContent>
            {
              productSkuList && productSkuList.length > 0 &&
                <div className="content">
                  <div className="content-title">
										<span>发货商品清单</span>
										<span>共{productSkuTotalQuantity}件商品</span>
                  </div>
                  {
                    productSkuList.map((item) => {
                      const { orderDocNo, goodsList } = item || {};
                      return (
                        <div className="order-list">
                          <div className="order-title">
                            订单：{orderDocNo}
                          </div>
                          {
                            goodsList && goodsList.length > 0 &&
                              <div className="product-list">
                                {
                                  goodsList.map((item, index) => {
                                    const { imgUrl, name, code, deliveryQuantity, salesOrderQuantity, pzAmount } = item;
                                    return (
                                      <div className="product-item" key={code + index}>
                                        <div className="content-left">
                                          {
                                            imgUrl ?
                                              <img src={imgUrl} alt=""/>
                                              :
                                              <img src="https://order.fwh1988.cn:14501/static-img/scm/ico-nopic.png" alt=""/>
                                          }
                                        </div>
                                        <div className="content-right">
                                          <div className="product-title">{name}</div>
                                          <div className="product-code">货号：{code}</div>
                                          <div className="product-number">发货数/订货数：<span>{deliveryQuantity}/{salesOrderQuantity}</span></div>
                                          <div className="product-quota">配赠额度：<span>{pzAmount}</span></div>
                                        </div>
                                      </div>
                                    );
                                  })
                                }
                              </div>
                          }
                        </div>
                      );
                    })
                  }
                </div>
            }

          </PageWrapContent>
          <PageWrapFooter>
            <div className="docNo">
              <div className="text">发货单号：</div>
              <div className="value" id="refundOrderDocNo">{docNo}</div>
              <span
                className="copy-order-doc"
                onClick={this.copyRefundOrderDocNo}
              >复制</span>
            </div>
            {
              statusValue === "CONFIRM_GOODS" &&
                <div className="receiveDate">
                  <div className="text">收货时间：</div>
                  <div className="value">{receiveDate}</div>
                </div>
            }
            {
              typeof (boxingQuantity) === "number" &&
                <div className="boxingQuantity">
                  <div className="text">装货总件数：</div>
                  <div className="value">{boxingQuantity}</div>
                </div>
            }
            <div className="receiveOrg">
              <div className="text">收货门店：</div>
              <div className="value">{receiveOrg}</div>
            </div>
            <div className="deliveryWarehouseOrg">
              <div className="text">发货仓库：</div>
              <div className="value">{deliveryWarehouseOrg}</div>
            </div>
            <div className="memo">
              <div className="text">备注：</div>
              <div className="value">{memo}</div>
            </div>
          </PageWrapFooter>
              <div className="receive-confirm">
                <span
                  className="receive-confirm-button binning-detail-button"
                  onClick={() => this.goBinningDetailList()}
                >装箱明细</span>
								{isShowConfirmReceiptBtn &&<span
                  className="receive-confirm-button"
                  onClick={() => this.confirmReceive()}
                >确认收货</span>}
              </div>
        </Spin>
      </Wrapper>
    );
  }
}

export default ReceiveOrderDetail;

const Wrapper = styled.div`// styled
  & {
    width: 100%;
    height: auto;
    min-height: 100%;
    background-color: #F2F2F2;
    padding-bottom: ${(props) => props.theme.isShowConfirmReceiptBtn ? 60 : 0 }px;
    p{
      margin: 0;
      padding: 0;
    }
    .ant-spin-nested-loading{
      width: 100%;
      height: auto;
    }
    .receive-confirm{
      width: 100%;
      position: fixed;
      bottom: 0;
      height: 50px;
      padding: 10px 12px 10px 0;
      background-color: #fff;
      text-align: right;
      .receive-confirm-button{
        display: inline-block;
        width: 72px;
        height: 30px;
        border: 1px solid #307DCD;
        color: #307DCD;
        line-height: 30px;
        text-align: center;
        font-size: 12px;
        border-radius: 3px;
      }
      .binning-detail-button{
        margin-right:1rem;
      }
    }
  }
`;
const PageWrapHeader = styled.div`// styled
  & {
    width: 100%;
    height: auto;
    background-color: #F2F2F2;
    .order-status{
      height: 70px;
      padding: 16px 0 0 68px;
      background-image: url("https://order.fwh1988.cn:14501/static-img/scm/img_bg_returned_status.png");
      -webkit-background-size: 100% 70px;background-size: 100% 70px;
      background-repeat: no-repeat;
      background-position: center center;
      i{
        border:1px solid #307DCD;
        color: #307DCD;
        position: absolute;
        top: 15px;
        left: 16px;
        display: inline-block;
        width: 40px;
        height: 40px;
        text-align: center;
        line-height: 40px;
        border-radius: 20px;
        &:before{
          font-size: 18px;
        }
      }
      div:first-of-type{
        font-size: 16px;
        height: 16px;
        line-height: 16px;
        margin-bottom: 8px;
      }
      div:nth-of-type(2){
        color: #666666;
      }
    }
    .logistics-information{
      background-color: #fff;
      .receive-address,.logistics-info{
        padding: 12px 12px 12px 12px;
        overflow: hidden;
        border-top: 1px solid #D8D8D8;
        position: relative;
        .scm-icon-wuliu, .scm-icon-dizhi{
          position: absolute;
          width: 24px;
          height: 24px;
          font-size: 13px;
          color: #fff;
          text-align: center;
          line-height: 24px;
          background-color: rgba(255, 93, 93, 1);
          border-radius: 12px;
          top: 50%;
          left: 12px;
          transform: translateY(-50%);
        }
        .scm-icon-wuliu{
          background-color: rgba(48, 125, 205, 1);
          left: 0;
        }
        .scm-icon-jiantou-you{
          position: absolute;
          top: 50%;
          right: 12px;
          transform: translateY(-50%);
          font-size: 10px;
        }
        >p:first-of-type{
          font-size: 14px;
          color: #2F2F2F;
          line-height: 14px;
          .phone{
            color: #757575;
          }
        }
        .receive-address-info,.receive-logistics-info{
          float: left;
          font-size: 13px;
          line-height: 17px;
          padding-left: 36px;
          padding-right: 24px;
          color: #333333;
          >p{
            position: relative;
            .name{
              color: #2F2F2F;
              margin-right: 5px;
              font-size: 14px;
            }
            .phone{
              color: #757575;
              font-size: 14px;
            }
            .text{
              position: absolute;
              top: 0;
              left: 0;
              width: 68px;
            }
            .value{
              display: inline-block;
              padding-left: 68px;
            }
          }
          >p:last-of-type{
            margin-top: 8px;
          }
        }
      }
      .logistics-info{
        padding-left: 0;
        margin-left: 12px;
      }
    }
  }
`;
const PageWrapContent = styled.div`// styled
  & {
    width: 100%;
    height: auto;
    margin-top: 10px;
    .content{
      background-color: #fff;
      .content-title{
        width: 100%;
        height: 40px;
        line-height: 40px;
        font-size: 14px;
        color: #333333;
        padding: 0 12px;
        border-bottom: 1px solid #D8D8D8;
        span:last-of-type{
          float: right;
          font-size: 13px;
          color: #666666;
        }
      }
    }
    .order-list{
      padding: 0 12px;
      overflow: hidden;
      .order-title{
        height: 32px;
        line-height: 32px;
        font-size: 12px;
        color: #757575;
      }
      .product-list{
        background-color: #fff;
      }
      .product-item{
        padding: 10px;
        display: flex;
        margin-bottom: 12px;
        background-color: #F2F2F2;
        border-radius: 8px;
        >div{
          display: inline-block;
          float: left;
        }
        .content-left{
          width: 86px;
          height: 86px;
          margin-right: 10px;
          img{
            width: 100%;
            height: 100%;
          }
        }
        .content-right{
          width: 100%;
          flex: 1;
          position: relative;
          .product-title{
            height: 34px;
            font-size:13px;
            line-height: 16px;
            color: #2F2F2F;
            width: 100%;
            overflow : hidden;
            text-overflow: ellipsis;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
          }
          .product-code,.product-number, .product-quota{
            font-size: 12px;
            line-height: 12px;
            color: #595959;
            margin-top: 8px;
          }
          .product-number{
            margin-top: 4px;
            >span{
              color: #52C41A;
            }
          }
          .product-quota{
            margin-top: 4px;
            >span{
              color: #52C41A;
            }
          }
        }
      }
    }
  }
`;
const PageWrapFooter = styled.div`// styled
  & {
    width: 100%;
    height: auto;
    padding: 12px;
    background-color: #fff;
    font-size: 12px;
    color: #666666;
    line-height: 12px;
    margin-top: 10px;
    >div{
      position: relative;
      margin-bottom: 8px;
      min-height: 12px;
      .text{
        width: 75px;
        position: absolute;
        top: 0;
        left: 0;
      }
      .value{
        padding-left: 75px;
        color: #333333;
      }
    }
    >div:last-of-type{
      margin-bottom: 0;
    }
    .docNo{
      position: relative;
      .copy-order-doc{
        position: absolute;
        top: -2px;
        right: 0;
        display: inline-block;
        width: 32px;
        height: 16px;
        text-align: center;
        line-height: 14px;
        font-size: 8pt;
        border: 1px solid #307DCD;
        background-color:rgba(255,255,255,1);
        border-radius: 2px;
        color: #307DCD;
      }
    }
  }
`;
