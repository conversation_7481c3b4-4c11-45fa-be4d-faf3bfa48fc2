import { autowired, bean } from "@classes/ioc/ioc";
import { action, observable } from "mobx";
import { $ReceiveService } from "../../classes/service/$receive-order-service";

@bean(ReceiveOrderDetailMv)
export class ReceiveOrderDetailMv {
  @autowired($ReceiveService)
  public $myService: $ReceiveService;
  @observable public receiveOrderDetail: object;
  @action
  public getReceiveOrderDetail(params) {
    return this.$myService.getReceiveOrderDetail(params).then((res) => {
      console.log("getReceiveOrderDetail", res);
      this.receiveOrderDetail = res;
    });
  }
  @action
  public confirmReceive(params) {
    return this.$myService.confirmReceive(params);
  }
}
