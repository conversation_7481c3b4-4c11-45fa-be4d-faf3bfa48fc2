import * as React from "react";
import { $CartType } from "../../classes/const/$cart-type";
import styled from "styled-components";

export class GoodDetail extends React.Component<any, any> {

  constructor(props) {
    super(props);
    this.state = {
      isShowButton: false,
      selectIndex: 2,
    };
  }

  public showButton = (goods) => {
    const { isShowButton } = this.state;
    if (isShowButton) {// 收起
      this.setState({
        isShowButton: false,
        selectIndex: 2,
      });
    } else { // 展开
      this.setState({
        isShowButton: true,
        selectIndex: goods.virtualSuitList ? goods.virtualSuitList.length : 0,
      });
    }
  }

  public render() {
    const { goods } = this.props;
    const { isShowButton, selectIndex } = this.state;
    return (
      <ShopDetail>
        {
          goods.virtualSuitList.slice(0, selectIndex).map((product, index) => {
            return (
              <p key={index}>
                <span>套装明细</span>
                <span>{product.name ? product.name.length > 10 ? product.name.slice(0, 10) + "..." : product.name : null}</span>
                <span>{product.isActive === $CartType.ISACTIVE_KEY ? "无效" : null}</span>
                <span>x{product.quantity}</span>
              </p>
            );
          })
        }
        {
          goods.virtualSuitList ? goods.virtualSuitList.length > 2 ?
            <ShowButton onClick={() => this.showButton(goods)}>
              {isShowButton ? "点击收起" : "展开更多"}
              {isShowButton ? <i className="scmIconfont scm-icon-jiantou-shang"/> :
                <i className="scmIconfont scm-icon-arrow-down"/>}
            </ShowButton> : null : null}
      </ShopDetail>
    );
  }
}

const ShopDetail = styled.div`// styled
  & {
    position: relative;
    padding: 9px 0;
    margin-top: 8px;
    :before {
      content: '';
      position: absolute;
      background-color: #D8D8D8 !important;
      display: block;
      z-index: 1;
      top: 0;
      right: auto;
      bottom: 0;
      left: 0;
      width: 100%;
      height: 1.2px;
      transform-origin: 50% 100%;
      transform: scaleY(0.5);
    }
    > p {
      margin-bottom: 8px;
      > span:nth-of-type(1) {
        border-radius: 11px;
        border: 1px solid rgba(255, 134, 39, 1);
        font-size: 8px;
        font-family: "PingFangSC-Regular";
        font-weight: 400;
        color: rgba(255, 134, 39, 1);
        margin-right: 16px;
        display: inline-block;
        width: 48px;
        height: 16px;
        line-height: 16px;
        text-align: center;
      }
      > span:nth-of-type(2) {
        display: inline-block;
        width: 100px;
        text-align: left;
        font-size: 9px;
        font-family: "PingFangSC-Regular";
        font-weight: 400;
        color: rgba(117, 117, 117, 1);
        margin-right: 5px;
      }
      > span:nth-of-type(3) {
        display: inline-block;
        width: 18px;
        text-align: left;
        font-size: 9px;
        font-family: "PingFangSC-Regular";
        font-weight: 400;
        color: rgba(117, 117, 117, 1);
        margin-right: 6px;
      }
      > span:nth-of-type(4) {
        display: inline-block;
        width: calc(100% - 48px - 150px - 6px);
        text-align: right;
        font-size: 9px;
        font-family: "PingFangSC-Regular";
        font-weight: 400;
        color: rgba(117, 117, 117, 1);
      }
    }
  }
`;

const ShowButton = styled.div`// styled
  & {
    font-size: 9px;
    font-family: "PingFangSC-Regular";
    font-weight: 400;
    color: rgba(117, 117, 117, 1);
    text-align: right;
    .scmIconfont {
      color: #999999;
      position: relative;
      top: 2px;
    }
  }
`;
