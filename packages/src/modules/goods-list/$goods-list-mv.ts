import { autowired, bean } from "@classes/ioc/ioc";
import { action, observable } from "mobx";
import { $ComponentService } from "../../classes/service/$component-service";

@bean($GoodsListMv)
export class $GoodsListMv {
  @autowired($ComponentService)
  public $componentService: $ComponentService;

  @observable public goodsList: any;

  @observable public isSpin: boolean = false;

  @observable public orderPriceViewPermission: string;

  @observable public retailPriceViewPermission: string;

  @action
  public showSpin() {
    this.isSpin = true;
  }

  @action
  public hideSpin() {
    this.isSpin = false;
  }

  @action
  public fetchGoodsList(params) {
    this.$componentService.queryGoodsList(params).then((data) => {
      const { productList, orderPriceViewPermission, retailPriceViewPermission } = data;
      this.orderPriceViewPermission = orderPriceViewPermission;
      this.retailPriceViewPermission = retailPriceViewPermission;
      this.goodsList = productList;
      this.hideSpin();
    });
  }
}
