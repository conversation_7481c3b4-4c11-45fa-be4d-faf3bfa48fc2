import { autowired } from "@classes/ioc/ioc";
import { observer } from "mobx-react";
import * as React from "react";
import { withRouter } from "react-router";
import styled from "styled-components";
import { $GoodsListMv } from "./$goods-list-mv";
import { NoGoods } from "../../components/no-goods/no-goods";
import { Spin } from "antd";
import { $CartType } from "../../classes/const/$cart-type";
import { GoodDetail } from "./shop-detail";

declare let require: any;

@withRouter
@observer
class GoodsList extends React.Component<any, any> {

  @autowired($GoodsListMv)
  public $goodsListMv: $GoodsListMv;

  public componentDidMount() {
    document.title = "发货单详情";
    this.loadGoodsList();
  }

  public loadGoodsList = () => {
    this.$goodsListMv.showSpin();
    const { orderId, SalesOrder } = this.props.match.params;
    const params = { docId: orderId, docType: SalesOrder };
    this.$goodsListMv.fetchGoodsList(params);
  }

  public render() {
    const { goodsList, isSpin, orderPriceViewPermission, retailPriceViewPermission } = this.$goodsListMv;
    return (
      <GoodsListWapper>
        <Spin spinning={isSpin}>
          {
            goodsList ? goodsList.length > 0 ? goodsList.map((goods) => {
              return (
                <Goods>
                  <div>
                    <img
                      src={goods.imageUrl ? goods.imageUrl : "https://order.fwh1988.cn:14501/static-img/scm/ico-nopic.png"}
                      alt=""/>
                    {
                      goods.isActive === "Y" ? null : <div className="nothing">无效</div>
                    }
                  </div>
                  <div>
                    <p>
                      <span>{goods.name ? goods.name.length > 18 ? `${goods.name.slice(0, 18)}...` : goods.name : null}</span>
                      <span>x{goods.quantity}</span>
                    </p>
                    <p>
                      {
                        (orderPriceViewPermission === $CartType.ORDERPRICEVIEWPERMISSION) && (goods.orderPrice !== null) ?
                          <span className="redprice">
                            <span style={{ fontSize: "10px" }}>￥</span>
                            {goods.orderPrice}
                            <span className="blackprice">
                              {goods.unitOfMeasure && `/${goods.unitOfMeasure}`}
                              </span>
                          </span> : null
                      }
                      {
                        (retailPriceViewPermission === $CartType.RETAILPRICEVIEWPERMISSION) && (goods.retailPrice !== null) ?
                          <span className="blackprice">
                            {((orderPriceViewPermission === $CartType.ORDERPRICEVIEWPERMISSION) && (goods.orderPrice !== null)) ? " (" : null}零售
                            ￥{goods.retailPrice}{((orderPriceViewPermission === $CartType.ORDERPRICEVIEWPERMISSION) && (goods.orderPrice !== null)) ? ")" : null}{((orderPriceViewPermission !== $CartType.ORDERPRICEVIEWPERMISSION) || (goods.orderPrice === null)) ?
                            <span
                              className="blackprice">{goods.unitOfMeasure && `/${goods.unitOfMeasure}`}</span> : null}
                          </span> : null
                      }
                    </p>
                    {
                      goods && goods.virtualSuitList.length > 0 && <GoodDetail goods={goods}/>
                    }
                  </div>
                </Goods>
              );
            }) : <NoGoods title="暂无商品清单" height={document.documentElement.clientHeight}/> : null
          }
        </Spin>
      </GoodsListWapper>
    );
  }
}

export default GoodsList;

const GoodsListWapper = styled.div`// styled
  & {
    width: 100%;
    height: auto;
  }
`;

const Goods = styled.div`// styled
  & {
    width: 100%;
    height: auto;
    padding: 10px 15px;
    box-sizing: border-box;
    position: relative;
    margin-bottom: 20px;
    .redprice {
      font-size: 14px;
      color: #ff3030;
      font-family: SourceHanSansCN-Normal, SourceHanSansCN;
    }
    .blackprice {
      font-size: 10px;
      color: #999999;
      font-family: SourceHanSansCN-Normal, SourceHanSansCN;
    }
    > div:nth-of-type(1) {
      width: 80px;
      height: 78px;
      display: inline-block;
      margin-right: 10px;
      position: absolute;
      > img {
        width: 80px;
        height: 78px;
        border-radius: 3px;
        border: 0;
        position: absolute;
        top: 0;
        left: 0;
      }
      .nothing {
        position: absolute;
        left: 10px;
        top: 10px;
        width: 60px;
        height: 60px;
        line-height: 60px;
        text-align: center;
        border-radius: 50%;
        background: rgba(0, 0, 0, 0.50);
        color: white;
      }
    }
    > div:nth-of-type(2) {
      display: inline-block;
      width: calc(100% - 80px - 15px);
      //position: absolute;
      //top: 10px;
      margin-left: 90px;
      > p:first-child {
        > span:nth-of-type(1) {
          color: #333;
          font-size: 12px;
          display: inline-block;
          width: 176px;
        }
        > span:last-child {
          position: absolute;
          top: 10px;
          right: 15px;
          color: #999999;
          font-size: 12px;
        }
      }
      > p:last-child {
        > span.price {
          color: #FF3030;
          font-size: 10px;
        }
        > .blackprice {
          color: #3F3F3F;;
          font-size: 10px;
        }
      }
    }
  }
`;
