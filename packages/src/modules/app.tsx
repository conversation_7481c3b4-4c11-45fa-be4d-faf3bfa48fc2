import React from "react";
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON> } from "react-router-dom";
import asyncComponent from "./AsyncComponent";
import { ScanningLogin } from "./scanning-login/scanning-login";
import ChooseReturnType from "../components/choose-returnType/choose-returnType";

const Home = asyncComponent(() => import("./home"));
const NoAuthority = asyncComponent(() => import("./no-authority/no-authority"));
const SessionExpired = asyncComponent(() => import("./session-expired/session-expired"), true);
const AccountFlowDetail = asyncComponent(() => import("./account-flow-detail/account-flow-detail"));
const AccountInfo = asyncComponent(() => import("./account-info/account-info"));
const NewAccountInfo = asyncComponent(() => import("./new-account-info/new-account-info"));
const DebtAccount = asyncComponent(() => import("./debt-account/debt-account"));
const NewModeDebtAccountRecord = asyncComponent(() => import("./new-mode-debt-account-record/new-mode-debt-account-record"));
const NewModeDebtAccountInfo = asyncComponent(() => import("./new-mode-debt-account-info/new-mode-debt-account-info"));
const NewAccountInfoDetail = asyncComponent(() => import("./new-account-info-detail/new-account-info-detail"));
const GiftsLimitList = asyncComponent(() => import("./gifts-limit-list/gifts-limit-list"));
const FreezeList = asyncComponent(() => import("./freeze-list/freeze-list"));
// const CartCoupon = asyncComponent(() => import("./cart-coupon/cart-coupon"));
const CommodityDetails = asyncComponent(() => import("./commodity-details/commodity-details"));
const VirtualsuitDetail = asyncComponent(() => import("./commodity-details/virtualsuit-detail"));
const ConfirmOrder = asyncComponent(() => import("./confirm-order/confirm-order"));
const CouponDetail = asyncComponent(() => import("./coupon-detail/coupon-detail"));
const Coupon = asyncComponent(() => import("./coupon/coupon"));
const DeliveryOrder = asyncComponent(() => import("./delivery-order/delivery-order"));
const ExchangeGoodsDetailEdit = asyncComponent(() => import("./exchange-goods-detail-edit/exchange-goods-detail-edit"));
const ExchangeGoodsDetail = asyncComponent(() => import("./exchange-goods-detail/exchange-goods-detail"));
const ExchangeGoodsList = asyncComponent(() => import("./exchange-goods-list/exchange-goods-list"));
const GoodsList = asyncComponent(() => import("./goods-list/goods-list"));
const ReturnedGoodsList = asyncComponent(() => import("./returned-goods-list/returned-goods-list"));
const SuitDetail = asyncComponent(() => import("./home/<USER>"));
const SuitList = asyncComponent(() => import("./home/<USER>"));
const Login = asyncComponent(() => import("./login/login"), true);
const MessageBusiness = asyncComponent(() => import("./message-business/message-business"));
const MessageCompanyDetail = asyncComponent(() => import("./message-company-detail/message-company-detail"));
const MessageCompany = asyncComponent(() => import("./message-company/meassage-company"));
const MessageNotification = asyncComponent(() => import("./message-notification/message-notification"));
const MyInfo = asyncComponent(() => import("./my-info/my-info"));
const OrderPartySelection = asyncComponent(() => import("./order-party-selection/order-party-selection"));
const OrderSchemeSelection = asyncComponent(() => import("./order-scheme-selection/order-scheme-selection"));
const ProductList = asyncComponent(() => import("./product-list"));
const SearchList = asyncComponent(() => import("./product-list/search"));
const ProChooseGift = asyncComponent(() => import("./promotion-choose-products/pro-choose-gift"));
const PromotionMatch = asyncComponent(() => import("./promotion-match/promotion-match"));
const Repayment = asyncComponent(() => import("./repayment/repayment"));
const Recharge = asyncComponent(() => import("./recharge/recharge"));
// const ResubmitOrder = asyncComponent(() => import("./resubmit-order/resubmit-order"));
const ReturnedDetail = asyncComponent(() => import("./returned-sales/returned-detail"));
const ReturnedSales = asyncComponent(() => import("./returned-sales/returned－sales"));
const EditAddress = asyncComponent(() => import("./shipping-address/edit-address"));
const NewAddress = asyncComponent(() => import("./shipping-address/new-address"));
const ShippingAddress = asyncComponent(() => import("./shipping-address/shipping-address"));
const ShopCart = asyncComponent(() => import("./shop-cart/cart"));
const LogisticsTracking = asyncComponent(() => import("./shop-order/logistics-tracking"));
const OrderDetail = asyncComponent(() => import("./shop-order/order-detail"));
const OrderList = asyncComponent(() => import("./shop-order/order-list"));
const AllShopOrderList = asyncComponent(() => import("./all-shop-order-list/all-shop-order-list"));
const ExpenseNodeList = asyncComponent(() => import("./expense-node/expense-node-list"));
const ExpenseNodeDetail = asyncComponent(() => import("./expense-node/expense-node-detail"));
const OfflineTransferRecordList = asyncComponent(() => import("./offline-transfer-record-list/offline-transfer-record-list"));
const OfflineTransferRecordDetail = asyncComponent(() => import("./offline-transfer-record-detail/offline-transfer-record-detail"));
const CombinedPayment = asyncComponent(() => import("./combined-payment/combined-payment"));
const AllOrderPayment = asyncComponent(() => import("./all-order-payment/all-order-payment"));
const AddressForm = asyncComponent(() => import("./submit-address-form/address-form"));
const SubmitOrderPayment = asyncComponent(() => import("./submit-order-payment/submit-order-payment"));
const SubmitOrder = asyncComponent(() => import("./submit-order/submit-order"));
const SubmitOrderSuccess = asyncComponent(() => import("./submit-order/submit-order-success"));
const Transfer = asyncComponent(() => import("./transfer/transfer"));
// const CreditList = asyncComponent(() => import("./credit-list/credit-list"));
// const CreditDetail = asyncComponent(() => import("./credit-detail/credit-detail"));
// const OrderReport = asyncComponent(() => import("./order-report/order-report"));
const TestImg = asyncComponent(() => import("./test-img/test-img"));
const PaymentInformationList = asyncComponent(() => import("./payment-information-list"));
const RechargeRecord = asyncComponent(() => import("./recharge-record/recharge-record"));
const RepaymentRecord = asyncComponent(() => import("./repayment-record/repayment-record"));
const CreditAmountList = asyncComponent(() => import("./credit-amount-list/credit-amount-list"));
const PaymentResultShow = asyncComponent(() => import("./payment-result-show/payment-result-show"));
const ReceiveOrderList = asyncComponent(() => import("./receive-order-list/receive-order-list"));
const ReceiveOrderDetail = asyncComponent(() => import("./receive-order-detail/receive-order-detail"));
const LogisticsInformationList = asyncComponent(() => import("./logistics-information-list/logistics-information-list"));
const InitiatingReturns = asyncComponent(() => import("./initiating-returns/initiating-returns"));
const ChooseItemList = asyncComponent(() => import("./../components/choose-item/choose-item"));
const ChooseReturnsProduct = asyncComponent(() => import("./initiating-returns/choose-returns-product"));
const FillInLogistics = asyncComponent(() => import("./fill-in-logistics/fill-in-logistics"));
const LogisticsCompanyList = asyncComponent(() => import("./logistics-company-list/logistics-company-list"));
const ConfirmResultShow = asyncComponent(() => import("./confirm-result-show/confirm-result-show"));
const ReturnsProductErrorList = asyncComponent(() => import("./initiating-returns/returns-product-errorList"));
const AccountInfoList = asyncComponent(() => import("./account-info-list/account-info-list"));
const MyBankList = asyncComponent(() => import("./my-bank-list/my-bank-list"));
const OweGoodsSelect = asyncComponent(() => import("./owe-goods-select/owe-goods-select"));
const ChooseShop = asyncComponent(() => import("../components/choose-shop/choose-shop"));
const BinningDetailList = asyncComponent(() => import("../modules/binning-detail-list/binning-detail-list"));
const ExpenseNodeCombPayment = asyncComponent(() => import("../modules/expense-node-combined-payment/expense-node-combined-payment"))
const BillList = asyncComponent(() => import("../modules/bill-list/bill-list"));
const BillDetail = asyncComponent(() => import("../modules/bill-detail/bill-detail"));
const BillTurnover = asyncComponent(() => import("../modules/bill-detail/bill-turnover"));
const BillFlowSummary = asyncComponent(() => import("../modules/bill-detail/bill-flow-summary"));
const NewBillDetail = asyncComponent(() => import("../modules/new-bill-detail/new-bill-detail"));
const OldBillList = asyncComponent(() => import("../modules/bill-list/old-bill-list"));
const GiftAccountInfo = asyncComponent(() => import("../modules/gift-account-info/gift-account-info"));
const GiftTransferStore = asyncComponent(() => import("../modules/gift-transfer-store/gift-transfer-store"));

export const SITE_PATH = "scm";

export class App extends React.Component<any, any> {

  public render() {
    return (
      <BrowserRouter>
        <div>
          <Route path={`/${SITE_PATH}/login`} component={Login}/>
          <Route path={`/${SITE_PATH}/home`} component={Home}/>
          <Route path={`/${SITE_PATH}/commodity-details/:productSkuId`} component={CommodityDetails}/>
          <Route path={`/${SITE_PATH}/virtualsuit-detail/:productSkuId`} component={VirtualsuitDetail}/>
          <Route path={`/${SITE_PATH}/suit-list`} component={SuitList}/>
          <Route path={`/${SITE_PATH}/suit-detail/:activitySuitId`} component={SuitDetail}/>
          <Route path={`/${SITE_PATH}/select`} component={OrderPartySelection}/>
          <Route path={`/${SITE_PATH}/select-scheme`} component={OrderSchemeSelection}/>
          <Route path={`/${SITE_PATH}/shop/list`} component={ProductList}/>
          <Route path={`/${SITE_PATH}/SearchList/:keyword`} component={SearchList}/>
          <Route path={`/${SITE_PATH}/shop/cart`} component={ShopCart}/>
          <Route path={`/${SITE_PATH}/submit/order`} component={SubmitOrder}/>
          <Route path={`/${SITE_PATH}/submit/success`} component={SubmitOrderSuccess}/>
          <Route path={`/${SITE_PATH}/confirm/order`} component={ConfirmOrder}/>
          {/*<Route path={`/${SITE_PATH}/resubmit/order/:orderId/:paymentModeId/:amount`} component={ResubmitOrder}/>*/}
          <Route path={`/${SITE_PATH}/promotion/match`} component={PromotionMatch}/>
          <Route path={`/${SITE_PATH}/ProChooseGift`} component={ProChooseGift}/>
          {/*<Route path={`/${SITE_PATH}/cart-coupon`} component={CartCoupon}/>*/}
          <Route path={`/${SITE_PATH}/submit/address`} component={AddressForm}/>
          <Route path={`/${SITE_PATH}/shop/order-list`} component={OrderList}/>
          <Route path={`/${SITE_PATH}/shop/all-shop-order-list`} component={AllShopOrderList}/>
          <Route path={`/${SITE_PATH}/offline-transfer-record-list`} component={OfflineTransferRecordList}/>
          <Route path={`/${SITE_PATH}/offline-transfer-record-detail/:oid/:docType`}
                 component={OfflineTransferRecordDetail}/>
          <Route path={`/${SITE_PATH}/shop/combined-payment`} component={CombinedPayment}/>
          <Route path={`/${SITE_PATH}/shop/all-order-payment`} component={AllOrderPayment}/>
          <Route path={`/${SITE_PATH}/shop/order-detail/:orderId/:orderSchemeName`} component={OrderDetail}/>
          <Route path={`/${SITE_PATH}/goods-list/:orderId/:SalesOrder`} component={GoodsList}/>
          <Route path={`/${SITE_PATH}/returned-goods-list/:orderId/:SalesOrder`} component={ReturnedGoodsList}/>
          <Route path={`/${SITE_PATH}/delivery-order/:orderId`} component={DeliveryOrder}/>
          <Route path={`/${SITE_PATH}/returned-sales`} component={ReturnedSales}/>
          <Route path={`/${SITE_PATH}/returned-detail/:oid`} component={ReturnedDetail}/>
          <Route path={`/${SITE_PATH}/shop/logistics-tracking`} component={LogisticsTracking}/>
          <Route
            path={`/${SITE_PATH}/submit/order-payment/:record/:orderId/:showPaymentBtn`}
            component={SubmitOrderPayment}/>
          <Route path={`/${SITE_PATH}/my`} component={MyInfo}/>
          <Route path={`/${SITE_PATH}/message-notification`} component={MessageNotification}/>
          <Route path={`/${SITE_PATH}/message-company`} component={MessageCompany}/>
          <Route path={`/${SITE_PATH}/message-company-detail/:id`} component={MessageCompanyDetail}/>
          <Route path={`/${SITE_PATH}/message-business`} component={MessageBusiness}/>
          <Route path={`/${SITE_PATH}/account-info/:accountId/:accountTypeCode`} component={AccountInfo}/>
          <Route path={`/${SITE_PATH}/debt-account/:accountId`} component={DebtAccount} />
          <Route path={`/${SITE_PATH}/new-mode-debt-account-record/:accountId`} component={NewModeDebtAccountRecord} />
          <Route path={`/${SITE_PATH}/new-mode-debt-account-info/:accountId`} component={NewModeDebtAccountInfo} />
          <Route path={`/${SITE_PATH}/new-account-info/:accountId/:accountTypeCode`} component={NewAccountInfo} />
          <Route path={`/${SITE_PATH}/new-account-info-detail/:accountId`} component={NewAccountInfoDetail} />
          <Route path={`/${SITE_PATH}/gift-account-info/:orderPartyId`} component={GiftAccountInfo} />
          <Route path={`/${SITE_PATH}/gift-transfer-store/:orderPartyId`} component={GiftTransferStore} />
          <Route path={`/${SITE_PATH}/gifts-limit-list/:accountId`} component={GiftsLimitList} />
          <Route path={`/${SITE_PATH}/freeze-list/:accountId`} component={FreezeList} />
          <Route path={`/${SITE_PATH}/account-info-list/:accountId/:accountTypeCode`} component={AccountInfoList}/>
          <Route path={`/${SITE_PATH}/account-info-detail/:accountFlowId`} component={AccountFlowDetail}/>
          <Route path={`/${SITE_PATH}/shippingAddress`} component={ShippingAddress}/>
          <Route path={`/${SITE_PATH}/newAddress`} component={NewAddress}/>
          <Route path={`/${SITE_PATH}/editAddress/:id`} component={EditAddress}/>
          <Route path={`/${SITE_PATH}/my-repayment/:amount`} component={Repayment}/>
          <Route path={`/${SITE_PATH}/my-recharge/:accountId`} component={Recharge}/>
          <Route path={`/${SITE_PATH}/my-transfer/:accountId/:accountType`} component={Transfer}/>
          {/*<Route path={`/${SITE_PATH}/credit-list`} component={CreditList}/>*/}
          {/*<Route path={`/${SITE_PATH}/credit-detail`} component={CreditDetail}/>*/}
          <Route path={`/${SITE_PATH}/coupon`} component={Coupon}/>
          <Route path={`/${SITE_PATH}/coupon-detail`} component={CouponDetail}/>
          <Route path={`/${SITE_PATH}/exchange-goods-list`} component={ExchangeGoodsList}/>
          <Route path={`/${SITE_PATH}/exchange-goods-detail`} component={ExchangeGoodsDetail}/>
          <Route path={`/${SITE_PATH}/exchange-goods-detail-edit`} component={ExchangeGoodsDetailEdit}/>
          <Route path={`/${SITE_PATH}/session-expired`} component={SessionExpired}/>
          <Route path={`/${SITE_PATH}/no-authority`} component={NoAuthority}/>
          {/*<Route path={`/${SITE_PATH}/order-report`} component={OrderReport}/>*/}
          <Route path={`/${SITE_PATH}/test-img`} component={TestImg}/>
          <Route path={`/${SITE_PATH}/scanning-login`} component={ScanningLogin}/>
          <Route path={`/${SITE_PATH}/payment-information-list/:orderId`} component={PaymentInformationList}/>
          <Route path={`/${SITE_PATH}/receive-order-list`} component={ReceiveOrderList}/>
          <Route path={`/${SITE_PATH}/receive-order-detail/:deliveryId`} component={ReceiveOrderDetail}/>
          <Route path={`/${SITE_PATH}/logistics-information-list/:deliveryId`} component={LogisticsInformationList}/>
          <Route path={`/${SITE_PATH}/recharge-record`} component={RechargeRecord}/>
          <Route path={`/${SITE_PATH}/repayment-record`} component={RepaymentRecord}/>
          <Route path={`/${SITE_PATH}/credit-amount-list`} component={CreditAmountList}/>
          <Route path={`/${SITE_PATH}/payment-result-show/:paymentMode`} component={PaymentResultShow}/>
          <Route path={`/${SITE_PATH}/choose-item`} component={ChooseItemList}/>
          <Route path={`/${SITE_PATH}/initiating-returns`} component={InitiatingReturns}/>
          <Route path={`/${SITE_PATH}/choose-returns-product`} component={ChooseReturnsProduct}/>
          <Route path={`/${SITE_PATH}/returns-product-errorList`} component={ReturnsProductErrorList}/>
          <Route path={`/${SITE_PATH}/fill-in-logistics/:orderId`} component={FillInLogistics}/>
          <Route path={`/${SITE_PATH}/logistics-company-list/:orderId`} component={LogisticsCompanyList}/>
          <Route path={`/${SITE_PATH}/confirm-result-show/:result/:fromWhere`} component={ConfirmResultShow}/>
          <Route path={`/${SITE_PATH}/my-bank-list`} component={MyBankList}/>
          <Route path={`/${SITE_PATH}/owe-goods-select`} component={OweGoodsSelect}/>
          <Route path={`/${SITE_PATH}/choose-shop`} component={ChooseShop}/>
          <Route path={`/${SITE_PATH}/expense-node-list/:activeType/:orgId`} component={ExpenseNodeList}/>
          <Route path={`/${SITE_PATH}/expense-node-detail/:expenseOid`} component={ExpenseNodeDetail}/>
          <Route path={`/${SITE_PATH}/choose-returnType`} component={ChooseReturnType}/>
          <Route path={`/${SITE_PATH}/binning-detail-list/:deliveryId`} component={BinningDetailList}/>
          <Route path={`/${SITE_PATH}/expense-node-combined-payment`} component={ExpenseNodeCombPayment}/>
          <Route path={`/${SITE_PATH}/bill-list`} component={BillList}/>
          <Route path={`/${SITE_PATH}/old-bill-list`} component={OldBillList} />
          <Route path={`/${SITE_PATH}/bill-detail/:orderId`} component={BillDetail}/>
          <Route path={`/${SITE_PATH}/new-bill-detail/:oid/:type`} component={NewBillDetail}/>
          <Route path={`/${SITE_PATH}/bill-turnover`} component={BillTurnover}/>
          <Route path={`/${SITE_PATH}/bill-flow-summary`} component={BillFlowSummary}/>
        </div>
      </BrowserRouter>
    );
  }
}
