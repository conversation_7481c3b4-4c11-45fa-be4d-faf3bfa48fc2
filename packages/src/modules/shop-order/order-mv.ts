import { autowired, bean } from "@classes/ioc/ioc";
import { action, observable } from "mobx";
import { $OrderService } from "../../classes/service/$order-service";
import { $OrderPartyService } from "../../classes/service/$order-party-service";
import { beanMapper } from "../../helpers/bean-helpers";
import { $SalesOrderStatus } from "@classes/entity/$sales-order-status";

@bean($OrderMv)
export class $OrderMv {
  @autowired($OrderService)
  public $orderService: $OrderService;

  @autowired($OrderPartyService)
  public $orderPartyService: $OrderPartyService;

  @observable public orderStatus = [
    {
      activeKey: "ALL",
      name: "全部",
      value: "ALL",
    },
    {
      activeKey: "AUDIT",
      name: "待审核",
      value: "AUDIT",
    },
    {
      activeKey: "PAYMENT",
      name: "待付款",
      value: "PAYMENT",
    },
    {
      activeKey: "WAIT_DELIVER",
      name: "待发货",
      value: "WAIT_DELIVER",
    },
    {
      activeKey: "DELIVER",
      name: "待收货",
      value: "DELIVER",
    },
  ];

  @observable public key = "ALL";
  @observable public pageIndex = 0;
  @observable public scrollHeight = 0;
  @observable public finished = false;
  @observable public isLoad = false;
  @observable public pageSize = 10;
  @observable public orderListInfo = [];

  @observable public orderItemList = [];

  @observable public expand = false;

  @observable public orderDetail: any;

  @observable public editOrderId: number;

  @observable public isSpin: boolean = false;

  @observable public isBySkuType: boolean;

  @observable public orderPriceViewPermission: string;

  @observable public retailPriceViewPermission: string;

  @observable public freightViewPermission: string;

  @observable public salesOrderStatusList: $SalesOrderStatus[] = [];

  @observable public isCreateFreightFeeDocument: string;

  @observable public freightFeeDocumentPaymentStatusCode: string;

  @action
  public showSpin() {
    this.isSpin = true;
  }

  @action
  public hideSpin() {
    this.isSpin = false;
  }

  @action
  public setPriceViewPermission(freightViewPermission) {
    this.freightViewPermission = freightViewPermission;
  }

  @action
  public setEditOrderId(editOrderId) {
    this.editOrderId = editOrderId;
  }
  @action
  public changeIsBySkuType(boolean) {
    this.isBySkuType = boolean;
  }

  @action
  public cancleEditOrderId(editOrderId) {
    this.editOrderId = null;
  }

  @action
  public changePage() {
    this.pageIndex ++;
  }

  @action
  public getList(params) {
    return this.$orderService.queryOrderList(params);
  }

  @action
  public getDetail(params) {
    return this.$orderService.queryOrderDetail(params);
  }
  @action
  public copyOrder(params) {
    return this.$orderService.copyOrder(params);
  }
  @action
  public getTracking(params) {
    return this.$orderService.queryLogisticsTracking(params);
  }

  @action
  public cancleOrder(orderId) {
    return this.$orderService.reoveOrder(orderId);
  }

  @action
  public editOrder(orderId) {
    return this.$orderService.compileOrder(orderId);
  }

  @action
  public auditOrder(orderId) {
    return this.$orderService.examineOrder(orderId);
  }
  @action
  public checkBatchPaymentMode(params) {
    return this.$orderService.checkBatchPaymentMode(params);
  }
  @action
  public setOrderDetail(obj) {
    this.orderDetail = obj;
  }
  @action
  public saveShopAndScheme(params) {
    return this.$orderPartyService.saveShopAndScheme(params);
  }
  @action
  public queryOldData(obj) {
    beanMapper(obj, this);
    console.log(this);
  }

  @action
  public salesOrderStatus(params) {
    return this.$orderService.salesOrderStatus(params).then((data) => {
      this.salesOrderStatusList = data.map((status) => new $SalesOrderStatus(status));
    });
  }

  @action
  public clearMVData() {
    this.orderStatus = [
      {
        activeKey: "ALL",
        name: "全部",
        value: "ALL",
      },
      {
        activeKey: "AUDIT",
        name: "待审核",
        value: "AUDIT",
      },
      {
        activeKey: "PAYMENT",
        name: "待付款",
        value: "PAYMENT",
      },
      {
        activeKey: "WAIT_DELIVER",
        name: "待发货",
        value: "WAIT_DELIVER",
      },
      {
        activeKey: "DELIVER",
        name: "待收货",
        value: "DELIVER",
      },
    ];
    this.pageIndex = 0;
    this.scrollHeight = 0;
    this.finished = false;
    this.isLoad = false;
    this.pageSize = 10;
    this.key = "ALL";
    this.orderListInfo = [];
    this.orderItemList = [];
    this.expand = false;
    this.editOrderId = 0;
    this.isSpin = false;
    this.orderPriceViewPermission = "";
    this.retailPriceViewPermission = "";
    this.freightViewPermission = "";
    this.isBySkuType = false;
  }
}
