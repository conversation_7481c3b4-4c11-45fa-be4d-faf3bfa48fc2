import { observer } from "mobx-react";
import * as React from "react";
import { with<PERSON>out<PERSON> } from "react-router";
import styled from "styled-components";
// import "./logisticsTracking.css";

declare let window: any;

@withRouter
@observer
class LogisticsTracking extends React.Component<any, any> {

  constructor(props) {
    super(props);
    this.state = {
      isShowButton: true,
      selectIndex: 3,
    };
  }

  public showMore = (goods) => {
    this.setState({
      selectIndex: goods.logisticsTravelList ? goods.logisticsTravelList.length : 0,
      isShowButton: false,
    });
  }

  public render() {
    const { logisticsTravelList, data } = this.props;
    const { isShowButton, selectIndex } = this.state;
    return (
      <LogisticsTrackingPage>
        {
          logisticsTravelList.slice(0, selectIndex).map((item, index) => {
            const date = item.time && item.time.split(" ");
            return (
              <LogisticsTrackingContent style={{
                paddingTop: index === 0 ? "12px" : 0,
                paddingBottom: index === logisticsTravelList.length - 1 ? "12px" : 0
              }}>
                <LogisticsTrackingLeft>
                  <div>
                    {date[0]}
                  </div>
                  <div>
                    {date[1]}
                  </div>
                </LogisticsTrackingLeft>
                <LogisticsTrackingRight
                  style={{ borderLeft: ((index === logisticsTravelList.length - 1) || (index === 2 && isShowButton)) ? "none" : "1px solid #ddd" }}>
                  {
                    index === 0 ? <i className={"scmIconfont scm-icon-select-box-on logisticsIcon"}/> :
                      <i
                        className={"scmIconfont scm-shixinyuanxing logisticsIcon"}
                        style={{
                          left: ((index === logisticsTravelList.length - 1) || (index === 2 && isShowButton)) ? "-3px" : "-4px",
                        }}
                      />
                  }
                  <div style={{ color: index === 0 ? "#333333" : "#999999" }}>
                    {item.contextList && item.contextList[0]}
                    {item.contextList && item.contextList[1] &&
                    <a href={"tel:" + item.contextList[1]} style={{ color: "#307DCD" }}>{item.contextList[1]}</a>}
                    {item.contextList && item.contextList[2] && item.contextList[2]}
                    {item.contextList && item.contextList[3] &&
                    <a href={"tel:" + item.contextList[3]} style={{ color: "#307DCD" }}>{item.contextList[3]}</a>}
                    {item.contextList && item.contextList[4] && item.contextList[4]}
                  </div>
                </LogisticsTrackingRight>
              </LogisticsTrackingContent>
            );
          })
        }
        {
          isShowButton && logisticsTravelList.length > 3 && <div onClick={() => this.showMore(data)}
                                                                 style={{ textAlign: "center", paddingBottom: "12px" }}
                                                                 className={"showMore"}>
            点击查看更多物流详情
            <i className={"scmIconfont scm-next-s"}/>
          </div>
        }
      </LogisticsTrackingPage>
    );
  }
}

export default LogisticsTracking;

const LogisticsTrackingPage = styled.div`// styled
  & {
    width: 100%;
    height: auto;
    box-sizing: border-box;
    background: #fff;
    .showMore {
      font-size: 12px;
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: 400;
      color: rgba(153, 153, 153, 1);
      .scm-next-s {
        font-size: 11px;
        color: #999999;
        margin-left: 7px;
      }
    }
  }
`;

const LogisticsTrackingContent = styled.div`// styled
  & {
    width: 100%;
    height: auto;
    padding: 12px;
  }
`;

const LogisticsTrackingLeft = styled.div`// styled
  & {
    width: 25%;
    min-height: 50px;
    display: inline-block;
    text-align: right;
    padding-right: 10px;
    vertical-align: top;
    font-family: PingFangSC-Regular, PingFang SC;
    font-weight: 400;
    color: rgba(153, 153, 153, 1);
    > div:first-child {
      font-size: 12px;
    }
    > div:nth-of-type(2) {
      font-size: 10px;
    }
  }
`;

const LogisticsTrackingRight = styled.div`// styled
  & {
    width: 75%;
    min-height: 50px;
    display: inline-block;
    border-left: 1px solid #ddd;
    padding-left: 10px;
    position: relative;
    .logisticsIcon {
      position: absolute;
      top: -5px;
      left: -8px;
    }
    > div {
      padding-bottom: 8px;
      font-size: 12px;
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: 400;
      color: rgba(153, 153, 153, 1);
    }
    .scm-icon-select-box-on {
      color: #52C41A;
      font-size: 17px;
    }
    .scm-shixinyuanxing {
      font-size: 7px;
      color: #D2D2D2;
      left: -4px;
      top: 0;
    }
  }
`;
