import { autowired } from "@classes/ioc/ioc";
import { Spin } from "antd";
import { Checkbox, List, Modal, Toast } from "antd-mobile";
import { observer } from "mobx-react";
import * as React from "react";
import { withRouter } from "react-router";
import styled from "styled-components";
import DateUtils from "../../classes/utils/DateUtils";
import { SITE_PATH } from "../app";
import { $CartMv } from "../shop-cart/cart-mv";
import { $SubmitOrderMv } from "../submit-order/submit-order-mv";
import { $OrderMv } from "./order-mv";
import { $PayType } from "../../classes/const/$pay-type";
import { $CartType } from "../../classes/const/$cart-type";
import { $OrderType } from "../../classes/const/$order-type";
import disabledOprate from "../../components/noWX-disabled-operate";
import { gaEvent } from "../../classes/website-statistics/website-statistics";
import { $AppStore, AppStoreKey } from "../../classes/stores/app-store-mv";
import { CustomModal } from "../../components/custom-modal/custom-modal";
import { $OrderService } from "@classes/service/$order-service";

declare let window: any;
const Item = List.Item;
const Brief = Item.Brief;
const CheckboxItem = Checkbox.CheckboxItem;

const alert = Modal.alert;

@withRouter
@observer
class OrderDetail extends React.Component<any, any> {
  @autowired($AppStore)
  public $AppStore: $AppStore;

  @autowired($CartMv)
  public $CartMv: $CartMv;

  @autowired($OrderMv)
  public $OrderMv: $OrderMv;

  @autowired($SubmitOrderMv)
  public $SubmitOrderMv: $SubmitOrderMv;

  @autowired($OrderService)
  public $orderService: $OrderService;

  constructor(props) {
    super(props);
    this.state = {
      scanningLogin: localStorage.getItem("scanningLogin") === "Y",
      showPayInfo: false,
      isCheckOrder: true,
      payInfo: null,
      sumAmount: 0,
      freightInfo: null,
    };
  }

  public componentDidMount() {
    document.title = "订单详情";
    gaEvent("订单详情");
    window.scroll(0, 0);
    const params = {
      orderId: this.props.match.params.orderId,
    };
    this.$OrderMv.showSpin();
    this.$OrderMv.getDetail(params).then((data) => {
      console.log(data);
      this.$OrderMv.hideSpin();
      this.$OrderMv.setOrderDetail(data);
    }).catch((err) => {
      this.$OrderMv.hideSpin();
      Toast.fail(err.response.body.message);
    });
    /*this.$SubmitOrderMv.fetchQueryRole().then((data) => {
      if (data.tokenPageModes === "ORDER_CONFIRM_MODES") {  //门店
        this.$SubmitOrderMv.setIsRole(false);
      } else {
        this.$SubmitOrderMv.setIsRole(true);
      }
    });*/
  }

  public cancelOrder() {
    const orderId = this.props.match.params.orderId;
    this.$OrderMv.cancleOrder(orderId).then((data) => {
      if (data.result) {
        Toast.info("已取消", 3);
        const params = {
          orderId: this.props.match.params.orderId,
        };
        this.$OrderMv.getDetail(params).then((data1) => {
          this.$OrderMv.setOrderDetail(data1);
        });
      }
    });
  }

  public reject() {
    const orderId = this.props.match.params.orderId;
    this.$OrderMv.cancleOrder(orderId).then((data) => {
      if (data.result) {
        const params = {
          orderId: this.props.match.params.orderId,
        };
        this.$OrderMv.getDetail(params).then((data1) => {
          this.$OrderMv.setOrderDetail(data1);
        });
      }
    });
  }

  public copyOrder(orderOrgId, orderSchemeId) {
    const orderId = this.props.match.params.orderId;
    sessionStorage.setItem("editOrderId", null);
    this.$OrderMv.copyOrder({ slaesOrderId: orderId }).then((res) => {
      const { errorMessage, isSkip } = res;
      if (errorMessage) {
        Toast.info(errorMessage);
      }
      if (isSkip) {
        const saveParams = { orderPartyId: orderOrgId, orderSchemeId };
        this.$OrderMv.saveShopAndScheme(saveParams).then((data) => {
          const message = data.errorMessage;
          if (message) {
            Toast.info(message);
          } else {
            this.$AppStore.clearPageMv(AppStoreKey.SHOPCART);
            this.$CartMv.openDiscount();
            this.props.history.push({
              pathname: `/${SITE_PATH}/shop/cart`,
            });
          }
        });
      }
    });
  }

  public editOrder() {
    const orderId = this.props.match.params.orderId;
    this.$OrderMv.editOrder(orderId).then((data) => {
      if (data.result) {
        this.$AppStore.clearPageMv(AppStoreKey.SHOPCART);
        this.props.history.push({ pathname: `/${SITE_PATH}/shop/cart` });
        sessionStorage.setItem("editOrderId", orderId);
      }
    });
  }

  public auditOrder(isEditing) {
    if (isEditing === $OrderType.ISEDITING) { // 订单在编辑中
      alert("订单正在编辑中，需编辑后重新提交订单，是否继续？", "", [
        { text: "取消", onPress: () => console.log("cancel") },
        { text: "继续", onPress: () => this.editOrder() },
      ]);
    } else {
      const orderId = this.props.match.params.orderId;
      this.$OrderMv.auditOrder(orderId).then((data) => {
        if (data.result) {
          Toast.info("审核通过，请等待客服人员审核", 3);
          const params = {
            orderId: this.props.match.params.orderId,
          };
          this.$OrderMv.getDetail(params).then((data2) => {
            this.$OrderMv.setOrderDetail(data2);
          });
        }
      });
    }
  }

  public submitFinance = (isCreateFreightFeeDocument) => {
    const orderId = this.props.match.params.orderId;
    const { isCheckOrder } = this.state;
    let payRange = null;
    if (isCreateFreightFeeDocument === $CartType.ISCREATEFREIGHTFEEDOCUMENT) {
      payRange = isCheckOrder ? "All" : "Freight";
    } else {
      payRange = null;
    }
    this.$OrderMv.checkBatchPaymentMode({ salesOrderIds: [orderId] }).then((data) => {
      const { errorCode, errorMessage } = data;
      if (errorCode === "0") {
        if (this.props.location.state) {
          if (this.props.location.state.backSource === "single") { // 单个门店
            this.$AppStore.clearPageMv(AppStoreKey.SINGLEPAYMENTSTORAGE);
            window.location.href = `/${SITE_PATH}/submit/order-payment/34657/${orderId}/notPay?backSource=single&payRange=${payRange}`;
          } else {// 多个门店
            this.$AppStore.clearPageMv(AppStoreKey.SINGLEPAYMENTSTORAGE);
            window.location.href = `/${SITE_PATH}/submit/order-payment/34657/${orderId}/notPay?backSource=all&payRange=${payRange}`;
          }
        } else {
          this.$AppStore.clearPageMv(AppStoreKey.SINGLEPAYMENTSTORAGE);
          window.location.href = `/${SITE_PATH}/submit/order-payment/34657/${orderId}/notPay?backSource=single&payRange=${payRange}`;
        }
      } else if (errorCode === $OrderType.INVALIDPAYMENT) {
        alert(`${errorMessage}`, "", [
          {
            text: "确认", onPress: () => {
              this.componentDidMount();
            },
          },
        ]);
      }
    });
  }

  public deliveryOrder() {
    const orderId = this.props.match.params.orderId;
    window.location.href = `/${SITE_PATH}/delivery-order/${orderId}`;
  }

  public toGoodList() {
    this.props.history.push({ pathname: `/${SITE_PATH}/goods-list/${this.props.match.params.orderId}/SalesOrder` });
  }

  public toDeliveryOrder() {
    this.props.history.push({ pathname: `/${SITE_PATH}/delivery-order/${this.props.match.params.orderId}` });
  }

  public toPay() {
    this.$AppStore.clearPageMv(AppStoreKey.SINGLEPAYMENTSTORAGE);
    window.location.href = `/${SITE_PATH}/submit/order-payment/partPay/${this.props.match.params.orderId}/notPay`;
  }

  public toRecordDetail() {
    window.location.href = `/${SITE_PATH}/payment-information-list/${this.props.match.params.orderId}?orderType=${$OrderType.SALESORDER}`;
  }

  public getPriceShowHtml(data, name) {
    const { isBySkuType, totalAmount, amountList } = data;
    console.log(isBySkuType);
    return isBySkuType ? amountList.map((item) => {
        return <div><span>{item.name} </span><span>{"¥ " + item.amountBySkuType}</span></div>
      })
      :
      <div><span>{name}</span> <span>{"¥ " + totalAmount}</span></div>
  }

  public renderLabel = (orderDetail) => {
    let label = null;
    switch (orderDetail.paymentStatusCode) {
      case $CartType.PAYEND:
      case $CartType.BALANCEPAYEND:
      case $CartType.PARTPAIDAUDITING:
      case $CartType.PAYAUDITING:
      case $CartType.DEPOSITPAYPARTAUDITING:
      case $CartType.DEPOSITPAYAUDITING:
      case $CartType.BALANCEPAYPARTAUDITING:
      case $CartType.BALANCEPAYAUDITING:
      case $CartType.DEPOSITPAYEND:
        label = orderDetail.paymentStatus;
        break;
      default:
        label = "未支付 ¥ " + orderDetail.unPayableAmount;
        break;
    }
    return label;
  }

  public showFreightModal = () => {
    const orderId = this.props.match.params.orderId;
    this.setState({
      showFreight: true,
    }, () => {
      this.$orderService.feedocumentDetail({ salesOrderId: orderId, docType: "Freight" }).then((data) => {
        console.log("运费信息", data);
        this.setState({
          freightInfo: data,
        });
      });
    });
  }

  public close = () => {
    this.setState({
      showFreight: false,
    });
  }

  public closePay = () => {
    this.setState({
      showPayInfo: false,
    });
  }

  public showPayInfo = (orderId, isCreateFreightFeeDocument) => {
    this.$orderService.salesorderPayinfo({ salesOrderIdList: [orderId] }).then((data) => {
      console.log("支付信息", data);
      this.setState({
        payInfo: data,
        sumAmount: data.totalPayInfo && data.totalPayInfo.totalPayAmount,
      });
      if (data.totalPayInfo && data.totalPayInfo.freightTotalAmount === 0) {
        this.submitFinance(isCreateFreightFeeDocument);
      } else {
        this.setState({
          showPayInfo: true,
          isCheckOrder: true,
        }, () => {
          const { orderDetail } = this.$OrderMv;
          const isLimitPaymentMode = orderDetail && orderDetail.paymentInfo && orderDetail.paymentInfo.isLimitPaymentMode;
          if (isLimitPaymentMode) {
            this.changeCheck({ target: { checked: false } });
          }
        });
      }
    });
  }

  public renderContent = () => {
    const { freightInfo } = this.state;
    return (
      <FreightContent>
        <p>
          <span>
            <i className={"scmIconfont scm-bianma"}/>
            编码
          </span>
          <span className={"value name"}>{freightInfo && freightInfo.code}</span>
        </p>
        <p>
          <span>
            <i className={"scmIconfont scm-mendian"}/>
            门店
          </span>
          <span className={"value name"}>{freightInfo && freightInfo.orderOrgName}</span>
        </p>
        <p>
          <span>
            <i className={"scmIconfont scm-kuaidi"}/>
            运费
          </span>
          <span className={"value"}>¥{freightInfo && freightInfo.amount}</span>
        </p>
        <p>
          <span>
            <i className={"scmIconfont scm-dingdanjilu"}/>
            关联订单号
          </span>
          <p style={{ display: "inline-block" }}>
            {
              freightInfo && freightInfo.itemList && freightInfo.itemList.length > 0 && freightInfo.itemList.map((item) => {
                return (
                  <p className={"value smallName"} key={item.id}>{item.relatedDocNo}</p>
                );
              })
            }
          </p>
        </p>
      </FreightContent>
    );
  }

  public changeCheck = (e) => {
    const { payInfo } = this.state;
    console.log(e.target.checked, "isCheckOrder");
    this.setState({
      isCheckOrder: e.target.checked,
    }, () => {
      if (e.target.checked) {
        this.setState({
          sumAmount: payInfo.totalPayInfo && (payInfo.totalPayInfo.salesOrderTotalAmount + payInfo.totalPayInfo.freightTotalAmount),
        }, () => {
          console.log(this.state.sumAmount, "true");
        });
      } else {
        this.setState({
          sumAmount: payInfo.totalPayInfo && payInfo.totalPayInfo.freightTotalAmount,
        }, () => {
          console.log(this.state.sumAmount, "false");
        });
      }
    });
  }

  public renderPayContent = (isLimitPaymentMode) => {
    const { isCheckOrder, payInfo, sumAmount } = this.state;
    return (
      <PayContent>
        {
          isLimitPaymentMode && <div className={"title-notice"}>由于订货方案限制了货款的支付方式，请先单独支付运费！</div>
        }
        <div>
          <p>
            <CheckboxItem onChange={(val) => this.changeCheck(val)} checked={isCheckOrder} disabled={isLimitPaymentMode}>
              <i className={"scmIconfont scm-dingdanjilu"}/>
              <span className={"title"}>订单</span>
              <span>
              <span className={"toPay"}>待付金额：</span>
              <span
                className={"toPay amount"}>￥{payInfo && payInfo.totalPayInfo && payInfo.totalPayInfo.salesOrderTotalAmount}</span>
            </span>
            </CheckboxItem>
          </p>
          <p>
            <CheckboxItem disabled={true} checked={true}>
              <i className={"scmIconfont scm-kuaidi"}/>
              <span className={"title"}>运费</span>
              <span>
              <span className={"toPay"}>待付金额：</span>
              <span
                className={"toPay amount"}>￥{payInfo && payInfo.totalPayInfo && payInfo.totalPayInfo.freightTotalAmount}</span>
            </span>
            </CheckboxItem>
          </p>
        </div>
        <div className={"total"}>
          <CheckboxItem checked={isCheckOrder} onChange={(val) => this.changeCheck(val)} disabled={true}>
            <span className={"sum"}>合计</span>
            <span>
              <span className={"info"}>总计:</span>
              <span className={"Symbol"}>￥</span>
              <span className={"Symbol red"}>{Number(sumAmount).toFixed(2)}</span>
            </span>
            <span>
              <span className={"info"}>商品总数:</span>
              <span
                className={"black"}> {payInfo && payInfo.totalPayInfo && payInfo.totalPayInfo.totalProductSkuCount} </span>
              <span className={"info"}>件</span>
            </span>
          </CheckboxItem>
        </div>
      </PayContent>
    );
  }

  public render() {
    const { orderDetail, isSpin } = this.$OrderMv;
    console.log("orderDetail", orderDetail);
    const { scanningLogin, showFreight, showPayInfo } = this.state;
    const { orderSchemeName } = this.props.match.params;
    const isLimitPaymentMode = orderDetail && orderDetail.paymentInfo && orderDetail.paymentInfo.isLimitPaymentMode;
    return (
      <OrderDetailPage>
        <Spin spinning={isSpin} style={{ position: "relative", bottom: "50%", left: "50%" }}>
          {
            orderDetail ?
              <div style={{ paddingBottom: 60 }}>
                <List className="my-list">
                  <Item
                    extra={orderDetail.docStatus}
                    className="status"
                  >
                    订单状态
                  </Item>
                </List>
                {
                  orderDetail.retailPriceViewPermission === $CartType.RETAILPRICEVIEWPERMISSION && orderDetail && orderDetail.retailAmount &&
                  <div className="retail-price">
                    <div className="price-header">
                      <span className="scmIconfont-wrap"><i className="scmIconfont scm-icon-baoliujine"></i></span>
                      <h5>零售价</h5>
                    </div>
                    <div className="price-content">
                      {
                        this.getPriceShowHtml(orderDetail.retailAmount, "零售价总额")
                      }
                    </div>
                  </div>
                }
                {
                  orderDetail &&
                  // orderDetail.orderPriceViewPermission === $CartType.ORDERPRICEVIEWPERMISSION ?
                  <div
                    className={`${
                      (orderDetail.orderPriceViewPermission !== $CartType.ORDERPRICEVIEWPERMISSION &&
                        orderDetail.discountAmountViewPermission !== $CartType.ORDERPREFERENTIALACTIVITIESPERMISSION &&
                        (orderDetail.freightViewPermission !== $CartType.ORDERFREIGHTVIEWPERMISSION ||
                          orderDetail.isUseFreight !== $PayType.ISUSEFREIGHT ||
                          orderDetail.freightAmount <= 0
                        )) ?
                        "order-price displayNone"
                        :
                        "order-price"
                      }`}>
                    <div className="price-header">
                      <span className="scmIconfont-wrap"><i className="scmIconfont scm-icon-jiageguanli"></i></span>
                      <h5>订货价</h5>
                    </div>
                    <div className="price-content">
                      {
                        orderDetail.orderPriceViewPermission === $CartType.ORDERPRICEVIEWPERMISSION &&
                        this.getPriceShowHtml(orderDetail.orderAmount, "订货价总额")
                      }
                      {
                        orderDetail.discountAmountViewPermission === $CartType.ORDERPREFERENTIALACTIVITIESPERMISSION &&
                        <div><span>活动优惠</span>
                          <span>{`${orderDetail.activityDiscountAmount === 0 ? "¥ 0" : "- ¥ " + Math.abs(orderDetail.activityDiscountAmount)}`}</span>
                        </div>
                      }
                      {
                        orderDetail.orderPriceViewPermission === $CartType.ORDERPRICEVIEWPERMISSION &&
                        <div><span>货款</span>
                          <span>{"¥ " + Number(orderDetail.totalAmount - orderDetail.freightAmount).toFixed(2)}</span>
                        </div>
                      }
                      {
                        (orderDetail.freightViewPermission === $CartType.ORDERFREIGHTVIEWPERMISSION && orderDetail.isUseFreight === $PayType.ISUSEFREIGHT && orderDetail.freightAmount > 0) && (orderDetail.isCreateFreightFeeDocument !== $CartType.ISCREATEFREIGHTFEEDOCUMENT || orderDetail.freightFeeDocumentPaymentStatusCode === "") &&
                        <div><span>运费</span> <span>{"¥ " + orderDetail.freightAmount}</span></div>
                      }
                      {
                        ((orderDetail.isCreateFreightFeeDocument === $CartType.ISCREATEFREIGHTFEEDOCUMENT) && (orderDetail.freightFeeDocumentPaymentStatusCode !== "" && orderDetail.freightFeeDocumentPaymentStatusCode !== null)) &&
                        <div>
                          <span onClick={this.showFreightModal} className={"seeFreight"}>查看关联运费单</span>
                          <span/></div>
                      }
                    </div>
                    {
                      orderDetail.orderPriceViewPermission === $CartType.ORDERPRICEVIEWPERMISSION &&
                      <p className="total-price"><span>{"¥ " + orderDetail.totalAmount}</span><span>订单合计：</span></p>
                    }
                  </div>
                }
                {
                  (orderDetail.orderPriceViewPermission === $CartType.ORDERPRICEVIEWPERMISSION) && (orderDetail.docStatusCode !== $CartType.AUDITON) ?
                    <List className="my-list margin-top">
                      <Item
                        extra={this.renderLabel(orderDetail)}
                        className="pay-status"
                        onClick={() => this.toRecordDetail()}
                        arrow="horizontal"
                      >
                        付款情况
                      </Item>
                    </List> : null
                }
                {
                  orderDetail.paymentList ? orderDetail.paymentList.length > 0 ? orderDetail.orderPriceViewPermission === $CartType.ORDERPRICEVIEWPERMISSION ?
                    orderDetail.paymentList.map((payment, index) => {
                      return (
                        <List className="my-list">
                          <Item
                            extra={"¥ " + payment.amount}
                            className="pay-status"
                          >
                            {payment.name}
                          </Item>
                        </List>
                      );
                    })
                    : null : null : null
                }
                <List className="my-list margin-top" style={{ marginTop: "10px" }}>
                  <Item arrow="horizontal" onClick={() => this.toGoodList()}>商品清单</Item>
                </List>
                <List className="my-list">
                  <Item extra={orderDetail.deliveryOrderQuantity} arrow="horizontal"
                        onClick={() => this.toDeliveryOrder()}>出库／发货单</Item>
                </List>
                <div className="distribution-info-title margin-top">
                  配送信息
                </div>
                <div className="distribution-info-content">
                  <ul>
                    <li>
                      <span>收货人：</span><span>{orderDetail.addressInfo ? orderDetail.addressInfo.contactPerson : ""}</span>
                    </li>
                    <li>
                      <span>手机号码：</span><span>{orderDetail.addressInfo ? orderDetail.addressInfo.phoneNumber : ""}</span>
                    </li>
                    <li className="address">
                      <span>收货地址：</span><span>{orderDetail.addressInfo ? orderDetail.addressInfo.location : ""}</span>
                    </li>
                    <li><span>配送方式：</span><span>{orderDetail.distributionModeName}</span></li>
                  </ul>
                </div>
                <div className="order-info-title margin-top">
                  订单信息
                </div>
                <div className="order-info-content">
                  <ul>
                    <li><span>下单门店：</span><span>{orderDetail.orderOrgName}</span></li>
                    <li><span>订单号：</span><span>{orderDetail.docNo}</span></li>
                    <li><span>订货方案：</span><span>{orderDetail.orderSchemeName}</span></li>
                    <li>
                      <span>下单时间：</span><span>{DateUtils.toStringFormat(orderDetail.docDate, "yyyy-MM-dd HH:mm:ss")}</span>
                    </li>
                    {
                      orderDetail.memo &&
                      <li><span>备注：</span><span>{orderDetail.memo}</span></li>
                    }
                  </ul>
                </div>
              </div> : null
          }
        </Spin>
        {
          orderDetail && (orderDetail.isShowSendFinancialBtn || orderDetail.isShowCancelBtn || orderDetail.isShowEditBtn || orderDetail.isShowRejectBtn || orderDetail.isShowAuditBtn || orderDetail.isShowDeliveryBtn || orderDetail.isShowCopySalesOrderBtn) &&
          <div className="button-group">
            {
              orderDetail.isShowCancelBtn ? <span onClick={() => this.cancelOrder()}>取消订单</span> : null}
            {
              orderDetail.isShowEditBtn ? <span
                onClick={() => {
                  this.$AppStore.queryShopOverdue(orderDetail.orderOrgId, () => alert(`即将切换至${orderDetail.orderOrgName}门店${orderSchemeName}方案，是否继续？`, "", [
                    { text: "取消", onPress: () => console.log("cancel") },
                    { text: "继续", onPress: () => this.editOrder() },
                  ]));
                }
                }
              >编辑订单</span> : null
            }
            {
              orderDetail.isShowCopySalesOrderBtn &&
              <span
                onClick={() => {
                  this.$AppStore.queryShopOverdue(orderDetail.orderOrgId, () => alert("", "点击去购物车，商品将添加至购物中，部分商品会因已售罄导致添加失败请注意核对", [
                    { text: "关闭", onPress: () => console.log("cancel") },
                    {
                      text: "去购物车",
                      onPress: () => this.copyOrder(orderDetail.orderOrgId, orderDetail.orderSchemeId),
                    },
                  ]));
                }}>复制订单</span>
            }
            {
              orderDetail.isShowRejectBtn ?
                <span
                  onClick={() =>
                    alert(`确认拒绝这笔订单？`, "", [
                      { text: "取消", onPress: () => console.log("cancel") },
                      { text: "继续", onPress: () => this.reject() },
                    ])
                  }
                >
                      审核拒绝
                    </span>
                : null
            }
            {
              orderDetail.isShowSendFinancialBtn ?
                <span onClick={() => {
                  if (scanningLogin) {
                    disabledOprate();
                  } else {
                    if ((orderDetail.isCreateFreightFeeDocument !== $CartType.ISCREATEFREIGHTFEEDOCUMENT) || (orderDetail.freightFeeDocumentPaymentStatusCode === $CartType.PAYEND)) {
                      this.submitFinance(orderDetail.isCreateFreightFeeDocument);
                    } else {
                      this.showPayInfo(orderDetail.orderId, orderDetail.isCreateFreightFeeDocument);
                    }
                  }
                }}>付款</span>
                : null
            }
            {
              orderDetail.isShowAuditBtn ?
                <span className="button-red"
                      onClick={() => this.auditOrder(orderDetail.isEditing)}>审核通过</span> : null
            }
          </div>
        }
        <CustomModal
          header={"运费单"}
          confirm={this.close}
          content={this.renderContent()}
          confirmName={"确定"}
          visible={showFreight}
          close={this.close}
          isCancle={false}
        />
        <CustomModal
          header={"支付信息"}
          confirm={() => this.submitFinance(orderDetail.isCreateFreightFeeDocument)}
          content={this.renderPayContent(isLimitPaymentMode)}
          confirmName={"付款"}
          visible={showPayInfo}
          close={this.closePay}
          isCancle={true}
        />
      </OrderDetailPage>
    );
  }
}

export default OrderDetail;

const OrderDetailPage = styled.div`// styled
  & {
    width: 100%;
    min-height: ${document.documentElement.clientHeight}px;
    box-sizing: border-box;
    overflow-y: auto;
    background-color: #f2f2f2;
    .my-list .am-list-item .am-list-line .am-list-content, .am-list-item .am-list-line .am-list-extra {
       white-space: normal;
    }
    .retail-price, .order-price{
      width: 100%;
      height: auto;
      padding: 20px 16px 0 48px;
      background-color: #fff;
      font-size: 13px;
      line-height: 13px;
      margin-bottom: 11px;
      position: relative;
      .price-header{
        width: 100%;
        .scmIconfont-wrap{
          display: inline-block;
          width: 20px;
          height: 20px;
          text-align: center;
          line-height: 17px;
          border: 1px solid #FF4D4F;
          border-radius: 100%;
          position: absolute;
          top: 16px;
          left: 16px;
          >i{
            color: #FF4D4F;
            font-size: 10px;
          }
        }
        h5{
          width: 100%;
          height: 13px;
          color: #666666;
          margin: 0;
        }
      }
      .price-content{
        padding-bottom: 16px;
        >div{
          color:#333333;
          height: 13px;
          margin-top: 20px;
          >span:first-of-type{
            float: left;
          }
          >span:last-of-type{
            float: right;
          }
        }
      }
    }
    .displayNone{
      display: none;
    }
    .order-price{
      .scmIconfont-wrap{
        border: 1px solid #307DCD;
        >i{
          color:#307DCD;
        }
      }
      .total-price{
        height: 40px;
        line-height: 40px;
        position: relative;
        &:before{
          content: "";
          display: inline-block;
          position: absolute;
          height: 1px;
          background-color: #D8D8D8;
          top: 0;
          right: -16px;
          width: calc(100% + 16px);
        }
        >span{
          float: right!important;
        }
        >span:first-of-type{
          color: #FF4242;
        }
      }
    }
    .my-list2{
    height:40px;
    line-height:40px;
    background:#ffffff;
    padding:0 15px;
    position:relative;
    &:after{
      content: '';
    position: absolute;
    background-color: #ddd;
    display: block;
    z-index: 1;
    top: auto;
    right: auto;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 1PX;
    transform-origin: 50% 50%;
    transform: scaleY(0.5);
    }
      .left{
        float:left;
        font-size:14px;
        color:#333333;
      }
      .right{
        float:right;
        font-size:12px;
        color:#999999;
        span{
          color:#151515;
        }
      }
    }
    .detail-content2-list{
      margin-top: 10px;
      .detail-content2{
        padding-left: 16px;
        p{
          margin:0;
          font-size: 12px;
          color: #333333;
          line-height:24px;
          span{
            float: right;
            color: #333333;
          }
        }
      }
    }
    .total-price{
      .am-list-line{
        display: block;
        text-align: right;
        height: 40px;
        .am-list-extra{
          display: inline-block;
          height: 40px;
          line-height: 40px;
          color: #FF4242;
          padding: 0;
        }
        .am-list-content{
          display: inline-block;
          color: #333333;
          height: 40px;
          padding: 0;
          line-height: 40px;
          font-size: 12px!important;
        }
      }
    }
    .margin-top{
      margin-top: 10px;
    }
    .distribution-info-title,.order-info-title{
      width: 100%;
      height: 40px;
      line-height: 40px;
      background-color: #fff;
      padding-left: 15px;
      color: #333;
      margin-bottom: 1px;
    }
    .distribution-info-content,.order-info-content{
      width: 100%;
      height: auto;
      background-color: #fff;
      padding: 6px 15px;
      >ul{
        margin: 0;
        padding: 0;
        width: 100%;
        list-style: none;
        li{
          width: 100%;
          position: relative;
          min-height: 24px;
          line-height: 24px;
          font-size: 12px;
          color:rgba(117,117,117,1);
          padding-left: 77px;
          span:first-of-type{
            display: inline-block;
            width: 77px;
            position: absolute;
            top: 0;
            left: 0;
          }
        }
      }
    }
    .detail-content{
      > p{
        margin:0;
        font-size: 12px;
        color: #757575;
        line-height:24px;
      }
    }
    .am-list-body::before{
      height:0;
    }
    .am-list-item{
      min-height:40px;
    }
    .h50 .am-list-item{
      min-height:50px;
    }
    .am-list-item .am-list-line .am-list-content{
      font-size:14px;
      color:#333333;
    }
    .am-list-item .am-list-line .am-list-extra{
      flex-basis:50%;
      font-size:12px;
    }
    .red{
      color:#FF3030;
    }
    .gray .am-list-line .am-list-content{
      color:#999;
      font-size:12px;
    }
    .status .am-list-line .am-list-extra{
      color:#FF3030;
    }
    .price .am-list-line .am-list-extra{
      color: #757575;
    }
    .button.am-list-item .am-list-line .am-list-extra{
      color:#FF3030;
      border:1px solid #FF3030;
      flex-basis:72px;
      padding:5px 11px;
      border-radius:3px;
    }
    .button.am-list-item .am-list-content{
      font-size:12px;
      color:#757575;
    }
    .button-group{
      background:#ffffff;
      padding:15px 0 15px 0;
      text-align:right;
      position: fixed;
      bottom:0;
      left:0;
      width:100%;
      z-index: 99;
      span{
        border:1px solid #307DCD;
        border-radius:3px;
        padding:6px 11px;
        color:#307DCD;
        font-size:12px;
        margin-right:16px;
        &.button-red{
          color:#FF3030;
          border:1px solid #FF3030;
        }
      }
    }
    .seeFreight {
      font-size:13px;
      font-family:SourceHanSansCN-Normal,SourceHanSansCN;
      font-weight:400;
      color:rgba(48,125,205,1);
    }
  }
`;

const FreightContent = styled.div`// styled
  & {
    p {
      margin-bottom: 27px;
      line-height: 20px;
      > span:first-child {
        display: inline-block;
        width: 95px;
        margin-right: 24px;
        font-size: 14px;
        font-family: SourceHanSansCN-Regular, SourceHanSansCN;
        font-weight: 400;
        color: rgba(51, 51, 51, 1);
        vertical-align: top;
        .scmIconfont {
          margin-right: 8px;
        }
        .scm-bianma {
          color: #9254DE;
        }
        .scm-mendian {
          color: #307DCD;
        }
        .scm-kuaidi {
          color: #60B547;
        }
        .scm-dingdanjilu {
          color: #FF8627;
        }
      }
      .value {
        font-size: 13px;
        font-family: SourceHanSansCN-Regular, SourceHanSansCN;
        font-weight: 400;
        color: rgba(102, 102, 102, 1);
        margin-bottom: 16px;
      }
      .value:last-child {
        margin-bottom: 0;
      }
      .name {
        display: inline-block;
        width: calc(100% - 95px - 24px);
        text-overflow: ellipsis;
        overflow: hidden;
        white-space: nowrap;
      }
    }
    p:last-child {
      margin-bottom: 0;
    }
  }
`;

const PayContent = styled.div`// styled
  & {
    width: 100%;
    height: auto;
    .scm-dingdanjilu {
      color: #FF8627;
    }
    .scm-kuaidi {
      color: #60B547;
    }
    .scmIconfont {
      margin-right: 8px;
    }
    .title {
      font-size: 14px;
      font-family: SourceHanSansCN-Regular, SourceHanSansCN;
      font-weight: 400;
      color: rgba(51, 51, 51, 1);
      margin-right: 24px;
    }
    .toPay {
      font-size: 13px;
      font-family: SourceHanSansCN-Regular, SourceHanSansCN;
      font-weight: 400;
      color: #999999;
    }
    .amount {
      color: #333333;
    }
    .am-list-item .am-list-thumb:first-child {
      margin-right: 8px;
    }
    .am-list-item {
      padding-left: 0;
    }
    > div {
      > p:first-child {
        margin-bottom: 15px;
      }
      > p:last-child {
        margin-bottom: 15px;
      }
    }
    .total {
      border-top: 1px solid #EFEFEF;
      padding-top: 20px;
    }
    .sum {
      font-size: 14px;
      font-family: SourceHanSansCN-Regular, SourceHanSansCN;
      font-weight: 400;
      color: rgba(51, 51, 51, 1);
      margin-right: 8px;
    }
    .info {
      font-size: 12px;
      font-family: SourceHanSansCN-Regular, SourceHanSansCN;
      font-weight: 400;
      color: rgba(153, 153, 153, 1);
    }
    .Symbol {
      font-size: 12px;
      font-family: SourceHanSansCN-Regular, SourceHanSansCN;
      font-weight: 400;
      color: #FF3030;
    }
    .red {
      font-size: 16px;
      margin-right: 8px;
    }
    .black {
      color: #333333;
      font-size: 12px;
    }
    .title-notice {
      font-size: 16px;
      color: red;
      font-weight: bold;
    }
    .am-checkbox.am-checkbox-disabled {
      &.am-checkbox-checked {
        .am-checkbox-inner {
          border-color: #108ee9;
          background: #108ee9;

          &:after{
            border-color: #fff;
          }
        }
      }
    }
  }
`;
