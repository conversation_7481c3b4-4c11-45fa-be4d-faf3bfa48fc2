import { autowired } from "@classes/ioc/ioc";
import { Spin } from "antd";
import { Modal, Toast } from "antd-mobile";
import { observer } from "mobx-react";
import * as React from "react";
import { withRouter } from "react-router";
import styled from "styled-components";
import { $ActiveType } from "../../classes/const/$active-type";
import { $OrderType } from "../../classes/const/$order-type";
import { Footer } from "../../components/footer/footer";
import { NoGoods } from "../../components/no-goods/no-goods";
import { SITE_PATH } from "../app";
import { $CartMv } from "../shop-cart/cart-mv";
import { $OrderMv } from "./order-mv";
import { SingleOrderInfo } from "../../components/single-order-info/single-order-info";
import { ScrollAbilityWrapComponent } from "../../components/scroll-ability/wrap";
import { LoadingTip } from "../../components/loading-marked-words";
import { gaEvent } from "../../classes/website-statistics/website-statistics";
import { $AppStore, AppStoreKey } from "../../classes/stores/app-store-mv";
import { map } from "lodash";

const alert = Modal.alert;

declare let window: any;
declare let $: any;

@withRouter
@observer
class OrderListWrap extends React.Component<any, any> {
  @autowired($AppStore)
  public $AppStore: $AppStore;
  @autowired($CartMv)
  public $CartMv: $CartMv;

  @autowired($OrderMv)
  public $myMv: $OrderMv;

  constructor(props) {
    super(props);
    this.state = ({
    });
  }
  // 离开记录滚动高度
  public saveData = () => {
    this.$myMv.scrollHeight = $(".scroll-ability-wrap").scrollTop();
    this.$AppStore.savePageMv(AppStoreKey.SINGLESHOPORDERLISTSTORE, this.$myMv);
  }
  public componentWillUnmount(): void {
    this.saveData();
  }
  public componentDidMount() {
    document.title = "订单列表";
    gaEvent("订单列表");
    setTimeout(() => {
      const oldData = this.$AppStore.getPageMv(AppStoreKey.SINGLESHOPORDERLISTSTORE)
      if (oldData) {
        this.$myMv.queryOldData(JSON.parse(oldData));
        this.$AppStore.clearPageMv(AppStoreKey.SINGLESHOPORDERLISTSTORE);
        $(".scroll-ability-wrap").scrollTop(this.$myMv.scrollHeight);
      } else {
        this.$myMv.clearMVData();
        this.initPage();
      }
    }, 50);
  }
  public initPage = () => {
    const { state } = this.props.location;
    this.$myMv.salesOrderStatus({isContainsN: false}).then(() => {
      if (state && state.status) {
        this.searchOrder(state.status);
        this.$CartMv.setIsAgency();
      } else {
        this.searchOrder("ALL");
        this.$CartMv.fetchShopcartproductnum();
      }
    });
  }

  public componentWillReceiveProps(nextProps: Readonly<any>, nextContext: any): void {
    const { isScrollEnd } = nextProps;
    console.log(isScrollEnd);
    if (isScrollEnd && isScrollEnd !== this.props.isScrollEnd) {
      this.loadData();
    }
  }

  public loadData = () => {
    const { key, finished } = this.$myMv;
    if (finished) {
      return;
    }
    this.searchOrder(key);
  }

  public searchOrder(val) {
    console.log("", val);
    const { pageIndex, pageSize, salesOrderStatusList } = this.$myMv;
    if ((val === $OrderType.AUDIT || val === $OrderType.DELIVER || val === $OrderType.DONE || val === $OrderType.PAYMENT || val === $OrderType.ALL || val === $OrderType.WAITDELIVER) && pageIndex === 0) {
      this.$myMv.orderListInfo = [];
    }
    const params = { pageSize, pageIndex, type: val, orderPartyRange: "SINGLE", statusList: map(this.$myMv.salesOrderStatusList.filter((party) => party.checked), "value") };
    this.$myMv.isLoad = true;
    this.$myMv.key = val;
    this.$myMv.showSpin();
    this.$myMv.getList(params).then((res) => {
      this.$myMv.hideSpin();
      const { itemCount, isBySkuType, salesOrderList } = res;
      this.$myMv.changeIsBySkuType(isBySkuType);
      const { loadingEnd } = this.props;
      if (salesOrderList) {
        this.$myMv.orderListInfo = pageIndex ? this.$myMv.orderListInfo.concat(salesOrderList) : salesOrderList;
        this.$myMv.changePage();
        this.$myMv.finished = this.$myMv.orderListInfo.length >= itemCount;
        this.$myMv.isLoad = false;
        loadingEnd && loadingEnd();
      } else {
        this.$myMv.finished = true;
        this.$myMv.isLoad = false;
        loadingEnd && loadingEnd();
      }
    }).catch((err) => {
      this.$myMv.hideSpin();
      Toast.fail(err.response.body.message);

    });
  }
  public pageWindowSkip = (url) => {
    this.saveData();
    setTimeout(() => {
      window.location.href = url;
    }, 50);
  }
  public orderButton = (btnType, orderId, orderOrgId, orderSchemeId, payRange) => {
    switch (btnType) {
      case "cancel":
        this.$myMv.cancleOrder(orderId).then((data) => {
          if (data.result) {
            Toast.info("订单已取消", 3);
            setTimeout(() => {
              window.location.reload();
            }, 2500);
            /*const { key } = this.$myMv;
            this.searchOrder(key);*/
          }
        });
        break;
      case "reject":
        this.$myMv.cancleOrder(orderId).then((data) => {
          if (data.result) {
            window.location.reload();
            /*const { key } = this.$myMv;
            this.searchOrder(key);*/
          }
        });
        break;
      case "edit":
        this.$myMv.editOrder(orderId).then((data) => {
          if (data.result) {
            // this.$myMv.setEditOrderId(orderId);
            sessionStorage.setItem("editOrderId", orderId);
            sessionStorage.setItem("isSetShopCartData", "true");
            this.$AppStore.clearPageMv(AppStoreKey.SHOPCART);
            this.props.history.push({
              pathname: `/${SITE_PATH}/shop/cart`, state: {
                orderId,
                backSource: "single",
                shopCartInfoFromOrder: data.shopCartInfo,
              },
            });
          }
        });
        break;
      case "auditPass":
        this.$myMv.auditOrder(orderId).then((data) => {
          if (data.result) {
            Toast.info("审核通过，请等待客服人员审核", 3);
            setTimeout(() => {
              window.location.reload();
            }, 2500);
          }
        });
        break;
      case "submitFinance":
      case "nowPay":
      case "goOnPay":
        this.$AppStore.clearPageMv(AppStoreKey.SINGLEPAYMENTSTORAGE);
        this.pageWindowSkip(`/${SITE_PATH}/submit/order-payment/34657/${orderId}/notPay?payRange=${payRange}`);
        break;
      case "deliveryOrder":
        this.pageWindowSkip(`/${SITE_PATH}/delivery-order/${orderId}`);
        break;
      case "capyOrder":
        sessionStorage.setItem("editOrderId", null);
        this.$myMv.copyOrder({ slaesOrderId: orderId }).then((res) => {
          console.log(res);
          const { errorMessage, isSkip, shopCartInfo } = res;
          if (errorMessage) {
            Toast.info(errorMessage);
          }
          if (isSkip) {
            const saveParams = { orderPartyId: orderOrgId, orderSchemeId };
            this.$myMv.saveShopAndScheme(saveParams).then((data) => {
              const message = data.errorMessage;
              if (message) {
                Toast.info(message);
              } else {
                sessionStorage.setItem("isSetShopCartData", "true");
                this.$AppStore.clearPageMv(AppStoreKey.SHOPCART);
                this.$CartMv.openDiscount();
                this.props.history.push({
                  pathname: `/${SITE_PATH}/shop/cart`,
                  state: {
                    shopCartInfoFromOrder: shopCartInfo,
                  },
                });
              }
            });
          }
        })
        break;
      default:
        break;
    }
  }

  public goToOrderDetail = (orderId, orderSchemeName, paymentModeListLength) => {
    this.props.history.push({
      pathname: `/${SITE_PATH}/shop/order-detail/${orderId}/${orderSchemeName}`,
      state: {
        backSource: "single",
        paymentModeListLength,
      },
    });
  }

  public changeOrderStatus = (value) => {
    document.getElementsByClassName("orderPage")[0].style.position = "fixed";
    setTimeout(() => {
      document.getElementsByClassName("orderPage")[0].style.position = "relative";

    }, 10)
    this.$myMv.pageIndex = 0;
    this.searchOrder(value);
  }

  public render() {
    const { isBySkuType, orderStatus, orderListInfo, isSpin, isLoad, key, finished } = this.$myMv;
    const { isAgency } = this.$CartMv;
    return (
      <OrderPage className="orderPage">
        <SearchBar>
          {
            orderStatus.map((status, index) => {
              return (
                <div
                  key={index}
                  onClick={() => {
                    this.changeOrderStatus(status.value);
                  }}
                  className={status.value === key ? "active" : null}
                >
                  {status.name}
                </div>
              );
            })
          }
        </SearchBar>
        <Spin spinning={isSpin}>
          <OrderLists
            // style={{ marginBottom: orderListInfo ? orderListInfo.length > 2 ? 50 : 0 : 0 }}
          >
            {
              orderListInfo ? orderListInfo.length > 0 ?
                orderListInfo.map((order, index) => {
                  return <div key={index}>
                    <SingleOrderInfo
                      order={order}
                      goToOrderDetail={this.goToOrderDetail}
                      // retailPriceViewPermission={retailPriceViewPermission}
                      // orderPriceViewPermission={orderPriceViewPermission}
                      // freightViewPermission={freightViewPermission}
                      isBySkuType={isBySkuType}
                      orderButton={this.orderButton}
                      componentAfreshDidMount={() => {
                        this.componentDidMount()
                      }}
                      showCheckboxItem={false}
                      chooseOne={null}
                    />
                    <MarginBottom/>
                  </div>;
                })
                : <NoGoods title="暂无订单" height={document.documentElement.clientHeight - 100}/> :
                <NoGoods title="暂无订单" height={document.documentElement.clientHeight - 100}/>
            }
            {
              orderListInfo && orderListInfo.length > 0 ?
                <LoadingTip
                  isFinished={finished}
                  isLoad={isLoad}
                /> : null
            }
          </OrderLists>
        </Spin>
        <Footer
          isAgency={isAgency}
          activeKey={$ActiveType.ORDER_KEY}
          count={this.$CartMv.totalCount}
          leaveCurrentPage={this.saveData}
        />
      </OrderPage>
    );
  }
}

const OrderList = ScrollAbilityWrapComponent(OrderListWrap);
export default OrderList;

const OrderPage = styled.div`// styled
  & {
    width: 100%;
    min-height: calc(${document.documentElement.clientHeight}px);
    height: auto;
    background-color: #f5f5f5;
    //overflow-y: auto;
    color: #2a2a2a;
    margin-bottom: 50px;

    > .orderPage span {
      text-align: center;
    }
  }
`;

const SearchBar = styled.div`// styled
  & {
    width: 100%;
    height: 40px;
    line-height: 20px;
    background-color: #fff;
    border-bottom: 1px solid #d8d8d8;
    display: flex;
    flex-direction: row;
    text-align: center;
    position: fixed;
    color: #8a8a8a;
    z-index: 99;
    padding: 10px 15px;
    box-sizing: border-box;
  }

  > div {
    height: 30px;
    flex: 1;
    font-size: 14px;
    color: #666;
  }

  > .active {
    color: #307DCD;
    font-size: 14px;
    border-bottom: 2px solid #307DCD;
  }
`;

const OrderLists = styled.div`// styled
  & {
    width: 100%;
    /*height: 600px;*/
    padding-top: 50px;
    //overflow-y: auto;
  }
`;

const MarginBottom = styled.div`// styled
  & {
    width: 100%;
    height: 10px;
    background: #f5f5f5;
  }
`;
