import { action, observable } from "mobx";
import { autowired, bean } from "@classes/ioc/ioc";
import { $OrderService } from "../../classes/service/$order-service";
import { $LogisticsTracking } from "../../classes/entity/$logistics-tracking";

@bean($LogisticsTrackingMv)
export class $LogisticsTrackingMv {

  @autowired($OrderService)
  public $orderService: $OrderService;

  @observable public logisticsTracking: $LogisticsTracking[] = [];

  @observable public isSpin: boolean = false;

  @action
  public showSpin() {
    this.isSpin = true;
  }

  @action
  public hideSpin() {
    this.isSpin = false;
  }

  @action
  public fetchLogisticsTracking(params) {
    this.showSpin();
    this.$orderService.queryLogisticsTracking(params).then((data) => {
      const { detail } = data;
      this.logisticsTracking = detail.map((logistics) => new $LogisticsTracking(logistics));
    }).then(this.hideSpin());
  }

}
