import React from "react";
import { autowired } from "@classes/ioc/ioc";
import styled from "styled-components";
import { observer } from "mobx-react";
import { Spin } from "antd";
import { $MessageCompanyDetailMv } from "./message-company-detail-mv";
import { withRouter } from "react-router";

@withRouter
@observer
class MessageCompanyDetail extends React.Component<any, any> {
  @autowired($MessageCompanyDetailMv)
  public $messageCompanyDetailMv: $MessageCompanyDetailMv;

  public componentDidMount() {
    document.title = "公司公告详情";
    const { id } = this.props.match.params;
    this.$messageCompanyDetailMv.fetchMessageCompanyDetail({ id });
  }

  public render() {
    const { messageCompanyDetail, isSpin } = this.$messageCompanyDetailMv;
    return (
      <MessageCompanyDetailPage>
        <Spin spinning={isSpin}>
          <DetailHeader>
            <p>{messageCompanyDetail.title}</p>
            <p>{messageCompanyDetail.annoucementTime}</p>
          </DetailHeader>
          <DetailContent>
            <ContentHeader>
              购物流程
            </ContentHeader>
            <ContentDetails>
              <ContentDetail dangerouslySetInnerHTML={{ __html: messageCompanyDetail.content }}/>
            </ContentDetails>
          </DetailContent>
        </Spin>
      </MessageCompanyDetailPage>
    );
  }

}

export default MessageCompanyDetail;

const MessageCompanyDetailPage = styled.div`// styled
  & {
    width: 100%;
    height: calc(${document.documentElement.clientHeight}px);
    background: #ECF6FF;
    padding: 10px 15px;
  }
`;

const DetailContent = styled.div`// styled
  & {
    width: 100%;
    height: calc(${document.documentElement.clientHeight}px - 94px);
    margin: 10px auto;
    background: #fff;
    padding: 10px 15px;
    border-radius: 5px;
  }
`;

const ContentHeader = styled.div`// styled
  & {
    width: 100%;
    height: 45px;
    line-height: 45px;
    border-bottom: 1px solid #D8D8D8;
    text-align: center;
    font-size: 14px;
    color: #333333;
  }
`;

const ContentDetails = styled.div`// styled
  & {
    width: 100%;
    height: calc(${document.documentElement.clientHeight}px - 94px - 75px);
  }
`;

const ContentDetail = styled.div`// styled
  & {
    width: 100%;
    height: calc(${document.documentElement.clientHeight}px - 94px - 75px);
    margin: 10px auto;
    overflow-y: auto;

    .video-wrap {
      width: 100%;

      > video {
        width: 100%;
      }
    }

    .image-wrap{
      width: 100%;
      
      > img {
        width: 100%;
        height: auto;
      }
    }
  }
`;

const DetailHeader = styled.div`// styled
  & {
    width: 100%;
    height: 64px;
    border-radius: 5px;
    background: #fff;
    text-align: center;
    padding-top: 7px;
    > p {
      margin-bottom: 7px;
    }
    > p:nth-of-type(1) {
      font-size: 16px;
      color: #307DCD;
    }
    > p:nth-of-type(2) {
      font-size: 12px;
      color: #999999;
    }
  }
`;
