import { autowired, bean } from "@classes/ioc/ioc";
import { action, observable } from "mobx";
import { $MyInfoService } from "../../classes/service/$my-info-service";
import { $MessageCompanyDetail } from "../../classes/entity/$message-company-detail";

@bean($MessageCompanyDetailMv)
export class $MessageCompanyDetailMv {

  @autowired($MyInfoService)
  public $myInfoService: $MyInfoService;

  @observable public isSpin: boolean = false;

  @observable public messageCompanyDetail: $MessageCompanyDetail = new $MessageCompanyDetail({});

  @action
  public showSpin() {
    this.isSpin = true;
  }

  @action
  public hideSpin() {
    this.isSpin = false;
  }

  @action
  public fetchMessageCompanyDetail(params) {
    this.showSpin();
    this.$myInfoService.queryAnnouncementDetail(params).then((data) => {
      this.messageCompanyDetail = new $MessageCompanyDetail(data);
    }).then(this.hideSpin());
  }
}
