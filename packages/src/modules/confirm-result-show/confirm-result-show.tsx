import * as React from "react";
import styled from "styled-components";
import { observer } from "mobx-react";
import { withRouter } from "react-router";
import { SITE_PATH } from "../app";
import { $AppStore, AppStoreKey } from "../../classes/stores/app-store-mv";
import { autowired } from "@classes/ioc/ioc";

declare let window: any;

@withRouter
@observer
export default class ConfirmResultShow extends React.Component<any, any> {
  @autowired($AppStore)
  public $AppStore: $AppStore;
  constructor(props) {
    super(props);
    this.state = {
      comeBackList: {
        buttonStyle: "blue",
        cb: this.comeBackList,
        name: "返回列表",
      },
      result: "",
      fromWhere: "",
      orderPartyName: "",
      orgId: "",
      resultInfo: {
        code: "", // "success", "failure", "waiting"
        introduce: null,
        imgUrl: "",
        operationButtonList: [],
      },
    };
  }

  public componentDidMount(): void {
    document.title = "确认结果展示";
    const { result, fromWhere } = this.props.match.params;
    const { state } = this.props.location;
    if (state && state.orderPartyName && state.orgId) {
      this.setState({
        orderPartyName: state.orderPartyName,
        orgId: state.orgId,
      });
    }
    this.setState({ result, fromWhere }, () => {
      this.showDifferentResoult();
    });
  }
  public comeBackList = () => {
    this.$AppStore.clearPageMv(AppStoreKey.RETURNEDSALES);
    this.props.history.push({
      pathname: `/${SITE_PATH}/returned-sales`,
      state: {
        orderPartyName: this.state.orderPartyName,
        orgId: this.state.orgId,
      },
    });
  }

  public showDifferentResoult = () => {
    const { result, fromWhere } = this.state;
    switch (result) {
      case "success":
        if (fromWhere === "fill-in-logistics") {
          this.setState({
            resultInfo: {
              code: result,
              imgUrl: "https://order.fwh1988.cn:14501/static-img/scm/img_successful.png",
              introduce: `<div>
                          <span>提交成功</span>
                         </div>`,
              operationButtonList: [
                this.state.comeBackList,
              ],
            },
          });
        } else if (fromWhere === "initiating-returns") {
          this.setState({
            resultInfo: {
              code: result,
              imgUrl: "https://order.fwh1988.cn:14501/static-img/scm/img_successful.png",
              introduce: `<div>
                          <span>提交成功</span>
                          <span>请等待客服审核结果</span>
                         </div>`,
              operationButtonList: [
                this.state.comeBackList,
              ],
            },
          });
        }
        break;
      default:
        break;
    }
  }

  public render() {
    const { code, imgUrl, introduce, operationButtonList } = this.state.resultInfo;
    return (
      <PaymentResultShowWrap>
        <div className="result-icon">
          <img src={imgUrl} alt=""/>
        </div>
        <div className="result-detail" dangerouslySetInnerHTML={{ __html: introduce }}/>
        <div className="result-operation">
          {
            operationButtonList.length > 0 && operationButtonList.map((item) => {
              console.log("burto", item);
              const { cb, name, buttonStyle } = item;
              return <div
                onClick={cb}
                className={`operate-button ${buttonStyle}`}
                key={name}
              >
                {name}
              </div>;
            })
          }
        </div>
      </PaymentResultShowWrap>
    );
  }
}

const PaymentResultShowWrap = styled.div`// styled
  & {
    position: relative;
    padding-top: 56px;
    .result-icon, .result-detail, .result-operation {
    }
    .result-icon {
      width: 96px;
      height: 73px;
      margin: 0 auto;
    }
    .result-detail {
      min-height: 67px;
      > div {
        text-align: center;
        margin-top: 11px;
        span:first-of-type {
          color: #2F2F2F;
          font-size: 14px;
          display: block;
        }
        span:nth-of-type(2) {
          color: #595959;
          font-size: 12px;
          margin-top: 6px;
          display: block;
        }
      }
    }
    .result-operation {
      text-align: center;
      .operate-button {
        width: 180px;
        height: 42px;
        line-height: 40px;
        text-align: center;
        margin: 0 auto 12px;
        font-size: 14px;
        border-radius: 3px;
      }
      .blue {
        background-color: #307DCD;
        color: #ffffff;
      }
      .white {
        background-color: #ffffff;
        color: #595959;
        border: 1px solid #D9D9D9;
      }
    }
  }
`;
