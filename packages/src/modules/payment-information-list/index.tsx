import { autowired } from "@classes/ioc/ioc";
import { Spin } from "antd";
import { Modal } from "antd-mobile";
import { observer } from "mobx-react";
import React from "react";
import { withRouter } from "react-router";
import styled from "styled-components";
import { PaymentInformationListMv } from "./payment-information-list-mv";
import { $InterfaceErrorCode } from "../../classes/const/$interface-error-code";
import { SinglePaymentRecordInfo } from "../../components/single-payment-record-info";
import NoAuthority from "../no-authority/no-authority";
import { gaEvent } from "../../classes/website-statistics/website-statistics";
import { getUrlParam } from "@classes/utils/UrlUtils";

const alert = Modal.alert;

@withRouter
@observer
class PaymentInformationList extends React.Component<any, any> {
  @autowired(PaymentInformationListMv)
  public $mv: PaymentInformationListMv;

  constructor(props) {
    super(props);
    this.state = {
      isGetPermissions: false,
      isHavePermissions: false,
      isLoading: false,
      orderId: "",
    };
  }

  public componentWillMount(): void {
    console.log(this.props);
    this.loadData();
  }

  public componentDidMount() {
    document.title = "付款情况";
    gaEvent("付款情况");
    // this.loadData();
  }

  public loadData = () => {
    const { orderId } = this.props.match.params || { orderId: "" };
    this.setState({ isLoading: true, orderId });
    const params = {
      orderId,
      orderType: getUrlParam("orderType"),
    };
    this.$mv.queryPaymentInfoList(params).then((errorCode) => {
      console.log(errorCode);
      this.setState({
        isGetPermissions: true,
        isHavePermissions: errorCode !== $InterfaceErrorCode.NO_PAGE_VIEW_PERMISSIONS,
        isLoading: false,
      });
    });
  }

  public render() {
    const { isLoading, isHavePermissions, isGetPermissions } = this.state;
    const { priceAccountList, paymentInfoList } = this.$mv;
    return (
      <Wrapper className="wrapper">
        <Spin spinning={isLoading} className="spining">
          {
            isGetPermissions ? isHavePermissions ?
              <div>
                <Header>
                  {
                    priceAccountList.map((item, index) => {
                      return (
                        <div key={index}>
                          <p>{item.amount}</p>
                          <p>{item.name}</p>
                        </div>
                      );
                    })
                  }
                </Header>
                <Content>
                  {
                    paymentInfoList && paymentInfoList.length > 0 && paymentInfoList.map((item, index) => {
                      return (
                        <SinglePaymentRecordInfo
                          key={index}
                          record={item}
                        />
                      );
                    })
                  }
                </Content>
              </div>
              : <NoAuthority/> : <div/>
          }
        </Spin>
      </Wrapper>
    );
  }
}

export default PaymentInformationList;

const Wrapper = styled.div`// styled
  & {
    width: 100%;
    height: 100%;
    background-color: #fff;
    .ant-spin-nested-loading {
      width: 100%;
      height: 100%;
    }
  }
`;
const Content = styled.div`// styled
  & {
    width: 100%;
    height: 100%;
    background-color: #fff;
    padding: 0 16px 16px;
  }
`;

const Header = styled.div`// styled
  & {
    padding: 18px 0px;
    display: flex;
    background: #ECF6FF;
    margin-bottom: 10px;
    div {
      width: 100%;
      height: 100%;
      text-align: center;
      border-right: 1px solid #CDDAE6;
      p {
        margin: 0;
        font-size: 12px;
        line-height: 12px;
        color: #333;
      }
      p:nth-child(1) {
        line-height: 17px;
        margin-bottom: 7px;
        font-size: 13px;
        color: #FF3030;
      }
    }
    > div:last-child {
      border: 0;
    }
  }
`;
