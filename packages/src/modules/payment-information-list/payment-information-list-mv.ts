import { autowired, bean } from "@classes/ioc/ioc";
import { action, computed, extendObservable, observable } from "mobx";
import { $ComponentService } from "../../classes/service/$component-service";
import { $InterfaceErrorCode } from "../../classes/const/$interface-error-code"
import { findIndex } from "lodash";

@bean(PaymentInformationListMv)
export class PaymentInformationListMv {
  @autowired($ComponentService)
  public $componentService: $ComponentService;

  @observable public paymentInfoList = [];
  // @observable public payableAmount = "";
  // @observable public auditedAmount = "";
  // @observable public unAuditedAmount = "";
  // @observable public unPayableAmount = "";
  @observable public priceAccountList = [];

  @action
  public queryPaymentInfoList(params) {
    return new Promise((resolve) => {
      this.$componentService.queryPaymentInfoList(params).then((data) => {
        console.log(data);
        if (data.errorCode !== $InterfaceErrorCode.NO_PAGE_VIEW_PERMISSIONS) {
          const { payableAmount, auditedAmount, unAuditedAmount, unPayableAmount, orderPaymentList } = data;
          // this.payableAmount = payableAmount;
          // this.auditedAmount = auditedAmount;
          // this.unAuditedAmount = unAuditedAmount;
          // this.unPayableAmount = unPayableAmount;
          this.paymentInfoList = orderPaymentList;
          this.priceAccountList = [
            {
              amount: payableAmount !== null ? `¥ ${Number(payableAmount).toFixed(2)}` : null,
              name: "应付金额",
            },
            {
              amount: auditedAmount !== null ? `¥ ${Number(auditedAmount).toFixed(2)}` : null,
              name: "已确认金额",
            },
            {
              amount: unAuditedAmount !== null ? `¥ ${Number(unAuditedAmount).toFixed(2)}` : null,
              name: "待确认金额",
            },
            {
              amount: unPayableAmount !== null ? `¥ ${Number(unPayableAmount).toFixed(2)}` : null,
              name: "未支付金额",
            },
          ];
        }
        resolve(data.errorCode);
      });
    });
  }
}
