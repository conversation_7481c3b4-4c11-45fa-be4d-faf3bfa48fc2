import { autowired, bean } from "@classes/ioc/ioc";
import { action, observable } from "mobx";
import { $ComponentService } from "../../classes/service/$component-service";

@bean($LogisticsCompanyListMv)
export class $LogisticsCompanyListMv {

  @autowired($ComponentService)
  public $ComponentService: $ComponentService;

  @observable public logisticsCompanyList: any[] = [];
  @action
  public setLogisticsCompanyList(list) {
    this.logisticsCompanyList = list;
  }
  @action
  public fetchLogisticsCompanyList(params) {
    return this.$ComponentService.fetchLogisticsCompanyList(params);
  }
}
