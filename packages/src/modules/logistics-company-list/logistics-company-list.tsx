import { autowired } from "@classes/ioc/ioc";
import { Spin } from "antd";
import { Checkbox, List, Toast } from "antd-mobile";
import { observer } from "mobx-react";
import React from "react";
import styled from "styled-components";
import { $LogisticsCompanyListMv } from "./logistics-company-list-mv";
import { SITE_PATH } from "../app";
import { NoGoods } from "../../components/no-goods/no-goods";
import { withRouter } from "react-router";
import { ScrollAbilityWrapComponent } from "../../components/scroll-ability/wrap";
import { LoadingTip } from "../../components/loading-marked-words";
const CheckboxItem = Checkbox.CheckboxItem;
declare let window: any;
const Item = List.Item;

@withRouter
@observer
class LogisticsCompanyListWrap extends React.Component<any, any> {

  @autowired($LogisticsCompanyListMv)
  public $myMv: $LogisticsCompanyListMv;

  constructor(props) {
    super(props);
    this.state = {
      pageIndex: 0,
      pageSize: 20,
      isFinished: false,
      isSpin: false,
      selectOid: "",
    };
  }

  public componentDidMount() {
    document.title = "物流公司列表";
    const orderId = this.props.match.params.orderId;
    this.setState({ orderId }, () => {
      this.fetchLogisticsCompanyList();
    });
  }
  public componentWillReceiveProps(nextProps: Readonly<any>, nextContext: any): void {
    const { isScrollEnd } = nextProps;
    console.log(isScrollEnd);
    if (isScrollEnd && isScrollEnd !== this.props.isScrollEnd) {
      this.loadData();
    }
  }
  public fetchLogisticsCompanyList = () => {
    const { pageIndex, pageSize } = this.state;
    const params = { pageIndex, pageSize };
    this.setState({ isSpin: true })
    this.$myMv.fetchLogisticsCompanyList(params).then((res) => {
      const { itemCount, list } = res;
      const newLogisticsCompanyList = pageIndex ? this.$myMv.logisticsCompanyList.concat(list) : list;
      this.$myMv.setLogisticsCompanyList(newLogisticsCompanyList);
      this.setState({
        isFinished: newLogisticsCompanyList.length >= itemCount,
        isSpin: false,
        pageIndex: pageIndex + 1,
      });
      const { loadingEnd } = this.props;
      loadingEnd && loadingEnd(newLogisticsCompanyList.length >= itemCount);
    });
  }

  public loadData = () => {
    const { isFinished } = this.state;
    if (isFinished) {
      return;
    }
    this.fetchLogisticsCompanyList();
  }
  public selectedTarge = (oid) => {
    console.log("selectedTarge", oid);
    this.setState({ selectOid: oid});
  }
  public goToFillInLogistics = (oid) => {
    const { logisticsCompanyList } = this.$myMv;
    const { selectOid, orderId } = this.state;
    console.log("selectedTarge", oid);
    const selectCompanyTarget = logisticsCompanyList.find((item) => item.oid === selectOid);
    const { state } = this.props.location;
    if (selectOid && selectCompanyTarget) {
      this.props.history.push({
        pathname: `/${SITE_PATH}/fill-in-logistics/${orderId}`,
        state: {
          selectCompanyTarget,
          orgId: state && state.orgId,
          orderPartyName: state && state.orderPartyName,
        },
      });
    }
  }
  public render() {
    const { logisticsCompanyList } = this.$myMv;
    const { isSpin, isFinished, selectOid } = this.state;
    return (
      <PageWrap>
        <Spin spinning={isSpin}>
          {
            logisticsCompanyList && logisticsCompanyList.length > 0 && logisticsCompanyList.map((item) => {
              const { oid, name } = item;
              return(
                <CheckboxItem
                  checked={oid === selectOid}
                  className="scm-checkbox-item"
                  disabled={false}
                  onChange={() => this.selectedTarge(oid)}
                  key={oid}
                >
                  {name}
                </CheckboxItem>
              );
            })
          }
          {
            logisticsCompanyList && logisticsCompanyList.length === 0 &&
              <NoGoods
                title="暂无任何物流公司"
                height={document.documentElement.clientHeight - 66}
              />
          }
          {
            logisticsCompanyList && logisticsCompanyList.length > 0 &&
              <LoadingTip
                isFinished={isFinished}
                isLoad={isSpin}
              />
          }
        </Spin>
        <div className="footer">
          <span
            className={`confirm-button ${selectOid ? "" : "disabled"}`}
            onClick={this.goToFillInLogistics}
          >确定</span>
        </div>
      </PageWrap>
    );
  }
}
const LogisticsCompanyList = ScrollAbilityWrapComponent(LogisticsCompanyListWrap);
export default LogisticsCompanyList;

const PageWrap = styled.div`// styled
  & {
    width: 100%;
    min-height: calc(${document.documentElement.clientHeight}px);
    height: auto;
    overflow-y: auto;
    background-color: rgba(242, 242, 242, 1);
    padding-bottom: 66px;
    .am-list-item {
      padding-left: 12px;
      border-bottom: 1px solid rgba(216, 216, 216, 1);
      .am-list-item .am-list-thumb:first-child{
        margin-top: 50%;
        transform: translateY(-50%);
        margin-right: 12px;
      }
    }
    .am-list .am-list-body::before {
      height: 0;
    }
    .am-list .am-list-body::after {
      height: 0;
    }
    .am-list-item .am-list-line .am-list-content {
      width: 80%;
      height: auto;
      font-size: 14px;
      color: rgba(47, 47, 47, 1);
      white-space: normal;
      line-height: 16px;
    }
    .footer{
      width: 100%;
      height: 66px;
      background-color: #fff;
      text-align: center;
      padding: 12px 16px;
      position: fixed;
      bottom: 0;
      .confirm-button{
        display: inline-block;
        width: 100%;
        height: 100%;
        background-color: rgba(48, 125, 205, 1);
        border-radius: 3px;
        text-align: center;
        font-size: 16px;
        color: #ffffff;
        line-height: 42px;
      }
      .disabled{
        background-color: rgba(217, 217, 217, 1);
      }
    }
  }
`;
