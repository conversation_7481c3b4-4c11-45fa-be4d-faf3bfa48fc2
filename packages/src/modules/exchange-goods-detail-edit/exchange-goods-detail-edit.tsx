import { autowired } from "@classes/ioc/ioc";
import { Input, Select, Spin } from "antd";
import { ActivityIndicator, Button, InputItem, List, Modal, Picker, TextareaItem, Toast } from "antd-mobile";
import { merge } from "lodash";
import { observer } from "mobx-react";
import React from "react";
import styled from "styled-components";
import { REQUEST_SERVER } from "../../helpers/ajax-helpers";
import { SITE_PATH } from "../app";
import { $ExchangeGoodsDetailEditMv } from "./$exchange-goods-detail-edit-mv";
import { withRouter } from "react-router";
import { RecognitionEnvironmentComponent } from "../../components/recognition-environment-component/recognition-environment-component";
import { WxUploadImgMv } from "../../components/wx-upload-img/wx-upload-img-mv";
import { InputNumberItemComponent } from "../../components/input-number-item-component/input-number-item-component";
import { $AppStore, AppStore<PERSON>ey } from "../../classes/stores/app-store-mv";

declare let window: any;
const Item = List.Item;
const Search = Input.Search;
const alert = Modal.alert;
const Option = Select.Option;

@withRouter
@observer
class ExchangeGoodsDetailEdit extends React.Component<any, any> {

  @autowired($AppStore)
  public $AppStore: $AppStore;
  @autowired($ExchangeGoodsDetailEditMv)
  public $exchangeGoodsDetailEditMv: $ExchangeGoodsDetailEditMv;

  @autowired(WxUploadImgMv)
  public wxUploadImgMv: WxUploadImgMv;

  constructor(props) {
    super(props);
    this.state = {
      percent: 0,
      showProgress: false,
      data: [],
      valueAdd: undefined,
      valueEdit: undefined,
      animating: false,
    };
  }

  public componentDidMount() {
    document.title = "换货详情编辑";
    this.$exchangeGoodsDetailEditMv.isDisabled = true;
    // console.log(this.$exchangeGoodsDetailEditMv.isDisabled);
    this.$exchangeGoodsDetailEditMv.fetchReginTree();
    // const { id } = this.props.location.state;
    const params = { id: sessionStorage.getItem("exchange-goods-detail-id") };
    this.$exchangeGoodsDetailEditMv.fetchExchangeGoodsRequestDetail(params).then(() => {
      const ua = window.navigator.userAgent.toLowerCase();
      if (ua.match(/MicroMessenger/i) == "micromessenger") { // 微信上传图片
        this.wxUploadImgMv.imgList = this.$exchangeGoodsDetailEditMv.pics;
        console.log(this.wxUploadImgMv.imgList);
      }
    });
  }

  public handleSearch = (value) => {
    const params = { keyword: value };
    this.$exchangeGoodsDetailEditMv.fetchProductLoad(params).then((data) => {
      this.setState({ data: data.productList });
      this.$exchangeGoodsDetailEditMv.setProductList(data.productList);
    });
  }

  public handleChangeAdd = (value) => {
    // console.log(value);
    this.setState({ valueAdd: value });
    // this.$exchangeGoodsDetailEditMv.setSku(value);
    this.$exchangeGoodsDetailEditMv.setProduct(value);
  }

  public handleChangeEdit = (value) => {
    // console.log(value);
    this.setState({ valueEdit: value });
    // this.$exchangeGoodsDetailEditMv.setSku(value);
    this.$exchangeGoodsDetailEditMv.setProduct(value);
  }

  public clearSkuAdd = () => {
    this.setState({ valueAdd: undefined });
  }

  public clearSkuEdit = () => {
    this.setState({ valueEdit: undefined });
  }

  public chooseAddress(orgionId) {
    this.$exchangeGoodsDetailEditMv.setRegionId(orgionId);
  }

  public uploadImage = (file: any) => {
    const formData = new FormData();
    formData.append("file", file.file);
    // console.log(file.file);
    if (file.file.size / 1024 > 20480) {
      Toast.info("图片大小不能超过20MB", 3);
      return;
    }
    const xhr = new XMLHttpRequest();
    xhr.open("POST", `${REQUEST_SERVER}/k/integration/scm/payvoucher/upload`, true);
    xhr.setRequestHeader("ssoSessionId", localStorage.getItem("token"));
    xhr.onload = () => {
      if (xhr.status === 200) {
        const url = JSON.parse(xhr.responseText).data.data.pic.url;
        const _file = merge({}, file.file, { url }, {
          thumbUrl: url,
          uid: JSON.parse(xhr.responseText).data.data.pic.uid,
        });
        this.addFile(_file);
      }
    };
    xhr.upload.addEventListener("progress", this.OnProgRess, false);
    xhr.send(formData);
  }

  public addFile = (file) => {
    const { pics } = this.$exchangeGoodsDetailEditMv;
    const fileList = pics.slice();
    fileList.push(file);
    this.$exchangeGoodsDetailEditMv.setPics(fileList);
    this.setState({ showProgress: false });
  }

  public onUpload = (files, type, index) => {
    if (type === "add") {
      this.setState({ files }, () => this.uploadImage(files[files.length - 1]));
    } else if (type === "remove") {
      this.$exchangeGoodsDetailEditMv.setPics(files);
    }
  }

  public OnProgRess = (e) => {
    // console.log(e);
    const { pics } = this.$exchangeGoodsDetailEditMv;
    if (pics) {
      if (pics.length === 0) {
        // console.log(pics.length);
        document.getElementsByClassName("am-progress-outer")[0].style.left = `15px`;
      }
      if (pics.length === 1) {
        // console.log(pics.length);
        document.getElementsByClassName("am-progress-outer")[0].style.left = `84px`;
        // console.log(document.getElementsByClassName("am-progress-outer")[0].style.left);
      }
      if (pics.length === 2) {
        // console.log(pics.length);
        document.getElementsByClassName("am-progress-outer")[0].style.left = `152px`;
        // console.log(document.getElementsByClassName("am-progress-outer")[0].style.left);
      }
      if (pics.length === 3) {
        // console.log(pics.length);
        document.getElementsByClassName("am-progress-outer")[0].style.left = `223px`;
        // console.log(document.getElementsByClassName("am-progress-outer")[0].style.left);
      }
      if (pics.length === 4) {
        // console.log(pics.length);
        document.getElementsByClassName("am-progress-outer")[0].style.left = `292px`;
        // console.log(document.getElementsByClassName("am-progress-outer")[0].style.left);
      }
    }
    const loaded = Math.floor(100 * (e.loaded / e.total));
    this.setState({ percent: loaded, showProgress: true });
  }

  public exchangegoodsrequestUpdate = (id, exchangeType) => {
    const params = { id, exchangeType };
    this.$exchangeGoodsDetailEditMv.exchangegoodsrequestUpdate(params).then((data) => {
      if (data.result) {
        if (exchangeType === "DEAL") {
          Toast.info("已成交", 3);
          this.$AppStore.clearPageMv(AppStoreKey.EXCHANGEGOODSLIST);
          window.location.href = `/${SITE_PATH}/exchange-goods-list`;
        } else {
          Toast.info("关闭成功", 3);
          this.$AppStore.clearPageMv(AppStoreKey.EXCHANGEGOODSLIST);
          window.location.href = `/${SITE_PATH}/exchange-goods-list`;
        }
      }
    });
  }

  public showModel = () => {
    this.setState({
      valueAdd: undefined,
    });
    this.$exchangeGoodsDetailEditMv.putQuantityAdd();
    this.$exchangeGoodsDetailEditMv.showModel();
  }

  public edit = () => {
    this.$exchangeGoodsDetailEditMv.setIsDisabled();
  }

  public save = () => {
    this.setState({ animating: true });
    let params = null;
    if (this.wxUploadImgMv.isWx === false) {
      params = this.$exchangeGoodsDetailEditMv.exchangeGoodsInfo;
    } else { // 微信上传图片
      this.$exchangeGoodsDetailEditMv.exchangeGoodsInfo.images = this.wxUploadImgMv.imgList;
      params = this.$exchangeGoodsDetailEditMv.exchangeGoodsInfo;
    }
    this.$exchangeGoodsDetailEditMv.setIsSaving(true);
    this.$exchangeGoodsDetailEditMv.saveGoods(params).then((data) => {
      this.setState({ animating: false });
      this.$exchangeGoodsDetailEditMv.setIsSaving(false);
      if (data.result) {
        Toast.info("保存成功", 3);
        this.$AppStore.clearPageMv(AppStoreKey.EXCHANGEGOODSLIST);
        window.location.href = `/${SITE_PATH}/exchange-goods-list`;
      }
    }).catch(() => {
      this.setState({ animating: false });
      this.$exchangeGoodsDetailEditMv.setIsSaving(true);
    });
  }

  public add = () => {
    if (this.$exchangeGoodsDetailEditMv.quantityAdd) {
      this.$exchangeGoodsDetailEditMv.hideModel();
      this.$exchangeGoodsDetailEditMv.add();
    } else {
      Toast.info("请填写数量", 3);
    }
  }

  public changeQuantityAdd = (value) => {
    this.$exchangeGoodsDetailEditMv.setQuantityAdd(value);
  }

  public changeQuantityEdit = (value) => {
    this.$exchangeGoodsDetailEditMv.setQuantityEdit(value);
  }

  public removeProduct = (skuId) => {
    this.$exchangeGoodsDetailEditMv.removeProduct(skuId);
  }

  public hideAddModel = () => {
    this.$exchangeGoodsDetailEditMv.hideModel();
  }

  public editModel = (skuId) => {
    const { isDisabled } = this.$exchangeGoodsDetailEditMv;
    if (!isDisabled) {
      const { exchangeGoodsInfo } = this.$exchangeGoodsDetailEditMv;
      this.$exchangeGoodsDetailEditMv.setSku(skuId);
      exchangeGoodsInfo.requestItemList.map((list) => {
        if (list.skuId === skuId) {
          this.$exchangeGoodsDetailEditMv.setQuantityEdit(list.quantity);
          this.setState({ valueEdit: list.name });
        }
      });
      this.$exchangeGoodsDetailEditMv.showEdit();
    }
  }

  public confim = () => {
    if (this.$exchangeGoodsDetailEditMv.quantityEdit) {
      this.$exchangeGoodsDetailEditMv.updateProduct();
      this.$exchangeGoodsDetailEditMv.hideEdit();
    } else {
      Toast.info("请填写数量", 3);
    }
  }

  public hideEditModel = () => {
    this.$exchangeGoodsDetailEditMv.hideEdit();
  }

  public clearMemo = () => {
    this.$exchangeGoodsDetailEditMv.clearMemo();
  }

  public setPics = (files) => {
    this.$exchangeGoodsDetailEditMv.setPics(files);
  }

  public onBlur = () => {
    window.scroll(0, 0);
  }

  public render() {
    const { addressTree, isSpin, pics, exchangeGoodsInfo, isModel, isDisabled, isEdit, quantityAdd, quantityEdit, isSaving } = this.$exchangeGoodsDetailEditMv;
    const { percent, showProgress, data, valueAdd, valueEdit, animating } = this.state;
    // const { id } = this.props.location.state;
    const options = data.map((d) => <Option key={d.skuId}>{d.name}</Option>);
    return (
      <ExchangeGoodsPage>
        <Spin spinning={isSpin}>
          <List style={{ position: "relative" }}>
            <InputItem
              className="address_showInput"
              placeholder="标题"
              value={exchangeGoodsInfo.title}
              onChange={(e) => this.$exchangeGoodsDetailEditMv.setTitle(e)}
              disabled={isDisabled}
              type="text"
              maxLength={30}
            >
              标题
            </InputItem>
            <InputItem
              className="address_showInput"
              type="text"
              placeholder="联系人"
              value={exchangeGoodsInfo.contactPerson}
              onChange={(e) => this.$exchangeGoodsDetailEditMv.setContactPerson(e)}
              disabled={isDisabled}
            >
              联系人
            </InputItem>
            <InputItem
              className="address_showInput"
              type="number"
              placeholder="联系电话"
              value={exchangeGoodsInfo.phoneNumber}
              onChange={(e) => this.$exchangeGoodsDetailEditMv.setPhoneNumber(e)}
              disabled={isDisabled}
              maxLength={11}
            >
              联系电话
            </InputItem>

            <Picker
              cols={3}
              data={addressTree}
              extra="请选择(可选)"
              onOk={(e) => this.chooseAddress(e)}
              title="选择地区"
              value={exchangeGoodsInfo.regionId}
              onChange={(v) => this.$exchangeGoodsDetailEditMv.setRegionId(v)}
              disabled={isDisabled}
            >
              <List.Item arrow={isDisabled ? "" : "horizontal"}>地区</List.Item>
            </Picker>
            <Memo>
              <TextareaItem
                autoHeight={true}
                title="备注"
                placeholder="请填写备注"
                value={exchangeGoodsInfo.memo}
                disabled={isDisabled}
                onChange={(e) => this.$exchangeGoodsDetailEditMv.setMemo(e)}
                maxLength={180}
              />
              {
                isDisabled ? null :
                  <i className="scmIconfont scm-icon-del" onClick={this.clearMemo}></i>
              }
            </Memo>
            <Spacing/>
            <div className="uploadImg">
              <span>图片</span>
              {
                isDisabled ? exchangeGoodsInfo.images.map((goods, index) => {
                  return (
                    <img src={goods.url} alt="" key={index} style={{ width: 64, height: 64, margin: "10px 5px" }}/>
                  );
                }) : <div style={{ position: "relative" }}>
                  <RecognitionEnvironmentComponent
                    pics={pics}
                    limitLength={5}
                    setPics={this.setPics}
                    isExchange={true}
                  />
                  {/*<ImagePicker*/}
                  {/*files={pics}*/}
                  {/*selectable={pics.length < 6}*/}
                  {/*onChange={this.onUpload}/>*/}
                  {/*<Progress percent={percent} style={{ display: showProgress ? "block" : "none", left: "15px" }}/>*/}
                </div>
              }
            </div>
          </List>
          <Spacing/>
          <ExchangeGoodsList>
            <ExchangeGoodsTab>
              商品列表
            </ExchangeGoodsTab>
            <ExchangeGoodsContents>
              {
                exchangeGoodsInfo.requestItemList.map((item, index) => {
                  return (
                    <ExchangeGoodsContent key={index}>
                      <p onClick={() => this.editModel(item.skuId)}>
                        {item.name}
                      </p>
                      <p>
                        <span>{item.code}</span>
                        <span>￥{item.retailPrice}</span>
                        <span>数量 {item.quantity}</span>
                      </p>
                      {
                        isDisabled ? "" : <i className="scmIconfont scm-icon-del"
                                             onClick={() => this.removeProduct(item.skuId)}/>
                      }
                    </ExchangeGoodsContent>
                  );
                })
              }
            </ExchangeGoodsContents>
            {
              isDisabled ? "" : <ExchangeGoodsAdd onClick={this.showModel}>
                + 添加商品
              </ExchangeGoodsAdd>}
          </ExchangeGoodsList>
        </Spin>
        <ButtonList className="bottom_btn">
          {
            isDisabled ? <div>
                {
                  exchangeGoodsInfo.isShowEditBtn ? <Button type="primary" onClick={this.edit}>
                    编辑
                  </Button> : null
                }
                {
                  exchangeGoodsInfo.isShowDeleteBtn ? <Button
                    type="primary"
                    onClick={() =>
                      alert("", "确认关闭吗", [
                        { text: "取消", onPress: () => console.log("cancel") },
                        {
                          text: "关闭",
                          onPress: () => this.exchangegoodsrequestUpdate(sessionStorage.getItem("exchange-goods-detail-id"), "CANCEL")
                        },
                      ])
                    }
                  >
                    关闭
                  </Button> : null
                }
                {
                  exchangeGoodsInfo.isShowDealBtn ?
                    <Button type="primary"
                            onClick={() => this.exchangegoodsrequestUpdate(sessionStorage.getItem("exchange-goods-detail-id"), "DEAL")}>
                      确认成交
                    </Button> : null
                }
              </div>
              : <Button type="primary" onClick={this.save} style={{ zIndex: 100 }} disabled={isSaving}>
                保存
              </Button>
          }
        </ButtonList>
        <ActivityIndicator
          toast={true}
          text="保存中..."
          animating={animating}
        />
        <Model style={{ display: isModel ? "block" : "none" }}>
          <ModelContent>
            <ModelHeader>
              添加商品
              <i className="scmIconfont scm-icon-del" onClick={this.hideAddModel}></i>
            </ModelHeader>
            <Spacing/>
            <ModelSearch>
              {/*<SearchInput style={{ width: "100%" }} edit={true} modelData={product}/>*/}
              <div>
                <Select
                  showSearch={true}
                  value={valueAdd}
                  placeholder={this.props.placeholder}
                  style={this.props.style}
                  defaultActiveFirstOption={false}
                  showArrow={false}
                  filterOption={false}
                  onSearch={this.handleSearch}
                  onChange={this.handleChangeAdd}
                  notFoundContent={null}
                >
                  {options}
                </Select>
                <i className="scmIconfont scm-icon-del" onClick={this.clearSkuAdd}/>
              </div>
              {/*<Input*/}
                {/*onChange={(val) => this.changeQuantityAdd(val)}*/}
                {/*placeholder="数量"*/}
                {/*type="number"*/}
                {/*value={String(quantityAdd)}*/}
              {/*/>*/}
              <InputNumberItemComponent
                className={"exchangeGoodsInput"}
                style={{ textAlign: "center" }}
                type="money"
                onChange={(val) => this.changeQuantityAdd(val)}
                value={quantityAdd && String(quantityAdd)}
                placeholder="数量"
                onBlur={this.onBlur}
              />
            </ModelSearch>
            <Button type="primary" onClick={this.add}>
              确认添加
            </Button>
          </ModelContent>
        </Model>
        <Model style={{ display: isEdit ? "block" : "none" }}>
          <ModelContent>
            <ModelHeader>
              编辑商品
              <i className="scmIconfont scm-icon-del" onClick={this.hideEditModel}/>
            </ModelHeader>
            <Spacing/>
            <ModelSearch>
              {/*<SearchInput style={{ width: "100%" }} edit={true} modelData={modelData}/>*/}
              <div style={{ width: "100%" }}>
                <Select
                  showSearch={true}
                  value={valueEdit}
                  placeholder={this.props.placeholder}
                  defaultActiveFirstOption={false}
                  showArrow={false}
                  filterOption={false}
                  onSearch={this.handleSearch}
                  onChange={this.handleChangeEdit}
                  notFoundContent={null}
                >
                  {options}
                </Select>
                <i className="scmIconfont scm-icon-del" onClick={this.clearSkuEdit}/>
              </div>
              {/*<Input*/}
                {/*onChange={(val) => this.changeQuantityEdit(val)}*/}
                {/*placeholder="数量"*/}
                {/*type="number"*/}
                {/*value={String(quantityEdit)}*/}
              {/*/>*/}
              <InputNumberItemComponent
                className={"exchangeGoodsInput"}
                style={{ textAlign: "center" }}
                type="money"
                placeholder="数量"
                onChange={(val) => this.changeQuantityEdit(val)}
                value={quantityEdit && String(quantityEdit)}
                onBlur={this.onBlur}
              />
            </ModelSearch>
            <Button type="primary" onClick={this.confim}>
              确认更新
            </Button>
          </ModelContent>
        </Model>
      </ExchangeGoodsPage>
    );
  }
}

export default ExchangeGoodsDetailEdit;

const ExchangeGoodsPage = styled.div`// styled
  & {
    .am-textarea-label {
      color: #333;
      font-size: 14px;
    }
    .am-textarea-control textarea, .am-textarea-control textarea:disabled {
      font-size: 14px;
      color: #666666;
    }
    .am-list-item .am-list-line .am-list-extra {
      flex-basis: 50%;
      font-size: 14px;
      color: #666666;
    }
    .am-list-item .am-input-control input, .am-list-item .am-input-control input:disabled {
      font-size: 14px;
      color: #666666;
      text-align: right;
    }
    .am-list-item.am-input-item {
      border-bottom: 1px solid #D8D8D8;
      box-shadow: inset 0 0 0 0 #D8D8D8;
    }
    .am-list-item .am-input-label {
      color: #333;
      font-size: 14px;
    }
    .am-list-item .am-list-line .am-list-content {
      color: #333;
      font-size: 14px;
    }
    .am-list-body .am-list-item:last-child {
      border-bottom: 1px solid #D8D8D8;
    }
    .am-image-picker {
      display: inline-block;
    }
    .am-image-picker-list .am-flexbox .am-flexbox-item {
      width: 64px;
      height: 64px;
    }
    .am-image-picker-list .am-flexbox .am-flexbox-item:after {
      padding-bottom: 0;
    }
    .uploadImg {
      height: auto;
      overflow: hidden;
      > span {
        font-size: 14px;
        color: #333;
        display: block;
        border-bottom: 1px solid #D8D8D8;
        padding: 10px 15px;
      }
      .am-image-picker-list .am-flexbox:nth-of-type(2) {
        position: absolute;
        top: 8px;
        left: 76%;
        .am-flexbox-item:nth-of-type(2) {
          margin-left: 40px;
        }
      }
      @media screen and (width: 414px) {
        .am-image-picker-list .am-flexbox:nth-of-type(2) {
          position: absolute;
          top: 8px;
          left: 69%;
          .am-flexbox-item:nth-of-type(2) {
            margin-left: 65px;
          }
        }
      }
    }
    .am-progress-outer {
      position: absolute;
      top: 45%;
      left: 15px;
      width: 50px;
      border-radius: 5px;
    }
    .am-progress-bar {
      border: 2px solid #307dcd;
      border-radius: 5px;
    }
  }
`;

const ExchangeGoodsList = styled.div`// styled
  & {
    width: 100%;
    height: auto;
    margin-bottom: 70px;
  }
`;

const Spacing = styled.div`// styled
  & {
    width: 100%;
    height: 10px;
    background: #F2F2F2;
  }
`;

const ExchangeGoodsTab = styled.div`// styled
  & {
    width: 100%;
    height: 40px;
    padding: 10px 15px;
    font-size: 14px;
    color: #333;
    border-bottom: 1px solid #D8D8D8;
  }
`;
const ExchangeGoodsHeader = styled.div`// styled
  & {
    width: 100%;
    height: auto;
    border-top: 1px solid #D8D8D8;
    > span {
      display: inline-block;
    }
    > span:first-child {
      width: 50%;
    }
    > span:not(:first-child) {
      width: 25%;
    }
  }
`;

const Model = styled.div`// styled
  & {
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.25);
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 99;
    padding: 0 28px;
    .exchangeGoodsInput {
      width: 124px;
      height: 36px !important;
      min-height: 36px !important;
      border: 1px solid #D8D8D8;
      margin-top: 24px;
      padding: 4px 11px;
      box-sizing: border-box;
      .am-list-line {
        padding-right: 0 !important;
      }
      .fake-input-placeholder {
        font-size: 14px;
      }
      .am-input-control .fake-input-container .fake-input {
        text-align: center !important;
      }
      .am-input-control .fake-input-container .fake-input-placeholder {
        text-align: center;
      }
      .am-input-control .fake-input-container .fake-input.focus:after {
        right: unset !important;
      }
    }
  }
`;

const ModelContent = styled.div`// styled
  & {
    width: 85%;
    height: 254px;
    background: #FFFFFF;
    border-radius: 10px;
    position: absolute;
    top: 50%;
    margin-top: -178px;
    .am-button-primary {
      height: 36px;
      line-height: 36px;
      margin: 10px 15px;
      background: #307DCD;
      border-radius: 3px;
      font-size: 14px;
      color: #FFFFFF;
    }
  }
`;

const ModelHeader = styled.div`// styled
  & {
    width: 100%;
    height: 40px;
    line-height: 40px;
    font-size: 14px;
    color: #666666;
    text-align: center;
    position: relative;
    .scm-icon-del {
      position: absolute;
      top: -12px;
      right: 0;
    }
  }
`;

const Memo = styled.div`// styled
  & {
    position: relative;
    > i {
      position: absolute;
      top: 10px;
      right: 15px;
      color: #666666;
    }
  }
`;

const ModelSearch = styled.div`// styled
  & {
    width: 100%;
    height: auto;
    padding: 20px 15px;
    border-bottom: 1px solid #D8D8D8;
    position: relative;
    .ant-select {
      width: 100%;
    }
    .scm-icon-del {
      position: absolute;
      top: 25px;
      right: 20px;
      color: #999;
    }
    .ant-select-selection {
      border: 1px solid #999;
      border-radius: 3px;
    }
    .ant-input {
      margin-top: 24px;
      text-align: center;
      width: 124px;
      height: 36px;
      border: 1px solid #999;
      border-radius: 3px;
    }
  }
`;

const ExchangeGoodsContents = styled.div`// styled
  & {
    width: 100%;
    height: auto;
    .scm-icon-increase {
      color: #307DCD;
    }
  }
`;

const ExchangeGoodsAdd = styled.div`// styled
  & {
    width: 100px;
    height: 30px;
    margin: 10px auto;
    padding: 5px 10px;
    border: 1px solid #307DCD;
    color: #307DCD;
    border-radius: 3px;
    /*position: fixed;
    bottom: 100px;
    left: 0px;
    right: 0px;
    background: #fff;*/
  }
`;

const ExchangeGoodsContent = styled.div`// styled
  & {
    width: 100%;
    height: 64px;
    border-bottom: 1px solid #D8D8D8;
    padding: 10px 15px;
    position: relative;
    .scm-icon-del {
      color: #999;
      position: absolute;
      top: 22px;
      right: 15px;
      z-index: 99;
    }
    > p {
      margin-bottom: 10px;
    }
    > p:nth-of-type(1) {
      font-size: 12px;
      color: #333333;
    }
    > p:nth-of-type(2) {
      > span:nth-of-type(1) {
        font-size: 12px;
        color: #999999;
        margin-right: 15px;
      }
      > span:nth-of-type(2) {
        font-size: 12px;
        color: #FF3030;
        margin-right: 15px;
      }
      > span:nth-of-type(3) {
        font-size: 12px;
        color: #999999;
      }
    }
  }
`;

const ButtonList = styled.div`// styled
  & {
    width: 100%;
    height: 66px;
    background: #fff;
    padding: 10px 15px;
    position: fixed;
    bottom: 0;
    z-index: 100;
    .am-button-primary {
      background: #307DCD;
      border-radius: 3px;
      > span {
        font-family: PingFangSC-Regular;
        font-size: 16px;
        color: #FFFFFF;
      }
    }
    > div {
      .am-button-primary {
        background: #fff;
        display: inline-block;
        text-align: center;
        margin-left: 15px;
      }
      .am-button:last-child {
        background: #307dcd;
      }
      .am-button:last-child span {
        background: #307dcd;
        color: #fff;
      }
      .am-button span {
        width: 95px;
        height: 42px;
        line-height: 42px;
        background: #fff;
        border-radius: 3px;
        font-size: 16px;
        color: #307DCD;
        text-align: center;
        display: inline-block;
      }
    }
  }
`;
