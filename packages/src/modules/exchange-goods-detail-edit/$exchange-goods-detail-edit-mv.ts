import { autowired, bean } from "@classes/ioc/ioc";
import { findIndex, remove } from "lodash";
import { action, observable } from "mobx";
import { $exchangeGoodsInfo } from "../../classes/entity/$exchange-goods-info";
import { $File } from "../../classes/entity/$file";
import { $requestItem } from "../../classes/entity/$request-item";
import { $skuList } from "../../classes/entity/$sku-list";
import { $ExchangeGoodsService } from "../../classes/service/$exchange-goods-service";
import { $ShippingAddressService } from "../../classes/service/$shipping-address-service";

@bean($ExchangeGoodsDetailEditMv)
export class $ExchangeGoodsDetailEditMv {
  @autowired($ShippingAddressService)
  public $shippingAddressService: $ShippingAddressService;

  @autowired($ExchangeGoodsService)
  public $exchangeGoodsService: $ExchangeGoodsService;

  @observable public pics: $File[] = [];

  @observable public addressTree: any;

  @observable public exchangeGoodsInfo: $exchangeGoodsInfo = new $exchangeGoodsInfo({});

  @observable public isSpin: boolean = false;

  @observable public productList: $skuList[] = [];

  @observable public exchangeGoodsDetail: $exchangeGoodsInfo[] = [];

  @observable public isModel: boolean = false;

  @observable public isEdit: boolean = false;

  @observable public isDisabled: boolean = true;

  @observable public quantity: number;

  @observable public product: $skuList;

  @observable public skuId: number;

  @observable public quantityAdd: number;

  @observable public quantityEdit: number;

  @observable public isSaving: boolean = false;

  @action
  public setIsSaving(isSaving: boolean) {
    this.isSaving = isSaving;
  }

  @action
  public showSpin() {
    this.isSpin = true;
  }

  @action
  public hideSpin() {
    this.isSpin = false;
  }

  @action
  public showModel() {
    this.isModel = true;
  }

  @action
  public hideModel() {
    this.isModel = false;
  }

  @action
  public showEdit() {
    this.isEdit = true;
  }

  @action
  public hideEdit() {
    this.isEdit = false;
  }

  @action
  public setPics(pics: any[]) {
    this.pics = pics;
    this.exchangeGoodsInfo.images = pics;
  }

  @action
  public setProductList(list) {
    this.productList = list;
  }

  @action
  public setProduct(val) {
    this.productList.map((product) => {
      if (product.skuId === Number(val)) {
        this.product = product;
        // console.log(this.product);
      }
    });
  }

  @action
  public setIsDisabled() {
    this.isDisabled = false;
  }

  @action
  public setSku(val) {
    this.skuId = val;
  }

  @action
  public clearMemo() {
    this.exchangeGoodsInfo.memo = undefined;
  }

  @action
  public removeProduct(skuId) {
    remove(this.exchangeGoodsInfo.requestItemList, (requestItem: $requestItem) => requestItem.skuId === skuId);
  }

  @action
  public saveGoods(params) {
    return this.$exchangeGoodsService.saveExchangeGoods(params);
  }

  @action
  public fetchProductLoad(keyword) {
    return this.$exchangeGoodsService.queryProductLoad(keyword);
  }

  @action
  public fetchReginTree() {
    this.$shippingAddressService.queryAddressList({}).then((data) => {
      this.addressTree = data.regionTree;
    });
  }

  @action
  public fetchExchangeGoodsRequestDetail(id) {
    return this.$exchangeGoodsService.queryExchangegoodsrequestDetail(id).then((data) => {
      this.exchangeGoodsInfo = new $exchangeGoodsInfo(data);
      this.pics = this.exchangeGoodsInfo.images;
    });
  }

  @action
  public exchangegoodsrequestUpdate(params) {
    return this.$exchangeGoodsService.exchangegoodsrequestUpdate(params);
  }

  @action
  public saveAddress(formData, type) {
    if (type === "edit") {
      return this.$shippingAddressService.editAddress(formData);
    } else {
      return this.$shippingAddressService.saveAddress(formData);
    }
  }

  @action
  public setTitle(title) {
    this.exchangeGoodsInfo.title = title;
  }

  @action
  public setQuantityAdd(val) {
    if (val === "") {
      this.quantityAdd = null;
      this.product.quantity = Number(val);
    } else {
      this.quantityAdd = Number(val);
      this.product.quantity = Number(val);
    }
  }

  @action
  public putQuantityAdd() {
    this.quantityAdd = undefined;
  }

  @action
  public setQuantityEdit(val) {
    if (val === "") {
      this.quantityEdit = null;
    } else {
      this.quantityEdit = Number(val);
    }
  }

  @action
  public updateProduct() {
    const index = findIndex(this.exchangeGoodsInfo.requestItemList, { skuId: this.skuId });
    this.exchangeGoodsInfo.requestItemList[index].quantity = this.quantityEdit;
  }

  @action
  public add() {
    // console.log(this.quantity);
    // console.log(this.product);
    const index = findIndex(this.exchangeGoodsInfo.requestItemList, { skuId: this.product.skuId });
    if (index > -1) {
      this.exchangeGoodsInfo.requestItemList[index] = this.product;
    } else {
      this.exchangeGoodsInfo.requestItemList.push(this.product);
    }
  }

  @action
  public setContactPerson(contactPerson) {
    this.exchangeGoodsInfo.contactPerson = contactPerson;
  }

  @action
  public setPhoneNumber(phoneNumber) {
    if (phoneNumber) {
      if (phoneNumber.length <= 15) {
        this.exchangeGoodsInfo.phoneNumber = phoneNumber ? Number(phoneNumber) : phoneNumber;
      }
    }
  }

  @action
  public setRegionId(regionId) {
    this.exchangeGoodsInfo.regionId = regionId;
  }

  @action
  public setMemo(memo) {
    this.exchangeGoodsInfo.memo = memo;
  }

}
