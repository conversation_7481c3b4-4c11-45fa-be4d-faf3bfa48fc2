import { autowired } from "@classes/ioc/ioc";
import { observer } from "mobx-react";
import { transaction } from "mobx";
import React from "react";
import { withRouter } from "react-router";
import styled from "styled-components";
import { $GiftsLimitListMv } from "./gifts-limit-list-mv";
import { NoGoods } from "../../components/no-goods/no-goods";
import { SITE_PATH } from "../app";
import { Spin } from "antd";
import { ScrollAbilityWrapComponent } from "../../components/scroll-ability/wrap";
import { LoadingTip } from "../../components/loading-marked-words";
import { $AppStore } from "../../classes/stores/app-store-mv";
import { CustomMonthModal } from "../../components/custom-month-modal/custom-month-modal";
import { AccountBusinessModal } from "../../components/account-business-modal/account-business-modal";
import { AccountFlowItem } from "../../../src/components/account-flow-item/account-flow-item";
import { $AccountType } from "@classes/const/$account-type";
import moment from "moment";
import { Toast } from "antd-mobile";
import { $SearchDateType } from "@classes/const/$search-date-type";
import { $SearchTimeType } from "@classes/const/$search-time-type";

@withRouter
@observer
class GiftsLimitListWrap extends React.Component<any, any> {

  @autowired($GiftsLimitListMv)
  public $myMv: $GiftsLimitListMv;

  @autowired($AppStore)
  public $AppStore: $AppStore;

  public constructor(props) {
    super(props);
    this.state = {
      showDateModal: false,
      showBusinessModal: false,
    };
  }

  public componentDidMount() {
    this.$myMv.accountFlowList = [];
    document.title = "配赠上限流水";
    this.$myMv.clearMVData();
    if (moment().isBefore(moment("2025-02-01"))) {
      this.$myMv.startTime = moment().format("YYYY-01-16");
    } else {
      this.$myMv.startTime = $SearchDateType.FirstDayOfMonth;
    }
    this.$myMv.endTime = $SearchDateType.LastDayOfMonth;
    this.initPage();
  }

  public componentWillReceiveProps(nextProps: Readonly<any>, nextContext: any): void {
    const { isScrollEnd } = nextProps;
    const { showDateModal, showHfpzModal } = this.state;
    if (isScrollEnd && isScrollEnd !== this.props.isScrollEnd && !showDateModal && !showHfpzModal) {
      this.loadData();
    }
  }

  public initPage = () => {
    const { accountId } = this.props.match.params || { accountId: "" };
    const { pageSize, pageIndex, key, status, businessType, startTime, endTime } = this.$myMv;
    this.$myMv.showSpin();
    this.$myMv.isLoading = true;
    const params = {
      accountId,
      condition: key,
      pageIndex,
      pageSize,
      status,
      businessType,
      start: startTime,
      end: endTime,
    };
    const { loadingEnd } = this.props;
    loadingEnd && loadingEnd(false);
    this.firstLoadFetchAccountInfo(params);
  }

  public firstLoadFetchAccountInfo = (params) => {
    transaction(() => {
      this.$myMv.fetchAccountInfo(params, false);
    });
  }

  public queryAccountInfo = (activeKey) => {
    this.$myMv.key = activeKey;
    this.$myMv.isFinished = false;
    this.$myMv.pageIndex = 0;
    this.initPage();
  }

  public loadData = () => {
    const { isFinished, isLoading, pageIndex } = this.$myMv;
    if (!isFinished) {
      this.$myMv.showSpin();
      if (!isLoading) {
        this.$myMv.pageIndex = pageIndex + 1;
        this.loadList();
      }
    }
  }

  public loadList = () => {
    const { pageSize, key, pageIndex, businessType, startTime, endTime, status } = this.$myMv;
    this.$myMv.showSpin();
    const params = {
      accountId: this.props.match.params.accountId,
      condition: key,
      pageIndex,
      pageSize,
      status,
      businessType,
      start: startTime,
      end: endTime,
    };
    this.$myMv.isLoading = true;
    const { loadingEnd } = this.props;
    this.$myMv.fetchAccountInfo(params, true).then((res) => {
      loadingEnd && loadingEnd(res);
    });
    // 发送请求改变 isFinished， isLoading状态
  }

  public onSearchDate = (startTime, endTime, dateType) => {
    if (moment(endTime).isBefore(startTime)) {
      Toast.info("结束时间不得大于开始时间");
      return;
    }
    this.$myMv.startTime = startTime;
    this.$myMv.endTime = endTime;
    this.$myMv.dateType = dateType;
    this.$myMv.pageIndex = 0;
    this.initPage();
  }

  public onSearchBusiness = (val) => {
    this.$myMv.businessType = val[0];
    this.$myMv.pageIndex = 0;
    this.initPage();
  }

  public goAccountDetail = (item) => {
    sessionStorage.setItem("detailParams", JSON.stringify(item));
    this.props.history.push({ pathname: `/${SITE_PATH}/new-account-info-detail/${this.props.match.params.accountId}` });
  }

  public render() {
    const { showDateModal, showBusinessModal } = this.state;
    const { accountFlowList, key, isLoading, orderQuotaLimit, isFinished,
            startTime, endTime, after, inAmount, outAmount, businessType, dateType } = this.$myMv;
    const isFixed = showDateModal || showBusinessModal;
    return (
      <Spin spinning={isLoading} style={{ pointerEvents: isFixed ? "none" : "auto" }}>
        <Wrapper className="account-content-info-warp" >
          <SHeadBg />
          <SHeader>
            <div>总配赠额度</div>
            <div>{orderQuotaLimit || 0}</div>
          </SHeader>
          <SSearch>
            <div className="search-more">
              <div onClick={() => this.setState({ showDateModal: true })}>
                <span>
                  {
                    dateType === $SearchTimeType.MONTH ? startTime.slice(0, 7) : `${startTime}~${endTime}`
                  }
                </span>
                <i className="scmIconfont scm-icon-xiajiantou" />
              </div>
              <div onClick={() => this.setState({ showBusinessModal: true })}>
                <span>业务类型</span>
                <i className="scmIconfont scm-icon-xiajiantou" />
              </div>
            </div>
            <div className="search-key">
              <span className={key === "ALL" ? "active" : ""} onClick={() => this.queryAccountInfo("ALL")}>全部</span>
              <span className={key === "IN" ? "active" : ""} onClick={() => this.queryAccountInfo("IN")}>增加</span>
              <span className={key === "OUT" ? "active" : ""} onClick={() => this.queryAccountInfo("OUT")}>减少</span>
            </div>
            {
              key === "ALL" && (after > 0 || inAmount > 0 || outAmount > 0) &&  <div className="search-board">
                {
                  after > 0 && <div>
                    <div>{after}</div>
                    <div>结余</div>
                  </div>
                }
                {
                  inAmount > 0 && <div>
                    <div className="red">{inAmount}</div>
                    <div>增加</div>
                  </div>
                }
                {
                  outAmount > 0 && <div>
                    <div>{outAmount}</div>
                    <div>减少</div>
                  </div>
                }
              </div>
            }
            {
              (key === "IN" || key == "OUT") && <div className="single-board">
                {
                  key === "IN" && <div className="red">增加 <span>{inAmount}</span></div>
                }
                {
                  key == "OUT" && <div className="gray">减少 <span>{outAmount}</span></div>
                }
              </div>
            }
          </SSearch>
          <SList>
            {
              accountFlowList && accountFlowList.length > 0 ? accountFlowList.map((item, index) => {
                return <AccountFlowItem bottomTitle={"配赠额度"} item={item} key={index} goAccountFlowDetail={() => this.goAccountDetail(item)}/>;
              }) : <NoGoods title="暂无数据" />
            }
            {
              accountFlowList && accountFlowList.length > 0 ?
                <LoadingTip
                  isFinished={isFinished}
                  isLoad={isLoading}
                /> : null
            }
          </SList>
          <CustomMonthModal
            visible={showDateModal}
            type={"range"}
            dateType={dateType}
            startDate={startTime}
            endDate={endTime}
            pageType={$AccountType.RETURN_FREE_DISTRIBUTION}
            onCancel={() => this.setState({ showDateModal: false })}
            onConfirm={this.onSearchDate}
          />
          <AccountBusinessModal
            showLimit={true}
            visible={showBusinessModal}
            onCancel={() => this.setState({ showBusinessModal: false })}
            onConfirm={this.onSearchBusiness}
          />
        </Wrapper>
      </Spin>
    );
  }
}

const GiftsLimitList = ScrollAbilityWrapComponent(GiftsLimitListWrap);

export default GiftsLimitList;

const Wrapper = styled.div`// styled
  & {
    width: 100%;
    height: 100vh;
    padding: 15px;
    display: flex;
    flex-direction: column;
    align-items: center;
    background: #F7F7F7;

    .am-modal-content {
      z-index: 1000;

      .modal-title {
        padding: 12px;
      }
    }
  }
`;

const SHeadBg = styled.div`
  & {
    position: absolute;
    top: 0;
    width: 100vw;
    height: 30vw;
    background: #437DF0;
  }
`;

const SHeader = styled.div`
  & {
    width: 90vw;
    color: #FFF;
    z-index: 10;
    margin-top: 16px;
    display: flex;
    flex-direction: column;

    > div:nth-child(1) {
      font-size: 13px;
    }

    > div:nth-child(2) {
      font-size: 32px;
      font-weight: 500;
    }
  }
`;

const SList = styled.div`
  & {
    width: 100%;
    margin-top: 12px;
    border-radius: 8px;
    overflow: scroll;
    background: #FFF;

    .date-title {
      padding: 12px;
      font-size: 16px;
      font-weight: 500;
      color: #333;
    }
  }
`;

const SSearch = styled.div`// styled
  & {
    width: 100%;
    height: auto;
    background: #fff;
    margin-top: 12px;
    border-radius: 8px;
    z-index: 100;

    .search-key {
      width: 100%;
      height: 40px;
      line-height: 40px;
      display: flex;
      background: #F7F7F7;

      > span {
        display: inline-block;
        width: 100%;
        text-align: center;
        color: #666;
        margin: 0 30px;
        font-size: 14px;
      }
      .active {
        border-bottom: 2px solid #307DCD;
        color: #307DCD;
        font-size: 14px;
      }
    }

    .search-more {
      display: flex;
      justify-content: space-between;
      padding: 12px;
      border-radius: 8px;
      background: #FFF;

      > div {
        > span {
          color: #333;
          font-size: 14px;
          margin-right: 3px;
        }
        > img {
          width: 14px;
          height: 14px;
        }
      }
    }

    .search-board {
      padding: 12px 36px;
      display: flex;
      justify-content: space-between;
      background: linear-gradient(181deg, #F0F3FC 4%, #FFFFFF 64%);

      > div {
        display: flex;
        flex-direction: column;
        text-align: left;

        > div:nth-child(1) {
          font-size: 18px;
          font-weight: 500;
          color: #333;
        }

        > div:nth-child(2) {
          font-size: 14px;
          color: #666;
          margin-top: 5px;
        }

        .red {
          color: #FF3030 !important;
        }
      }
    }

    .single-board {
      padding: 12px 24px;
      background: #FFF;
      font-size: 14px;

      .red {
        display: flex;
        align-items: center;

        > span {
          margin-left: 5px;
          color: #FF3030;
          font-size: 18px;
          font-weight: 500;
        }
      }

      .gray {
        display: flex;
        align-items: center;

        > span {
          margin-left: 5px;
          color: #333333;
          font-size: 18px;
          font-weight: 500;
        }
      }
    }

  }
`;
