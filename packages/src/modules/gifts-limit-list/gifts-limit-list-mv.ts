import { autowired, bean } from "@classes/ioc/ioc";
import { action, observable } from "mobx";
import { $ComponentService } from "../../classes/service/$component-service";
import { $OrderPartyService } from "../../classes/service/$order-party-service";
import { beanMapper } from '../../helpers/bean-helpers';
import { $SearchDateType } from "@classes/const/$search-date-type";
import { $SearchTimeType } from "@classes/const/$search-time-type";

@bean($GiftsLimitListMv)
export class $GiftsLimitListMv {

  @autowired($ComponentService)
  public $componentService: $ComponentService;

  @autowired($OrderPartyService)
  public $orderPartyService: $OrderPartyService;

  @observable public isSpin: boolean = false;
  @observable public isFinished: boolean = false;
  @observable public isLoading: boolean = false;
  @observable public isShowLoading: boolean = true;
  @observable public orderQuotaLimit: number = 0;
  @observable public accountFlowListSize: number;
  @observable public pageIndex: number = 0;
  @observable public scrollHeight: number = 0;
  @observable public pageSize: number = 10;
  @observable public key: string = "ALL";
  @observable public accountFlowList: any[];
  @observable public status: string = '';
  @observable public businessType: string = '';
  @observable public startTime: string = $SearchDateType.FirstDayOfMonth;
  @observable public endTime: string = $SearchDateType.LastDayOfMonth;
  @observable public after: number; // 结余
  @observable public inAmount: number; // 收入
  @observable public outAmount: number; // 支出
  @observable public dateType: number = $SearchTimeType.MONTH;

  @action
  public showSpin() {
    this.isSpin = true;
  }

  @action
  public hideSpin() {
    this.isSpin = false;
  }

  @action
  public fetchAccountInfo(params, blean) {
    return new Promise((resolve, reject) => {
      this.$componentService.queryGiftsLimitList(params).then((data) => {
        this.isLoading = false;
        const { accountFlowList, accountFlowListSize, orderQuotaLimit, after, outAmount, inAmount, errorCode } = data;
        this.accountFlowListSize = accountFlowListSize;
        this.accountFlowList = blean ? this.accountFlowList.concat(accountFlowList) : accountFlowList;
        this.orderQuotaLimit = orderQuotaLimit;
        this.after = after;
        this.inAmount = inAmount;
        this.outAmount = outAmount;
        this.isFinished = this.accountFlowList.length >= accountFlowListSize;
        this.hideSpin();
        data.isFinished = this.accountFlowList.length >= accountFlowListSize;
      }).catch((err) => {
        reject(err);
      });
    });
  }

  @action
  public queryOldData(obj) {
    beanMapper(obj, this);
    console.log(this);
  }

  @action
  public clearMVData() {
    this.isSpin = false;
    this.isFinished = false;
    this.isLoading = false;
    this.isShowLoading = true;
    this.orderQuotaLimit = 0;
    this.accountFlowListSize = 0;
    this.scrollHeight = 0;
    this.pageIndex = 0;
    this.pageSize = 10;
    this.key = "ALL";
    this.accountFlowList = [];
    this.after = 0;
    this.inAmount = 0;
    this.outAmount = 0;
  }
}
