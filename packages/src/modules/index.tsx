import * as React from "react";
import * as ReactDOM from "react-dom";
import { post } from "../helpers/ajax-helpers";
import { App } from "./app";
import asyncComponent from "./AsyncComponent";
import "./iconfont.less";
// import "./index.less";
import "./index.less";
import FastClick from "fastclick";
import { fixInputFocus } from "./fast-click-helper";
import { gaInit } from "../classes/website-statistics/website-statistics";
// import VConsole from "vconsole";

FastClick.attach(document.body);
fixInputFocus();
const NoAuthority = asyncComponent(() => import("./no-authority/no-authority"));
declare let window: any;

// const vConsole = new VConsole();

function getParameterByName(name, url) {
  if (!url) {
    url = window.location.href;
  }
  name = name.replace(/[\[\]]/g, "\\$&");
  const regex = new RegExp("[?&]" + name + "(=([^&#]*)|&|#|$)"),
    results = regex.exec(url);
  if (!results) {
    return null;
  }
  if (!results[2]) {
    return "";
  }
  return decodeURIComponent(results[2].replace(/\+/g, " "));
}

let ssoSessionId = getParameterByName("ssoSessionId");
if (!ssoSessionId) {
  ssoSessionId = localStorage.getItem("token");
} else {
  localStorage.setItem("token", ssoSessionId);
}
if (ssoSessionId) {
  post("/integration/scm/ordermall/login/permission/query", {}, "routeSkip").then((res) => {
    // console.log("res--------", res);
    if (res) {
      if (res.permission === "agree") {
        gaInit(ssoSessionId);
        ReactDOM.render(
          <App/>, window.document.getElementById(window.ENV.reactMountPoint),
        );
      } else {
        ReactDOM.render(
          <NoAuthority/>, window.document.getElementById(window.ENV.reactMountPoint),
        );
      }
    }
  });
} else {
  ReactDOM.render(
    <App/>, window.document.getElementById(window.ENV.reactMountPoint),
  );
}
window.addEventListener("pageshow", (e) => {
  // 通过persisted属性判断是否存在 BF Cache
  if (e.persisted) {
    location.reload();
  }
});

// document.body.addEventListener("touchmove", (e) => {
//   const className = e.srcElement.parentElement.className;
//   if (className && className.indexOf && className.indexOf('not-touch') !== -1) {
//     e.preventDefault();
//   }
// }, { passive: false });
// window.addEventListener("touchstart", function(e) {e.preventDefault(); });
// window.addEventListener("touchmove", function(e) {e.preventDefault(); });
