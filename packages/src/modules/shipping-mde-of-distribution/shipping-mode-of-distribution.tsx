import * as React from "react";
import {observer} from "mobx-react";
import styled from "styled-components";
import {Radio} from "antd";

const RadioGroup = Radio.Group;

@observer
export class ShippingModeOfDistribution extends React.Component<any, any> {
  constructor(props) {
    super(props);
    this.state = {
      defaultDistributionMode: {},
      distributionModeId: 0,
      replaceDistributionModeList: [],
      closeShippingDistribution: null,
      changeDefaultShippingDistribution: null,
    };
  }

  public onChange = (e) => {
    this.setState({
      distributionModeId: e.target.value,
    });
  }

  public componentDidMount(): void {
    const {replaceDistributionModeList, defaultDistributionMode, closeShippingDistribution, changeDefaultShippingDistribution} = this.props;
    this.setState({
      defaultDistributionMode,
      replaceDistributionModeList,
      closeShippingDistribution,
      changeDefaultShippingDistribution,
      distributionModeId: defaultDistributionMode ? defaultDistributionMode.distributionModeId : 0,
    });
  }

  public confirmDefaultDistribution = () => {
    const {changeDefaultShippingDistribution} = this.state;
    changeDefaultShippingDistribution && changeDefaultShippingDistribution(this.state.distributionModeId);
  }

  public render() {
    const {replaceDistributionModeList, defaultDistributionMode, closeShippingDistribution} = this.state;
    let replaceDistributionModeHtml = null;
    let backgroundReturnDefaultDistributionModeHtml = null;
    if (replaceDistributionModeList.length > 0) {
      replaceDistributionModeHtml = replaceDistributionModeList.map((item, index) => {
        if (item.isDefault === "Y") {
          backgroundReturnDefaultDistributionModeHtml =
            <Radio
              key={item.distributionModeId}
              value={item.distributionModeId}
            >
              {item.distributionModeName}
              <span className="icon-default-distribution">默认</span>
            </Radio>;
        } else {
          return (
            <Radio
              key={item.distributionModeId}
              value={item.distributionModeId}
            >
              {item.distributionModeName}
            </Radio>
          );
        }
      });
    }
    return (
      <StyledModal>
        <div>
          <div className="distribution-mode-hint"><i className="scmIconfont scm-icon-fahuo"/>配送方式（不同的配送方式运费不同）</div>
          <div className="distribution-mode-list">
            <RadioGroup onChange={this.onChange} value={this.state.distributionModeId}>
              {
                defaultDistributionMode &&
                <Radio
                  key={defaultDistributionMode.distributionModeId}
                  value={defaultDistributionMode.distributionModeId}
                >
                  {defaultDistributionMode.distributionModeName}
                  {
                    defaultDistributionMode.isDefault === "Y" &&
                    <span className="icon-default-distribution">默认</span>
                  }
                </Radio>
              }
              {backgroundReturnDefaultDistributionModeHtml}
              {replaceDistributionModeHtml}
            </RadioGroup>
          </div>
          <DistributionModeFooter>
            <span onClick={closeShippingDistribution}>取消</span>
            <span onClick={this.confirmDefaultDistribution}>确定</span>
          </DistributionModeFooter>
        </div>
      </StyledModal>
    );
  }
}

const StyledModal = styled.div`// styled
  & {
    width: 100%;
    position: fixed;
    top: 0;
    left: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.65);
    z-index: 99;
    > div {
      z-index: 100;
      width: 80%;
      height: auto;
      background: #FFFFFF;
      border-radius: 5px;
      position: relative;
      margin: 100px auto;
      //padding: 20px 20px 0px 20px;
      .distribution-mode-hint{
        width: 100%;
        font-size: 13px;
        line-height: 40px;
        height: 40px;
        color: #666;
        border-radius: 5px 5px 0 0;
        background:rgba(242,242,242,1);
        padding-left: 15px;
        >i{
          margin-right: 10px;
        }
      }
      .distribution-mode-list{
        width: 100%;
        height: auto;
        .ant-radio-group{
          max-height: 292px;
          overflow-y: scroll;
          padding-left: 0;
          -webkit-overflow-scrolling:touch;
          .ant-radio-wrapper{
           padding-left: 15px !important;
           border-bottom: 1px solid #D8D8D8 !important;
           color: #333333 !important;
           height: 41px !important;
           width: 100% !important;
           line-height: 40px !important;
           margin-right: 0;
             >span{
                >.icon-default-distribution{
                  display: inline-block;
                  width:28px;
                  height:16px;
                  background-color:rgba(236,246,255,1);
                  border-radius:2px;
                  color:rgba(48,125,205,1);
                  font-size: 10px;
                  text-align: center;
                  line-height: 16px;
                  margin-left: 12px;
              }
             }
           }
           .ant-radio-inner{
            border: 1px solid #ccc;
            width: 18px;
            height: 18px;
            &:after{
            position: absolute;
            background-color: transparent;
            display: none;
            top: 1.5px;
            right: 6px;
            z-index: 999;
            width: 5px;
            height: 11px;
            border-style: solid;
            border-width: 0 1px 1px 0;
            content: ' ';
            transform: rotate(45deg);
           }
          }
        }
        .ant-radio-wrapper-checked{
           >span{
           position: relative;
           .ant-radio-inner{
              border-color: #108ee9;
              background: #108ee9;
              &:after{
                display: block;
                border-color: #fff;
                margin-left: 2px;
            }
           }
        }
        }
      }
    }
  }
`;
const DistributionModeFooter = styled.div`
  &{
    width: 100%;
    height: 60px;
    line-height: 60px;
    text-align: center;
    >span{
      width: 50%;
      display: inline-block;
      font-size: 18px;
      border-left: 1px solid #D8D8D8;
    }
    >span:first-of-type{
      border: 0;
    }
  }
`;
