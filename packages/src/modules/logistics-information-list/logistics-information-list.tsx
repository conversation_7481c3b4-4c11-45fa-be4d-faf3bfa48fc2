import { autowired, bean } from "@classes/ioc/ioc";
import { Spin } from "antd";
import { Modal } from "antd-mobile";
import { observer } from "mobx-react";
import React from "react";
import { withRouter } from "react-router";
import styled from "styled-components";
import { LogisticsInformationListMv } from "./logistics-information-list-mv";
import { LoadingTip } from "../../components/loading-marked-words";
import { SITE_PATH } from "../app";
import { NoGoods } from '../../components/no-goods/no-goods';
import LogisticsTracking from "../shop-order/logistics-tracking";
const alert = Modal.alert;

@withRouter
@observer
class LogisticsInformationList extends React.Component<any, any> {
  @autowired(LogisticsInformationListMv)
  public $myMv: LogisticsInformationListMv;

  constructor(props) {
    super(props);
    this.state = {
    };
  }
  public componentDidMount(): void {
    document.title = "物流信息列表";
    const { deliveryId } = this.props.match.params || { deliveryId: "" };
    this.setState({ deliveryId }, () => {
      this.loadData();
    });
  }
  public loadData = () => {
    const { deliveryId } = this.state;
    const params = {
      deliveryId,
    };
    this.setState({ isSpinning: true })
    this.$myMv.getLogisticsInfoList(params).then((res) => {
      console.log("res", res);
      const { logisticsList } = res;
      this.setState({
        isSpinning: false,
      });
      this.$myMv.logisticsList = logisticsList;
    }).catch((res) => {
      this.setState({ isSpinning: false });
    });
  }

  public render() {
    const { isSpinning } = this.state;
    const { logisticsList } = this.$myMv;
    return (
      <Wrapper className="wrapper">
        <Spin spinning={isSpinning} className="spinning">
          {
            logisticsList && logisticsList.length > 0 && logisticsList.map((item) => {
              const { companyName, logisticsDocNo, logisticsDocStatusName, logisticsTravelList, logisticsDocStatusCode} = item;
              return (
                <LogisticsContent>
                  <div className="logistics-item">
                    <div className="left">
                      <i className={`scmIconfont scm-icon-fahuo`} />
                    </div>
                    <div className="right">
                      <div>{companyName ? companyName : "未知物流"}</div>
                      <div>快递单号：{logisticsDocNo ? logisticsDocNo : "暂无"}</div>
                    </div>
                    {
                      logisticsDocStatusName && <div className={"status"} style={{color: logisticsDocStatusCode === "No_Msg" ? "#999999" : "#307DCD"}}>
                        {logisticsDocStatusName}
                      </div>
                    }
                  </div>
                  {logisticsTravelList && logisticsTravelList.length > 0 && <LogisticsTracking
                    logisticsTravelList={logisticsTravelList}
                    data={item}
                  />}
                </LogisticsContent>
              );
            })
          }
          {
            logisticsList && logisticsList.length === 0 ?
              <NoGoods
                title="暂无物流信息"
                height={document.documentElement.clientHeight}
              /> : null
          }
        </Spin>
      </Wrapper>
    );
  }
}

export default LogisticsInformationList;

const Wrapper = styled.div`// styled
  & {
    width: 100%;
    height: auto;
    background-color: #F2F2F2;
  }
`;

const LogisticsContent = styled.div`// styled
  & {
    width: 100%;
    height: auto;
    margin-bottom: 12px;
    .logistics-item{
      padding: 12px;
      background-color: #fff;
      overflow: hidden;
      position: relative;
      border-bottom: 1px solid #D8D8D8;
      .left{
        display: inline-block;
        width: 24px;
        height: 24px;
        position: absolute;
        top: 50%;
        transform: translateY(-50%);
        .scmIconfont{
          float: left;
          background-color: #307DCD;
          display: inline-block;
          width: 24px;
          height: 24px;
          text-align: center;
          line-height: 24px;
          color: #ffffff;
          border-radius: 12px;
          &:before{
            font-size: 16px;
          }
        }
      }
      .right{
        padding-left: 36px;
        >div:first-of-type{
          color: #2F2F2F;
          font-size: 14px;
          line-height: 14px;
        }
        >div:last-of-type{
          color: #333333;
          font-size: 13px;
          line-height: 13px;
          margin-top: 8px;
        }
      }
      .status {
        position: absolute;
        right: 12px;
        top: 30%;
        font-size:13px;
        font-family:PingFangSC-Regular,PingFang SC;
        font-weight:400;
        color:rgba(48,125,205,1);
      }
    }
  }
`;
