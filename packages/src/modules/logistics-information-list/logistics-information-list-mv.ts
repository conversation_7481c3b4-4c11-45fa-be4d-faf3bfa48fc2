import { autowired, bean } from "@classes/ioc/ioc";
import { action, observable } from "mobx";
import { $ReceiveService } from "../../classes/service/$receive-order-service";
import { Logistics } from "@classes/entity/logistics";

@bean(LogisticsInformationListMv)
export class LogisticsInformationListMv {
  @autowired($ReceiveService)
  public $myService: $ReceiveService;

  @observable public logisticsList: Logistics[] = [];

  @action
  public getLogisticsInfoList(params) {
    return this.$myService.getLogisticsInfoList(params);
  }
}
