import { autowired } from "@classes/ioc/ioc";
import { observer } from "mobx-react";
import * as React from "react";
import { withRouter } from "react-router";
import styled from "styled-components";
import { $ReturnGoodsListMv } from "./$returned-goods-list-mv";
import { NoGoods } from "../../components/no-goods/no-goods";
import { Spin } from "antd";
import { $CartType } from "../../classes/const/$cart-type";
import { ScrollAbilityWrapComponent } from "../../components/scroll-ability/wrap";
import { LoadingTip } from "../../components/loading-marked-words";

declare let require: any;

@withRouter
@observer
class ReturnedGoodsListWrap extends React.Component<any, any> {

  @autowired($ReturnGoodsListMv)
  public $returnGoodsListMv: $ReturnGoodsListMv;

  constructor(props) {
    super(props);
    this.state = {
      pageIndex: 0,
      pageSize: 20,
      refundOrderOid: "",
      finished: true,
      isShow: false,
    };
  }

  public componentDidMount() {
    document.title = "退款明细"
    this.loadReturnedGoodsList("init");
  }

  public componentWillReceiveProps(nextProps: Readonly<any>, nextContext: any): void {
    const { isScrollEnd } = nextProps;
    console.log(isScrollEnd);
    if (isScrollEnd && isScrollEnd !== this.props.isScrollEnd) {
      this.loadData();
    }
  }

  public loadReturnedGoodsList = (sting) => {
    this.$returnGoodsListMv.showSpin();
    const { orderId } = this.props.match.params;
    const { pageIndex, pageSize } = this.state;
    this.setState({
      isShow: true,
    });
    // console.log(this.props.match.params);
    let newPageIndex = null;
    if (sting === "init") {
      newPageIndex = pageIndex;
    } else {
      newPageIndex = pageIndex + 1;
      this.setState({ pageIndex: this.state.pageIndex + 1 });
    }
    const params = { pageSize, pageIndex: newPageIndex, refundOrderOid: orderId };
    this.$returnGoodsListMv.fetchReturnedGoodsList(params).then((bloon) => {
      this.setState({ finished: bloon, isShow: false });
      const { loadingEnd } = this.props;
      loadingEnd && loadingEnd(bloon);
    });
  }

  public loadData = () => {
    if (this.state.finished) {
      return;
    }
    this.loadReturnedGoodsList("concat");
  }

  public render() {
    const { goodsList, isSpin, refundedAmount } = this.$returnGoodsListMv;
    const { finished, isShow } = this.state;
    console.log(this.state);
    return (
      <GoodsListWapper className="goodsListPage">
        <Spin spinning={isSpin}>
          <div className="goods-list-header">
            <div>已退货商品</div>
            <div>已退款 <span style={{ color: "#FF4242" }}>¥ {refundedAmount}</span></div>
          </div>
          <GoodsListContent className="goods-list-content">
            {
              goodsList && goodsList.length > 0 &&
              goodsList.map((item, index) => {
                const { productSkuCode, productSkuName, productImgUrl, quantity, docTime, price } = item;
                return (
                  <div className="product-item" key={productSkuCode + index}>
                    <div className="content-left">
                      {
                        productImgUrl ?
                          <img src={productImgUrl} alt=""/>
                          :
                          <img src="https://order.fwh1988.cn:14501/static-img/scm/ico-nopic.png" alt=""/>
                      }
                    </div>
                    <div className="content-right">
                      <div className="product-title">{productSkuName}</div>
                      <div className="product-code">退货入库日期：{docTime}</div>
                      <div className="product-code">退货单价：{price}</div>
                      <span>x{quantity}</span>
                    </div>
                  </div>
                )
              })
            }
          </GoodsListContent>
          {
            goodsList && goodsList.length > 0 &&
            <LoadingTip
              isFinished={finished}
              isLoad={isShow}
            />
          }
        </Spin>
      </GoodsListWapper>
    );
  }
}

const ReturnedGoodsList = ScrollAbilityWrapComponent(ReturnedGoodsListWrap);
export default ReturnedGoodsList;

const GoodsListContent = styled.div`// styled
  & {
    width: 100%;
    height: auto;
    background-color: #fff;

    .product-item {
      height: 100px;
      padding: 12px 16px 10px;
      display: flex;

      > div {
        display: inline-block;
        float: left;
      }

      .content-left {
        width: 80px;
        height: 78px;
        margin-right: 10px;

        img {
          width: 100%;
          height: 100%;
        }
      }

      .content-right {
        padding-right: 83px;
        width: 100%;
        flex: 1;
        position: relative;

        .product-title {
          max-height: 32px;
          font-size: 12px;
          line-height: 16px;
          color: #333333;
          width: 100%;
          overflow: hidden;
          text-overflow: ellipsis;
          display: -webkit-box;
          -webkit-line-clamp: 2;
          -webkit-box-orient: vertical;
        }

        .product-code {
          font-size: 10px;
          color: #666666;
          margin-top: 10px;
        }

        span {
          position: absolute;
          top: 0;
          right: 0;
          font-size: 12px;
          color: rgba(102, 102, 102, 1);
        }
      }
    }
  }
`;

const GoodsListWapper = styled.div`// styled
  & {
    width: 100%;
    height: auto;
    min-height: calc(${document.documentElement.clientHeight}px);
    background: rgba(242, 242, 242, 1);

    .goods-list-header {
      width: 100%;
      height: 40px;
      border-bottom: 1px solid #D8D8D8;
      line-height: 40px;
      color: #333333;
      padding: 0 16px;
      background-color: #fff;
      overflow: hidden;

      > div:first-of-type {
        display: inline-block;
        float: left;
      }

      > div:nth-of-type(2) {
        display: inline-block;
        float: right;

        > span {
          font-size: 16px;
          margin-left: 2px;
        }
      }
    }

    .goods-list-content {

    }

    .common-bottomTotal {
      width: 100%;
      text-align: center;
      //padding-top: 10px;
    }
  }
`;
