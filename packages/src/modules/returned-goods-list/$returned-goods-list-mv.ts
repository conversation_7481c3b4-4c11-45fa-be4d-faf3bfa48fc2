import { autowired, bean } from "@classes/ioc/ioc";
import { action, observable } from "mobx";
import { $ComponentService } from "../../classes/service/$component-service";

@bean($ReturnGoodsListMv)
export class $ReturnGoodsListMv {
  @autowired($ComponentService)
  public $componentService: $ComponentService;

  @observable public goodsList: any;

  @observable public isSpin: boolean = false;

  @observable public orderPriceViewPermission: string;

  @observable public retailPriceViewPermission: string;

  @observable public refundedAmount: number;

  @observable public itemCount: number;

  @action
  public showSpin() {
    this.isSpin = true;
  }

  @action
  public hideSpin() {
    this.isSpin = false;
  }

  @action
  public fetchReturnedGoodsList(params) {
    return new Promise((resolve) => {
      this.$componentService.queryReturnedGoodsList(params).then((data) => {
        const { pageIndex } = params;
        const { itemCount, refundOrder } = data;
        const { refundOrderOid, refundOrderDocNo, itemList, refundedAmount } = refundOrder;
        this.itemCount = itemCount;
        this.refundedAmount = refundedAmount;
        if (pageIndex > 0) {
          this.goodsList = this.goodsList.concat(itemList);
        } else {
          this.goodsList = itemList;
        }
        resolve(this.goodsList.length >= this.itemCount);
        this.hideSpin();
      }).then(() => { this.hideSpin(); });
    })
  }
}
