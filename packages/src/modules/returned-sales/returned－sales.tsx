import * as React from "react";
import { withRouter } from "react-router";
import { observer } from "mobx-react";
import { autowired } from "@classes/ioc/ioc";
import { $ReturnedSalesMv } from "./$returned-sales-mv";
import styled from "styled-components";
import { NoGoods } from "../../components/no-goods/no-goods";
import { Spin, } from "antd";
import { SITE_PATH } from "../app";
import $OrderStatusType from "../../classes/const/$order-status-type";
import { ScrollAbilityWrapComponent } from "../../components/scroll-ability/wrap";
import { LoadingTip } from "../../components/loading-marked-words";
import { Button, Toast } from "antd-mobile";
import { gaEvent } from "../../classes/website-statistics/website-statistics";
import { $AppStore, AppStoreKey } from "../../classes/stores/app-store-mv";

@withRouter
@observer
class ReturnedSalesWrap extends React.Component<any, any> {
  @autowired($AppStore)
  public $AppStore: $AppStore;
  @autowired($ReturnedSalesMv)
  public $myMv: $ReturnedSalesMv;

  constructor(props) {
    super(props);
    this.state = {

    };
  }
  // 离开记录滚动高度
  // public componentWillUnmount(): void {
  //   this.$myMv.scrollHeight = $(".scroll-ability-wrap").scrollTop();
  //   this.$AppStore.savePageMv(AppStoreKey.RETURNEDSALES, this.$myMv);
  // }
  public componentDidMount() {
    document.title = "退货单列表";
    gaEvent("退货单列表");
    // setTimeout(() => {
    //   const oldData = this.$AppStore.getPageMv(AppStoreKey.RETURNEDSALES);
    //   if (oldData) {
    //     this.$myMv.queryOldData(JSON.parse(oldData));
    //     this.$AppStore.clearPageMv(AppStoreKey.RETURNEDSALES);
    //     $(".scroll-ability-wrap").scrollTop(this.$myMv.scrollHeight);
    //   } else {
    //     this.$myMv.clearMVData();
    //     this.initPage();
    //   }
    // }, 50);
    this.$myMv.pageIndex = 0;
    this.initPage();
  }
  public initPage = () => {
    const { state } = this.props.location;
    let orgId = null;
    let orderPartyName = null;
    if (state && state.orgId) {
      orgId = state.orgId;
      orderPartyName = state.orderPartyName;
      this.$myMv.orgId = orgId;
      this.$myMv.orderPartyName = orderPartyName;
    }
    const { itemShowCount, pageSize, pageIndex } = this.$myMv;
    const params = {
      itemShowCount,
      orgId: orgId ? orgId : null,
      pageIndex,
      pageSize,
    };
    this.$myMv.showSpin();
    const { loadingEnd } = this.props;
    this.$myMv.fetchReturnedSalesList(params)
      .then(() => loadingEnd && loadingEnd())
      .catch(() => loadingEnd && loadingEnd());
  }
  public componentWillReceiveProps(nextProps: Readonly<any>, nextContext: any): void {
    const { isScrollEnd } = nextProps;
    if (isScrollEnd && isScrollEnd !== this.props.isScrollEnd) {
      this.loadData();
    }
  }

  public loadData = () => {
    const { pageSize, itemShowCount, orgId, finished, pageIndex } = this.$myMv;
    if (finished) {
      return;
    }
    this.$myMv.isLoad = true;
    const params = {
      itemShowCount,
      orgId: orgId ? orgId : null,
      pageIndex,
      pageSize,
    };
    const { loadingEnd } = this.props;
    this.$myMv.fetchReturnedSalesList(params)
      .then(() => loadingEnd && loadingEnd())
      .catch(() => loadingEnd && loadingEnd());
  }

  public toReturnedDetail(oid) {
    const { orgId, orderPartyName } = this.$myMv;
    this.props.history.push({
      pathname: `/${SITE_PATH}/returned-detail/${oid}`,
      state: {
        orderPartyName,
        orgId,
      },
    });
  }
  public fillInLogistics(refundOrderOid) {
    const { orgId, orderPartyName } = this.$myMv;
    this.props.history.push({
      pathname: `/${SITE_PATH}/fill-in-logistics/${refundOrderOid}`,
      state: {
        orderPartyName,
        orgId,
      },
    });
  }

  public getStatusColor(docStatusCode) {
    let colorObj = {};
    const { orderStatusTypeList } = $OrderStatusType;
    orderStatusTypeList.map((item) => {
      if (item.statusCode === docStatusCode) {
        colorObj = item.style;
      }
    });
    return colorObj;
  }

  public goToInitiatingReturns = () => {
    const { orgId, orderPartyName } = this.$myMv;
    console.log("orgId", orgId)
    this.$AppStore.clearPageMv(AppStoreKey.INITIATINGRETURNS);
    this.$myMv.businesssControlCheck({ orgId, businessTypeControl: "ReturnGoodsControl" }).then(res => {
      console.log("111", res)
      if (res.errorCode == "-1") {
        Toast.info(res.errorMessage)
      } else {
        this.props.history.push({
          pathname: `/${SITE_PATH}/initiating-returns`,
          state: {
            fromWhere: orgId ? "my" : "select",
            orderPartyName,
            orgId,
          },
        });
      }
    })

  }

  public render() {
    const { returnedSalesList, isSpin, isShowConfirmRefundPermission, finished, isLoad } = this.$myMv;
    const noGoods = <NoGoods title="暂无退货单" height={document.documentElement.clientHeight} />
    return (
      <DeliveryPage style={{ color: "" }}>
        <div className={isShowConfirmRefundPermission ? "orderPage haveFixBottom" : "orderPage"}>
          <Spin spinning={isSpin}>
            <DeliveryLists>
              {
                returnedSalesList ? returnedSalesList.length > 0 ?
                  returnedSalesList.map((order, index) => {
                    const { refundOrderOid, refundOrgCode, refundOrgName, docStatusName, docStatusCode, itemList, productSkuTotalQuantity, productSkuNotShowTotalQuantity, isShowLogisticsPermission } = order;
                    const colorObj = this.getStatusColor(docStatusCode);
                    return (
                      <Delivery key={refundOrgCode + index}>
                        <div className="refund-order-header">
                          <i className="scmIconfont scm-icon-shop" />
                          <div>{refundOrgName}</div>
                          <div style={colorObj}>{docStatusName}</div>
                        </div>
                        <div className="refund-order-content">
                          {
                            itemList && itemList.length > 0 &&
                            itemList.map((item, inIndex) => {
                              const { productSkuCode, productSkuName, productImgUrl, quantity } = item;
                              return (
                                <div className="product-item" key={productSkuCode + inIndex}>
                                  <div className="content-left">
                                    {
                                      productImgUrl ?
                                        <img src={productImgUrl} alt="" />
                                        :
                                        <img src="https://order.fwh1988.cn:14501/static-img/scm/ico-nopic.png" alt="" />
                                    }
                                  </div>
                                  <div className="content-right">
                                    <div className="product-title">{productSkuName}</div>
                                    <div className="product-code">货号：{productSkuCode}</div>
                                    <span>x{quantity}</span>
                                  </div>
                                </div>
                              );
                            })
                          }
                        </div>
                        <div className="refund-order-foot">
                          {
                            productSkuNotShowTotalQuantity > 0 ?
                              <div className="remain-conduct"><span>{`共 ${productSkuTotalQuantity} 件商品`}</span>还有<span
                                style={{ color: "#FF3030" }}>{` ${productSkuNotShowTotalQuantity} `}</span>件商品</div>
                              :
                              <div className="remain-conduct"><span>{`共 ${productSkuTotalQuantity || 0} 件商品`}</span></div>
                          }
                          <div className="button-list">
                            {
                              isShowLogisticsPermission &&
                              <div className="fill-in-logistics" onClick={() => this.fillInLogistics(refundOrderOid)}>填写物流</div>
                            }
                            <div className="get-more-info" onClick={() => this.toReturnedDetail(refundOrderOid)}>查看详情</div>
                          </div>
                        </div>
                      </Delivery>
                    );
                  })
                  : noGoods : noGoods
              }
              {
                returnedSalesList && returnedSalesList.length > 0 &&
                <LoadingTip
                  isFinished={finished}
                  isLoad={isLoad}
                />
              }
            </DeliveryLists>
          </Spin>
          {
            isShowConfirmRefundPermission &&
            <Confirmbtn>
              <Button type={"primary"} onClick={this.goToInitiatingReturns}>发起退货</Button>
            </Confirmbtn>
          }
        </div>
      </DeliveryPage>
    );
  }

}

const ReturnedSales = ScrollAbilityWrapComponent(ReturnedSalesWrap)
export default ReturnedSales;

const DeliveryPage = styled.div`// styled
  & {
    width: 100%;
    height: auto;
    background: #F2F2F2;
    .orderPage{
      width: 100%;
      height: auto;
      background: #F2F2F2;
      .common-bottomTotal {
        width: 100%;
        text-align: center;
        //padding-top: 10px
      }
    }
    .haveFixBottom{
      padding-bottom: 66px;
    }
  }
`;

const Confirmbtn = styled.div`// styled
  & {
    width: 100%;
    height: 66px;
    position: fixed;
    bottom: 0;
    padding: 12px 16px;
    background: #fff;
    .am-button-primary {
      span {
        font-size: 16px;
        font-family: SourceHanSansCN-Normal, SourceHanSansCN;
        font-weight: 400;
        color: rgba(255, 255, 255, 1);
      }
    }
  }
`;

const DeliveryLists = styled.div`// styled
  & {
    width: 100%;
    height: auto;
    background-color: #F2F2F2;
    .bottom-line {
      text-align: center;
    }
  }
`;

const Delivery = styled.div`// styled
  & {
    width: 100%;
    background-color: #fff;
    box-sizing: border-box;
    margin-bottom: 10px;

    .refund-order-header {
      border-bottom: 1px solid #d8d8d8;
      padding-left: 38px;
      position: relative;
      display: flex;
      height: 41px;
      line-height: 40px;

      i {
        position: absolute;
        display: inline-block;
        font-size: 16px;
        color: #307DCD;
        //top: 13px;
        left: 16px;
        width: 16px;
        height: 14px;
      }

      div:first-of-type {
        flex: 1;
        font-size: 14px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }

      div:last-of-type {
        width: 94px;
        font-size: 13px;
        float: right;
        padding-right: 16px;
        text-align: right;
      }
    }

    .refund-order-content {
      width: 100%;
      height: auto;

      .product-item {
        height: 100px;
        padding: 12px 16px 10px;
        display: flex;

        > div {
          display: inline-block;
          float: left;
        }

        .content-left {
          width: 80px;
          height: 78px;
          margin-right: 10px;

          img {
            width: 100%;
            height: 100%;
          }
        }

        .content-right {
          padding-right: 83px;
          width: 100%;
          flex: 1;
          position: relative;

          .product-title {
            max-height: 32px;
            font-size: 12px;
            line-height: 16px;
            color: #333333;
            width: 100%;
            overflow: hidden;
            text-overflow: ellipsis;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
          }

          .product-code {
            font-size: 10px;
            color: #666666;
            margin-top: 10px;
          }

          span {
            position: absolute;
            top: 0;
            right: 0;
            font-size: 12px;
            color: rgba(102, 102, 102, 1);
          }
        }
      }
    }

    .refund-order-foot {
      width: 100%;
      height: 83px;
      text-align: right;
      font-family: SourceHanSansCN-Normal;
      font-weight: 400;

      .remain-conduct {
        height: 33px;
        line-height: 30px;
        padding-right: 16px;
        border-bottom: 1px solid rgba(216, 216, 216, 1);
        font-size: 13px;
        color: #666666;

        span:first-of-type {
          margin-right: 16px;
        }
      }

      .button-list {
        height: 50px;
        padding-right: 16px;
        line-height: 50px;
        .fill-in-logistics,.get-more-info {
          border-radius: 3px;
          border: 1px solid rgba(48, 125, 205, 1);
          display: inline-block;
          padding: 9px 12px;
          font-size: 12px;
          line-height: 12px;
          color: rgba(48, 125, 205, 1);
        }
        .fill-in-logistics{
          margin-right: 8px;
        }
      }
    }

    //> div:nth-of-type(1) {
    //  position: relative;
    //  border-bottom: 1px solid #D8D8D8;
    //  padding: 0 10px;
    //  height: 40px;
    //  line-height: 40px;
    //  color: #333333;
    //  font-size: 14px;
    //  span {
    //    float: left;
    //  }
    //  > span:last-child {
    //    float: right;
    //    color: #FF3030;
    //    font-size: 12px;
    //  }
    //}
    //> div:nth-of-type(2) {
    //  position: relative;
    //  border-bottom: 1px solid #D8D8D8;
    //  padding: 10px 10px 0px 10px;
    //  font-size: 12px;
    //  > p {
    //    margin-bottom: 10px;
    //    > span:nth-of-type(1) {
    //      color: #999999;
    //    }
    //    > span:nth-of-type(2) {
    //      color: #2A2A2A;
    //    }
    //  }
    //}
    //> div:nth-of-type(3) {
    //  position: relative;
    //  padding: 10px;
    //  height: 60px;
    //  > span {
    //    float: right;
    //    color: #307DCD;
    //    border: 1px solid #307DCD;
    //    border-radius: 5px;
    //    width: 72px;
    //    height: 30px;
    //    padding: 5px;
    //    margin-right: 5px;
    //  }
    //  > span:nth-of-type(1) {
    //    color: #FF3030;
    //    border: 1px solid #FF3030;
    //  }
    //}
  }
`;
