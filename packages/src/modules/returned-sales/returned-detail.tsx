import { autowired } from "@classes/ioc/ioc";
import { List, Toast } from "antd-mobile";
import { observer } from "mobx-react";
import * as React from "react";
import { withRouter } from "react-router";
import styled from "styled-components";
import { $ReturnedSalesMv } from "./$returned-sales-mv";
import $OrderStatusType from "../../classes/const/$order-status-type";

import { SITE_PATH } from "../app";
import { gaEvent } from "../../classes/website-statistics/website-statistics";

declare let window: any;
const Item = List.Item;

@withRouter
@observer
class ReturnedDetail extends React.Component<any, any> {
  @autowired($ReturnedSalesMv)
  public $returnedSalesMv: $ReturnedSalesMv;

  constructor(props) {
    super(props);
    this.state = {
      orderPartyName: "",
      orgId: "",
    };
  }

  public componentDidMount() {
    document.title = "退货单详情";
    gaEvent("退货单详情");
    const params = {
      refundOrderOid: this.props.match.params.oid,
    };
    const { orderPartyName, orgId } = this.props.location.state || {};
    this.setState({ orderPartyName, orgId })
    this.$returnedSalesMv.fetchReturnedDetail(params);
  }
  public toGoodList() {
    this.props.history.push({ pathname: `/${SITE_PATH}/returned-goods-list/${this.props.match.params.oid}/RefundOrder` });
  }
  public showMoreReturnedSalesList() {
    this.$returnedSalesMv.showMoreReturnedSalesList();
  }
  public getReturnedStatusIcon(status) {
    const { returnedStatusCode } = $OrderStatusType;
    const { WAITCUSTOMSERVICEADUIT, WAITSENDGOODS, WAITWAREHOUSECONFIRMGOODS, WAITFINANCIALCONFIRMREFUNDFEE, REFUNDFEEFINASH, REFUNDFEECLOSED } = returnedStatusCode;
    let iconClass = "";
    switch (status) {
      case WAITCUSTOMSERVICEADUIT:
        iconClass = "scm-icon-shenhe";
        break;
      case WAITSENDGOODS:
        iconClass = "scm-icon-tuihuoshenqingdanhao"
        break;
      case WAITWAREHOUSECONFIRMGOODS:
        iconClass = "scm-icon-shouhuo"
        break;
      case WAITFINANCIALCONFIRMREFUNDFEE:
        iconClass = ""
        break;
      case REFUNDFEEFINASH:
        iconClass = "scm-icon-wancheng"
        break;
      case REFUNDFEECLOSED:
        iconClass = "scm-icon-zuofei"
        break;
      default:
        break;
    }
    return iconClass;
  }
  public copyRefundOrderDocNo() {
    const range = document.createRange();
    range.selectNode(document.getElementById("refundOrderDocNo"));
    const selection = window.getSelection();
    if (selection.rangeCount > 0) {
      selection.removeAllRanges();
    }
    selection.addRange(range);
    document.execCommand("copy");
    Toast.info("复制成功", .5);
  }
  public fillInLogistics(refundOrderOid) {
    const { orgId, orderPartyName } = this.state;
    this.props.history.push({
      pathname: `/${SITE_PATH}/fill-in-logistics/${refundOrderOid}`,
      state: {
        orderPartyName,
        orgId,
      },
    });
  }
  public render() {
    const { returnedDetail, returnedSalesShowList, returnedSalesList } = this.$returnedSalesMv;
    const { WAITSENDGOODS, WAITWAREHOUSECONFIRMGOODS } = $OrderStatusType.returnedStatusCode;
    return (
      <OrderDetailPage>
        {
          returnedDetail &&
            <div className="order-detail-wrap" style={{ paddingBottom: `${returnedDetail.isShowLogisticsPermission ? "60px" : ""}`}}>
              <div className="order-detail-header">
                <div className="order-status">
                  <i className={`scmIconfont ${this.getReturnedStatusIcon(returnedDetail.docStatusCode)}`} />
                  <div>{returnedDetail.docStatusName}</div>
                  <div>{returnedDetail.operateTime}</div>
                </div>
                {
                  returnedDetail.auditMemo &&
                  <div className="order-processing-results">
                    <span>处理结果:</span>
                    <div>{returnedDetail.auditMemo}</div>
                  </div>
                }
                <div className="order-refund-warp">
                  {
                    returnedDetail.refundedAmount > 0 &&
                    <div className="order-refund-detail" onClick={() => {this.toGoodList()}}>
                      <div className="refund-price-show">
                        <div>已退款：<span style={{color: "##FF4242", fontSize: "16px"}}>¥ {returnedDetail.refundedAmount}</span></div>
                        <div>
                          {/*<i className="scmIconfont icon-tishi icon-test" />*/}
                          <span className="scm-icon-test">i</span>
                          <span>退款已退至您的余额账户</span>
                        </div>
                      </div>
                      <div className="refund-price-detail">查看退款明细<i className="scmIconfont scm-icon-jiantou-you" /></div>
                    </div>
                  }
                  {
                    (returnedDetail.docStatusCode === WAITWAREHOUSECONFIRMGOODS || returnedDetail.docStatusCode === WAITSENDGOODS) && returnedDetail.waitRefundQuantity > 0 &&
                    <div className="remain-goods-not-in-storage">还有 <span>{returnedDetail.waitRefundQuantity}</span> 件商品未退入仓库 </div>
                  }
                </div>
              </div>
              <div className="order-detail-content">
                <div className="refund-goods-total-quantity">申请退货的商品 <span>共 {returnedDetail.productSkuTotalQuantity} 件商品</span></div>
                <div className="cut-off-rule" />
                {
                  returnedSalesShowList && returnedSalesShowList.length > 0 &&
                  <div className="refund-product-list">
                    {
                      returnedSalesShowList.map((obj, index) => {
                        const {productSkuCode, productImgUrl, productSkuName, quantity} = obj;
                        return <div className="product-item" key={productSkuCode + index}>
                          <div className="content-left">
                            {
                              productImgUrl ? <img src={productImgUrl} alt=""/> :
                                <img src="https://order.fwh1988.cn:14501/static-img/scm/ico-nopic.png" alt=""/>
                            }
                          </div>
                          <div className="content-right">
                            <div className="product-title">{productSkuName}</div>
                            <div className="product-code">货号：{productSkuCode}</div>
                            <span>x{quantity}</span>
                          </div>
                        </div>
                      })
                    }
                  </div>
                }
                {
                  returnedSalesShowList && returnedSalesList && returnedSalesList.length > returnedSalesShowList.length &&
                  <div className="get-more-goods" onClick={() => {this.showMoreReturnedSalesList()}}>
                    查看所有商品
                    <i className="scmIconfont scm-icon-jiantou-shang" />
                  </div>
                }
              </div>
              <ul className="order-detail-foot">
                <li><span>退货单号：</span> <span id="refundOrderDocNo">{returnedDetail.refundOrderDocNo}</span> <span onClick={() => this.copyRefundOrderDocNo()}>复制</span></li>
                <li><span>申请时间：</span> {returnedDetail.applyTime}</li>
                <li><span>退货门店：</span> {returnedDetail.refundOrgName}</li>
                <li><span>退货类型：</span> {returnedDetail.refundOrderTypeName}</li>
              </ul>
              {
                returnedDetail.isShowLogisticsPermission &&
                  <div className="operate-button-list">
                    <div className="fill-in-logistics" onClick={() => this.fillInLogistics(returnedDetail.refundOrderOid)}>填写物流</div>
                  </div>
              }
            </div>
        }
      </OrderDetailPage>
    );
  }
}

export default ReturnedDetail;
// const smallColor = #666666
const OrderDetailPage = styled.div`// styled
  & {
    width: 100%;
    min-height: 100%;
    .order-detail-wrap{
      width: 100%;
      min-height: 100%;
      box-sizing: border-box;
      overflow-y: auto;
      background-color: #F2F2F2;
      font-size: 14px;
      color: #333333;
      .cut-off-rule{
        width: 100%;
        height:1px;
        background-color: #D8D8D8;
      }
      .order-detail-header{
        width: 100%;
        height: auto;
        >div{
          width: 100%;
          background-color: #fff;
        }
        .order-status{
          height: 70px;
          padding: 16px 0 0 68px;
          position: relative;
          //border: 1px dashed #333;
          background-image: url("https://order.fwh1988.cn:14501/static-img/scm/img_bg_returned_status.png");
          -webkit-background-size: 100% 70px;background-size: 100% 70px;
          background-repeat: no-repeat;
          background-position: center center;
          i{
            border:1px solid #307DCD;
            color: #307DCD;
            position: absolute;
            top: 15px;
            left: 16px;
            display: inline-block;
            width: 40px;
            height: 40px;
            text-align: center;
            line-height: 40px;
            border-radius: 20px;
            &:before{
              font-size: 18px;
            }
          }
          div:first-of-type{
            font-size: 16px;
            height: 16px;
            line-height: 16px;
            margin-bottom: 8px;
          }
          div:nth-of-type(2){
            color: #666666;
          }
        }
        .order-processing-results{
          height: auto;
          padding: 11px 16px 13px 86px;
          position: relative;
          line-height:18px;
          border-top: 1px solid #D8D8D8;
          span{
            position: absolute;
            top: 11px;
            left: 16px;
          }
        }
        .order-refund-warp{
          margin-top: 10px;
          .order-refund-detail{
            height: 64px;
            padding: 0 16px;
            border-bottom: 1px solid #D8D8D8;
            overflow: hidden;
            .refund-price-show{
              padding: 14px 0 13px;
              display: inline-block;
              float: left;
              >div:first-of-type{
                font-size: 14px;
                line-height: 16px;
                height: 16px;
              }
              >div:last-of-type{
                font-size: 13px;
                line-height: 13px;
                margin-top: 8px;
                color: #666666;
                .scm-icon-test{
                 display: inline-block;
                 width: 14px;
                 height: 14px;
                 border-radius: 7px;
                 text-align: center;
                 line-height: 12px;
                 border: 1px solid #666666;
                 font-size: 13px;
                 margin-right: 6px;
                }
              }
            }
            .refund-price-detail{
              display: inline-block;
              float: right;
              height: 100%;
              font-size: 13px;
              color: #666666;
              line-height: 64px;
              >i{
                font-size: 12px;
                margin-left: 16px;
              }
            }
          }
          .remain-goods-not-in-storage {
            height: 40px;
            color:rgba(51,51,51,1);
            line-height: 40px;
            padding-left: 16px;
            text-align: left;
            >span{
              color: #FF3030;
            }
          }
        }
      }
      .order-detail-content{
        margin-top: 10px;
        width: 100%;
        height: auto;
        background-color: #fff;
        .refund-goods-total-quantity{
          width: 100%;
          height: 40px;
          line-height: 40px;
          padding: 0 16px;
          span{
            font-size: 13px;
            color: #666666;
            float: right;
          }
        }
        .refund-product-list{
          width: 100%;
          height: auto;
          .product-item{
            height: 100px;
            padding: 12px 16px 10px;
            display: flex;
            >div{
              display: inline-block;
              float: left;
            }
            .content-left{
              width: 80px;
              height: 78px;
              margin-right: 10px;
              img{
                width: 100%;
                height: 100%;
              }
            }
            .content-right{
              padding-right: 83px;
              width: 100%;
              flex: 1;
              position: relative;
              .product-title{
                max-height: 32px;
                font-size:12px;
                line-height: 16px;
                color: #333333;
                width: 100%;
                overflow : hidden;
                text-overflow: ellipsis;
                display: -webkit-box;
                -webkit-line-clamp: 2;
                -webkit-box-orient: vertical;
              }
              .product-code{
                font-size: 10px;
                color: #666666;
                margin-top: 10px;
              }
              span{
                position: absolute;
                top: 0;
                right: 0;
                font-size: 12px;
                color:rgba(102,102,102,1);
              }
            }
          }
        }
        .get-more-goods{
          border-top: 1px solid #D8D8D8;
          height: 40px;
          font-size: 13px;
          text-align: center;
          line-height: 40px;
          i{
            font-size: 12px;
            margin-left: 12px;
            display: inline-block;
            transform: rotate(180deg);
            &:before{
              transform: rotate(180deg);
            }
          }
        }
      }
      .order-detail-foot{
        width: 100%;
        padding: 12px 16px;
        background-color: #fff;
        margin-top: 10px;
        >li{
          list-style: none;
          height: 14px;
          font-size: 12px;
          line-height: 14px;
          margin-bottom: 13px;
          position: relative;
          span:first-of-type{
            display: inline-block;
            color: #666666;
            height: 14px;
          }
          span:nth-of-type(3){
            position: absolute;
            top: 0;
            right: 0px;
            display: inline-block;
            width: 32px;
            height: 16px;
            text-align: center;
            line-height: 16px;
            font-size: 10px;
            background-color:rgba(255,255,255,1);
            border-radius:2px;
            border:1px solid rgba(48,125,205,1);
            color: rgba(48,125,205,1);
          }
        }
        >li:last-of-type{
          margin-bottom: 0;
        }
      }
      .operate-button-list{
        width: 100%;
        height: 48px;
        background: #ffffff;
        padding: 9px 12px;
        text-align: right;
        position: fixed;
        bottom: 0;
        left: 0;
        z-index: 99;
        .fill-in-logistics {
          border-radius: 3px;
          border: 1px solid rgba(48, 125, 205, 1);
          display: inline-block;
          padding: 8px 12px;
          font-size: 12px;
          line-height: 12px;
          color: rgba(48, 125, 205, 1);
        }
      }
    }
  }
`;
