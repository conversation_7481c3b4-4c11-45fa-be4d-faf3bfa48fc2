import { autowired, bean } from "@classes/ioc/ioc";
import { action, observable } from "mobx";
import { $ComponentService } from "../../classes/service/$component-service";
import { beanMapper } from "../../helpers/bean-helpers";

@bean($ReturnedSalesMv)
export class $ReturnedSalesMv {
  @autowired($ComponentService)
  public $componentService: $ComponentService;
  @observable public pageIndex: number = 0;
  @observable public pageSize: number = 20;
  @observable public itemShowCount: number = 3;
  @observable public scrollHeight: number = 0;
  @observable public orgId = null;
  @observable public orderPartyName: string;
  @observable public totalSalesCount: number;
  @observable public returnedSalesList: any[];
  @observable public returnedSalesShowList: any[];
  @observable public returnedDetail: object;
  @observable public isSpin: boolean = false;
  @observable public finished: boolean = false;
  @observable public isLoad: boolean = false;
  @observable public isShowConfirmRefundPermission: boolean = false;
  @action
  public showSpin() {
    this.isSpin = true;
  }

  @action
  public hideSpin() {
    this.isSpin = false;
  }

  @action
  public fetchReturnedSalesList(params) {
    return new Promise((resolve, reject) => {
      this.$componentService.queryReturnedSalesList(params).then((data) => {
        const {pageIndex} = params;
        if (!pageIndex) {
          this.returnedSalesList = [];
        }
        const {itemCount, refundOrderList, isShowConfirmRefundPermission } = data;
        this.totalSalesCount = itemCount;
        this.isShowConfirmRefundPermission = isShowConfirmRefundPermission;
        this.returnedSalesList = [...this.returnedSalesList, ...refundOrderList];
        this.setPageIndex();
        this.isLoad = false;
        this.finished = this.returnedSalesList.length >= itemCount;
        this.hideSpin();
        resolve();
      }).catch(() => {
        this.hideSpin();
        this.isLoad = false;
        reject();
      });
    });
  }

//校验业务参数管控
@action
public businesssControlCheck(params){
  return this.$componentService.businesssCtroCheck(params)
}
  @action
  public setPageIndex() {
    this.pageIndex += 1;
  }

  @action
  public initPageIndex() {
    this.pageIndex = 0;
  }
  @action
  public showMoreReturnedSalesList() {
    this.returnedSalesShowList = this.returnedSalesList;
  }
  @action
  public fetchReturnedDetail(params) {
    this.$componentService.queryReturnedDetail(params).then((data) => {
      this.returnedDetail = data.refundOrder;
      const {itemList} = data.refundOrder;
      this.returnedSalesList = itemList;
      if (itemList.length <= 3) {
        this.returnedSalesShowList = itemList;
      } else if (itemList.length > 3) {
        this.returnedSalesShowList = itemList.slice(0, 3);
        console.log(itemList.slice(0, 3));
      }
    });
  }
  @action
  public queryOldData(obj) {
    beanMapper(obj, this);
  }
  @action
  public clearMVData() {
    this.pageIndex = 0;
    this.pageSize = 20;
    this.itemShowCount = 3;
    this.scrollHeight = 0;
    this.orgId = null;
    this.orderPartyName = "";
    this.totalSalesCount = 0;
    this.returnedSalesList = [];
    this.returnedSalesShowList = [];
    this.returnedDetail = {};
    this.isSpin = false;
    this.finished = false;
    this.isLoad = false;
    this.isShowConfirmRefundPermission = false;
  }
}
