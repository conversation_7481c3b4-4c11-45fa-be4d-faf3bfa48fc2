import { autowired } from "@classes/ioc/ioc";
import { List } from "antd-mobile";
import { observer } from "mobx-react";
import React from "react";
import { withRouter } from "react-router";
import { SITE_PATH } from "../app";
import { $AddressMv } from "../submit-address-form/submit-address-mv";
import { $CustomerEmployeeMv } from "./$customer-employee-mv";

declare let window: any;
const Item = List.Item;

@withRouter
@observer
export class CustomerEmployee extends React.Component<any, any> {
  @autowired($CustomerEmployeeMv)
  public $customerEmployeeMv: $CustomerEmployeeMv;

  @autowired($AddressMv)
  public $AddressMv: $AddressMv;

  public componentDidMount() {
    this.$customerEmployeeMv.fetchCustomerEmployee();
  }

  public setCustomerEmployee(name, mobile, contactPersonId) {
    this.$AddressMv.setCustomerEmployee(name, mobile, contactPersonId);
    this.props.history.push({ pathname: `/${SITE_PATH}/newAddress` });
  }

  public renderList = (list) => {
    if (!this.$customerEmployeeMv.customerEmployeeList) {
      return false;
    }
    return this.$customerEmployeeMv.customerEmployeeList.map((item, index) => {
      return (
        <Item
          key={index}
          arrow="horizontal"
          extra={item.mobile}
          onClick={() => this.setCustomerEmployee(item.name, item.mobile, item.oid)}
        >
          {item.name}
        </Item>
      );
    });
  }

  public render() {
    return (
      <div className="iScroll">
        {/*<ReactIScroll
          iScroll={iScroll}
          options={{
            mouseWheel: true,
          }}
        >
          <List>
            {this.renderList(this.$customerEmployeeMv.customerEmployeeList)}
          </List>
        </ReactIScroll>*/}
      </div>
    );
  }
}
