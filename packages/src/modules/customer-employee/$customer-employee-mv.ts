import {autowired, bean} from "@classes/ioc/ioc";
import {action, observable} from "mobx";
import {$ComponentService} from "../../classes/service/$component-service";

@bean($CustomerEmployeeMv)
export class $CustomerEmployeeMv {
  @autowired($ComponentService)
  public $componentService: $ComponentService;
  @observable public customerEmployeeList: any;

  @action
  public fetchCustomerEmployee() {
    return this.$componentService.queryCustomerEmployee().then((data) => {
      const {customerEmployeeList} = data;
      this.customerEmployeeList = customerEmployeeList;
    });
  }
}
