import { autowired, bean } from "@classes/ioc/ioc";
import { action, observable } from "mobx";
import { $ComponentService } from "../../classes/service/$component-service";
import { beanMapper } from "../../helpers/bean-helpers";
import { $MyInfoService } from "@classes/service/$my-info-service";
import { $SearchKey } from "@classes/const/$search-key";
import { $SearchDateType } from "@classes/const/$search-date-type";
import { $SearchTimeType } from "@classes/const/$search-time-type";
import { $CreditAccount } from "@classes/entity/$credit-account";

@bean($NewModeDebtAccountInfoMv)
export class $NewModeDebtAccountInfoMv {

  @autowired($MyInfoService)
  public $myInfoService: $MyInfoService;

  @autowired($ComponentService)
  public $componentService: $ComponentService;

  @observable public isSpin: boolean = false;

  @observable public isFinished: boolean = false;

  @observable public isLoading: boolean = false;

  @observable public totalWaitRepayAmount: number = 0;

  @observable public pageIndex: number = 0;

  @observable public scrollHeight: number = 0;

  @observable public pageSize: number = 10;

  @observable public key: string = $SearchKey.ALL;

  @observable public itemCount: number;

  @observable public itemList: any[];

  @observable public creditAccountList: any[] = [];

  @observable public businessType: any[] = []; // 选择业务类型

  @observable public startTime: string = $SearchDateType.FirstDayOfMonth;

  @observable public endTime: string = $SearchDateType.LastDayOfMonth;

  @observable public dateType: number = $SearchTimeType.MONTH;

  @observable public allSurplusToReturnAmount: number = 0;

  @observable public selectSurplusToReturnAmount: number = 0;

  @observable public selectCreditAccountList: any[] = [];

  @observable public automaticType: string;

  @observable public configId: string;

  @observable public effectiveCreditAccount: number;

  @observable public changeAmount: number = 0;

  @action
  public showSpin() {
    this.isSpin = true;
  }

  @action
  public hideSpin() {
    this.isSpin = false;
  }

  @action
  public fetchAccountInfo(params, isConcat?) {
    return new Promise((resolve, reject) => {
      this.$myInfoService.loadNoLimitCredit(params).then((data) => {
        this.isLoading = false;
        const { totalWaitRepayAmount, itemList, itemCount, changeAmount, errorCode } = data;
        this.totalWaitRepayAmount = totalWaitRepayAmount;
        this.itemCount = itemCount;
        this.changeAmount = changeAmount;
        this.itemList = isConcat ? this.itemList.concat(itemList) : itemList;
        this.isFinished = this.itemList.length >= itemCount;
        this.hideSpin();
        resolve(this.isFinished);
      }).catch((err) => {
        reject(err);
      });
    });
  }

  @action
  public checkTongLianPaymentStatus(params) {
    return this.$componentService.checkTongLianPaymentStatus(params);
  }

  @action
  public cancelTongLianPay(params) {
    return this.$componentService.cancelTongLianPay(params);
  }

  @action
  public checkContinuePay(params) {
    return this.$componentService.checkContinuePay(params);
  }

  @action
  public queryCreditrepaymentAuditedAmount(params) {
    return this.$componentService.queryCreditrepaymentAuditedAmount(params);
  }

  @action
  public fetchCreditAccountList(params) {
    this.$componentService.queryCreditaccountListLoad(params).then((data) => {
      this.allSurplusToReturnAmount = data.allSurplusToReturnAmount;
      this.automaticType = data.automaticType;
      this.configId = data.configId;
      this.effectiveCreditAccount = data.effectiveCreditAccount;
      this.creditAccountList = data.creditAccountList.map((credit) => new $CreditAccount(credit));
      data.creditAccountList.filter((account) => account.surplusToReturnAmount !== 0).map((val) => {
        this.selectCreditAccountList.push({
          accountId: val.accountId,
          isMulti: val.isMulti,
          seqNo: val.seqNo,
          planId: val.planId,
        });
        this.selectSurplusToReturnAmount += val.surplusToReturnAmount;
      });
    });
  }

  @action
  public queryOldData(obj) {
    beanMapper(obj, this);
    console.log(this);
  }

  @action
  public clearMVData() {
    this.isSpin = false;
    this.isFinished = false;
    this.isLoading = false;
    this.scrollHeight = 0;
    this.pageIndex = 0;
    this.pageSize = 10;
    this.key = $SearchKey.ALL;
    this.itemList = [];
  }

  @action
  public resetItemList() {
    this.isSpin = false;
    this.isFinished = false;
    this.isLoading = false;
    this.pageIndex = 0;
    this.itemList = [];
  }

}
