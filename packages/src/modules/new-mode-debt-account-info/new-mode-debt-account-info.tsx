import { autowired } from "@classes/ioc/ioc";
import { observer } from "mobx-react";
import { transaction } from "mobx";
import React from "react";
import { withRouter } from "react-router";
import styled from "styled-components";
import { $NewModeDebtAccountInfoMv } from "./new-mode-debt-account-info-mv";
import { NoGoods } from "../../components/no-goods/no-goods";
import { SITE_PATH } from "../app";
import { Spin } from "antd";
import { ScrollAbilityWrapComponent } from "../../components/scroll-ability/wrap";
import { LoadingTip } from "../../components/loading-marked-words";
import { $AppStore, AppStoreKey } from "../../classes/stores/app-store-mv";
import { CustomMonthModal } from "../../components/custom-month-modal/custom-month-modal";
import { Checkbox, Modal, Toast } from "antd-mobile";
import moment from "moment";
import RightSwiperModal from "../../components/right-swiper-modal";
import { $SearchKey, SearchKeyName } from "@classes/const/$search-key";
import { $SearchDateType } from "@classes/const/$search-date-type";
import { $SearchTimeType } from "@classes/const/$search-time-type";
import { $ValidityType } from "@classes/const/$validity-type";
import { findIndex, remove } from "lodash";
import { $OrderType } from "@classes/const/$order-type";

const SearchKeyList = [
  {
    key: $SearchKey.ALL,
    name: SearchKeyName[$SearchKey.ALL],
  },
  {
    key: $SearchKey.IN,
    name: SearchKeyName[$SearchKey.IN],
  },
  {
    key: $SearchKey.OUT,
    name: SearchKeyName[$SearchKey.OUT],
  },
];

const CheckboxItem = Checkbox.CheckboxItem;
const alert = Modal.alert;

@withRouter
@observer
class NewModeDebtAccountInfoWrap extends React.Component<any, any> {

  @autowired($NewModeDebtAccountInfoMv)
  public $myMv: $NewModeDebtAccountInfoMv;

  @autowired($AppStore)
  public $AppStore: $AppStore;

  public constructor(props) {
    super(props);
    this.state = {
      showDateModal: false, // 时间弹窗
      showBusinessModal: false, // 业务类型查询弹窗
      showRepaymentModal: false, // 立即还款弹窗
    };
  }

  public componentDidMount() {
    document.title = "欠款账户";
    this.loadCreditAccountList();
    this.initPage();
  }

  public loadCreditAccountList() {
    const params = {
      creditType: "Effective",
      validityType: $ValidityType.NEW_CREDIT,
    };
    this.$myMv.fetchCreditAccountList(params);
  }

  public componentWillUnmount() {
    this.$myMv.dateType = $SearchTimeType.MONTH;
    this.$myMv.startTime = $SearchDateType.FirstDayOfMonth;
    this.$myMv.endTime = $SearchDateType.LastDayOfMonth;
    this.$myMv.businessType = [];
    this.$myMv.pageIndex = 0;
    this.$myMv.selectSurplusToReturnAmount = 0;
  }

  public componentWillReceiveProps(nextProps: Readonly<any>, nextContext: any): void {
    const { isScrollEnd } = nextProps;
    const { showDateModal, showBusinessModal, showRepaymentModal } = this.state;
    if (isScrollEnd && isScrollEnd !== this.props.isScrollEnd && !showDateModal && !showBusinessModal && !showRepaymentModal) {
      this.loadData();
    }
  }

  public initPage = () => {
    const { accountId } = this.props.match.params || { accountId: "" };
    const { pageSize, pageIndex, key, businessType, startTime, endTime } = this.$myMv;
    this.$myMv.showSpin();
    this.$myMv.isLoading = true;
    const params = {
      accountId,
      condition: key,
      pageIndex,
      pageSize,
      businessType,
      start: startTime,
      end: endTime,
    };
    const { loadingEnd } = this.props;
    loadingEnd && loadingEnd(false);
    this.firstLoadFetchAccountInfo(params);
  }

  public firstLoadFetchAccountInfo = (params) => {
    transaction(() => {
      this.$myMv.fetchAccountInfo(params, false);
    });
  }

  public queryAccountInfo = (activeKey) => {
    this.$myMv.key = activeKey;
    this.$myMv.isFinished = false;
    this.$myMv.pageIndex = 0;
    this.initPage();
  }

  public loadData = () => {
    const { isFinished, isLoading, pageIndex } = this.$myMv;
    if (!isFinished) {
      this.$myMv.showSpin();
      if (!isLoading) {
        this.$myMv.pageIndex = pageIndex + 1;
        this.loadList();
      }
    }
  }

  public loadList = () => {
    const { pageSize, key, startTime, endTime, businessType, pageIndex } = this.$myMv;
    this.$myMv.showSpin();
    const params = {
      accountId: this.props.match.params.accountId,
      pageIndex,
      pageSize,
      businessType,
      condition: key,
      start: startTime,
      end: endTime,
    };
    this.$myMv.isLoading = true;
    const { loadingEnd } = this.props;
    this.$myMv.fetchAccountInfo(params, true).then((res) => {
      loadingEnd && loadingEnd(res);
    });
  }

  public onSearchDate = (startTime, endTime, dateType) => {
    if (moment(endTime).isBefore(startTime)) {
      Toast.info("结束时间不得大于开始时间");
      return;
    }
    this.$myMv.isFinished = false;
    this.$myMv.pageIndex = 0;
    this.$myMv.startTime = startTime;
    this.$myMv.endTime = endTime;
    this.$myMv.dateType = dateType;
    this.initPage();
  }

  public onSearchBusiness = (businessType, title?) => {
    this.setState({ showBusinessModal: false }, () => {
      this.$myMv.businessType = businessType;
      this.$myMv.isFinished = false;
      this.$myMv.pageIndex = 0;
      this.initPage();
    });
  }

  public onShowRepaymentModal = (showRepaymentModal) => {
    this.setState({ showRepaymentModal });
  }

  public onCheckedCredit = (e, credit, surplusToReturnAmount) => {
    // const { selectCreditAccountList } = this.$myMv;
    // console.log(selectCreditAccountList);
    if (e.target.checked) {
      const index = findIndex(this.$myMv.creditAccountList, { planId: credit.planId });
      this.$myMv.creditAccountList[index].checkFlag = true;
      this.$myMv.selectCreditAccountList.push({
        accountId: credit.accountId,
        isMulti: credit.isMulti,
        seqNo: credit.seqNo,
        planId: credit.planId,
      });
      this.$myMv.selectSurplusToReturnAmount = this.$myMv.selectSurplusToReturnAmount + surplusToReturnAmount;
      // console.log(this.$myMv.selectSurplusToReturnAmount);
    } else {
      const index = findIndex(this.$myMv.creditAccountList, { planId: credit.planId });
      this.$myMv.creditAccountList[index].checkFlag = false;
      remove(this.$myMv.selectCreditAccountList, (val) => val.planId === credit.planId);
      this.$myMv.selectSurplusToReturnAmount = parseFloat(Number(this.$myMv.selectSurplusToReturnAmount).toFixed(2)) - surplusToReturnAmount;
    }
  }

  public hideModel = () => {
    this.setState({ showRepaymentModal: false });
  }

  public checkTongLianPaymentStatus = () => {
    const { selectCreditAccountList, selectSurplusToReturnAmount } = this.$myMv;
    console.log("入参", selectCreditAccountList);
    if (selectSurplusToReturnAmount <= 0) {
      return;
    }
    const params = {
      creditAccountList: selectCreditAccountList,
      docType: $OrderType.CREDITACCOUNTREPAYMENT,
    };
    this.$myMv.showSpin();
    this.$myMv.checkTongLianPaymentStatus(params).then((data) => {
      console.log("data", data);
      const { errorCode, errorMsg, docInfo } = data;
      this.$myMv.hideSpin();
      if (errorCode && errorCode !== "0") {
        Toast.fail(errorMsg);
      } else if (docInfo && docInfo.oid) {
        const { oid, code, totalAmount } = docInfo;
        alert(`提示`, `你有${totalAmount.toFixed(2)}元正在付款`, [
          {
            text: "不付款了", onPress: () => {
              this.cancelTongLianPay(oid);
            },
          },
          {
            text: "去付款", onPress: () => {
              this.checkContinuePay(oid);
            },
          },
        ]);
      } else {
        // 继续主流程操作
        this.goToRepayment();
      }
    }).catch((err) => {
      console.log("checkTongLianPaymentStatus", err);
      this.$myMv.hideSpin();
    });
  }

  public goToRepayment = () => {
    const { selectSurplusToReturnAmount, selectCreditAccountList, isSpin } = this.$myMv;
    const { accountId } = this.props.match.params;
    if (isSpin) {
      return;
    }
    if (selectSurplusToReturnAmount > 0) {
      this.$myMv.showSpin();
      const params = {
        creditAccountList: selectCreditAccountList,
      };
      this.$myMv.queryCreditrepaymentAuditedAmount(params)
        .then((data) => {
          this.$myMv.hideSpin();
          if (data.message) {
            if (Number((selectSurplusToReturnAmount - data.amount).toFixed(2)) <= 0) { // 为了不跳转
              Toast.info(`${data.message}`, 5);
            } else {
              this.$myMv.selectSurplusToReturnAmount -= data.amount;
              Toast.info(`${data.message}`, 5, () => {
                localStorage.setItem("selectCreditAccountList", JSON.stringify(selectCreditAccountList));
                this.$AppStore.clearPageMv(AppStoreKey.REPAYMENTSTORAGE);
                this.pageWindowSkip(`/${SITE_PATH}/my-repayment/${this.$myMv.selectSurplusToReturnAmount && Number(this.$myMv.selectSurplusToReturnAmount).toFixed(2)}?accountId=${accountId}`);
              });
            }
          } else {
            localStorage.setItem("selectCreditAccountList", JSON.stringify(selectCreditAccountList));
            this.$AppStore.clearPageMv(AppStoreKey.REPAYMENTSTORAGE);
            this.pageWindowSkip(`/${SITE_PATH}/my-repayment/${this.$myMv.selectSurplusToReturnAmount && Number(this.$myMv.selectSurplusToReturnAmount).toFixed(2)}?accountId=${accountId}`);
          }
        }).catch(() => {
          this.$myMv.hideSpin();
        });
    } else {
      Toast.info("请选择还款金额", 3);
    }
  }

  public cancelTongLianPay = (oid) => {
    const params = {
      docType: $OrderType.CREDITACCOUNTREPAYMENT,
      oid,
    };
    this.$myMv.showSpin();
    this.$myMv.cancelTongLianPay(params).then((data) => {
      this.$myMv.hideSpin();
      console.log("cancelTongLianPay", data);
      const { errorCode, errorMessage } = data;
      if (errorCode && errorCode !== "0") {
        alert(`提示`, "显示接口里的错误信息", [
          {
            text: "我知道了", onPress: () => {
              window.location.reload();
            },
          },
        ]);
      } else {
        window.location.reload();
      }
    }).catch(() => {
      this.$myMv.hideSpin();
    });
  }

  public checkContinuePay = (oid) => {
    this.$myMv.showSpin();
    const params = {
      docType: $OrderType.CREDITACCOUNTREPAYMENT,
      oid,
    };
    this.$myMv.checkContinuePay(params).then((data) => {
      this.$myMv.hideSpin();
      const { errorCode, errorMessage, redirectUrl } = data;
      if (errorCode && errorCode !== "0") {
        Toast.fail(errorMessage);
      } else if (redirectUrl) {
        this.pageWindowSkip(`${redirectUrl}&redirectUrl=${document.location.protocol}//${document.domain}:${window.location.port}/scm/payment-result-show/allinpay`);
      } else {
        console.log(data);
      }
    }).catch(() => {
      this.$myMv.hideSpin();
    });
  }

  public pageWindowSkip = (url) => {
    // this.saveMV()
    setTimeout(() => {
      window.location.href = url;
    }, 50);
  }

  public goToRepaymentRecord = () => {
    sessionStorage.setItem("validityType", $ValidityType.NEW_CREDIT);
    this.props.history.push({ pathname: `/${SITE_PATH}/repayment-record`});
  }

  public accountFlowDetail = (accountFlowId) => {
    this.props.history.push({ pathname: `/${SITE_PATH}/account-info-detail/${accountFlowId}` });
  }

  public render() {
    const { accountTypeCode } = this.props.match.params || { accountTypeCode: "" };
    const { showDateModal, showBusinessModal, showRepaymentModal } = this.state;
    const { itemList, key, isLoading, isFinished, startTime, endTime,
            dateType, businessType, itemCount, changeAmount, totalWaitRepayAmount,
            creditAccountList, selectCreditAccountList, selectSurplusToReturnAmount } = this.$myMv;
    const isFixed = showDateModal || showBusinessModal || showRepaymentModal;
    const swiperData = [
      {
        title: "增加",
        businessTypeList: ["订货付款", "费用付款", "扣款", "其他"],
      },
      {
        title: "减少",
        businessTypeList: ["还款"]
      }
    ]

    return (
      <Spin spinning={isLoading} style={{ pointerEvents: isFixed ? "none" : "auto" }}>
        <Wrapper>
          <SHead>
            <div>待还金额(元)</div>
            <div>{totalWaitRepayAmount}</div>
          </SHead>
          <SSearch>
            <div className="search-title">使用明细</div>
            <div className="search-more">
              <div onClick={() => this.setState({ showDateModal: true })}>
                <span>
                  {
                    dateType === $SearchTimeType.MONTH ? startTime.slice(0, 7) : `${startTime}~${endTime}`
                  }
                </span>
                <i className="scmIconfont scm-icon-xiajiantou" />
              </div>
              <div onClick={() => this.setState({ showBusinessModal: true })}>
                <span>业务类型</span>
                <i className="scmIconfont scm-icon-xiajiantou" />
              </div>
            </div>
            <div className="search-key">
              {
                SearchKeyList.map((item, index) => {
                  return <span key={index} className={key === item.key && "active"} onClick={() => this.queryAccountInfo(item.key)}>
                    {item.name}
                  </span>;
                })
              }
            </div>
          </SSearch>
          <SList>
            {
              itemCount > 0 && <div className="total"> 总 <span>{itemCount}</span> 笔&nbsp;&nbsp;&nbsp;合计： <span>{changeAmount}</span></div>
            }
            {
              itemList && itemList.length > 0 ? itemList.map((item, index) => {
                return <div className="item" key={index} onClick={() => this.accountFlowDetail(item.flowId)}>
                  <div>
                    <div><span>{item.label}</span> {item.accountUse}</div>
                    <div>{item.date}</div>
                  </div>
                  <div>
                    <div><span>{item.amount}</span> <i className="scmIconfont scm-icon-jiantou-you" /></div>
                    <div>待还金额: {item.waitRepayAmount}</div>
                  </div>
                </div>;
              }) : <NoGoods title="暂无数据" />
            }
            {
              itemList && itemList.length > 0 ?
                <LoadingTip
                  isFinished={isFinished}
                  isLoad={isLoading}
                /> : null
            }
          </SList>
          <SButton>
            <div onClick={this.goToRepaymentRecord}>还款记录</div>
            <div onClick={() => this.onShowRepaymentModal(true)}>立即还款</div>
          </SButton>
          <CustomMonthModal
            visible={showDateModal}
            type={"range"}
            dateType={dateType}
            startDate={startTime}
            endDate={endTime}
            pageType={accountTypeCode}
            onCancel={() => this.setState({ showDateModal: false })}
            onConfirm={this.onSearchDate}
          />
          <RightSwiperModal
            swiperData={swiperData}
            visible={showBusinessModal}
            businessType={businessType}
            onSearch={this.onSearchBusiness}
            onClose={() => this.setState({ showBusinessModal: false })}
          />
          <SModel style={{ display: showRepaymentModal ? "block" : "none" }}>
            <LayerStyle onClick={this.hideModel} />
            <EffectiveCreditAccountInfos>
              <EffectiveCreditAccountInfoHeader>
                <span>还款</span>
                <i onClick={this.hideModel} className="scmIconfont scm-icon-guanbi" style={{ position: "absolute", right: 20 }} />
              </EffectiveCreditAccountInfoHeader>
              <EffectiveCreditAccountInfoContents>
                {
                  creditAccountList.length > 0 ? creditAccountList.filter((list) => list.surplusToReturnAmount !== 0).map((credit, index) => {
                    return (
                      <div key={index}>
                        <CheckboxItem
                          checked={credit.checkFlag}
                          onChange={(e) => this.onCheckedCredit(e, credit, credit.surplusToReturnAmount)} />
                        <EffectiveCreditAccountInfoContent>
                          <div className="code">{credit.code}（{credit.ruleType}）</div>
                          <div className={"info"}>
                            <div>
                              <div>信用额度</div>
                              <div><span>￥</span>{Number(credit.creditAmount).toFixed(2)}</div>
                            </div>
                            <div>
                              <div>欠款金额</div>
                              <div><span>￥</span>{Number(credit.surplusToReturnAmount).toFixed(2)}</div>
                            </div>
                          </div>
                          <div className="show-date">生效日期：{credit.creditAmountExpiryDateFrom}</div>
                        </EffectiveCreditAccountInfoContent>
                      </div>
                    );
                  }) : <NoGoods title="暂无可用的信用~" />
                }
              </EffectiveCreditAccountInfoContents>
              <EffectiveCreditAccountInfoButton onClick={this.checkTongLianPaymentStatus} theme={{ selectSurplusToReturnAmount }}>
                立即还款 ￥{Number(selectSurplusToReturnAmount).toFixed(2)}
              </EffectiveCreditAccountInfoButton>
            </EffectiveCreditAccountInfos>
          </SModel>
        </Wrapper>
      </Spin>
    );
  }
}

const NewModeDebtAccountInfo = ScrollAbilityWrapComponent(NewModeDebtAccountInfoWrap);

export default NewModeDebtAccountInfo;

const EffectiveCreditAccountInfoButton = styled.div`// styled
  & {
    width: 80%;
    height: 45px;
    line-height: 45px;
    background-color: ${(props) => props.theme.selectSurplusToReturnAmount > 0 ? "#437DF0" : "#D9D9D9"};
    font-family: PingFangSC-Regular;
    font-size: 15px;
    font-weight: 500;
    color: #FFFFFF;
    text-align: center;
    position: fixed;
    margin-left: 10%;
    margin-bottom: 24px;
    border-radius: 24px;
    bottom: 0;
    z-index: 99;
  }
`;

const EffectiveCreditAccountInfoContent = styled.div`// styled
  & {
    display: inline-block;
    width: 90%;
    padding: 12px;
    background: #E8F0FF;
    border-radius: 4px;
    margin-bottom: 15px;
    vertical-align: middle;

    .code {
      color: #666;
      font-size: 12px;
    }

    .show-date {
      margin-top: 8px;
      color: #666;
      font-size: 12px;
    }

    .info {
      display: flex;
      justify-content: space-between;
      background: #FFF;
      border-radius: 4px;
      padding: 12px;
      margin-top: 8px;

      > div:nth-child(1) {
        display: flex;
        flex-direction: column;
        width: 50%;

        > div:nth-child(1) {
          color: #666;
          font-size: 14px;
        }

        > div:nth-child(2) {
          font-size: 18px;
          font-weight: 500;
          color: #333333;

          > span {
            font-size: 12px;
          }
        }
      }

      > div:nth-child(2) {
        display: flex;
        flex-direction: column;
        width: 50%;
        justify-content: flex-start;

        > div:nth-child(1) {
          color: #666;
          font-size: 14px;
        }

        > div:nth-child(2) {
          font-size: 18px;
          font-weight: 500;
          color: #FF3030;

          > span {
            font-size: 12px;
          }
        }
      }
    }
  }
`;

const EffectiveCreditAccountInfoContents = styled.div`// styled
  & {
    width: 100%;
    height: 400px;
    padding: 15px 15px 120px 15px;
    overflow-y: auto;
    > div {
      > .am-list-item {
        width: 10%;
        padding-left: 0;
        //vertical-align: unset;
        display: inline-block;
      }
    }
    .noGoods {
      img {
        width: 30%;
      }
    }
  }
`;

const EffectiveCreditAccountInfoHeader = styled.div`// styled
  & {
    width: 100%;
    height: 46px;
    line-height: 46px;
    background: #fff;
    padding: 0px 15px;
    box-sizing: border-box;
    text-align: center;
    > span:nth-of-type(1) {
      font-size: 15px;
      color: #333333;
      font-weight: 500;
    }
  }
`;


const EffectiveCreditAccountInfos = styled.div`// styled
  & {
    & {
      width: 100%;
      height: auto;
      background: #fff;
      border: 1px solid transparent;
    }
  }
`;

const LayerStyle = styled.div`// styled
  & {
    width: 100%;
    height: calc(${document.documentElement.clientHeight / 2}px);;
  }
`;

const SModel = styled.div`// styled
  & {
    width: 100%;
    height: calc(${document.documentElement.clientHeight}px);
    position: fixed;
    top: 0;
    left:0;
    background: rgba(0,0,0,0.5);
    z-index: 99;
    -webkit-transition: all 4s ease-in;
	  -moz-transition: all 4s ease-in;
	  -o-transition: all 4s ease-in;
	  transition: all 4s ease-in;

    .model{
      position: fixed;
      top: 0;
      left: 0;
      bottom: 0;
      right: 0;
      z-index: 999;

      .model-bg {
        position: fixed;
        top: 0;
        left: 0;
        bottom: 0;
        right: 0;
        background-color: rgba(0, 0, 0, 0.4);
      }

      .date-content{
        position: absolute;
        height: 375px;
        bottom: 0;
        left: 0;
        right: 0;
        background: #FFFFFF;

        .date-confirm {
          color: #FFFFFF;
          background: #437DF0;
          border-radius: 22.5px;
          padding: 8px 12px;
          display: flex;
          align-items: center;
          justify-content: center;
          position: absolute;
          bottom: 20px;
          width: 80vw;
          margin-inline-start: 10vw;
        }
      }
        .date-content-top {
          width: 100%;
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding:12px 16px;

          .date-tab {
            display: flex;
            justify-content: center;
            width: 100%;
            margin-left: 20px;

            > div {
              margin: 0 10px;
              color: #333333;
              font-size: 15px;
              font-weight: 500;
            }
          }

          > img {
            width: 20px;
            height: 20px;
          }
        }

        .data-content-center{
          padding: 6px 12px;

          .data-item {
            display: flex;

            .am-list-item {
              padding-left: 0;

              .am-list-thumb:first-child {
                margin-right: 0;
              }

              .am-list-line {
                padding-right: 8px;
              }
            }
          }
        }
      }
  }
`;

const SButton = styled.div`
  & {
    position: absolute;
    bottom: 0;
    background: #FFFFFF;
    padding-top: 8px;
    padding-bottom: 24px;
    width: 100%;
    display: flex;
    justify-content: space-around;

    > div:nth-child(1) {
      border: 1px solid #437DF0;
      font-size: 15px;
      font-weight: 500;
      color: #437DF0;
      background: #FFF;
      border-radius: 32px;
      padding: 10px 32px;
    }

    > div:nth-child(2) {
      border: 1px solid #437DF0;
      background: #437DF0;
      color: #FFF;
      font-size: 15px;
      font-weight: 500;
      border-radius: 32px;
      padding: 10px 32px;
    }
  }
`

const SHead = styled.div`
  & {
    width: 100%;
    padding: 12px;
    width: 100%;
    padding: 12px;
    color: #FFF;
    background: linear-gradient(102deg, #6095FD -26%, #437DF0 97%);
    border-radius: 4px;

    > div:nth-child(1) {
      font-size: 14px;
    }

    > div:nth-child(2) {
      font-size: 24px;
      font-weight: 500;
    }
  }
`

const Wrapper = styled.div`// styled
  & {
    width: 100%;
    height: 100vh;
    padding: 12px;
    display: flex;
    flex-direction: column;
    align-items: center;
    background: #F7F7F7;
    font-family: PingFang SC;
  }
`;

const SList = styled.div`
  & {
    width: 100%;
    margin-top: 12px;
    border-radius: 8px;
    overflow: scroll;
    background: #FFF;
    margin-bottom: 78px;

    .total {
      padding: 12px 12px 0;
      font-size: 12px;
      color: #666;

      > span:nth-child(1) {
        color: #333;
        font-weight: 500;
      }

      > span:nth-child(2) {
        color: #FF3030;
        font-weight: 500;
      }
    }

    .item {
      display: flex;
      justify-content: space-between;
      margin: 12px;
      padding-bottom: 12px;
      border-bottom: 0.5px solid #E8E8E8;

      > div:nth-child(1) {
        display: flex;
        flex-direction: column;

        > div:nth-child(1) {
          color: #333;
          font-size: 14px;
          font-weight: 500;

          > span {
            padding: 4px 8px;
            background: #F8F8F8;
            color: #333;
            font-size: 12px;
          }
        }

        > div:nth-child(2) {
          margin-top: 4px;
          color: #999;
          font-size: 12px;
        }
      }

      > div:nth-child(2) {
        display: flex;
        flex-direction: column;

        > div:nth-child(1) {
          text-align: right;

          > span {
            color: #FF3030;
            font-size: 18px;
            font-weight: 500;
          }
        }

        > div:nth-child(2) {
          margin-top: 4px;
          text-align: right;
          color: #999;
          font-size: 12px;
        }
      }
    }
  }
`;

const SSearch = styled.div`// styled
  & {
    width: 100%;
    height: auto;
    margin-top: 12px;
    border-radius: 8px;
    box-shadow: 0px 0px 10px 0px rgba(45, 45, 45, 0.0694);

    .search-title {
      font-size: 16px;
      font-weight: 500;
      padding: 0 0 8px;
    }

    .search-key {
      width: 100%;
      height: 40px;
      line-height: 40px;
      display: flex;
      background: #F7F7F7;

      > span {
        display: inline-block;
        width: 100%;
        text-align: center;
        color: #666;
        margin: 0 30px;
        font-size: 14px;
      }

      .active {
        border-bottom: 2px solid #307DCD;
        color: #307DCD;
        font-size: 14px;
      }
    }

    .search-more {
      display: flex;
      justify-content: space-between;
      padding: 12px;
      background: #FFF;

      > div {
        > span {
          color: #333;
          font-size: 14px;
          margin-right: 3px;
        }

        > img {
          width: 14px;
          height: 14px;
        }
      }
    }
  }
`;
