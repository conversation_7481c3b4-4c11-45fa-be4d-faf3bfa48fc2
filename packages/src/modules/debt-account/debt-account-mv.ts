import { autowired, bean } from "@classes/ioc/ioc";
import { action, observable } from "mobx";
import { $ComponentService } from "../../classes/service/$component-service";
import { $MyInfoService } from "@classes/service/$my-info-service";
import { $DebtAccountInfo } from "@classes/entity/$debt-account-info";

@bean($DebtAccountMv)
export class $DebtAccountMv {

  @autowired($ComponentService)
  public $componentService: $ComponentService;

  @autowired($MyInfoService)
  public $myInfoService: $MyInfoService;

  @observable public isSpin: boolean = false;

  @observable public isLoading: boolean = false;

  @observable public isShowLoading: boolean = true;

  @observable public accountInfo: $DebtAccountInfo = new $DebtAccountInfo({});

  @action
  public showSpin() {
    this.isSpin = true;
  }

  @action
  public hideSpin() {
    this.isSpin = false;
  }

  @action
  public fetchDebtAccount() {
    this.showSpin();
    this.$myInfoService.loadDebtAccountInfo().then((data) => {
      this.hideSpin();
      this.accountInfo = new $DebtAccountInfo(data);
    });
  }

}
