import { autowired } from "@classes/ioc/ioc";
import { observer } from "mobx-react";
import React from "react";
import { withRout<PERSON> } from "react-router";
import styled from "styled-components";
import { $DebtAccountMv } from "./debt-account-mv";
import { $AccountType } from "../../classes/const/$account-type";
import { SITE_PATH } from "../app";
import { Spin } from "antd";
import { ScrollAbilityWrapComponent } from "../../components/scroll-ability/wrap";
import { $AppStore } from "../../classes/stores/app-store-mv";
import { $DebtAccountType, DebtAccountName } from "@classes/const/$debt-account-type";

@withRouter
@observer
class DebtAccountWrap extends React.Component<any, any> {

  @autowired($DebtAccountMv)
  public $myMv: $DebtAccountMv;

  @autowired($AppStore)
  public $AppStore: $AppStore;

  public constructor(props) {
    super(props);
    this.state = {};
  }

  public componentDidMount() {
    document.title = "欠款账户";
    this.$myMv.fetchDebtAccount();
  }

  public goInfo = (debtAccountType: string) => {
    if (debtAccountType === $DebtAccountType.Limit) {
      this.props.history.push({ pathname: `/${SITE_PATH}/account-info-list/${this.props.match.params.accountId}/${$AccountType.CREDIT}` });
    } else {
      this.props.history.push({ pathname: `/${SITE_PATH}/new-mode-debt-account-info/${this.props.match.params.accountId}` });
    }
  }

  public goTotalInfo = (debtAccountType: string) => {
    if (debtAccountType === $DebtAccountType.Limit) {
      this.props.history.push({ pathname: `/${SITE_PATH}/credit-amount-list` });
    } else {
      this.props.history.push({ pathname: `/${SITE_PATH}/new-mode-debt-account-record/${this.props.match.params.accountId}` });
    }
  }

  public render() {
    const { isSpin, accountInfo } = this.$myMv;
    return (
      <Spin spinning={isSpin}>
        <Wrapper className="account-content-info-warp" >
          <SHeadBg />
          <SHeader>
            <div className="header-title">
              <div>待还总金额(元)</div>
              <div className="header-amount"><span>¥</span>{accountInfo.totalWaitRepayAmount}</div>
            </div>
            <div className="header-title">
              <div>可用额度</div>
              <div className="header-amount"><span>¥</span>{accountInfo.totalAvailableAmount}</div>
              <div className="header-tips">总额度：{accountInfo.totalCreditAmount}</div>
            </div>
          </SHeader>
          {
            accountInfo.accountList.length > 0 && accountInfo.accountList.map((account, index) => {
              return <SCard key={index}>
                <div className="card-title">{DebtAccountName[account.validityType]}信用</div>
                <div className="card-content">
                  <div>
                    <div onClick={() => this.goInfo(account.validityType)}>
                      待还金额 <i className="scmIconfont scm-icon-jiantou-you" />
                    </div>
                    <div><span>¥</span>{account.waitRepayAmount}</div>
                    <div onClick={() => this.goTotalInfo(account.validityType)}>
                      总额度：{account.creditAmount} <i className="scmIconfont scm-icon-jiantou-you" />
                    </div>
                  </div>
                  <div>
                    <div>可用额度</div>
                    <div><span>¥</span>{account.availableAmount}</div>
                  </div>
                </div>
              </SCard>;
            })
          }
        </Wrapper>
      </Spin>
    );
  }
}

const DebtAccount = ScrollAbilityWrapComponent(DebtAccountWrap);

export default DebtAccount;

const SCard = styled.div`
  & {
    background: #FFF;
    border-radius: 4px;
    margin-top: 12px;
    width: 100%;

    .card-title {
      padding: 12px 12px 8px;
      color: #333;
      font-size: 16px;
      font-weight: 500;
      border-bottom: 0.5px solid #E8E8E8;
    }

    .card-content {
      display: flex;
      padding: 12px;
      justify-content: space-between;

      > div {
        width: 50%;
        display: flex;
        flex-direction: column;

        > div:nth-child(1) {
          display: flex;
          align-items: center;
          color: #666;
          font-size: 14px;
        }

        > div:nth-child(2) {
          font-size: 28px;
          font-weight: 500;
          color: #FF3030;
          overflow: scroll;

          > span {
            font-size: 20px;
          }
        }

        > div:nth-child(3) {
          font-size: 12px;
          color: #666;
          display: flex;
          align-items: center;
        }
      }
    }
  }
`;

const SHeader = styled.div`
  & {
    background: #FFF;
    width: 100%;
    z-index: 10;
    border-radius: 4px;
    padding: 12px;
    display: flex;
    box-shadow: 0px 0px 10px 0px rgba(45, 45, 45, 0.0694);
    justify-content: space-between;

    .header-title {
      width: 50%;
      display: flex;
      flex-direction: column;
      color: #666;
      font-size: 14px;

      .header-amount {
        font-size: 18px;
        color: #333;
        font-weight: 500;
        margin-top: 4px;

        > span {
          font-size: 12px;
        }
      }

      .header-tips {
        font-size: 12px;
      }
    }
  }
`;

const Wrapper = styled.div`// styled
  & {
    width: 100%;
    height: 100vh;
    padding: 12px;
    display: flex;
    flex-direction: column;
    align-items: center;
    background: #F7F7F7;
  }
`;

const SHeadBg = styled.div`
  & {
    position: absolute;
    top: 0;
    width: 100vw;
    height: 24vw;
    background: #437DF0;
  }
`;
