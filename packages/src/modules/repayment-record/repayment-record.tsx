import { autowired } from "@classes/ioc/ioc";
import { observer } from "mobx-react";
import * as React from "react";
import { withRouter } from "react-router";
import styled from "styled-components";
import { Spin } from "antd";
import { ScrollAbilityWrapComponent } from "../../components/scroll-ability/wrap";
import { RepaymentRecordMv } from "./repayment-record-mv";
import { LoadingTip } from "../../components/loading-marked-words";
import { NoGoods } from "../../components/no-goods/no-goods";
import { formatAmount } from "../../classes/utils/FormatAmount";
import { $ValidityType } from "@classes/const/$validity-type";

declare let require: any;
@withRouter
@observer
class RepaymentRecordWrapper extends React.Component<any, any> {

  @autowired(RepaymentRecordMv)
  public $myMv: RepaymentRecordMv;

  constructor(props) {
    super(props);
    this.state = {
      finished: true,
      isShow: false,
      isSpinning: true,
      pageIndex: 0,
      pageSize: 20,
      refundOrderOid: "",
    };
  }

  public componentDidMount() {
    document.title = "还款记录";
    this.loadRepaymentRecord();
  }

  public componentWillReceiveProps(nextProps: Readonly<any>, nextContext: any): void {
    const { isScrollEnd } = nextProps;
    console.log(isScrollEnd, this.props.isScrollEnd);
    if (isScrollEnd && isScrollEnd !== this.props.isScrollEnd) {
      this.loadData();
    }
  }

  public loadRepaymentRecord = () => {
    this.setState({ isSpinning: true })
    const { pageIndex, pageSize } = this.state;
    const validityType = sessionStorage.getItem("validityType") || $ValidityType.REGULAR_CREDIT;
    const params = { pageIndex, pageSize, validityType };
    this.$myMv.loadRepaymentRecord(params).then((res) => {
      const { creditPaymentList, itemCount } = res;
      const repaymentListLength = this.$myMv.setRepaymentRecordList(creditPaymentList, pageIndex);
      console.log(repaymentListLength, itemCount);
      this.setState({ isSpinning: false, pageIndex: pageIndex + 1, finished: repaymentListLength >= itemCount });
      const { loadingEnd } = this.props;
      loadingEnd && loadingEnd(repaymentListLength >= itemCount);
    }).catch((err) => {
      this.setState({ isSpinning: false });
    });
  }

  public loadData = () => {
    const { finished, isSpinning } = this.state;
    console.log(finished, isSpinning);
    if (finished || isSpinning) {
      return;
    }
    this.loadRepaymentRecord();
  }
  public render() {
    const { finished, isShow, isSpinning } = this.state;
    const { repaymentRecordList } = this.$myMv;
    console.log(this.state);
    return (
      <RepaymentRecordPage className="repayment-record-page">
        <Spin spinning={isSpinning}>
          <RepaymentRecordContent className="repayment-record-content">
            {
              repaymentRecordList.length > 0 ?
                repaymentRecordList.map((item) => {
                  const { oid, paymentModeName, amount, createTime, docStatusName } = item ;
                  return <div key={oid} className="repayment-record-item">
                    <div>
                      <span className="paymentModeName">{paymentModeName}</span>
                      <span className="amount">{formatAmount(amount)}</span>
                    </div>
                    <div>
                      <span className="repaymentTime">{createTime}</span>
                      <span className="docStatusName">{docStatusName}</span>
                    </div>
                  </div>;
              }) : <NoGoods title="暂无还款记录"/>
            }
            {
              repaymentRecordList && repaymentRecordList.length > 0 &&
							<LoadingTip
								isFinished={finished}
								isLoad={isShow}
							/>
            }
          </RepaymentRecordContent>

        </Spin>
      </RepaymentRecordPage>
    );
  }
}

const RepaymentRecord = ScrollAbilityWrapComponent(RepaymentRecordWrapper);
export default RepaymentRecord;

const RepaymentRecordContent = styled.div`// styled
  & {
    width: 100%;
    height: auto;
    .repayment-record-item{
      width: 100%;
      height: 56px;
      border-bottom: 1px solid #D8D8D8;
      >div:nth-of-type(1) {
         height: 34px;
         line-height: 34px;
         width: 100%;
         padding: 0 16px;
         color: #2F2F2F;
         .paymentModeName {
           float: left;
         }
         .amount{
           float: right;
         }
       }
      >div:nth-of-type(2) {
        height: 12px;
        width: 100%;
        padding: 0 16px;
        color: #595959;
        font-size: 12px;
        line-height: 12px;
        .repaymentTime {
          float: left;
        }
        .docStatusName{
          float: right;
          color: #FF3030;
        }
      }
    }
  }
`;

const RepaymentRecordPage = styled.div`// styled
  & {
    width: 100%;
    height: auto;
  }
`;
