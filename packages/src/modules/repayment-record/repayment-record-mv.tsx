import { autowired, bean } from "@classes/ioc/ioc";
import { observable, action} from "mobx";
import { $MyInfoService } from "../../classes/service/$my-info-service";

@bean(RepaymentRecordMv)
export class RepaymentRecordMv {

  @autowired($MyInfoService)
  public $myInfoService: $MyInfoService;

  @observable public repaymentRecordList: any[] = [];
  @action
  public loadRepaymentRecord(params) {
    return this.$myInfoService.loadRepaymentRecord(params);
  }
  @action
  public setRepaymentRecordList(list, pageIndex) {
    this.repaymentRecordList = pageIndex ? this.repaymentRecordList.concat(list) : list;
    return this.repaymentRecordList.length;
  }
}
