import { autowired } from "@classes/ioc/ioc";
import { List, Toast } from "antd-mobile";
import { observer } from "mobx-react";
import React from "react";
import { withRouter } from "react-router";
import styled from "styled-components";
import { $AccountFlowDetailMv } from "./account-flow-detail-mv";
import { $AccountType } from "../../classes/const/$account-type";
import { Spin } from "antd";
import { getUrlParam } from "../../classes/utils/UrlUtils";
const Item = List.Item;
declare let window: any;
import { AmountShowComponent } from "../../components/account-flow-amount-component";
import { SITE_PATH } from "../app";
import { gaEvent } from "../../classes/website-statistics/website-statistics";

@withRouter
@observer
class AccountFlowDetail extends React.Component<any, any> {
  @autowired($AccountFlowDetailMv)
  public $accountFlowDetailMv: $AccountFlowDetailMv;
  private relateDocType = {
    REFUNDORDER: "RefundOrder", // 退货单
    SALESORDER: "SalesOrder", // 订货单
    FEEDOCUMENT: "FeeDocument", // 订货单
  }
  public constructor(props) {
    super(props);
  }

  public componentDidMount() {
    document.title = "流水详情";
    gaEvent("流水详情");
    this.initPage();
  }

  public initPage = () => {
    const { accountFlowId } = this.props.match.params;
    const orderPartyId = getUrlParam("orderPartyId");
    this.$accountFlowDetailMv.showSpin();
    console.log("accountFlowId", accountFlowId);
    const params = { flowId: accountFlowId };
    if (orderPartyId) {
      this.$accountFlowDetailMv.saveShopAndScheme({ orderPartyId }).then((res) => {
        const message = res.errorMessage;
        if (message) {
          Toast.fail(message);
        } else {
          this.$accountFlowDetailMv.fetchAccountFlowInfo(params);
        }
      });
    } else {
      this.$accountFlowDetailMv.fetchAccountFlowInfo(params);
    }
  }
  public goToOrderDetail = (type, params?) => {
    switch (type) {
      case "orderDetail":
        const { relatedDocId, orderScheme } = params;
        this.props.history.push({
          pathname: `/${SITE_PATH}/shop/order-detail/${relatedDocId}/${orderScheme}`,
          state: {
            backSource: "single",
          },
        });
        break;
      case "returnedDetail":
        const { refundOrderId, orgName, orgId } = params;
        this.props.history.push({
          pathname: `/${SITE_PATH}/returned-detail/${refundOrderId}`,
          state: {
            orderPartyName: orgName,
            orgId,
          },
        });
        break;
      case "FeeDocument":
        const { feeDocumentId } = params;
        this.props.history.push({
          pathname: `/${SITE_PATH}/expense-node-detail/${feeDocumentId}`,
        });
        break;
      default:
        break;
    }

  }
  public render() {
    const { accountFlowInfo, isSpin } = this.$accountFlowDetailMv;
    const { REFUNDORDER, SALESORDER, FEEDOCUMENT } = this.relateDocType;
    const { docTypeText, docTypeValue, amount, tradeTime, flowNo, serviceCharge, relatedDocNo, orderScheme, orderOrgName, deductType, memo, salesOrderId, repaymentAmount, repaymentPaymentMode, rechargeAmount, rechargeMode, relateDocTypeText, relateDocTypeValue, createDate, rebateAmount, refundOrderId, orgName, orgId, feeDocumentId, relatedDocType, relatedDocId } = accountFlowInfo;
    return (
      <Wrapper>
        <Spin spinning={isSpin}>
          <Header>
            <AmountShowComponent
              label={docTypeText}
              value={amount}
              isShowRepaymentEnd={true}
            />
          </Header>
          <Content>
            <div className="base-info-item">
              <div className="label">交易时间</div>
              <div className="value">{tradeTime}</div>
            </div>
            <div className="base-info-item">
              <div className="label">流水号</div>
              <div className="value">{flowNo}</div>
            </div>
          </Content>
          {
            (docTypeValue === $AccountType.ORDERPAYMENT
              ||
              docTypeValue === $AccountType.DEDUCTDOCUMENTS
              ||
              docTypeValue === $AccountType.CREDITACCOUNTREPAYMENT
              ||
              docTypeValue === $AccountType.StoredAccountRecharge
              ||
              docTypeValue === $AccountType.RefundDocuments
              ||
              docTypeValue === $AccountType.RebateAccountDocuments
            ) &&
              <Footer>
                <div className="title">关联单据</div>
                {
                  docTypeValue === $AccountType.ORDERPAYMENT &&
                    <div
                      className="footer-info"
                      onClick={() => {
                        if (relatedDocType === SALESORDER) {
                          this.goToOrderDetail("orderDetail", { relatedDocId, orderScheme });
                        } else if (relatedDocType === FEEDOCUMENT) {
                          this.goToOrderDetail(FEEDOCUMENT, { feeDocumentId: relatedDocId });
                        }
                      }}
                    >
                      <div className="info-left">
                        {
                          relatedDocType !== FEEDOCUMENT &&
                            <div className="info-header">
                              <div className="label">订货方案：</div>
                              <div className="value">{orderScheme}</div>
                            </div>
                        }
												<div className="info-item">
													<div className="label">{relatedDocType === SALESORDER ? "订单号：" : "费用单号："}</div>
													<div className="value">{relatedDocNo}</div>
												</div>
												<div className="info-item">
													<div className="label">{relatedDocType === SALESORDER ? "下单门店：" : "订货组织："}</div>
													<div className="value">{orderOrgName}</div>
												</div>
                      </div>
                     <div className="payment-info-right">
											 <i className="scmIconfont scm-icon-jiantou-you"/>
                     </div>
                    </div>
                }
                {
                  docTypeValue === $AccountType.DEDUCTDOCUMENTS &&
                    <div className="footer-info">
                      <div className="info-left">
                        <div className="info-header">
                          <div className="label">扣款类型：</div>
                          <div className="value">{deductType}</div>
                        </div>
                        {
                          memo &&
                            <div className="info-item">
                              <div className="label">备注：</div>
                              <div className="value">{memo}</div>
                            </div>
                        }
                      </div>
                    </div>
                }
                {
                  docTypeValue === $AccountType.CREDITACCOUNTREPAYMENT &&
                    <div className="footer-info">
                      <div className="info-left">
                        <div className="info-header">
                          <div className="label">还款金额：</div>
                          <div className="value">{`￥${Number(repaymentAmount).toFixed(2)}`}</div>
                        </div>
                        <div className="info-item">
                          <div className="label">还款方式：</div>
                          <div className="value">{repaymentPaymentMode}</div>
                        </div>
                        {
                          typeof (serviceCharge) === "number" &&
                            <div className="info-item">
                              <div className="label">服务费：</div>
                              {/*<div className="value">{`${serviceCharge ? "￥" + Number(serviceCharge).toFixed(2) : serviceCharge}`}</div>*/}
                              <div className="value">{`￥${Number(serviceCharge).toFixed(2)}`}</div>
                            </div>
                        }
                        {
                          memo &&
                            <div className="info-item">
                              <div className="label">还款备注：</div>
                              <div className="value">{memo}</div>
                            </div>
                        }
                      </div>
                    </div>
                }
                {
                  docTypeValue === $AccountType.StoredAccountRecharge &&
                    <div className="footer-info">
                      <div className="info-left">
                        <div className="info-header">
                          <div className="label">充值金额：</div>
                          <div className="value">{`￥${Number(rechargeAmount).toFixed(2)}`}</div>
                        </div>
                        <div className="info-item">
                          <div className="label">充值方式：</div>
                          <div className="value">{rechargeMode}</div>
                        </div>
                        {
                          typeof(serviceCharge) === "number" &&
                            <div className="info-item">
                              <div className="label">服务费：</div>
                              {/*<div className="value">{`${serviceCharge ? "￥" + Number(serviceCharge).toFixed(2) : serviceCharge}`}</div>*/}
                              <div className="value">{`￥${Number(serviceCharge).toFixed(2)}`}</div>
                            </div>
                        }
                        {
                          memo &&
                            <div className="info-item">
                              <div className="label">充值备注：</div>
                              <div className="value">{memo}</div>
                            </div>
                        }
                      </div>
                    </div>
                }
                {
                  docTypeValue === $AccountType.RefundDocuments &&
                    <div
                      className="footer-info"
                      onClick={() => {
                        if (relateDocTypeValue === SALESORDER) {
                          this.goToOrderDetail("orderDetail", { relatedDocId: salesOrderId, orderScheme });
                        } else if (relateDocTypeValue === REFUNDORDER) {
                          this.goToOrderDetail("returnedDetail", { refundOrderId, orgName, orgId });
                        } else if (relateDocTypeValue === FEEDOCUMENT) {
                          this.goToOrderDetail(FEEDOCUMENT, { feeDocumentId });
                        }
                      }}
                    >
                      <div className="info-left">
                        <div className="info-header">
                          <div className="label">关联单据类型：</div>
                          <div className="value">{relateDocTypeText}</div>
                        </div>
                        {
                          relateDocTypeValue !== FEEDOCUMENT &&
                            <div className="info-item">
                              <div className="label">{relateDocTypeValue === SALESORDER ? "订货方案：" : relateDocTypeValue === REFUNDORDER ? "申请日期：" : ""}</div>
                              <div className="value">{relateDocTypeValue === SALESORDER ? orderScheme : relateDocTypeValue === REFUNDORDER ? createDate : ""}</div>
                            </div>
                        }
									    </div>
											<div className="payment-info-right">
												<i className="scmIconfont scm-icon-jiantou-you"/>
											</div>
                    </div>
                }
                {
                  docTypeValue === $AccountType.RebateAccountDocuments &&
                    <div className="footer-info">
                      <div className="info-left">
                        <div className="info-header">
                          <div className="label">返利金额：</div>
                          <div className="value">{`￥${Number(rebateAmount).toFixed(2)}`}</div>
                        </div>
                        {
                          memo &&
                            <div className="info-item">
                              <div className="label">备注：</div>
                              <div className="value">{memo}</div>
                            </div>
                        }
                      </div>
                    </div>
                }
              </Footer>
          }
        </Spin>
      </Wrapper>
    );
  }
}

export default AccountFlowDetail;

const Wrapper = styled.div`// styled
  & {
    width: 100%;
    min-height: calc(${document.documentElement.clientHeight}px);
    height: auto;
    background: #F2F2F2;
  }
`;

const Header = styled.div`// styled
  & {
    width: 100%;
    height: auto;
  }
`;
const Content = styled.div`// styled
  & {
    height: auto;
    margin: 12px 12px 0;
    background-color: #fff;
    box-shadow:0px 0px 3px 0px rgba(0,0,0,0.05);
    border-radius:8px;
    overflow: hidden;
    .base-info-item{
      height: 31px;
      width: 100%;
      line-height: 31px;
      padding: 0 12px;
      font-size: 12px;
      display: flex;
      justify-content: space-between;
      .label{
        color: #595959;
        width: auto;
        margin-right: 10px;
      }
      .value{
        flex: 1;
        color: #2F2F2F;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
    }
  }
`;
const Footer = styled.div`// styled
  & {
    margin: 12px 12px 0;
    .title{
      color: #2F2F2F;
      font-size: 13px;
      line-height: 13px;
      margin-bottom: 8px;
    }
    .footer-info{
      background-color: #fff;
      width: 100%;
      padding: 12px 12px 10px;
      height: auto;
      overflow: hidden;
      box-shadow:0px 0px 3px 0px rgba(0,0,0,0.05);
      border-radius:8px;
      position: relative;
      .info-left{
        width: calc(100% - 28px);
        height: auto;
        float: left;
        .info-header{
          color: #2F2F2F;
          font-size: 13px;
          line-height: 13px;
          font-weight:500;
          display: flex;
          .label{
            width: auto;
          }
          .value{
            width: auto;
            flex: 1;
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
          }
        }
        .info-item{
          width: 100%;
          font-size: 12px;
          line-height: 16px;
          color: #595959;
          overflow: hidden;
          height: auto;
          min-height: 16px;
          display: flex;
          margin-top: 11px;
          .label{
            width: auto;
          }
          .value{
            flex: 1;
          }
        }
        .info-item:nth-of-type(1){
          margin-top: 12px;
        }
      }
      .payment-info-right{
        width: auto;
        height: auto;
        position: absolute;
        top: 50%;
        right: 8px;
        transform: translateY(-50%);
        .scm-icon-jiantou-you{
          font-size: 12px;
        }
      }
    }
    .deduct-documents-info{
      .info-left{
        width: 100%;
      }
    }
  }
`;
