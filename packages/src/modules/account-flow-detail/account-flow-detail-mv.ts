import { autowired, bean } from "@classes/ioc/ioc";
import { action, observable } from "mobx";
import { $ComponentService } from "../../classes/service/$component-service";
import { $OrderPartyService } from "../../classes/service/$order-party-service";
import { $AcountFlowDetail } from "../../classes/entity/$acount-flow-detail";
@bean($AccountFlowDetailMv)
export class $AccountFlowDetailMv {
  @autowired($ComponentService)
  public $componentService: $ComponentService;

  @autowired($OrderPartyService)
  public $orderPartyService: $OrderPartyService;

  @observable public accountFlowInfo = new $AcountFlowDetail({});

  @observable public isSpin: boolean = false;

  @action
  public showSpin() {
    this.isSpin = true;
  }

  @action
  public hideSpin() {
    this.isSpin = false;
  }
  @action
  public saveShopAndScheme(params) {
    return this.$orderPartyService.saveShopAndScheme(params);
  }
  @action
  public fetchAccountFlowInfo(params) {
    return this.$componentService.queryAccountflowinfo(params).then((data) => {
      this.accountFlowInfo = new $AcountFlowDetail(data);
      this.hideSpin();
    });
  }
}
