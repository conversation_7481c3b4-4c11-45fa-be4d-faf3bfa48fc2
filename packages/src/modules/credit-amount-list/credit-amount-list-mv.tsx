import { autowired, bean } from "@classes/ioc/ioc";
import { observable, action} from "mobx";
import { $MyInfoService } from "../../classes/service/$my-info-service";

@bean(CreditAmountListMv)
export class CreditAmountListMv {

  @autowired($MyInfoService)
  public $myInfoService: $MyInfoService;

  @observable public creditAmountList: any[] = [];
  @action
  public loadCreditAmountList(params) {
    return this.$myInfoService.loadCreditAmountList(params);
  }
  @action
  public setCreditAmountList(list, pageIndex) {
    this.creditAmountList = pageIndex ? this.creditAmountList.concat(list) : list;
    return this.creditAmountList.length;
  }
}
