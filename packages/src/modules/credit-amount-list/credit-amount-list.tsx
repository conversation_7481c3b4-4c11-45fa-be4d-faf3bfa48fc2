import { autowired } from "@classes/ioc/ioc";
import { observer } from "mobx-react";
import * as React from "react";
import { withRouter } from "react-router";
import styled from "styled-components";
import { Spin } from "antd";
import { ScrollAbilityWrapComponent } from "../../components/scroll-ability/wrap";
import { CreditAmountListMv } from "./credit-amount-list-mv";
import { LoadingTip } from "../../components/loading-marked-words";
import { NoGoods } from "../../components/no-goods/no-goods";
import { $CreditType } from "@classes/const/$credit-type";

declare let require: any;

@withRouter
@observer
class CreditAmountListWrapper extends React.Component<any, any> {

  @autowired(CreditAmountListMv)
  public $myMv: CreditAmountListMv;

  constructor(props) {
    super(props);
    this.state = {
      finished: true,
      isShow: false,
      isSpinning: true,
      pageIndex: 0,
      pageSize: 20,
      refundOrderOid: "",
    };
  }

  public componentDidMount() {
    document.title = "额度";
    this.loadCreditAmountList();
  }

  public componentWillReceiveProps(nextProps: Readonly<any>, nextContext: any): void {
    const { isScrollEnd } = nextProps;
    console.log(isScrollEnd, this.props.isScrollEnd);
    if (isScrollEnd && isScrollEnd !== this.props.isScrollEnd) {
      this.loadData();
    }
  }

  public loadCreditAmountList = () => {
    this.setState({ isSpinning: true })
    const { pageIndex, pageSize } = this.state;
    const params = { pageIndex, pageSize };
    this.$myMv.loadCreditAmountList(params).then((res) => {
      const { list, itemCount } = res;
      console.log("res", res);
      const listLength = this.$myMv.setCreditAmountList(list, pageIndex);
      console.log(listLength, itemCount);
      this.setState({ isSpinning: false, isShow: false, pageIndex: pageIndex + 1, finished: listLength >= itemCount });
      const { loadingEnd } = this.props;
      loadingEnd && loadingEnd(false);
    }).catch((err) => {
      this.setState({ isSpinning: false, isShow: false });
    });
  }

  public loadData = () => {
    const { finished, isSpinning } = this.state;
    console.log(finished, isSpinning);
    if (finished || isSpinning) {
      return;
    }
    this.setState({ isShow: true }, () => {
      this.loadCreditAmountList();
    })
  }

  public render() {
    const { finished, isShow, isSpinning } = this.state;
    const { creditAmountList } = this.$myMv;
    console.log(this.state);
    return (
      <PageWrap>
        <Spin spinning={isSpinning}>
          <Content>
            <div className="content-list">
              {
                creditAmountList && creditAmountList.length > 0 ?
                  creditAmountList.map((item, index) => {
                    const { ruleScopeText, availableAmount, totalAmount, usedAmount, validDate, repayDate, ruleScopeItem, ruleScopeValue, isMulti, repayDateList } = item;
                    return (
                      <div key={index} className="list-item">
                        <div className="item-top">
                          <div className="top-title">{ruleScopeText}</div>
                          <div className="top-content">
                            <div className="top-left">
                              <div className="available-amount">￥<span>{Number(availableAmount).toFixed(2)}</span></div>
                              <div className="text">可用额度</div>
                            </div>
                            <div className="top-right">
                              <div className="total-amount">
                                <span className="label">总额度：</span>{Number(totalAmount).toFixed(2)}
                              </div>
                              <div className="used-amount">
                                <span className="label">已用额度：</span>{Number(usedAmount).toFixed(2)}
                              </div>
                            </div>
                          </div>
                        </div>
                        <div className="item-bottom">
                          <div className="valid-date ">
                            <span className="label">有效期：</span>
                            <span className="value">{validDate}</span>
                          </div>
                          {
                            isMulti === $CreditType.ISMULTI ?
                              <div style={{padding: 0}}>
                                {
                                  repayDateList.map((date, key) => {
                                    return (
                                      <div className="repay-date">
                                        <span className="label" style={{width: 200}}>{`第${key + 1}期`}还款日期：</span>
                                        <span className="value" style={{paddingLeft: 100}}>{date}</span>
                                      </div>
                                    );
                                  })
                                }
                              </div>
                              : <div className="repay-date">
                                <span className="label">还款日期：</span>
                                <span className="value">{repayDateList && repayDateList[0]}</span>
                              </div>
                          }
                          {
                            ruleScopeValue !== "All" &&
                            <div className="ruleScopeItem">
                              <span className="label">适用范围：</span>
                              <span
                                className="value">{ruleScopeItem && ruleScopeItem.length > 0 ? ruleScopeItem.join("，") : ""}</span>
                            </div>
                          }
                        </div>
                      </div>);
                  }) : <NoGoods title="暂无记录"/>
              }
              {
                creditAmountList && creditAmountList.length > 0 &&
                <LoadingTip
                  isFinished={finished}
                  isLoad={isShow}
                />
              }
            </div>
          </Content>
        </Spin>
      </PageWrap>
    );
  }
}

const CreditAmountList = ScrollAbilityWrapComponent(CreditAmountListWrapper);
export default CreditAmountList;

const Content = styled.div`// styled
  & {
    height: auto;
    min-height: calc(${document.documentElement.clientHeight}px);
    background-color: #F2F2F2;
    .content-list{
      padding-top: 12px;
      .list-item{
        margin: 0 12px 12px;
        height: auto;
        border-bottom: 1px solid #D8D8D8;
        background-image: url("https://order.fwh1988.cn:14501/static-img/scm/img_bg_returned_status.png");
        -webkit-background-size: 100% 100%;background-size: 100% 100%;
        background-repeat: no-repeat;
        background-position: center center;
        border-radius: 8px;
        box-shadow:0px 0px 3px 0px rgba(0,0,0,0.05);
        color: #595959;
        font-size: 13px;
        line-height: 13px;
        .label{
           display: inline-block;
           width: 70px;
         }
        .item-top{
          width: 100%;
          padding: 12px 12px 8px;
          overflow: hidden;
          .top-title{
            color: #307DCD;
            font-size: 14px;
            line-height: 14px;
            margin-bottom: 12px;
          }
          .top-content{
            width: 100%;
            .top-left{
              float: left;
              width: auto;
              color: #2F2F2F;
              .available-amount{
                font-size: 14px;
                line-height: 14px;
                margin-bottom: 8px;
                >span{
                  font-size: 24px;
                  line-height: 24px;
                }
              }
               .text{
                  padding-left: 18px;
                }
            }
            .top-right{
              float: right;
              width: auto;
              padding-top: 11px;
              .total-amount{
                margin-bottom: 8px;
              }
            }
          }
        }
        .item-bottom{
          border-top: 1px solid #D8D8D8;
          box-shadow:0px 0px 0px 0px rgba(216,216,216,1);
          padding: 9px 12px 7px;
          >div{
            position: relative;
            padding-left: 65px;
            line-height: 18px;
            margin-bottom: 2px;
            min-height: 18px;
            .label{
              position: absolute;
              left: 0;
            }
          }
        }
      }
      .list-item:last-of-type{
        padding-bottom: 0;;
      }
    }
  }
`;

const PageWrap = styled.div`// styled
  & {
    width: 100%;
    height: auto;
  }
`;
