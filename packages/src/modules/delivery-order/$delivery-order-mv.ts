import { autowired, bean } from "@classes/ioc/ioc";
import { action, observable } from "mobx";
import { $ComponentService } from "../../classes/service/$component-service";

@bean($DeliveryOrderMv)
export class $DeliveryOrderMv {
  @autowired($ComponentService)
  public $componentService: $ComponentService;

  @observable public deliveryOrder: any;

  @observable public totalQuantity: number;
  @observable public totalPzAmount: number;

  @observable public isSpin: boolean = false;

  @action
  public showSpin() {
    this.isSpin = true;
  }

  @action
  public hideSpin() {
    this.isSpin = false;
  }

  @action
  public fetchDeliveryOrder(params) {
    this.$componentService.queryDeliveryOrderList(params).then((data) => {
      const { deliveryOrderList, totalQuantity, totalPzAmount } = data;
      this.deliveryOrder = deliveryOrderList;
      this.totalQuantity = totalQuantity;
      this.totalPzAmount = totalPzAmount;
      this.hideSpin();
    });
  }

  @action
  public confirmGoods(deliveryOrderId) {
    return this.$componentService.confirmGoods(deliveryOrderId);
  }
}
