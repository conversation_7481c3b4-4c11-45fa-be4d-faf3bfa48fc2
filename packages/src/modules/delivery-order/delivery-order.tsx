import * as React from "react";
import { withRouter } from "react-router";
import { observer } from "mobx-react";
import { autowired } from "@classes/ioc/ioc";
import { $DeliveryOrderMv } from "./$delivery-order-mv";
import styled from "styled-components";
import { NoGoods } from "../../components/no-goods/no-goods";
import { SITE_PATH } from "../app";
import { Spin } from "antd";

@withRouter
@observer
class DeliveryOrder extends React.Component<any, any> {

  @autowired($DeliveryOrderMv)
  public $deliveryOrderMv: $DeliveryOrderMv;

  constructor(props) {
    super(props);
  }

  public componentDidMount() {
    document.title = "发货单列表";
    this.loadDeliveryOrder();
  }

  public loadDeliveryOrder = () => {
    const { orderId } = this.props.match.params;
    const params = { orderId };
    this.$deliveryOrderMv.showSpin();
    this.$deliveryOrderMv.fetchDeliveryOrder(params);
  }

  public goToLogisticStracking = (order) => {
    console.log(order);
    const { deliveryOrderId } = order;
    this.props.history.push({
      pathname: `/${SITE_PATH}/receive-order-detail/${deliveryOrderId}`,
    });
  }

  public confirmGoods = (deliveryOrderId) => {
    this.$deliveryOrderMv.confirmGoods(deliveryOrderId);
  }

  public goToDetail = (deliveryOrderId) => {
    this.props.history.push({
      pathname: `/${SITE_PATH}/goods-list/${deliveryOrderId}/DeliveryOrder`,
    });
  }

  public render() {
    const { deliveryOrder, totalQuantity, totalPzAmount, isSpin } = this.$deliveryOrderMv;
    return (
      <DeliveryPage>
        {
          deliveryOrder && deliveryOrder.length > 0 ?
            <DeliveryHeader>
              <DeliveryHeaderRow>
                <span><img src="https://order.fwh1988.cn:14501/static-img/scm/ico-bg-goods.png" alt=""/>应出库商品</span>
                <span>
                  <span style={{ color: "#307DCD", fontSize: 14 }}>{totalQuantity}</span>
                  <span style={{ color: "#3F3F3F", fontSize: 14 }}> 件</span>
                </span>
              </DeliveryHeaderRow>
              <DeliveryHeaderRow>
                <span><img src="https://order.fwh1988.cn:14501/static-img/scm/ico-bg-goods.png" alt=""/>配赠额度</span>
                <span>
                  <span style={{ color: "#307DCD", fontSize: 14 }}>{totalPzAmount}</span>
                  <span style={{ color: "#3F3F3F", fontSize: 14 }}> 已获得</span>
                </span>
              </DeliveryHeaderRow>
            </DeliveryHeader> : null
        }
        <Spin spinning={isSpin}>
          <DeliveryLists>
            {
              deliveryOrder ? deliveryOrder.length > 0 ?
                deliveryOrder.map((order, index) => {
                  return (
                    <Delivery key={index}>
                      <div>
                        <span>发货单：{order.docNo}</span>
                        <span>{order.status}</span>
                      </div>
                      <div onClick={() => this.goToDetail(order.deliveryOrderId)}>
                        <p><span>发货仓库：</span><span>{order.warehouseOrgName}</span></p>
                        <p><span>订货组织：</span><span>{order.orderOrgName}</span></p>
                        <p><span>发货数量：</span><span>{order.totalQuantity}件</span></p>
                        <p><span>物流公司：</span><span>{order.logisticsCompany}</span></p>
                        <p><span>物流单号：</span><span>{order.logisticsDocumentNo}</span></p>
                      </div>
                      <div>
                        {
                          order.isDeliveryBtnShow ?
                            <span onClick={() => this.confirmGoods(order.deliveryOrderId)}>确认收货</span> : null
                        }
                        <span
                          onClick={() => this.goToLogisticStracking(order)}>查看详情</span>
                      </div>
                    </Delivery>
                  );
                })
                : <NoGoods title="暂无发货单" height={document.documentElement.clientHeight - 50}/> : null
            }
          </DeliveryLists>
        </Spin>
      </DeliveryPage>
    );
  }
}

export default DeliveryOrder;

const DeliveryPage = styled.div`// styled

  & {
    width: 100%;
    height: auto;
    background: #F2F2F2;
  }
`;

const DeliveryLists = styled.div`// styled

  & {
    width: 100%;
    height: auto;
    background: #F2F2F2;
  }
`;

const DeliveryHeader = styled.div`// styled
  background: #F2F2F2;
  font-size: 14px;
  color: #333333;
  padding-bottom: 10px;
  position: sticky;
  top: 0;
  z-index: 1;
`;

const DeliveryHeaderRow = styled.div`// styled

  & {
    width: 100%;
    height: 40px;
    padding: 0 15px;
    z-index: 99;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid #D8D8D8;
    background: #fff;

    &:last-child{
      border-bottom: none;
    }

    > span:nth-of-type(1) {
      display: flex;
      align-items: center;
      > img {
        width: 20px;
        height: 20px;
        margin-right: 5px;
      }
    }
  }
`;

const Delivery = styled.div`// styled

  & {
    width: 100%;
    background-color: #fff;
    box-sizing: border-box;
    margin-bottom: 10px;

    > div:nth-of-type(1) {
      position: relative;
      border-bottom: 1px solid #D8D8D8;
      padding: 10px 15px;
      color: #333333;
      font-size: 14px;

      > span:last-child {
        position: absolute;
        top: 10px;
        right: 15px;
        color: #FF3030;
        font-size: 12px;
      }
    }

    > div:nth-of-type(2) {
      position: relative;
      border-bottom: 1px solid #D8D8D8;
      padding: 10px 15px 0px 15px;

      > p {
        margin-bottom: 10px;

        > span:nth-of-type(1) {
          color: #999999;
          font-size: 12px;
        }

        > span:nth-of-type(2) {
          color: #2A2A2A;
          font-size: 12px;
        }
      }
    }

    > div:nth-of-type(3) {
      position: relative;
      padding: 10px 15px;
      height: 50px;

      > span {
        float: right;
        color: #307DCD;
        border: 0.5px solid #307DCD;
        border-radius: 3px;
        width: 72px;
        height: 30px;
        padding: 5px;
        margin-left: 15px;
        font-size: 12px;
        text-align: center;
      }

      > span:nth-of-type(1) {
        color: #FF3030;
        border: 1px solid #FF3030;
      }
    }
  }
`;
