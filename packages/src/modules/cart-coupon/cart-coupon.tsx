import { autowired } from "@classes/ioc/ioc";
import { Spin } from "antd";
import { Button, Checkbox, List } from "antd-mobile";
import { observer } from "mobx-react";
import * as React from "react";
import { withRouter } from "react-router";
import styled from "styled-components";
import DateUtils from "../../classes/utils/DateUtils";
import { SITE_PATH } from "../app";
import { $PromotionMatchMv } from "../promotion-match/promotion-match-mv";
import { $CartCouponMv } from "./cart-coupon-mv";
import { $AppStore, AppStoreKey } from "../../classes/stores/app-store-mv";

const Item = List.Item;
const CheckboxItem = Checkbox.CheckboxItem;

@withRouter
@observer
class CartCoupon extends React.Component<any, any> {
  @autowired($AppStore)
  public $AppStore: $AppStore;
  @autowired($CartCouponMv)
  public $mv: $CartCouponMv;

  @autowired($PromotionMatchMv)
  public $PromotionMatchMv: $PromotionMatchMv;

  constructor(props) {
    super(props);
    this.state = {};
  }

  public componentDidMount() {
    this.$mv.setCouponData(this.props.location.state.couponData);
    const params = {
      promotionId: this.props.location.state.couponData.promotionId,
      status: "NotUsed",
    };
    this.$mv.showSpin();
    this.$mv.fetchCouponList(params).then((data) => {
      this.$mv.hideSpin();
      this.$mv.setCouponList(data.couponList);
      this.$mv.setMaxLimit(data.maxLimit);
    });
  }

  public onChange = (index) => {
    this.$mv.setChecked(index);
  }

  public onSubmit = () => {
    this.$mv.saveCheckedCoupon();
    this.$AppStore.clearPageMv(AppStoreKey.PROMOTIONMATCH);
    setTimeout(() => {
      this.props.history.push({
        pathname: `/${SITE_PATH}/promotion/match`,
        state: {
          shopCartTotalAmount: this.props.location.state.shopCartTotalAmount,
          backSource: this.props.location.state ? this.props.location.state.backSource : null,
          prePageSource: this.props.location.state ? this.props.location.state.prePageSource : null,
        },
      });
    }, 0);
  }

  public render() {
    const { couponActiveList, couponNotActiveList, isSpin } = this.$mv;
    return (
      <CartCouponWrapper>
        <Spin spinning={isSpin}>
          {
            couponActiveList ? couponActiveList.map((v, index) => {
              return <CouponItemWrapper key={v.couponId}>
                <div className="check-box">
                  <CheckboxItem onChange={() => this.onChange(index)} checked={v.checked}>
                  </CheckboxItem>
                </div>
                <CouponItem>
                  <div className="coupon-box">
                    <div className="box-left">
                      <p>{v.actionType === "Deduct" ? `${v.actionValue ? "¥" + v.actionValue : v.actionTypeLabel}` : v.actionTypeLabel}</p>
                      <p>{v.orderCondition ? `${v.orderCondition.slice(2)}` : null}</p>
                    </div>
                    <div className="box-right">
                      <h4>{v.couponName ? v.couponName.length > 10 ? `${v.couponName.slice(0, 10)}...` : v.couponName : null}</h4>
                      <div className="bottom">
                        <p>适用范围：{v.productCondition}</p>
                        <p>有效期至：{DateUtils.toStringFormat(v.expiryTime, "yyyy-MM-dd")}</p>
                      </div>
                      <div className="coupon-img normal">
                        <div>{v.tag}</div>
                      </div>
                    </div>
                  </div>
                </CouponItem>
              </CouponItemWrapper>;
            }) : null
          }
          <div className="line">
            <img className="left" src="https://order.fwh1988.cn:14501/static-img/scm/icon-title-line-left-gray.png"
                 alt=""/>
            以下卡券不可用
            <img className="right" src="https://order.fwh1988.cn:14501/static-img/scm/icon-title-line-right-gray.png"
                 alt=""/>
          </div>
          {
            couponNotActiveList ? couponNotActiveList.map((v) => {
              return <CouponItemWrapper key={v.couponId}>
                <div className="check-box">
                  <CheckboxItem disabled={true}>
                  </CheckboxItem>
                </div>
                <CouponItem>
                  <div className="coupon-box">
                    <div className="box-left gray">
                      <p>{v.actionType === "Deduct" ? `${v.actionValue ? "¥" + v.actionValue : v.actionTypeLabel}` : v.actionTypeLabel}</p>
                      <p>{v.orderCondition ? `${v.orderCondition.slice(2)}` : null}</p>
                    </div>
                    <div className="box-right">
                      <h4>{v.couponName ? v.couponName.length > 10 ? `${v.couponName.slice(0, 10)}...` : v.couponName : null}</h4>
                      <div className="bottom">
                        <p>适用范围：{v.productCondition}</p>
                        <p>有效期至：{DateUtils.toStringFormat(v.expiryTime, "yyyy-MM-dd")}</p>
                      </div>
                      <div className="coupon-img overdue">
                        <div>{v.tag}</div>
                      </div>
                    </div>
                  </div>
                </CouponItem>
              </CouponItemWrapper>;
            }) : null
          }
        </Spin>
        <ConfirmButton>
          <Button type="primary" onClick={this.onSubmit}>完成</Button>
        </ConfirmButton>
      </CartCouponWrapper>
    );
  }
}

export default CartCoupon;

const CartCouponWrapper = styled.div`
  &{
    width:100%;
    min-height:${document.documentElement.clientHeight}px;
    background: #ECF6FF;
    padding:15px 15px 62px 0;
    .am-checkbox-inner:after{
      top:2.5px;
      right:7px;
    }
    .line{
      text-align:center;
      font-family: MicrosoftYaHei;
      font-size: 14px;
      color: #666666;
      margin-bottom:15px;
      margin-left:15px;
      padding:5px 0;
      img{
        width:40px;
        position:relative;
        top:-1px;
      }
      .left{
        margin-right:17px;
      }
      .right{
        margin-left:17px;
      }
    }
  }
`;

const CouponItemWrapper = styled.div`
  &{
    display:flex;
    margin-bottom:15px;
    align-items:center;
    .check-box{
      width:52px;
      // padding-top:20%;
      .am-list-item{
        background:none;
      }
    }
  }
`;

const CouponItem = styled.div`
  &{
    flex:1;
    .coupon-box{
      background:url("https://order.fwh1988.cn:14501/static-img/scm/ico-voucher-bg-s.png") no-repeat;
      background-size:cover;
      display:flex;
      align-items:center;
      height:${(document.documentElement.clientWidth - 30) * 160 / 614}px;
      .box-left{
        flex:2.2;
        text-align:center;
        color:#307DCD;
        font-size:10px;
        &.gray{
          color:#A0A0A0;
        }
        p{
          margin:0;
          padding:0 8px;
        }
        P:first-child{
          font-size:20px;
        }
      }
      .box-right{
        flex:5;
        position:relative;
        height:${(document.documentElement.clientWidth - 30) * 160 / 614}px;
        h4{
          font-size:14px;
          color:#333333;
          margin:0;
          position:absolute;
          top:9px;
          left:14px;
        }
        .bottom{
          font-size:10px;
          color:#999999;
          margin:0;
          position:absolute;
          bottom:9px;
          left:8px;
          transform:scale(0.9);
          p{
            margin:0;
          }
        }
        .coupon-img{
          position:absolute;
          right:13px;
          top:14px;
          height:${(document.documentElement.clientWidth - 30) * 160 / 614 - 30}px;
          width:${((document.documentElement.clientWidth - 30) * 160 / 614 - 30) * 128 / 110}px;
          div{
            font-size:10px;
            position:absolute;
            width:100%;
            bottom:2px;
            text-align:center;
            height:12px;
            line-height:12px;
          }
          &.normal{
            background:url("https://order.fwh1988.cn:14501/static-img/scm/ico-voucher-state-normal.png");
            background-size:cover;
          }
          &.overdue{
            background:url("https://order.fwh1988.cn:14501/static-img/scm/ico-voucher-state-overdue.png");
            background-size:cover;
          }
        }
      }
    }
  }
`;

const ConfirmButton = styled.div`
  &{
    padding: 15px;
    left:0;
    width:100%;
    position:fixed;
    bottom: 0px;
    background:#ECF6FF;
    .am-button{
      height:42px;
      line-height:42px;
      font-size:16px;
    }
    .am-button-primary{
      border-radius:3px;
    }
  }
`;
