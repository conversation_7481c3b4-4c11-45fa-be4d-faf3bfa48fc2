import { autowired, bean } from "@classes/ioc/ioc";
import { Toast } from "antd-mobile";
import { action, extendObservable, observable } from "mobx";
import { $ComponentService } from "../../classes/service/$component-service";
import { $MyInfoService } from "../../classes/service/$my-info-service";
import { $PromotionMatchMv } from "../promotion-match/promotion-match-mv";

@bean($CartCouponMv)
export class $CartCouponMv {
  @autowired($MyInfoService)
  public $myInfoService: $MyInfoService;

  @autowired($ComponentService)
  public $componentService: $ComponentService;

  @autowired($PromotionMatchMv)
  public $PromotionMatchMv: $PromotionMatchMv;

  @observable public couponList: any[];

  @observable public couponActiveList: any;

  @observable public couponNotActiveList: any;

  @observable public couponCheckedData: object;

  @observable public maxLimit: number;

  @observable public checkedNum: number = 0;

  @observable public isSelectCoupon: boolean = false;

  @observable public couponData: object;

  @observable public isSpin: boolean = false;

  @action
  public showSpin() {
    this.isSpin = true;
  }

  @action
  public hideSpin() {
    this.isSpin = false;
  }

  @action
  public fetchCouponList(params) {
    return this.$componentService.queryCouponList(params);
  }

  @action
  public setCouponData(data) {
    this.couponData = data;
  }

  @action
  public setCouponList(list) {
    this.couponList = list;
    this.couponActiveList = [];
    this.couponNotActiveList = [];
    this.couponList.map((v) => {
      extendObservable(v, {
        checked: false,
      });
      if (v.isActive === "Y") {
        this.couponActiveList.push(v);
      } else {
        this.couponNotActiveList.push(v);
      }
    });
    // console.log(this.$PromotionMatchMv.couponData);
    this.couponActiveList.map((v) => {
      this.couponData.usedCouponList.map((data) => {
        // console.log(v.couponId+"---"+data.couponId);
        if (v.couponId === data.couponId) {
          v.checked = true;
        }
      });
    });
    if (this.$PromotionMatchMv.couponData) {
      if (this.$PromotionMatchMv.couponData.usedCouponList) {
        this.checkedNum = this.$PromotionMatchMv.couponData.usedCouponList.length;
      }
    }
  }

  @action
  public setMaxLimit(maxLimit) {
    this.maxLimit = maxLimit;
  }

  @action
  public setChecked(index) {
    const { checked } = this.couponActiveList[index];
    if (checked === true) {
      this.couponActiveList[index].checked = false;
      this.checkedNum = this.checkedNum - 1;
    } else {
      if (this.maxLimit === null) {
        this.couponActiveList[index].checked = true;
        this.checkedNum = this.checkedNum + 1;
      } else {
        if (this.checkedNum === this.maxLimit) {
          Toast.info(`最多可选${this.checkedNum}个优惠券`, 3);
        } else {
          this.couponActiveList[index].checked = true;
          this.checkedNum = this.checkedNum + 1;
        }
      }
    }
  }

  @action
  public saveCheckedCoupon() {
    this.couponCheckedData = {};
    this.couponCheckedData.promotionId = this.couponData.promotionId;
    this.couponCheckedData.couponCheckedList = [];
    this.couponActiveList.map((v) => {
      if (v.checked) {
        this.couponCheckedData.couponCheckedList.push(v);
      }
    });
    this.isSelectCoupon = true;
  }
}
