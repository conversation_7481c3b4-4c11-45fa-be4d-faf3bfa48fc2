import { observer } from "mobx-react";
import React from "react";
import { with<PERSON>out<PERSON> } from "react-router";
import styled from "styled-components";
import { REQUEST_SERVER } from "../../helpers/ajax-helpers";

declare let window: any;

@withRouter
@observer
class TestImg extends React.Component<any, any> {

  public changeImg = (e) => {
    console.log(e.target.files);
    const file = window.document.getElementById("file").value; // 获取文件
    console.log(file);
    const index = file.lastIndexOf("."); // 获取最后一位小数点
    const extension = file.substr(index + 1);
    const arr = ["jpeg", "png", "jpg", "gif"];
    if (this.isInArray(arr, extension)) {
      this.UpladFile(e.target.files[0]);
    } else {
      alert("请选择正确的图片格式");
      return false;
    }
  }

  public UpladFile = (file) => {
    const formData = new FormData();
    formData.append("file", file);

    const xhr = new XMLHttpRequest();

    xhr.open("POST", `${REQUEST_SERVER}/k/integration/scm/payvoucher/upload`, true);
    xhr.setRequestHeader("ssoSessionId", localStorage.getItem("token"));
    xhr.onload = () => {
      if (xhr.status === 200) {
        const url = JSON.parse(xhr.responseText).data.data.pic.url;
        console.log(url);
        const img = window.document.getElementById("previewimg");
        img.src = url;
      }

    };
    xhr.send(formData);
  }

  public isInArray = (arr, value) => {
    for (let i = 0; i < arr.length; i++) {
      if (value === arr[i]) {
        return true;
      }
    }
    return false;
  }

  public render() {
    return (
      <TestImgPage>
        <div className="uploadImgBtn" id="uploadImgBtn">
          <input
            className="uploadImg"
            type="file"
            name="file"
            id="file"
            accept="image/*"
            onChange={this.changeImg}
          />
        </div>
        <div>
          <img src={"https://order.fwh1988.cn:14501/static-img/scm/ico-nopic.png"} id="previewimg" width="50px"
               height="50px"/>
        </div>
      </TestImgPage>
    );
  }
}

export default TestImg;

const TestImgPage = styled.div`// styled
  & {
    .uploadImgBtn {
      width: 50px;
      height: 50px;
      cursor: pointer;
      position: relative;
      background: url("https://order.fwh1988.cn:14501/static-img/scm/icon-add.png") no-repeat;
      -webkit-background-size: cover;
      background-size: cover;
    }

    .uploadImgBtn .uploadImg {
      position: absolute;
      right: 0;
      top: 0;
      width: 100%;
      height: 100%;
      opacity: 0;
      cursor: pointer;
    }
    //这是一个用做回显的盒子的样式
    .pic {
      width: 100px;
      height: 100px;
    }
    .pic img {
      width: 100%;
      height: 100%;
    }
  }
`;
