import { autowired } from "@classes/ioc/ioc";
import { Spin } from "antd";
import { Modal, SearchBar, Toast } from "antd-mobile";
import { observer } from "mobx-react";
import * as React from "react";
import { with<PERSON>outer } from "react-router";
import styled from "styled-components";
import { NoGoods } from "../../components/no-goods/no-goods";
import { SITE_PATH } from "../app";
import { $CartMv } from "../shop-cart/cart-mv";
import { $ExpenseNodeListMV } from "./expense-node-list-mv";
import { ScreeningStores } from "../../components/screening-stores/screening-stores";
import { map } from "lodash";
import { SingleExpenseOrderInfo } from "../../components/single-expense-order-info/single-expense-order-info";
import { ScrollAbilityWrapComponent } from "../../components/scroll-ability/wrap";
import { LoadingTip } from "../../components/loading-marked-words";
import { gaEvent } from "../../classes/website-statistics/website-statistics";
import { $AppStore, AppStoreKey } from "../../classes/stores/app-store-mv";
import { toJS, transaction } from "mobx";
import disabledOprate from "../../components/noWX-disabled-operate";
import { GoHome } from "../../components/go-home/go-home";

const alert = Modal.alert;
declare let window: any;

@withRouter
@observer
class ExpenseNodeListWrap extends React.Component<any, any> {
  @autowired($CartMv)
  public $CartMv: $CartMv;
  @autowired($AppStore)
  public $AppStore: $AppStore;
  @autowired($ExpenseNodeListMV)
  public $myMv: $ExpenseNodeListMV;

  constructor(props) {
    super(props);
    this.state = ({
    });
  }
  // 离开记录滚动高度
  public saveMV = () => {
    // this.$myMv.scrollHeight = $(".scroll-ability-wrap").scrollTop();
    // this.$AppStore.savePageMv(AppStoreKey.ALLSHOPORDERLISTSTORE, this.$myMv);
  }

  public componentWillUnmount(): void {
    this.saveMV();
  }
  public componentDidMount() {
    console.log("this.props.match.params", this.props.match.params);
    const orgId = this.props.match.params.orgId;
    const { orderStatus } = this.$myMv;
    const activeType = this.props.match.params.activeType;
    const activeArrLength = orderStatus.filter((item) => item.value === activeType).length;
    this.$myMv.orgId = orgId === "null" ? null : orgId;
    this.$myMv.key = activeArrLength ? activeType : "All";
    document.title = "费用单列表";
    gaEvent("费用单列表");
    setTimeout(() => {
      // const oldData = this.$AppStore.getPageMv(AppStoreKey.ALLSHOPORDERLISTSTORE);
      const oldData = null;
      if (oldData) {
        this.$myMv.queryOldData(JSON.parse(oldData));
        this.$AppStore.clearPageMv(AppStoreKey.ALLSHOPORDERLISTSTORE);
        $(".scroll-ability-wrap").scrollTop(this.$myMv.scrollHeight);
      } else {
        this.$myMv.clearMVData();
        this.initPage();
      }
    }, 50);
  }

  public initPage = () => {
    transaction(() => {
      this.$myMv.orderpartyList({ pageIndex: 0, pageSize: 99999999 }).then(() => {
        const { searchContent, key } = this.$myMv;
        console.log(this.props.location);
        if (this.props.location.state) {
          const { status } = this.props.location.state;
          this.searchOrder(status, searchContent);
          this.$CartMv.setIsAgency();
        } else {
          this.searchOrder(key, searchContent);
        }
      });
    });
  }
  public componentWillReceiveProps(nextProps: Readonly<any>, nextContext: any): void {
    const { isScrollEnd } = nextProps;
    if (isScrollEnd && isScrollEnd !== this.props.isScrollEnd) {
      this.loadData();
    }
  }

  public loadData = () => {
    const { key, searchContent, finished, showOrderPop } = this.$myMv;
    if (finished || showOrderPop) {
      return;
    }
    this.$myMv.changePage();
    this.searchOrder(key, searchContent);
  }

  public searchOrder = (val, searchContent) => {
    const { pageIndex, pageSize } = this.$myMv;
    if ((val === "All" || val === "Payment" || val === "Done") && pageIndex === 0) {
      this.$myMv.orderListInfo = [];
    }
    const { orgId } = this.$myMv;
    const params = {
      orderPartyIdList: orgId ? [Number(orgId)] : map(this.$myMv.orderPartyList.filter((party) => party.checked), "oid"),
      pageIndex,
      pageSize,
      keyword: searchContent,
      type: val,
    };
    this.$myMv.key = val;
    this.$myMv.showOrderPop = false;
    if (!orgId && map(this.$myMv.orderPartyList.filter((party) => party.checked), "oid").length === 0) {
      this.$myMv.orderListInfo = [];
      this.$myMv.finished = true;
      this.$myMv.isLoad = false;
      return;
    }
    this.$myMv.showSpin();
    this.$myMv.getList(params).then((res) => {
      this.$myMv.hideSpin();
      const { itemCount, feeDocumentList } = res;
      const { loadingEnd } = this.props;
      if (feeDocumentList) {
        this.$myMv.orderListInfo = pageIndex ? this.$myMv.orderListInfo.concat(feeDocumentList) : feeDocumentList;
        this.$myMv.finished = this.$myMv.orderListInfo.length >= itemCount;
        this.$myMv.isLoad = false;
        loadingEnd && loadingEnd();
      } else {
        this.$myMv.finished = true;
        this.$myMv.isLoad = false;
        loadingEnd && loadingEnd();
      }
    }).catch(() => {
      this.$myMv.hideSpin();
    });
  }
  public pageWindowSkip = (url) => {
    this.saveMV();
    setTimeout(() => {
      window.location.href = url;
    }, 50);
  }
  public orderButton = (docId, canPartialPay) => {
    this.$AppStore.clearPageMv(AppStoreKey.SINGLEPAYMENTSTORAGE);
    this.pageWindowSkip(`/${SITE_PATH}/submit/order-payment/:record/${docId}/:showPaymentBtn?feeDocumentId=${docId}&canPartialPay=${canPartialPay}`);
  }
  public goToOrderDetail = (docId) => {
    console.log("orderId", docId);
    this.props.history.push({
      pathname: `/${SITE_PATH}/expense-node-detail/${docId}`,
    });
  }

  public onChangeSearch = (val) => {
    console.log(val);
    this.$myMv.searchContent = val;
  }

  public onSearch = () => {
    const { key, searchContent } = this.$myMv;
    this.$myMv.setPage();
    this.searchOrder(key, searchContent);
    $("html").css("overflow", "scroll");
    $("body").css("overflow", "scroll");
    $(".scroll-ability-wrap").css("overflow", "scroll");
  }

  public showChooseShop = () => {
    this.$myMv.showOrderPop = true;
    $("html").css("overflow", "hidden");
    $("body").css("overflow", "hidden");
    $(".scroll-ability-wrap").css("overflow", "hidden");
    $(".scroll-ability-wrap").scrollTop(0);
  }

  public canclePop = () => {
    this.$myMv.showOrderPop = false;
    $("html").css("overflow", "scroll");
    $("body").css("overflow", "scroll");
    $(".scroll-ability-wrap").css("overflow", "scroll");
  }

  public changeOrderStatus = (value, searchContent) => {
    this.$myMv.setPage();
    setTimeout(() => {
      $("html").css("overflow", "scroll");
      $("body").css("overflow", "scroll");
      $(".scroll-ability-wrap").css("overflow", "scroll");
    }, 10);
    this.searchOrder(value, searchContent);
  }
  public goHome = () => {
    this.saveMV();
  }
  public confirmSearch = (selectObj) => {
    const { orderPartyList } = selectObj;
    this.$myMv.orderPartyList = orderPartyList || [];
    this.$myMv.showOrderPop = false;
    this.onSearch();
  }

  public goToCombinedpayment = () => {
    if (localStorage.getItem("scanningLogin") === "Y") {
      disabledOprate();
    } else {
      const { searchContent } = this.$myMv;
      this.props.history.push({
        pathname: `/${SITE_PATH}/expense-node-combined-payment`,
        state: {
          searchText: searchContent,
        },
      });
    }
  }

  public render() {
    const { orderStatus, orderListInfo, isSpin, orderPartyList, isLoad, key, searchContent, showOrderPop, finished, orgId } = this.$myMv;
    console.log("this.$myMv", this.$myMv);
    return (
      <PageWrap className="page-wrap">
        {
          !orgId &&
            <SelectBar>
              <SearchBar
                value={searchContent}
                placeholder="编码"
                onSubmit={this.onSearch}
                onChange={this.onChangeSearch}
              />
              <div
                className="right"
                style={{
                  borderLeft:  "none",
                  // width: "20%",
                }}
              >
                <span onClick={this.goToCombinedpayment}>合并付款</span>
                <span onClick={this.showChooseShop}>筛选</span>
              </div>
            </SelectBar>
        }
        <SearchTypeBar
          theme={{orgId}}
        >
          {
            orderStatus.map((status, index) => {
              return (
                <div
                  key={index}
                  onClick={() => this.changeOrderStatus(status.value, searchContent)}>
                  <span
                    className={status.value === key ? "active" : null}
                  >
                    {status.name}
                  </span>
                </div>
              );
            })
          }
        </SearchTypeBar>
        <Spin spinning={isSpin}>
          <OrderLists
            className={"orderList"}
            theme={{orgId}}
          >
            {
              orderListInfo ? orderListInfo.length > 0 ?
                orderListInfo.map((order, index) => {
                  return <div
                    key={order.docId}
                    onClick={() => this.goToOrderDetail(order.docId)}
                  >
                    <SingleExpenseOrderInfo
                      order={order}
                      orderButton={this.orderButton}
                    />
                    <MarginBottom/>
                  </div>;
                })
                : <NoGoods title="暂无费用单" height={document.documentElement.clientHeight - 90}/> :
                <NoGoods title="暂无费用单" height={document.documentElement.clientHeight - 90}/>
            }
            {
              orderListInfo && orderListInfo.length > 0 ?
                <LoadingTip
                  isFinished={finished}
                  isLoad={isLoad}
                /> : null
            }
          </OrderLists>
        </Spin>
        {
          showOrderPop &&
            <ScreeningStores
              confirmSearch={this.confirmSearch}
              canclePop={this.canclePop}
              isHideOrderStatus={true}
              isHideScheme={true}
              orderPartyList={toJS(orderPartyList)}
            />
        }
       <GoHome
          goHome={this.goHome}
        />
      </PageWrap>
    );
  }
}

const ExpenseNodeList = ScrollAbilityWrapComponent(ExpenseNodeListWrap);
export default ExpenseNodeList;

const PageWrap = styled.div`// styled
  & {
    width: 100%;
    min-height: calc(${document.documentElement.clientHeight}px);
    height: auto;
    background-color: #f5f5f5;
    color: #2a2a2a;
    > .page-wrap span {
      text-align: center;
    }
  }
`;

const MarginBottom = styled.div`// styled
  & {
    width: 100%;
    height: 10px;
    background: #f5f5f5;
  }
`;

const SelectBar = styled.div`// styled
  & {
    width: 100%;
    height: 40px;
    background-color: #F2F2F2;
    position: fixed;
    z-index: 99;
    :after {
      content: '';
      position: absolute;
      background-color: #D8D8D8 !important;
      display: block;
      z-index: 1;
      top: auto;
      right: auto;
      bottom: 0;
      left: 0;
      width: 100%;
      height: 1px;
      transform-origin: 50% 100%;
      transform: scaleY(0.5);
    }
    .am-search {
      background-color: #F2F2F2;
      padding: 7px 10px 7px 16px;
      height: 39px;
      width: 62% !important;
      display: inline-block;
    }
    .am-search-input {
      border-radius: 13px;
      height: 26px;
    }
    .am-search-input .am-search-clear {
      padding: 5.5px;
    }
    .am-search-input .am-search-synthetic-ph {
      height: 26px;
      line-height: 26px;
      width: 55% !important;
      text-align: left;
      padding-left: 15px;
    }
    .am-search-input input[type="search"] {
      height: 26px;
      padding-left: 34px !important;
    }
    .am-search-cancel {
      display: none;
    }
    .am-search-synthetic-ph-placeholder { /* WebKit, Blink, Edge */
      font-family: PingFangSC-Regular;
      font-size: 14px;
      color: #999999;
    }
    .right{
      float: right;
      width: 38%;
      border-left: 1px solid #D8D8D8;
      padding: 0 16px 0 7px;
      margin: 10px 0px;
      > span {
        float: right;
        font-size: 13px;
        font-family: "SourceHanSansCN-Normal";
        font-weight: 400;
        color: rgba(24, 144, 255, 1);
        width: 50%;
        text-align: center;
        /* overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap; */
      }
      >span:first-of-type{
        //margin-right: 0;
      }
    }
  }
`;

const SearchTypeBar = styled.div`// styled
  & {
    width: 100%;
    height: 40px;
    line-height: 20px;
    background-color: #fff;
    display: flex;
    justify-content: space-around;
    flex-direction: row;
    text-align: center;
    position: fixed;
    top: ${(props) => props.theme.orgId ? "0px" : "40px"};
    color: #8a8a8a;
    z-index: 99;
    padding: 10px 16px;
    box-sizing: border-box;
    > div {
      height: 30px;
      //flex: 1;
      > span {
        display: inline-block;
        height: 30px;
        font-size: 14px;
        color: #666;
      }
      > .active {
        color: #307DCD;
        font-size: 14px;
        border-bottom: 2px solid #307DCD;
      }
    }
    > div:nth-of-type(1) {
      text-align: left;
      width: 10%;
    }
    > div:nth-of-type(2) {
      width: 25%;
    }
    > div:nth-of-type(3) {
      width: 25%;
    }
    > div:nth-of-type(4) {
      width: 25%;
    }
    > div:last-child {
      text-align: right;
      width: 15%;
    }
  }
`;

const OrderLists = styled.div`// styled
  & {
    width: 100%;
    /*height: 600px;*/
    padding-top: ${(props) => props.theme.orgId ? "50px" : "90px"};
    // overflow-y: auto;
  }
`;

const Order = styled.div`// styled
  & {
    width: 100%;
    /*height: 360px;*/
    background-color: #fff;
    box-sizing: border-box;
    // margin-bottom: 10px;
    > div:nth-of-type(1) {
      > div {
        position: relative;
        :after {
          content: '';
          position: absolute;
          background-color: #D8D8D8 !important;
          display: block;
          z-index: 1;
          top: auto;
          right: auto;
          bottom: 0;
          left: 0;
          width: 100%;
          height: 1.2px;
          transform-origin: 50% 100%;
          transform: scaleY(0.5);
        }
      }
      > div:nth-of-type(1) {
        // border-bottom: 1px solid #D8D8D8;
        position: relative;
        padding: 10px 15px;
        > span:nth-of-type(1) {
          font-size: 14px;
          color: #333;
          font-family: "MicrosoftYaHei";
        }
        > .originType {
          display: inline-block;
          background: #ECF6FF;
          color: #307DCD;
          margin-left: 5px;
          text-align: center;
          border-radius: 2px;
          font-size: 10px;
          font-family: "MicrosoftYaHei";
        }
        > span:last-child {
          position: absolute;
          top: 10px;
          right: 15px;
          color: #FF3030;
          font-size: 12px;
          font-family: "MicrosoftYaHei";
        }
      }

      > div:nth-of-type(2) {
        // border-bottom: 1px solid #D8D8D8;
        padding: 10px 15px;
        > p {
          margin-bottom: 10px;
        }

        > p {
          > span:nth-of-type(1) {
            color: #999999;
            font-size: 12px;
            font-family: "MicrosoftYaHei";
          }
          > span:nth-of-type(2) {
            color: #666;
            font-size: 12px;
            font-family: "MicrosoftYaHei";
          }
        }
        > p:last-child {
          margin: 0;
        }
      }
      > div:nth-of-type(3) {
        // border-bottom: 1px solid #D8D8D8;
        padding: 10px 15px;
        > p {
          margin-bottom: 10px;
        }
        .name {
          color: #999999;
          font-size: 12px;
        }
        .amount {
          color: #FF3030;
          font-size: 12px;
        }
        .status {
          position: absolute;
          top: 10px;
          right: 15px;
          color: #FF8627;
          font-size: 12px;
          font-family: "MicrosoftYaHei";
        }
        .payment {
          color: #FF7362;
          background: #FFEBE8;
          padding: 2px 6px;
          border: 1px solid transparent;
          border-radius: 2px;
          font-size: 10px;
        }

        > p:last-child {
          margin: 0;
        }
      }

      > div:nth-of-type(4) {
        // border-bottom: 1px solid #D8D8D8;
        padding: 10px 15px;

        > p {
          margin-bottom: 10px;
        }

        .name {
          color: #999999;
          font-size: 12px;
        }

        .amount {
          color: #FF3030;
          font-size: 12px;
        }

        .status {
          position: absolute;
          top: 10px;
          right: 15px;
          color: #FF3030;
          font-size: 12px;
          font-family: "MicrosoftYaHei";
        }

        > p:last-child {
          margin: 0;
        }
      }

    }

    > div:nth-of-type(2) {
      position: relative;
      padding: 10px 15px;
      height: 50px;

      > span {
        float: right;
        color: #307DCD;
        // border: 0.5px solid #307DCD;
        border-radius: 3px;
        width: 22%;
        height: 30px;
        line-height: 20px;
        padding: 5px;
        margin-right: 15px;
        font-size: 12px;
        font-family: "MicrosoftYaHei";
        text-align: center;
        position: relative;
      }

      > span:after {
        content: "  ";
        position: absolute;
        left: 0;
        top: 0;
        z-index: 1;
        width: 200%;
        height: 200%;
        border: 1px solid #307DCD;
        border-radius: 3px;
        -webkit-transform-origin: 0 0;
        transform-origin: 0 0;
        -webkit-transform: scale(.5, .5);
        transform: scale(.5, .5);
      }

      > span:first-child {
        margin-right: 0px;
      }

      .nowPay {
        color: #FF3030;
        font-size: 12px;
        font-family: "MicrosoftYaHei";
        // border: 1px solid #FF3030;
        position: relative;
      }

      .nowPay:after {
        content: "  ";
        position: absolute;
        left: 0;
        top: 0;
        z-index: 1;
        width: 200%;
        height: 200%;
        border: 1px solid #FF3030;
        border-radius: 3px;
        -webkit-transform-origin: 0 0;
        transform-origin: 0 0;
        -webkit-transform: scale(.5, .5);
        transform: scale(.5, .5);
      }
    }
  }
`;
