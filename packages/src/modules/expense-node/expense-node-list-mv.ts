import { autowired, bean } from "@classes/ioc/ioc";
import { action, observable, ObservableSet } from "mobx";
import { $OrderService } from "../../classes/service/$order-service";
import { $OrderPartyService } from "../../classes/service/$order-party-service";
import { $SelectOrderparty } from "../../classes/entity/$select-orderparty";
import { beanMapper } from "../../helpers/bean-helpers";
import { $SalesOrderStatus } from "@classes/entity/$sales-order-status";
@bean($ExpenseNodeListMV)
export class $ExpenseNodeListMV {
  @autowired($OrderService)
  public $orderService: $OrderService;
  @autowired($OrderPartyService)
  public $orderPartyService: $OrderPartyService;

  @observable public orderStatus = [
    {
      activeKey: "All",
      name: "全部",
      value: "All",
    },
    {
      activeKey: "Payment",
      name: "待付款",
      value: "Payment",
    },
    {
      activeKey: "Done",
      name: "已完成",
      value: "Done",
    },
  ];
  @observable public pageIndex = 0;
  @observable public scrollHeight: number = 0;
  @observable public finished = false;
  @observable public isLoad = false;
  @observable public pageSize = 10;
  @observable public orgId = null;
  @observable public showOrderPop = false;
  @observable public key = "All";
  @observable public searchContent = "";
  @observable public orderListInfo = [];
  @observable public isSpin: boolean = false;
  @observable public orderPartyList: $SelectOrderparty[] = [];
  @action
  public showSpin() {
    this.isSpin = true;
  }

  @action
  public hideSpin() {
    this.isSpin = false;
  }
  @action
  public orderpartyList(params) {
    return this.$orderService.orderpartyList(params).then((data) => {
      this.orderPartyList = data.orderPartyList.map((party) => new $SelectOrderparty(party));
    });
  }

  @action
  public setSelectOrderParty(orderParty) {
    this.orderPartyList.map((status, index) => {
      if (status.oid === orderParty) {
        status.checked = !status.checked;
        status.showActiveOrderparty = !status.showActiveOrderparty;
      }
    });
  }

  @action
  public changePage() {
    this.pageIndex++;
  }

  @action
  public setPage() {
    this.pageIndex = 0;
  }

  @action
  public getList(params) {
    return this.$orderService.queryExpenseList(params);
  }

  @action
  public queryOldData(obj) {
    beanMapper(obj, this);
    console.log(this);
  }
  @action
  public clearMVData() {
    this.orderStatus = [
        {
          activeKey: "All",
          name: "全部",
          value: "All",
        },
        {
          activeKey: "Payment",
          name: "待付款",
          value: "Payment",
        },
        {
          activeKey: "Done",
          name: "已完成",
          value: "Done",
        },
      ];
    this.pageIndex = 0;
    this.scrollHeight = 0;
    this.finished = false;
    this.isLoad = false;
    this.pageSize = 10;
    this.showOrderPop = false;
    this.searchContent = "";
    this.orderListInfo = [];
    this.isSpin = false;
    this.orderPartyList = [];
  }
  /*
  * 费用单详情
  * */
  @observable public expenseOid = null;
  @observable public orderDetail = {};
  @action
  public getDetail(params) {
    return this.$orderService.queryExpenseOrderDetail(params);
  }
  @action
  public setOrderDetail(obj) {
    this.orderDetail = obj;
  }
  @action
  public clearMVDetailData() {
    this.orderDetail = {};
  }
}
