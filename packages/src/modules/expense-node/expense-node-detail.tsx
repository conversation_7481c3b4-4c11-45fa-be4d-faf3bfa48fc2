import { autowired } from "@classes/ioc/ioc";
import { Spin } from "antd";
import { List, Modal, SearchBar, Toast } from "antd-mobile";
import { observer } from "mobx-react";
import * as React from "react";
import { withRouter } from "react-router";
import styled from "styled-components";
import { SITE_PATH } from "../app";
import { $CartMv } from "../shop-cart/cart-mv";
import { $ExpenseNodeListMV } from "./expense-node-list-mv";
import { SingleExpenseOrderInfo } from "../../components/single-expense-order-info/single-expense-order-info";
import { gaEvent } from "../../classes/website-statistics/website-statistics";
import { $AppStore, AppStoreKey } from "../../classes/stores/app-store-mv";
import $OrderStatusType from '@classes/const/$order-status-type';
import { $OrderType } from '@classes/const/$order-type';
import DateUtils from '@classes/utils/DateUtils';
const Item = List.Item;
const alert = Modal.alert;
declare let window: any;

@withRouter
@observer
export default class ExpenseNodeDetailWrap extends React.Component<any, any> {
  @autowired($CartMv)
  public $CartMv: $CartMv;
  @autowired($AppStore)
  public $AppStore: $AppStore;
  @autowired($ExpenseNodeListMV)
  public $myMv: $ExpenseNodeListMV;

  constructor(props) {
    super(props);
    this.state = ({
    });
  }
  public componentDidMount() {
    console.log("this.props.match.params", this.props.match.params);
    const expenseOid = this.props.match.params.expenseOid;
    const { orderStatus } = this.$myMv;
    const activeType = this.props.match.params.activeType;
    const activeArrLength = orderStatus.filter((item) => item.value === activeType).length;
    this.$myMv.expenseOid = expenseOid === "null" ? null : expenseOid;
    this.$myMv.key = activeArrLength ? activeType : "All";
    document.title = "费用单详情";
    gaEvent("费用单详情");
    this.$myMv.clearMVDetailData();
    this.initPage();
  }

  public initPage = () => {
    this.searchOrder();
  }

  public searchOrder = () => {
    const { expenseOid } = this.$myMv;
    const params = {
      docId: expenseOid,
    };
    this.$myMv.showSpin();
    this.$myMv.getDetail(params).then((res) => {
      this.$myMv.hideSpin();
      console.log("res", res);
      this.$myMv.setOrderDetail(res);
    }).catch(() => {
      this.$myMv.hideSpin();
    });
  }
  public pageWindowSkip = (url) => {
    setTimeout(() => {
      window.location.href = url;
    }, 50);
  }
  public toRecordDetail() {
    const { expenseOid } = this.$myMv
    if (expenseOid) {
      window.location.href = `/${SITE_PATH}/payment-information-list/${expenseOid}?orderType=${$OrderType.FEEDOCUMENT}`;
    }
  }
  public getReturnedStatusIcon(status) {
    const { expenseStatusCode } = $OrderStatusType;
    const { PAY_ON, DONE, CLOSED } = expenseStatusCode;
    let iconClass = "";
    switch (status) {
      case PAY_ON:
        iconClass = "scm-fukuan2";
        break;
      case DONE:
        iconClass = "scm-shenhetongguo2";
        break;
      case CLOSED:
        iconClass = "scm-zuofei4";
        break;
      default:
        break;
    }
    return iconClass;
  }
  public goToPay = (docId, canPartialPay) => {
    this.$AppStore.clearPageMv(AppStoreKey.SINGLEPAYMENTSTORAGE);
    this.pageWindowSkip(`/${SITE_PATH}/submit/order-payment/:record/${docId}/:showPaymentBtn?feeDocumentId=${docId}&canPartialPay=${canPartialPay}`);
  }
  public render() {
    const { orderDetail, isSpin } = this.$myMv;
    const isFinish = orderDetail.docStatusCode === $OrderStatusType.expenseStatusCode.DONE;
    console.log("this.$myMv", this.$myMv, isFinish);
    return (
      <PageWrap className="page-wrap">
        <Spin spinning={isSpin}>
          {
            orderDetail &&
              <div className="order-detail-wrap">
                <div className="order-detail-header">
                  <div className="order-status">
                    <i className={`scmIconfont ${this.getReturnedStatusIcon(orderDetail.docStatusCode)}`} />
                    <div style={{ marginTop: `${isFinish ? "" : "11px"}`}}>{orderDetail.docStatus}</div>
                    {isFinish && <div>{DateUtils.toStringFormat(orderDetail.completeTime, "yyyy-MM-dd HH:mm")}</div>}
                    {/*{isFinish && <div>{DateUtils.toStringFormat(new Date(), "yyyy-MM-dd HH:mm")}</div>}*/}
                  </div>
                  {
                    orderDetail.isOverdue &&
										<div className="img-wrap">
											<img src="https://order.fwh1988.cn:14501/static-img/scm/icon_overdue.png" alt=""/>
										</div>
                  }
                </div>
                <div className="order-detail-content">
                  <SingleExpenseOrderInfo
                    order={{ ...orderDetail, isOverdue: false, isShowPaymentBtn: false}}
                    isHideAmountList={true}
                    isShowAmount={true}
                    isHideDocStatus={true}
                  />
                </div>
								<List className="my-list margin-top">
									<Item
										extra={orderDetail.paymentStatus}
										className="pay-status"
										onClick={() => this.toRecordDetail()}
										arrow="horizontal"
									>
										付款情况
									</Item>
								</List>
                {
                  (orderDetail.itemList && orderDetail.itemList.length > 0) &&
                    <div className="order-detail-list">
                      <div className="list-title">费用单明细</div>
                      <div className="list-content">
                        <div className="label">关联订单号：</div>
                        <div className="value">{orderDetail.itemList.map((item) => item.relatedDocNo).join("、")}</div>
                      </div>
                    </div>
                }
                {
                  orderDetail.isShowPaymentBtn &&
                    <div className="button-group">
                      <span onClick={() => this.goToPay(orderDetail.docId, orderDetail.canPartialPay)}>付款</span>
                    </div>
                }
              </div>
          }
        </Spin>
      </PageWrap>
    );
  }
}

const PageWrap = styled.div`// styled
  & {
    .order-detail-wrap{
      background-color: rgba(242, 242, 242, 1);
      width: 100%;
      min-height: calc(${document.documentElement.clientHeight}px);
      .order-detail-header {
        width: 100%;
        height: auto;
        background-color: #fff;
        margin-bottom: 10px;
        position: relative;
        .order-status {
          height: 70px;
          padding: 16px 0 0 68px;
          position: relative;
          width: 100%;
          background-image: url("https://order.fwh1988.cn:14501/static-img/scm/img_bg_returned_status.png");
          -webkit-background-size: 100% 70px;
          background-size: 100% 70px;
          background-repeat: no-repeat;
          background-position: center center;
          i {
            border: 1px solid #307DCD;
            color: #307DCD;
            position: absolute;
            top: 15px;
            left: 16px;
            display: inline-block;
            width: 40px;
            height: 40px;
            text-align: center;
            line-height: 40px;
            border-radius: 20px;

            &:before {
              font-size: 18px;
            }
          }
          div:first-of-type {
            font-size: 16px;
            height: 16px;
            line-height: 16px;
            margin-bottom: 8px;
          }
          div:nth-of-type(2) {
            color: #666666;
          }
        }
        .img-wrap{
          position: absolute;
          top: 0;
          right: 0;
          width: 83px;
          height: 52px;
          img{
            width: 100%;
            height: 100%;
          }
        }
      }
      .order-detail-content{
        margin-bottom: 10px;
      }
      .my-list{
        margin-bottom: 10px;
        .am-list-item .am-list-line .am-list-extra {
          white-space: normal;
          font-weight: 400;
          font-size: 12px;
          color:rgba(153,153,153,1);
        }
        .am-list-item .am-list-line .am-list-content{
          white-space: normal;
          font-weight:400;
          font-size: 14px;
          color:rgba(51,51,51,1);
        }
      }
      .order-detail-list{
        background-color: #fff;
        .list-title{
          position: relative;
          width: 100%;
          height: 40px;
          padding: 10px 15px;
          font-size: 14px;
          color: #333;
          font-family: "MicrosoftYaHei";
          &::after{
            content: '';
            position: absolute;
            background-color: #D8D8D8 !important;
            display: block;
            z-index: 1;
            top: auto;
            right: auto;
            bottom: 0;
            left: 0;
            width: 100%;
            height: 1.2px;
            transform-origin: 50% 100%;
            transform: scaleY(0.5);
          }
        }
        .list-content{
          color: rgba(102, 102, 102, 1);
          line-height: 26px;
          font-size: 12px;
          position: relative;
          padding: 5px 16px 5px 90px;
          .label{
            position: absolute;
            top: 5px;
            left: 16px;
            color:rgba(153,153,153,1);
          }
          .value{
            //color:rgba(153,153,153,1);
          }
        }
      }
      .button-group{
        background:#ffffff;
        padding: 10px 0;
        height: 50px;
        text-align:right;
        position: fixed;
        bottom:0;
        left:0;
        width:100%;
        z-index: 99;
        span{
          border:1px solid #307DCD;
          border-radius:3px;
          padding: 7px 24px;
          color:#307DCD;
          font-size:12px;
          line-height: 12px;
          top: 10px;
          right: 16px;
          position: absolute;
        }
      }
    }
  }
`;
