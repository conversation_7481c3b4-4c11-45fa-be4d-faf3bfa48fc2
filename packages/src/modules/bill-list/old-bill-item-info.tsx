import * as React from "react";
import { observer } from "mobx-react";
import { cloneDeep } from "lodash";
import styled from "styled-components";
interface BillItemInfoProps {
    order: any;
    confirmBill:(oid,billMonthly)=>void

}
@observer
export class OldBillItemInfo extends React.Component<BillItemInfoProps, any> {
    constructor(props) {
        super(props);
        this.state = ({
        });
    }

    public setStatusColor = (status) => {
        let color = ""
        switch (status){
            case "wait":
                color = "#FF3636";
                break;
            default:
                color = "#437DF0";
                break;
        }
        return color;
    }

    public render() {
        const { order,confirmBill} = this.props;
        const { id, orgCode, orgName, docNo, billMonthly, status, statusValue, balanceAmount, paymentAmount,hasConfirmBtnToken } = order;
        return (
            <BillItemInfoWapper>
                <div className="infoHeader">
                    <span>{orgName.length > 10 ? orgName.substring(0,16) + "..." : orgName}</span>
                    <span style={{color:this.setStatusColor(status)}}>{statusValue}</span>
                </div>
                <div className="infoContent">
                    <div>账单月份：<span>{billMonthly}</span></div>
                    <div>余&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;额：<span>¥{balanceAmount}</span></div>
                    <div>已付款未发货额：<span>¥{paymentAmount}</span></div>
                </div>
                <div className="infoFooter">
                    {hasConfirmBtnToken && <span onClick={(e)=>{confirmBill(id,billMonthly);e.stopPropagation()}}>确认</span>}
                </div>
            </BillItemInfoWapper>
        );
    }
}

const BillItemInfoWapper = styled.div`
    &{
        background-color:#fff;
        margin-top:12px;
        border-radius:8px;
        .infoHeader{
            height:44px;
            display:flex;
            align-items:center;
            justify-content:space-between;
            padding:0 14px;
            border-bottom:1px solid #eee;
            font-size: 14px;
            font-family: PingFangSC-Medium, PingFang SC;
            font-weight: 500;
            color: #333333;
            >span:last-of-type{
                font-size: 13px;
            }
        }
        .infoContent{
            height:100px;
            padding:12px 14px;
            border-bottom:1px solid #eee;
            display:flex;
            flex-direction:column;
            justify-content:space-around;
            font-size: 13px;
            font-family: PingFangSC-Regular, PingFang SC;
            font-weight: 400;
            color: #666666;
            >div span{
                font-family: PingFangSC-Medium, PingFang SC;
                font-weight: 500;
                color: #333333;
            }
        }
        .infoFooter{
            height:44px;
            display:flex;
            align-items:center;
            flex-direction:row-reverse;
            padding:14px;
            >span{
                border:1px solid #437DF0;
                display:inline-block;
                height:28px;
                width:70px;
                font-size: 12px;
                font-family: PingFangSC-Regular, PingFang SC;
                font-weight: 400;
                color: #437DF0;
                line-height:28px;
                border-radius:14px;
                text-align:center;
            }
           
        }
    }
`


