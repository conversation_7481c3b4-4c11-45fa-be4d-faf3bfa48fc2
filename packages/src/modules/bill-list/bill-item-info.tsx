import * as React from "react";
import { observer } from "mobx-react";
import styled from "styled-components";

interface BillItemInfoProps {
    order: any;
    confirmBill: (oid,billMonthly) => void;
    goDetail: (id, type, orderInfo) => void;
}
@observer
export class BillItemInfo extends React.Component<BillItemInfoProps, any> {
    constructor(props) {
        super(props);
        this.state = ({
        });
    }

    public setStatusColor = (status) => {
        let color = ""
        switch (status){
            case "wait":
                color = "#FF3636";
                break;
            default:
                color = "#437DF0";
                break;
        }
        return color;
    }

    public render() {
        const { order, confirmBill, goDetail } = this.props;
        const { id, orgName, billMonthly, status, statusValue, itemList, hasConfirmBtnToken, billMonthlyStart, billMonthlyEnd } = order;
        return (
            <BillItemInfoWapper>
                <div className="counter_header">
                  <div>
                    <div>{orgName.length > 10 ? orgName.substring(0,16) + "..." : orgName}</div>
                    <div>{billMonthlyStart} ~ {billMonthlyEnd}</div>
                  </div>
                  <div style={{color:this.setStatusColor(status)}}>{statusValue}</div>
                </div>
                {
                  itemList && itemList.length > 0 && itemList.map((item, index) => {
                    if (item.fundType === "HJ") {
                      return <div className="account_total" key={index}>
                        <div className="account_header" >
                          <div><span>账户合计</span><span>{item.finalAmount}</span></div>
                          <div>账户合计=可用余额-欠款+已付款未发货额</div>
                        </div>
                        {
                            item.item && item.item.length > 0 && item.item.map((subItem, subIndex) => {
                              if (subItem.fundType === "YE") {
                                return <div className="account_item" key={subIndex} onClick={() => goDetail(id, subItem.fundType, order)}>
                                  <div>
                                    <span>&nbsp;</span>
                                    可用余额
                                    <i className="scmIconfont scm-icon-jiantou-you" />  
                                  </div>
                                  <div>
                                    <span>期初余额</span>
                                    <span>本期变动</span>
                                    <span>期末余额</span>
                                  </div>
                                  <div>
                                    <span>{subItem.initAmount || 0}</span>
                                    <span style={{ color: subItem.changeAmount > 0 ? "#FF3030" : "#333333" }}>{subItem.changeAmount}</span>
                                    <span>{subItem.finalAmount}</span>
                                  </div>
                                </div>
                              } else if (subItem.fundType === "QK") {
                                return <div className="account_item" key={subIndex} onClick={() => goDetail(id, subItem.fundType, order)}>
                                  <div>
                                    <span>&nbsp;</span>
                                    {subItem.fundTypeLabel}
                                    <i className="scmIconfont scm-icon-jiantou-you" />  
                                  </div>
                                  <div>
                                    <span>期初待还金额</span>
                                    <span>本期变动</span>
                                    <span>期末待还金额</span>
                                  </div>
                                  <div>
                                    <span>{subItem.initAmount || 0}</span>
                                    <span style={{ color: subItem.changeAmount > 0 ? "#FF3030" : "#333333" }}>{subItem.changeAmount}</span>
                                    <span>{subItem.finalAmount}</span>
                                  </div>
                                </div>
                              } else if (subItem.fundType === "paid_not_delivery_amount") {
                                return <div className="account_item" key={subIndex} onClick={() => goDetail(id, subItem.fundType, order)}>
                                  <div>
                                    <span>&nbsp;</span>
                                    {subItem.fundTypeLabel}
                                    <i className="scmIconfont scm-icon-jiantou-you" />  
                                  </div>
                                  <div>
                                    <span>期初未发货额</span>
                                    <span>本期变动</span>
                                    <span>期末未发货额</span>
                                  </div>
                                  <div>
                                    <span>{subItem.initAmount || 0}</span>
                                    <span style={{ color: subItem.changeAmount > 0 ? "#FF3030" : "#333333" }}>{subItem.changeAmount}</span>
                                    <span>{subItem.finalAmount}</span>
                                  </div>
                                </div>
                              }
                            })
                          }
                      </div>
                    } else if (item.fundType === "FL") {
                      return <div className="account_total" key={index} onClick={() => goDetail(id, item.fundType, order)}>
                        <div className="account_header">
                          <div><span>返利账户</span><span><i className="scmIconfont scm-icon-jiantou-you" /> </span></div>
                        </div>
                        <div className="account_item2">
                          <div>
                            <span>期初额度</span>
                            <span>本期变动</span>
                            <span>期末额度</span>
                          </div>
                          <div>
                            <span>{item.initAmount || 0}</span>
                            <span style={{ color: item.changeAmount > 0 ? "#FF3030" : "#333333" }}>{item.changeAmount}</span>
                            <span>{item.finalAmount}</span>
                          </div>
                        </div>
                      </div>
                    }
                  })
                }
                {
                  hasConfirmBtnToken && <div className="counter_footer">
                    <span onClick={(e)=>{confirmBill(id,billMonthly);e.stopPropagation()}}>确认</span>
                  </div>
                }
            </BillItemInfoWapper>
        );
    }
}

const BillItemInfoWapper = styled.div`
  & {
    background-color:#fff;
    margin-top: 12px;
    border-radius:8px;

    .counter_header {
      padding: 12px;
      display: flex;
      border-bottom: 1px solid #eee;
      justify-content: space-between;
      align-items: center;

      > div:nth-child(1) {
        display: flex;
        flex-direction: column;
        align-items: left;
        font-size: 14px;
        font-weight: 500;
        color: #333333;
        
        > div:nth-child(2){
          margin-top: 4px;
          color: #999;
          font-size: 12px;
        }
      }

      > div:nth-child(2) {
        font-size: 14px;
      }
    }

    .account_total {
      margin: 12px;
      border: 0.5px solid #E8E8E8;

      .account_header {
        background: #F0F4FC;
        padding: 8px 12px;
        display: flex;
        flex-direction: column;
        
        > div:nth-child(1) {
          display: flex;
          justify-content: space-between;
          color: #333333;
          font-weight: 500;
          font-size: 16px;
        }

        > div:nth-child(2) {
          color: #999;
          font-size: 12px;
          margin-top: 4px;
        }
      }
    }

    .account_item2 {
      padding: 12px;

      > div:nth-child(1) {
        margin-top: 8px;
        display: flex;
        justify-content: space-between;

        > span {
          width: 33%;
          text-align: center;
          color: #666666;
          font-size: 14px;
        }
      }

      > div:nth-child(2) {
        display: flex;
        justify-content: space-between;

        > span {
          width: 33%;
          text-align: center;
          font-size: 18px;
          font-weight: 500;
          color: #333333;
        }
      }
    }

    .account_item {
      padding: 12px;

      > div:nth-child(1) {
        color: #333333;
        font-size: 16px;
        font-weight: 500;
        display: flex;

        > span {
          height: 100%;
          width: 4px;
          border-radius: 2px;
          background: #437DF0;
          margin-right: 16px;
        }

        > i {
          margin-left: auto;
        }
      }

      > div:nth-child(2) {
        margin-top: 8px;
        display: flex;
        justify-content: space-between;

        > span {
          width: 33%;
          text-align: center;
          color: #666666;
          font-size: 14px;
        }
      }

      > div:nth-child(3) {
        display: flex;
        justify-content: space-between;

        > span {
          width: 33%;
          text-align: center;
          font-size: 18px;
          font-weight: 500;
          color: #333333;
        }
      }
    }

    .infoContent {
      margin: 12px;
        padding:12px 14px;
        border-bottom:1px solid #eee;
        display:flex;
        flex-direction:column;
        justify-content:space-around;
        font-size: 13px;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #666666;
        >div span{
            font-family: PingFangSC-Medium, PingFang SC;
            font-weight: 500;
            color: #333333;
        }
    }
      .counter_footer {
        display: flex;
        align-items: center;
        flex-direction: row-reverse;
        padding: 6px 12px;

        > span {
          border: 1px solid #437DF0;
          display: inline-block;
          padding: 4px 32px;
          font-size: 14px;
          color: #FFF;
          background: #437DF0;
          border-radius: 24px;
          text-align: center;
        }
      }
    }
`