import { autowired } from "@classes/ioc/ioc";
import { Spin } from "antd";
import { Modal, Toast} from "antd-mobile";
import { observer } from "mobx-react";
import * as React from "react";
import { withRouter } from "react-router";
import styled from "styled-components";
import { NoGoods } from "../../components/no-goods/no-goods";
import { SITE_PATH } from "../app";
import { $BillListMv } from "./bill-list-mv";
import { GoHome } from "../../components/go-home/go-home";
import { ScreeningStores } from "./screening-stores-model";
import { ScrollAbilityWrapComponent } from "../../components/scroll-ability/wrap";
import { LoadingTip } from "../../components/loading-marked-words";
import { gaEvent } from "../../classes/website-statistics/website-statistics";
import { $AppStore, AppStoreKey } from "../../classes/stores/app-store-mv";
import { toJS, transaction } from "mobx";
import { map } from "lodash";
import { OldBillItemInfo } from "./old-bill-item-info";
import { ModelWapper } from "../../components/model-wapper/model-wapper";
import { getUrlParam } from "@classes/utils/UrlUtils";
import moment from "moment";

const alert = Modal.alert;
declare let window: any;

@withRouter
@observer
class AllShopOrderListWrap extends React.Component<any, any> {
    @autowired($AppStore)
    public $AppStore: $AppStore;
    @autowired($BillListMv)
    public $myMv: $BillListMv;

    constructor(props) {
        super(props);
        this.state = ({
            isShowExplain:false,//名词解释及数据说明弹窗控制
        });
    }
    // 离开记录滚动高度
    // public saveMV = () => {
    //     this.$myMv.scrollHeight = $(".scroll-ability-wrap").scrollTop();
    //     this.$AppStore.savePageMv(AppStoreKey.OLD_BILL_LIST, this.$myMv);
    // }
    // public componentWillUnmount(): void {
    //     this.saveMV();
    // }
    public componentDidMount() {
        document.title = "账单列表";
        gaEvent("账单列表");
        this.$myMv.showOrderPop = false;
        this.$myMv.beginDate = new Date("2023-01");
        this.$myMv.endDate = new Date("2025-01");
        setTimeout(() => {
            const oldData = this.$AppStore.getPageMv(AppStoreKey.OLD_BILL_LIST);
            if (oldData) {
                this.$myMv.queryOldData(JSON.parse(oldData));
                this.$AppStore.clearPageMv(AppStoreKey.OLD_BILL_LIST);
                $(".scroll-ability-wrap").scrollTop(this.$myMv.scrollHeight);
            } else {
                this.$myMv.clearMVData();
                this.$myMv.getBillStatus()
                if(getUrlParam("orderPartyId")){
                    this.$myMv.orderPartyId =Number(getUrlParam("orderPartyId"));
                    this.searchOrder("wait");
                }else{
                    this.initPage();
                }
            }
        }, 50);
    }

    public initPage = () => {
        transaction(() => {
            this.$myMv.orderpartyList({ pageIndex: 0, pageSize: 99999999 }).then(() => {
                if (getUrlParam("activeKey")) {
                    const  status = getUrlParam("activeKey");
                    this.searchOrder(status);
                } else {
                    this.searchOrder("all");
                }
            });
        });
    }
    public componentWillReceiveProps(nextProps: Readonly<any>, nextContext: any): void {
        const { isScrollEnd } = nextProps;
        if (isScrollEnd && isScrollEnd !== this.props.isScrollEnd) {
            this.loadData();
        }
    }

    public loadData = () => {
        const { key,  finished, showOrderPop } = this.$myMv;
        if (finished || showOrderPop) {
            return;
        }
        this.searchOrder(key);
    }

    public searchOrder = (val) => {
        const { pageIndex, pageSize, key,beginDate,endDate,orderPartyId } = this.$myMv;
        if (val != key) {
            this.$myMv.BillListInfo = [];
        }
        const params = {
            orgIdList:orderPartyId ? [orderPartyId] : map(this.$myMv.orderPartyList.filter((party) => party.checked), "oid"),
            beginDate:beginDate ? moment(beginDate).format("YYYY-MM") : '',
            endDate:endDate ? moment(endDate).format("YYYY-MM") : '',
            pageIndex,
            pageSize,
            status: val,
        };
        this.$myMv.key = val;
        this.$myMv.showOrderPop = false;
        this.$myMv.showSpin();
        this.$myMv.getBillList(params).then((res) => {
            this.$myMv.hideSpin();
            const { totalCount, billList } = res;
            const { loadingEnd } = this.props;
            if (billList) {
                this.$myMv.BillListInfo = pageIndex ? this.$myMv.BillListInfo.concat(billList) : billList;
                this.$myMv.changePage();
                this.$myMv.finished = this.$myMv.BillListInfo.length >= totalCount;
                this.$myMv.isLoad = false;
                loadingEnd && loadingEnd();
            } else {
                this.$myMv.finished = true;
                this.$myMv.isLoad = false;
                loadingEnd && loadingEnd();
            }
        }).catch(() => {
            this.$myMv.hideSpin();
        });
    }

    public goToBillDetail = (orderId) => {
        this.props.history.push({
            pathname: `/${SITE_PATH}/bill-detail/${orderId}/?orderPartyId=${getUrlParam("orderPartyId")}`,
        });
    }

    public onSearch = () => {
        const { key} = this.$myMv;
        this.$myMv.setPage();
        this.searchOrder(key);
        // document.getElementsByClassName("orderPage")[0].style.position = "relative";
        $("html").css("overflow", "scroll");
        $("body").css("overflow", "scroll");
        $(".scroll-ability-wrap").css("overflow", "scroll");
    }

    public showChooseShop = () => {
        this.$myMv.showOrderPop = true;
        $("html").css("overflow", "hidden");
        $("body").css("overflow", "hidden");
        $(".scroll-ability-wrap").css("overflow", "hidden");
        $(".scroll-ability-wrap").scrollTop(0);
    }

    public canclePop = () => {
        this.$myMv.showOrderPop = false;
        $("html").css("overflow", "scroll");
        $("body").css("overflow", "scroll");
        $(".scroll-ability-wrap").css("overflow", "scroll");
    }

    public changeOrderStatus = (value) => {
        this.$myMv.pageIndex = 0;
        setTimeout(() => {
            $("html").css("overflow", "scroll");
            $("body").css("overflow", "scroll");
            $(".scroll-ability-wrap").css("overflow", "scroll");
        }, 10);
        this.searchOrder(value);
    }
    public goHome = () => {
      this.$myMv.scrollHeight = $(".scroll-ability-wrap").scrollTop();
      this.$AppStore.savePageMv(AppStoreKey.OLD_BILL_LIST, this.$myMv);
    }
    public confirmSearch = (selectObj,beginDate,endDate ) => {
      if (moment(beginDate).isAfter(endDate)) {
        Toast.fail("开始时间不能大于结束时间");
        return;
      }
        const { orderPartyId } = this.$myMv;
        const { orderPartyList} = selectObj;
        if(!orderPartyId && orderPartyList.filter(item => item.checked).length <= 0){
            Toast.info("请至少选择一个门店")
            return
        }
        this.$myMv.orderPartyList = orderPartyList || [];
        this.$myMv.beginDate = beginDate;
        this.$myMv.endDate = endDate;
        this.$myMv.showOrderPop = false;
        this.onSearch();
    }
    public confirmBill = (oid,billMonthly) => {
        alert('账单确认', `确认${billMonthly}账单吗`, [
            { text: '取消', onPress: () => console.log('cancel')},
            { text: '确认', onPress: () =>  {
                this.$myMv.confirmBill({oid}).then(res=>{
                   if(res){
                       const { key} = this.$myMv;
                       this.$myMv.setPage();
                       this.searchOrder(key);
                   }
                })
            }},
          ]);
    }
    public render() {
        const { orderStatus, BillListInfo, isSpin, orderPartyList,orderPartyId,beginDate,endDate,isLoad, key, showOrderPop, finished} = this.$myMv;
        const { isShowExplain} = this.state;
        return (
            <OrderPage className="orderPage">
                <HeaderWapper>
                    <span onClick={() => this.setState({isShowExplain:true})}>数据说明及名词解释<i className="scmIconfont scm-icon-jiantou-you" /></span>
                    <span onClick={() => this.showChooseShop()}>筛选 <i className="scmIconfont scm-icon-arrow-down" /></span>
                </HeaderWapper>
                <SearchTypeBar>
                    {
                       orderStatus && orderStatus.map((status, index) => {
                            return (
                                <div key={status.value} onClick={() => {
                                    this.changeOrderStatus(status.value);
                                }}>
                                    <span className={status.value === key ? "active" : null}>
                                        {status.text}
                                    </span>
                                </div>
                            );
                        })
                    }
                </SearchTypeBar>
                <Spin spinning={isSpin}>
                    <BillList
                        className={"orderList"}
                    >
                        {
                            BillListInfo ? BillListInfo.length > 0 ?
                                BillListInfo.map((order, index) => {
                                    const { id } = order;
                                    return <div key={id} onClick={()=>this.goToBillDetail(id)}>
                                        <OldBillItemInfo
                                            order={order}
                                            confirmBill={this.confirmBill}
                                        />
                                    </div>;
                                })
                                : <NoGoods title="暂无账单" height={document.documentElement.clientHeight - 90} /> :
                                <NoGoods title="暂无账单" height={document.documentElement.clientHeight - 90} />
                        }
                        {
                            BillListInfo && BillListInfo.length > 0 ?
                                <LoadingTip
                                    isFinished={finished}
                                    isLoad={isLoad}
                                /> : null
                        }
                    </BillList>
                </Spin>
                <GoHome
                    goHome={this.goHome}
                />
                {
                    showOrderPop &&
                    <ScreeningStores
                        confirmSearch={this.confirmSearch}
                        canclePop={this.canclePop}
                        orderPartyList={toJS(orderPartyList)}
                        isShowOrderPartList={!orderPartyId}
                        beginDate={beginDate}
                        endDate={endDate}
                        isOldPage={true}
                        maxDate={new Date("2025-01-31")}
                    />
                }
                {
                   isShowExplain && <ModelWapper contentStyle={{ height: "auto", padding: "0 20px" }}>
                        <ModelContent>
                            <div className="modelTitle">数据说明及名词解释</div>
                            <div className="modelContent">
                                <div>期初：<span>账单开始日期</span></div>
                                <div>期末：<span>账单结束日期</span></div>
                                <div>本期变动：<span>账单时间内金额的变动</span></div>
                                <div>余额：<span>账户的余额 - 欠款金额</span></div>
                                <div>已付款未发货额：<span>已经付款但未发货的订单的订货额</span></div>
                            </div>
                            <div className="modeFooter">
                                <span onClick={() => this.setState({isShowExplain:false})}>关闭</span>
                            </div>
                        </ModelContent>

                    </ModelWapper>
                }
            </OrderPage>
        );
    }
}

const AllShopOrderList = ScrollAbilityWrapComponent(AllShopOrderListWrap);
export default AllShopOrderList;

const OrderPage = styled.div`// styled
  & {
    width: 100%;
    min-height: calc(${document.documentElement.clientHeight}px);
    height: auto;
    background-color: #f5f5f5;
    color: #2a2a2a;
  }
`;
const HeaderWapper = styled.div`
    &{
        height:46px;
        width:100%;
        padding:0 14px;
        display:flex;
        align-items:center;
        justify-content:space-between;
        background-color:#fff;
        border-bottom:1px solid #eee;
        position:fixed;
        top:0;
        z-index:99;
        .scmIconfont{
            font-size:12px;
        }
        >span:nth-of-type(1){
            font-size: 14px;
            font-family: PingFangSC-Medium, PingFang SC;
            font-weight: 500;
            color: #437DF0;
        }
        >span:nth-of-type(2){
            font-size: 14px;
            font-family: PingFangSC-Regular, PingFang SC;
            font-weight: 400;
            color: #437DF0;
        }
    }
`
const SearchTypeBar = styled.div`
    &{
        height:40px;
        width:100%;
        padding:0 20px;
        display:flex;
        cursor: pointer;
        align-items:center;
        justify-content:space-between;
        background-color:#fff;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #333333;
        position:fixed;
        top:46px;
        z-index:99;
        >div span{
            display:inline-block;
            height:26px;
        }
        .active{
            color:#437DF0 !important;
            border-bottom:2px solid #437DF0 !important;

        }
    }
`;
const BillList = styled.div`
    &{
        padding:0 12px;
        margin-top:96px;
    }
`
const ModelContent = styled.div`
    &{
        .modelTitle{
            font-size: 16px;
            font-family: PingFangSC-Medium, PingFang SC;
            font-weight: 500;
            color: #333333;
            text-align:center;
            padding:20px 0;
        }
        .modelContent{
            >div{
                font-size: 13px;
                font-family: PingFangSC-Regular, PingFang SC;
                font-weight: 400;
                color: #666666;
                margin-bottom:10px;
                white-space:nowrap;
                display:flex;
                >span{
                    font-family: PingFangSC-Medium, PingFang SC;
                    display:inline-block;
                    white-space: normal;
                    word-wrap:break-word;
                    font-weight: 500;
                    color: #333333;
                }
            }
        }
        .modeFooter{
            height:80px;
            line-height:80px;
            text-align:center;
            >span{
                display:inline-block;
                width: 236px;
                line-height:38px;
                height: 38px;
                background: #437DF0;
                border-radius: 19px;
                font-size: 14px;
                font-family: PingFangSC-Regular, PingFang SC;
                font-weight: 400;
                color: #FFFFFF;
                &:hover{
                    border:1px solid #eee;
                }
            }
        }
    }
`
