import { autowired, bean } from "@classes/ioc/ioc";
import { action, observable } from "mobx";
import { $BillService } from "../../classes/service/$bill-service";
import { $SelectOrderparty } from "../../classes/entity/$select-orderparty";
import { beanMapper } from "../../helpers/bean-helpers";

@bean($BillListMv)
export class $BillListMv {
  @autowired($BillService)
  public $BillService: $BillService;


  @observable public orderStatus = [];
  @observable public pageIndex = 0;
  @observable public scrollHeight: number = 0;
  @observable public finished = false;
  @observable public isLoad = false;
  @observable public pageSize = 10;
  @observable public showOrderPop = false;
  @observable public key = "all";
  @observable public beginDate: Date | null = null;
  @observable public endDate: Date | null = null;
  @observable public BillListInfo = [];
  @observable public orderPartyId:number = null;//从我的信息页面传过来的单位oid

  @observable public isSpin: boolean = false;

  @observable public orderPartyList: $SelectOrderparty[] = [];


  @action
  public showSpin() {
    this.isSpin = true;
  }

  @action
  public hideSpin() {
    this.isSpin = false;
  }


  @action
  public setSelectOrderParty(orderParty) {
    this.orderPartyList.map((status, index) => {
      if (status.oid === orderParty) {
        status.checked = !status.checked;
        status.showActiveOrderparty = !status.showActiveOrderparty;
      }
    });
  }

  @action
  public changePage() {
    this.pageIndex++;
  }

  @action
  public setPage() {
    this.pageIndex = 0;
  }


  //获取账单状态列表
  @action
  public getBillStatus = () => {
      return this.$BillService.queryBillStatus({}).then(res => {
          this.orderStatus = res;
      })
  }
  //获取账单列表
  @action
  public getBillList(params) {
    return this.$BillService.queryBillList(params);
  }
  //获取门店清单
  @action
  public orderpartyList(params) {
    return this.$BillService.orderpartyList(params).then((data) => {
      this.orderPartyList = data.orderPartyList.map((party) => new $SelectOrderparty(party));
    });
  }
  // 确认
  @action
  public confirmBill(params) {
    return this.$BillService.confirmBill(params)
  }


  @action
  public queryOldData(obj) {
    beanMapper(obj, this);
    console.log(this);
  }
  @action
  public clearMVData() {
    this.pageIndex = 0;
    this.scrollHeight = 0;
    this.finished = false;
    this.isLoad = false;
    this.pageSize = 10;
    this.showOrderPop = false;
    this.key = "all";
    this.BillListInfo = [];
    this.isSpin = false;
    this.orderPartyList = [];
    this.orderPartyId = null;
  }
}
