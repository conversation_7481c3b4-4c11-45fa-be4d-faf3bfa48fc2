import * as React from "react";
import { observer } from "mobx-react";
import { cloneDeep, findIndex } from "lodash";
import styled from "styled-components";
import moment from "moment";
import { DatePicker , InputItem , Icon} from 'antd-mobile';
import { autowired } from "@classes/ioc/ioc";
import { $BillListMv } from "./bill-list-mv";
import "./date-picker-style.less";

@observer
export class ScreeningStores extends React.Component<any, any> {
    @autowired($BillListMv)
    public $myMv: $BillListMv;
    constructor(props) {
        super(props);
        this.state = ({
            orderPartyList: [],
            orderListChange : [],
            beginDate: this.props.beginDate,
            endDate: this.props.endDate,
            isShowDatePicker: false,
            setDateType: "begin",
            selectValue:""
        });
    }
    public componentDidMount(): void {
        this.resetData();
    }

    public resetData = () => {
        console.log("看看展示的数据",this.props.orderPartyList)
        this.setState({
            orderPartyList: cloneDeep(this.props.orderPartyList.map(item => {return {...item,checked:true}}) || []),
            orderListChange : cloneDeep(this.props.orderPartyList.map(item => {return {...item,checked:true}}) || []),
            beginDate: this.props.beginDate,
            endDate: this.props.endDate,
            selectValue:""
        });
    }

    public changeOrderParty = (orderParty,item) => {
        console.log("orderParty",item)
        const { orderPartyList , orderListChange} = this.state;
        const changeOrderPartyItemIndex = findIndex(orderListChange,["oid",orderParty])
        const orderPartyListItemIndex = findIndex(orderPartyList,["oid",orderParty])
        orderListChange[changeOrderPartyItemIndex].checked = !orderListChange[changeOrderPartyItemIndex].checked;
        orderPartyList[orderPartyListItemIndex].checked = !orderPartyList[orderPartyListItemIndex].checked;
        // orderListChange[changeOrderPartyItemIndex].showActiveOrderparty = !orderListChange[changeOrderPartyItemIndex].showActiveOrderparty;
        // orderPartyList[orderPartyListItemIndex].showActiveOrderparty = !orderPartyList[orderPartyListItemIndex].showActiveOrderparty;
        this.setState({ orderPartyList });
        this.setState({ orderListChange });
    }

    public showDatePicker = (visible, type) => {
        this.setState({
            isShowDatePicker: visible,
            setDateType: type
        })
    }

    public datePickerConfirm = (dateTime) => {
        const { setDateType } = this.state;
        if(setDateType === "begin"){
            this.setState({beginDate:dateTime})
        }else{
            this.setState({endDate:dateTime})
        }
        this.datePickerCancel()
    }
    public datePickerCancel = () => {
        this.setState({ isShowDatePicker: false })
    }
    public branchScreening = (v) => {
      console.log("看看操作",v)
      const { orderListChange} = this.state;
      let arr = [];
      if(v=== "" || v===undefined){
        this.setState({orderPartyList:cloneDeep(orderListChange),selectValue:v},()=>{
          this.checkAll(false)
        })
        return
      }
      arr = orderListChange.filter((item) => {
            if (item.code.indexOf(v) != -1){
              return true;
            } else {
              return false;
            }
      })
      this.setState({orderPartyList:cloneDeep(arr),selectValue:v},()=>{
        const { orderPartyList,orderListChange} = this.state;
        orderPartyList.map((item) => {
             item.checked = false;
        });
        orderListChange.map(item => {
          item.checked = false;
        })
        this.setState({ orderPartyList,orderListChange });
      })
    }
    public  checkAll = () => {
      const { orderPartyList,orderListChange} = this.state;
      orderPartyList.map((item) => {
           if(!item.checked){
            const changeOrderPartyItemIndex = findIndex(orderListChange,["oid",item.oid])
            orderListChange[changeOrderPartyItemIndex].checked = true;
           }
           item.checked = true;
          //  item.showActiveOrderparty = true;
      });
      this.setState({ orderPartyList,orderListChange });
    }
    public invertSelection = () => {
      const { orderPartyList,orderListChange} = this.state;
      orderPartyList.map((item) => {
           item.checked = !item.checked;
           const changeOrderPartyItemIndex = findIndex(orderListChange,["oid",item.oid])
           orderListChange[changeOrderPartyItemIndex].checked = item.checked;
          //  item.showActiveOrderparty = ! item.showActiveOrderparty ;
      });
      this.setState({ orderPartyList });
    }

    public render() {
        const { isHideOrderStatus,isShowOrderPartList, isOldPage, goToOldPage, maxDate, minDate } = this.props;
        const { orderPartyList,orderListChange, isShowDatePicker,beginDate,endDate,selectValue, setDateType } = this.state;
        console.log("beginDate", beginDate)
        return (
            <OrderPop className={"not-touch"}>
                <PopLeft onClick={this.props.canclePop} className={"not-touch"} />
                <PopRight>
                    {
                        !isHideOrderStatus &&
                        <SearchStatus>
                            <p>
                                账单月份
                                {
                                  !isOldPage && <span onClick={goToOldPage} style={{ marginLeft: 90, color: "#307DCD", fontSize: 10 }}>
                                    查看25年1月及以前月份账单
                                    <i style={{ color: "#666", fontSize: 10 }} className="scmIconfont scm-icon-jiantou-you" />
                                  </span>
                                }
                            </p>
                            <p>
                                <div onClick={() => this.showDatePicker(true, "begin")}><span>开始</span><span className="content">{beginDate ? moment(beginDate).format("YYYY.MM"):null}</span><i className="scmIconfont scm-shaixuan" /></div>
                                <div onClick={() => this.showDatePicker(true, "end")}><span>结束</span><span className="content">{endDate ? moment(endDate).format("YYYY.MM") : null}</span><i className="scmIconfont scm-shaixuan" /></div>
                            </p>
                        </SearchStatus>
                    }
                   {isShowOrderPartList && <SelectParty>
                        <div>
                            门店清单
                        </div>
                        <div className={"outlet-name"} id={"outlet-name"} >
                          <InputItem
                            clear
                            // onBlur={(v) => this.branchScreening(v)}
                            value={selectValue}
                            onChange={(value)=>this.branchScreening(value)}
                            placeholder="门店名称，编码" labelNumber={2}><Icon type="search" size="sm" />
                          </InputItem>
                          <span onClick={()=>this.checkAll()}>全选</span>&nbsp;<span onClick={this.invertSelection}>反选</span>
                        </div>
                        <div className={"partys"}>
                            {
                                orderPartyList.length > 0 ? orderPartyList.map((item, index) => {
                                    return (
                                        <p
                                            key={index}
                                            onClick={() => this.changeOrderParty(item.oid,item)}
                                            className={item.checked ? "activeOrderParty" : ""}
                                        >
                                            {/* <i className="scmIconfont scm-icon-shop"/> */}
                                            <span>
                                                {item.name && item.name.length > 17 ? item.name.slice(0, 17) + "..." : item.name}
                                                <span
                                                    style={{ display: item.checked ? "block" : "none" }}
                                                >
                                                    <i className="scmIconfont scm-icon-guanbi" />
                                                </span>
                                            </span>
                                        </p>
                                    );
                                }) : <p>暂无门店</p>
                            }
                        </div>
                    </SelectParty>}
                    <PopButton>
                        <span onClick={this.resetData}>重置</span>
                        <span onClick={() => this.props.confirmSearch({orderPartyList:orderListChange},beginDate,endDate)}>确定</span>
                    </PopButton>
                </PopRight>
                <DatePicker
                    mode={"month"}
                    visible={isShowDatePicker}
                    onOk={this.datePickerConfirm}
                    onDismiss={this.datePickerCancel}
                    minDate={minDate}
                    maxDate={maxDate}
                    value={setDateType === "begin" ? beginDate : endDate}
                />
            </OrderPop>
        );
    }
}

const OrderPop = styled.div`// styled
  & {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    width: 100%;
    height: 100%;
    z-index: 99;
    .am-search {
      height: 88px;
      background-color: #fff;
      padding: 0;
      position: relative;
      :after {
        content: '';
        position: absolute;
        background-color: #D8D8D8 !important;
        display: block;
        z-index: 1;
        top: auto;
        right: auto;
        bottom: 0;
        left: 0;
        width: 100%;
        height: 1px;
        transform-origin: 50% 100%;
        transform: scaleY(0.5);
      }
    }
    .am-search-input {
      margin: 44px 8px 16px 8px;
      background: #F4F4F4;
      border-radius: 13px;
    }
    .am-search-cancel {
      line-height: 72px;
    }
    .am-search-input .am-search-synthetic-ph-placeholder {
      font-size: 13px;
      font-family: "SourceHanSansCN-Normal";
      font-weight: 400;
      color: rgba(153, 153, 153, 1);
    }
  }
`;

const PopLeft = styled.div`// styled
  & {
    display: inline-block;
    width: calc(100% - 300px);
    height: 100%;
    background: rgba(0, 0, 0, 0.3);
  }
`;

const PopRight = styled.div`// styled
  & {
    display: inline-block;
    width: 300px;
    height: 100%;
    background: #fff;
    z-index: 99;
    position: fixed;
    overflow: hidden;
  }
`;

const SelectParty = styled.div`// styled
  & {
    width: 300px;
    height: auto;
    padding: 16px 8px;
    > div:first-child {
        font-size: 14px;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #000000;
    }
    > div:last-child {
      width: 300px;
      height: calc(${document.documentElement.clientHeight - 44 - 18 - 16 - 48}px);
      overflow-y: auto;
      margin-top: 16px;
      padding-bottom: 155px;
      -webkit-overflow-scrolling: auto;
      > p {
        width: 282px;
        height: 32px;
        background: rgba(244, 244, 244, 1);
        border-radius: 4px;
        padding: 5px 12px;
        font-size: 13px;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #437DF0;
        > i {
          margin-right: 12px;
        }
      }
      .activeOrderParty {
        color: #307DCD;
        background: rgba(48, 125, 205, 0.1);
        position: relative;
        > span span {
          display: block;
          width: 0;
          height: 0;
          border-bottom: 20px solid #307DCD;
          border-left: 20px solid transparent;
          position: absolute;
          bottom: 0;
          right: 0;
          border-bottom-right-radius: 0 4px;
          > i {
            font-size: 5px;
            color: #fff;
            position: absolute;
            right: 2px;
            top: 10px;
          }
        }
      }
    }
    .outlet-name{
      display: flex;
      align-items: center;
      .am-list-item{
        flex: 1;
        margin-right:10px;
      }
      >span{
        color:#437DF0;
        width:35px;
        display: inline-block;
      }
      .am-input-control input{
        font-size: 15px;
       }
    }
    #outlet-name{
      .am-list-line{
         margin:5px 0;
         border:1px solid #eee!important;
         .am-input-label{
           display: flex;
           justify-content: center;
           .am-icon-search{
             color:#ccc;
           }
         }
       }
    }
  }
`;

const PopButton = styled.div`// styled
  & {
    width: 300px;
    height: 48px;
    position: fixed;
    bottom: 0;
    padding: 6px 12px;
    text-align: right;
    border-top: 1px solid #D8D8D8;
    z-index: 100;
    background: #fff;
    >span{
        display: inline-block;
        height: 36px;
        line-height:36px;
        text-align:center;
        border-radius:18px;
        font-size: 14px;
    }
    > span:first-child {
        width:92px;
        background: #F2F2F2;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #666666;
        margin-right:20px;

    }
    > span:last-child {
        width: 154px;
        background: #437DF0;
        font-family: PingFangSC-Medium, PingFang SC;
        font-weight: 500;
        color: #FFFFFF;
    }
  }
`;

const SearchStatus = styled.div`// styled
  & {
    width: 100%;
    height: auto;
    padding: 16px 0 8px 8px;
    //border-top: 1px solid #D8D8D8;
    border-bottom: 1px solid #D8D8D8;

    > p {
        font-size: 14px;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #000000;
    }

    > p:last-child {
      margin-bottom: 0;
      display:flex;
      > div {
          margin-left:12px;
          width: 124px;
          height: 34px;
          border-radius: 2px;
          border: 1px solid #D8D8D8;
          display:flex;
          align-items:center;
          padding:0 6px;
          font-size: 13px;
          font-family: PingFangSC-Regular, PingFang SC;
          font-weight: 400;
          color: #666666;
          .scmIconfont{
              font-size:12px;
          }
          .content{
              margin-left:10px;
              flex:1;
          }
      }
    }
  }
`;
