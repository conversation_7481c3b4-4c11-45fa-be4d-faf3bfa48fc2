import { autowired } from "@classes/ioc/ioc";
import { Input, Spin } from "antd";
import { DatePicker, Drawer, List, Picker, SearchBar } from "antd-mobile";
import { observer } from "mobx-react";
import React from "react";
import styled from "styled-components";
import { SITE_PATH } from "../app";
import { $ExchangeGoodsListMv } from "./$exchange-goods-list-mv";
import { withRouter } from "react-router";
import { ExchangeContents } from "./exchange-contents";
import { $AppStore, AppStoreKey } from "../../classes/stores/app-store-mv";
import { ScrollAbilityWrapComponent } from "../../components/scroll-ability/wrap";
declare let window: any;
const Item = List.Item;
const Search = Input.Search;
const nowTimeStamp = Date.now();
const now = new Date(nowTimeStamp);

@withRouter
@observer
class ExchangeGoodsListWrap extends React.Component<any, any> {

  @autowired($AppStore)
  public $AppStore: $AppStore;
  @autowired($ExchangeGoodsListMv)
  public $myMv: $ExchangeGoodsListMv;

  constructor(props) {
    super(props);
    this.state = {
    };
  }
  // 离开记录滚动高度
  public saveMV = () => {
    this.$myMv.scrollHeight = $(".scroll-ability-wrap").scrollTop();
    this.$AppStore.savePageMv(AppStoreKey.EXCHANGEGOODSLIST, this.$myMv);
  }
  public componentWillUnmount(): void {
    this.saveMV();
  }
  public pageWindowSkip = (url) => {
    this.saveMV()
    setTimeout(() => {
      window.location.href = url;
    }, 50);
  }
  public componentDidMount() {
    document.title = "换货列表";
    setTimeout(() => {
      const oldData = this.$AppStore.getPageMv(AppStoreKey.EXCHANGEGOODSLIST)
      if (oldData) {
        this.$myMv.queryOldData(JSON.parse(oldData));
        this.$AppStore.clearPageMv(AppStoreKey.EXCHANGEGOODSLIST);
        $(".scroll-ability-wrap").scrollTop(this.$myMv.scrollHeight);
      } else {
        this.$myMv.clearMVData();
        this.initPage();
      }
    }, 50);
  }
  public initPage = () => {
    this.searchList("");
    const params = { queryType: "search" };
    this.$myMv.fetchAddressTree(params);
    this.$myMv.fetchStatusList();
  }

  public componentWillReceiveProps(nextProps: Readonly<any>, nextContext: any): void {
    const { isScrollEnd } = nextProps;
    console.log(isScrollEnd);
    if (isScrollEnd && isScrollEnd !== this.props.isScrollEnd) {
      this.loadData();
    }
  }

  public goToDetail = (id) => {
    sessionStorage.setItem("exchange-goods-detail-id", id);
    this.pageWindowSkip(`/${SITE_PATH}/exchange-goods-detail-edit`);
  }

  public onOpenChange = () => {
    this.$myMv.open = !this.$myMv.open;
  }

  // public showCondition = () => {
  //   this.$myMv.open = !this.$myMv.open;
  // }

  public onChange = (val) => {
    this.$myMv.searchKeyword = val;
    this.$myMv.setSearchKeyword(val);
  }

  public chooseStartDate(startDate) {
    this.$myMv.startDate = startDate;
    this.$myMv.setStartDate(startDate);
  }

  public chooseEndDate(endDate) {
    this.$myMv.endDate = endDate;
    this.$myMv.setEndDate(endDate);
  }

  public chooseAddress(orgionId) {
    this.$myMv.setAddressRegionId(orgionId);
  }

  public chooseStatus = (code) => {
    this.$myMv.setStatusCode(code);
  }

  public clear = () => {
    this.$myMv.searchKeyword = "";
    this.$myMv.startDate = "";
    this.$myMv.endDate = "";
    this.$myMv.clearInfo();
    this.searchList("");
  }

  public searchList = (val) => {
    this.$myMv.showMore();
    this.$myMv.showSpin();
    if (val) {
      this.$myMv.exchangegoodsrequestList = [];
      this.$myMv.searchInfo.statusCode = [];
      this.$myMv.searchInfo.pageIndex = 0;
      console.log(this.$myMv.searchInfo);
    }
    setTimeout(() => {
      this.$myMv.fetchExchangegoodsrequestList(val).then(() => {
        this.$myMv.hideSpin();
        const { loadingEnd } = this.props;
        loadingEnd && loadingEnd();
      });
      this.$myMv.open = false;
    }, 0);
  }

  public loadData = () => {
    const { finished, open } = this.$myMv;
    if (finished || open) {
      return;
    }
    this.searchList("");
    this.$myMv.searchInfo.pageIndex += 1;
  }

  public addExchangeGoods = () => {
    this.pageWindowSkip(`/${SITE_PATH}/exchange-goods-detail`);
  }

  public render() {
    const { exchangegoodsrequestList, isSpin, addressTree, searchInfo, statusList, isShow, open, searchKeyword, endDate, startDate, finished } = this.$myMv;
    const sidebar = (
      <SearchPage>
        <SearchBar
          value={searchKeyword}
          placeholder="搜索"
          onSubmit={(value) => console.log(value, "onSubmit")}
          onClear={(value) => console.log(value, "onClear")}
          onFocus={() => console.log("onFocus")}
          onBlur={() => console.log("onBlur")}
          onCancel={() => console.log("onCancel")}
          showCancelButton={false}
          onChange={(val) => this.onChange(val)}
        />
        <SearchStatus>
          <p>
            状态
          </p>
          <p>
            {
              statusList.map((status, index) => {
                return (
                  <span key={index}
                        className={status.checked ? "active" : null}
                        onClick={() => this.chooseStatus(status.statusCode)}
                  >
                    {status.statusLabel}</span>
                );
              })
            }
          </p>
        </SearchStatus>
        <SearchDate>
          <p>
            状态
          </p>
          <p>
            <DatePicker
              mode="date"
              title="开始日期"
              extra="Optional"
              value={startDate}
              onChange={(startDate) => this.chooseStartDate(startDate)}
            >
              <span>
                {startDate ?
                  `${startDate.getFullYear()}-${(startDate.getMonth() + 1)}-${startDate.getDate()}`
                  : "开始日期"}
               </span>
            </DatePicker>
            <div>-</div>
            <DatePicker
              mode="date"
              title="结束日期"
              extra="Optional"
              value={endDate}
              onChange={(endDate) => this.chooseEndDate(endDate)}
            >
              <span>{endDate ? `${endDate.getFullYear()}-${(endDate.getMonth() + 1)}-${endDate.getDate()}` : "结束日期"}</span>
            </DatePicker>
          </p>
        </SearchDate>
        <SearchAddress>
          <p>
            地区
          </p>
          <div className="address">
            <Picker
              cols={3}
              data={addressTree}
              extra="请选择所在地区"
              onOk={(e) => this.chooseAddress(e)}
              title="选择地区"
              value={searchInfo.regionId}
              onChange={(v) => this.chooseAddress(v)}
            >
              <List.Item arrow="horizontal"/>
            </Picker>
          </div>
        </SearchAddress>
        <SearchButton>
          <span onClick={() => this.searchList("confirm")}>确定</span>
          <span onClick={this.clear}>重置</span>
        </SearchButton>
      </SearchPage>
    );
    // style={{}}
    return (
      <ExchangeGoodsPage className="exchangeGoodsPage">
        <Spin spinning={isSpin}>
          <Drawer
            className="my-drawer"
            style={{ minHeight: document.documentElement.clientHeight, height: "100%" }}
            enableDragHandle={true}
            contentStyle={{ color: "#A6A6A6", position: open ? "fixed" : "relative" }}
            sidebar={sidebar}
            position={"right"}
            open={open}
            onOpenChange={this.onOpenChange}
          >
            <ExchangeHeader>
              <div onClick={this.onOpenChange}>
                <i className="scmIconfont scm-icon-screen"></i>
                <span>筛选</span>
              </div>
              <div onClick={this.addExchangeGoods}>
                <i className="scmIconfont scm-icon-add"></i>
                <span>新增</span>
              </div>
            </ExchangeHeader>
            <ExchangeBorder/>
            <ExchangeContents
              exchangegoodsrequestList={exchangegoodsrequestList}
              isShow={isShow}
              goToDetail={this.goToDetail}
              finished={finished}
            />
          </Drawer>
        </Spin>
      </ExchangeGoodsPage>
    );
  }
}
const ExchangeGoodsList = ScrollAbilityWrapComponent(ExchangeGoodsListWrap);
export default ExchangeGoodsList;
const ExchangeGoodsPage = styled.div`// styled
  & {
    overflow-x: hidden;

    .my-drawer {
      position: relative;
    }

    .my-drawer .am-drawer-sidebar {
      background-color: #fff;
      position: fixed;
      top: 0;
      right: 0;
    }
    .am-drawer-overlay{
      position: fixed;
      top: 0;
      left: 0;
    }
    .my-drawer .am-drawer-sidebar .am-list {
      width: 300px;
      padding: 0;
    }
    .am-drawer.am-drawer-left .am-drawer-draghandle, .am-drawer.am-drawer-right .am-drawer-draghandle {
      width: 0;
    }
  }
`;

const ExchangeHeader = styled.div`// styled
  & {
    width: 100%;
    height: 40px;
    line-height: 20px;
    padding: 10px 15px;
    background: #fff;

    > div {
      display: inline-block;
      font-size: 14px;
      color: #307DCD;
      text-align: center;

      > i {
        margin-right: 5px;
      }
    }

    > div:last-child {
      float: right;
    }
  }
`;

const SearchPage = styled.div`// styled
  & {
    width: 286px;
    height: 100%;

    .am-search {
      background: #fff;
      padding: 0 15px;
    }

    .am-search-input input[type="search"] {
      background: #F2F2F2;
      border-radius: 14px;
    }

    .am-search-input {
      background: #F2F2F2;
      border-radius: 14px;
    }

    .am-search.am-search-start .am-search-input input[type="search"] {
      border-radius: 14px;
    }

    .am-search-input .am-search-synthetic-ph-placeholder {
      color: #999;
      font-size: 14px;
    }
  }
`;

const SearchStatus = styled.div`// styled
  & {
    width: 100%;
    height: 96px;
    padding: 15px;
    border-top: 1px solid #D8D8D8;
    border-bottom: 1px solid #D8D8D8;

    > p {
      margin-bottom: 10px;
      font-size: 14px;
    }

    > p:last-child {
      > span {
        display: inline-block;
        //width: 72px;
        height: 36px;
        border-radius: 3px;
        padding: 0px 15px;
        line-height: 36px;
        box-sizing: border-box;
        margin-right: 15px;
        background: #F0F0F0;
        color: #999;
        font-size: 14px;
        text-align: center;
      }

      .active {
        color: #307DCD;
        background: #ECF6FF;
      }

      > span:last-child {
        margin-right: 0;
      }
    }
  }
`;

const SearchDate = styled.div`// styled
  & {
    width: 100%;
    height: 96px;
    padding: 15px;
    border-bottom: 1px solid #D8D8D8;

    > p {
      margin-bottom: 10px;
      font-size: 14px;
    }

    > p:last-child {
      > div {
        display: inline-block;

        > span {
          display: inline-block;
          width: 114px;
          height: 36px;
          background: #F0F0F0;
          border-radius: 3px;
          font-size: 14px;
          color: #999999;
          padding: 10px;
          text-align: center;
          box-sizing: border-box;
        }
      }

      > div:nth-of-type(2) {
        color: #E6E6E6;
        margin: 0px 5px;
      }
    }
  }
`;

const SearchAddress = styled.div`// styled
  & {
    width: 100%;
    height: 96px;
    padding: 15px;
    border-bottom: 1px solid #D8D8D8;

    > p {
      margin-bottom: 10px;
      font-size: 14px;
    }

    .address {
      width: 100%;
      height: 38px;
      background: #F0F0F0;
      border-radius: 3px;
      padding: 10px 15px;
      box-sizing: border-box;
      text-align: center;

      > div span {
        font-size: 14px;
        color: #999;

        > span {
          float: right;
        }
      }

      .am-list-item {
        background: #F0F0F0;
        min-height: 38px;
        top: -10px;
      }

      .am-list-item .am-list-line .am-list-extra {
        flex-basis: 100%;
        font-size: 14px;
        color: #999999;
      }
    }
  }
`;

const SearchButton = styled.div`// styled
  & {
    width: 100%;
    height: 48px;
    padding: 10px 15px;
    position: fixed;
    bottom: 0;
    border-top: 1px solid #D8D8D8;

    > span {
      display: inline-block;
      width: 100px;
      height: 28px;
      line-height: 28px;
      float: right;
    }

    > span:nth-of-type(2) {
      background: #ECF6FF;
      border-radius: 18px 0 0 18px;
      font-size: 14px;
      color: #307DCD;
      text-align: center;
    }

    > span:nth-of-type(1) {
      background: #307DCD;
      border-radius: 0 18px 18px 0;
      font-size: 14px;
      color: #FFFFFF;
      text-align: center;
    }
  }
`;

const ExchangeBorder = styled.div`// styled
  & {
    width: 100%;
    height: 10px;
    background: #F2F2F2;
  }
`;
