import * as React from "react";
import { observer } from "mobx-react";
import { $ExchangeGoodsType } from "../../classes/const/$exchange-goods-type";
import styled from "styled-components";
import { NoGoods } from "../../components/no-goods/no-goods";
import { LoadingTip } from "../../components/loading-marked-words";
import { List } from "antd-mobile";
const Item = List.Item;

@observer
export class ExchangeContents extends React.Component<any, any> {
  public render() {
    const { exchangegoodsrequestList, isShow, finished } = this.props;
    return (
      <ExchangeContentsPage>
        {
          exchangegoodsrequestList ? exchangegoodsrequestList.length > 0 ? exchangegoodsrequestList.map((goods, index) => {
            return (
              <Item
                arrow="horizontal"
                key={index}
                onClick={() => this.props.goToDetail(goods.id)}
              >
                <span>{goods.title}</span><br/>
                <span>{goods.createTime}</span>
                <span style={{
                  color: goods.statusCode === $ExchangeGoodsType.ACTIVE ? "#307DCD" : "#999999",
                  background: goods.statusCode === $ExchangeGoodsType.ACTIVE ? "#ECF6FF" : "#F2F2F2",
                }}>{goods.statusLabel}</span>
              </Item>
            );
          }) : <NoGoods title="暂无换货单"/> : null
        }
        {
          exchangegoodsrequestList && exchangegoodsrequestList.length > 0 ?
            <LoadingTip
              isFinished={finished}
              isLoad={isShow}
            />
            :
            <div/>
        }
      </ExchangeContentsPage>
    );
  }
}
const ExchangeContentsPage = styled.div`// styled
  & {
    width: 100%;
    height: auto;
    overflow-x: hidden;

    .am-list-item {
      border-bottom: 1px solid #D8D8D8;
    }

    .am-list-content {
      position: relative;

      > span:nth-of-type(1) {
        font-size: 14px;
        color: #333;
      }

      > span:nth-of-type(2) {
        font-size: 12px;
        color: #999;
      }

      > span:nth-of-type(3) {
        position: absolute;
        top: 22px;
        right: 0px;
        font-size: 12px;
        color: #307DCD;
        border-radius: 10px;
        background: #ECF6FF;
        display: inline-block;
        width: 56px;
        height: 20px;
        line-height: 20px;
        text-align: center;
      }
    }
  }
`;
