import { autowired, bean } from "@classes/ioc/ioc";
import { action, observable } from "mobx";
import { $ExchangeGoodsService } from "../../classes/service/$exchange-goods-service";
import { $ShippingAddressService } from "../../classes/service/$shipping-address-service";
import { $exchangeGood } from "../../classes/entity/$exchange-good";
import { $SearchInfo } from "../../classes/entity/$search-info";
import { $statusList } from "../../classes/entity/$status-list";
import { beanMapper } from "../../helpers/bean-helpers";

@bean($ExchangeGoodsListMv)
export class $ExchangeGoodsListMv {
  @autowired($ShippingAddressService)
  public $shippingAddressService: $ShippingAddressService;

  @autowired($ExchangeGoodsService)
  public $exchangeGoodsService: $ExchangeGoodsService;

  @observable public isSpin: boolean = false;

  @observable public exchangegoodsrequestList: $exchangeGood[] = [];

  @observable public addressTree = [];

  @observable public searchInfo: $SearchInfo = new $SearchInfo();

  @observable public totalCount: number;
  @observable public scrollHeight: number = 0;

  @observable public statusList: $statusList[] = [];

  @observable public isShow: boolean = false;
  @observable public open: boolean = false;
  @observable public searchKeyword: string = "";
  @observable public startDate: string = "";
  @observable public endDate: string = "";
  @observable public finished: boolean = false;
  @action
  public showSpin() {
    this.isSpin = true;
  }

  @action
  public hideSpin() {
    this.isSpin = false;
  }

  @action
  public showMore() {
    this.isShow = true;
  }

  @action
  public hideMore() {
    this.isShow = false;
  }

  @action
  public fetchAddressTree(params) {
    return this.$shippingAddressService.queryAddressList(params).then((data) => {
      const { regionTree } = data;
      this.addressTree = regionTree;
    });
  }

  @action
  public fetchStatusList() {
    this.$exchangeGoodsService.queryExchangeGoodsRequestStatusList().then((data) => {
      this.statusList = data.statusList;
    });
  }

  @action
  public fetchExchangegoodsrequestList(val) {
    return new Promise((resolve, reject) => {
      if (val === "confirm") {
        this.statusList.map((status) => {
          if (status.checked) {
            this.searchInfo.statusCode.push(status.statusCode);
          }
        });
      }
      const params = this.searchInfo;
      this.$exchangeGoodsService.queryExchangegoodsrequestList(params).then((data) => {
        const { total, exchangeGoodsRequestList } = data;
        this.totalCount = total;
        exchangeGoodsRequestList.map((goods) =>
          this.exchangegoodsrequestList.push(new $exchangeGood(goods)),
        );
        this.finished = this.exchangegoodsrequestList.length >= total;
        resolve();
      }).then(() => {
        this.hideMore();
        reject();
      });
    });
  }

  @action
  public setAddressRegionId(regionId) {
    this.searchInfo.regionId = regionId;
  }

  @action
  public setStatusCode(code) {
    this.searchInfo.statusCode = [];
    this.statusList.map((status, index) => {
      if (status.statusCode === code) {
        status.checked = !status.checked;
      }
    });
  }

  @action
  public setSearchKeyword(val) {
    this.searchInfo.title = val;
  }

  @action
  public setStartDate(val) {
    let month = val.getMonth() + 1;
    let date = val.getDate();
    if (month < 10) {
      month = "0" + month;
    }
    if (date < 10) {
      date = "0" + date;
    }
    // console.log(month);
    // console.log(date);
    this.searchInfo.startTime = val.getFullYear() + "-" + (month) + "-" + date;
  }

  @action
  public setEndDate(val) {
    let month = val.getMonth() + 1;
    let date = val.getDate();
    if (month < 10) {
      month = "0" + month;
    }
    if (date < 10) {
      date = "0" + date;
    }
    // console.log(month);
    // console.log(date);
    this.searchInfo.endTime = val.getFullYear() + "-" + (month) + "-" + date;
  }

  @action
  public clearInfo() {
    this.statusList.map((status, index) => {
      if (status.statusCode === "ACTIVE") {
        status.checked = true;
      } else {
        status.checked = false;
      }
    });
    this.searchInfo.statusCode = ["ACTIVE"];
    this.searchInfo.title = "";
    this.searchInfo.startTime = "";
    this.searchInfo.endTime = "";
    this.searchInfo.pageIndex = 0;
    this.searchInfo.regionId = [];
  }
  @action
  public queryOldData(obj) {
    beanMapper(obj, this);
    console.log(this);
  }
  @action
  public clearMVData() {
   this.isSpin = false;
   this.exchangegoodsrequestList = [];
   this.addressTree = [];
   this.searchInfo = new $SearchInfo();
   this.totalCount = 0;
   this.scrollHeight = 0;
   this.statusList = [];
   this.isShow = false;
   this.open = false;
   this.searchKeyword = "";
   this.startDate = "";
   this.endDate = "";
   this.finished = false;
  }
}
