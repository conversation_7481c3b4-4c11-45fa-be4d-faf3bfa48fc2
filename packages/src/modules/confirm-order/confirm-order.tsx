import { autowired } from "@classes/ioc/ioc";
import { Button, Checkbox, ImagePicker, List, Toast } from "antd-mobile";
import { merge } from "lodash";
import { observer } from "mobx-react";
import React from "react";
import { withRouter } from "react-router";
import styled from "styled-components";
import { $CartType } from "../../classes/const/$cart-type";
import { REQUEST_SERVER } from "../../helpers/ajax-helpers";
import { SITE_PATH } from "../app";
import { $SubmitOrderMv } from "../submit-order/submit-order-mv";
import { $ConfirmOrderMv } from "./confirm-order-Mv";
import { $AppStore, AppStoreKey } from "../../classes/stores/app-store-mv";

declare let require: any;
declare let window: any;

const Item = List.Item;
const Brief = Item.Brief;
const CheckboxItem = Checkbox.CheckboxItem;

@withRouter
@observer
class ConfirmOrder extends React.Component<any, any> {
  @autowired($AppStore)
  public $AppStore: $AppStore;
  @autowired($ConfirmOrderMv)
  public $ConfirmOrderMv: $ConfirmOrderMv;

  @autowired($SubmitOrderMv)
  public $SubmitOrderMv: $SubmitOrderMv;

  constructor(props) {
    super(props);
    this.state = {
      isShowMabel: false,
    };
  }

  public componentDidMount() {
    document.title = "订单确认";
    this.loadConfirmOrder().then(() => {
      this.loadCapitalAccount();
    });
  }

  public loadConfirmOrder = () => {
    const params = {
      orderId: this.$SubmitOrderMv.relatedDocId,
      instalmentType: this.$SubmitOrderMv.instalmentType,
    };
    return this.$ConfirmOrderMv.fetchConfirmOrder(params);
  }

  public loadCapitalAccount = () => {
    const params = {
      paymentModeId: this.$SubmitOrderMv.paymentModeId,
      orderPartyId: this.$ConfirmOrderMv.orderPartyId,
    };
    this.$ConfirmOrderMv.fetchCapitalAccount(params);
  }

  public onSubmit = () => {
    const { offilinePayList, pics } = this.$ConfirmOrderMv;
    const { saveOrderPayData } = this.$SubmitOrderMv;
    const paymentItemList = saveOrderPayData.paymentItemList || [];
    let paymentModeId, paymentAccountId, amount;
    // if (paymentItemList.length === 2) {
    //   paymentModeId =  paymentItemList[1].paymentModeId;
    //   paymentAccountId = paymentItemList[1].paymentAccountId;
    //   amount = paymentItemList[1].amount;
    // } else
    if (paymentItemList) {
      if (paymentItemList.length === 1) {
        paymentModeId = paymentItemList[0].paymentModeId;
        paymentAccountId = paymentItemList[0].paymentAccountId;
        amount = paymentItemList[0].amount;
      }
    }
    const params = {
      relatedDocId: this.$SubmitOrderMv.relatedDocId,
      relatedDocObjectType: this.$SubmitOrderMv.relatedDocObjectType,
      paymentItemList: [{
        paymentModeId,
        paymentAccountId,
        sellerAccountId: offilinePayList.capitalAccountInfo.capitalAccountId,
        amount,
        voucherImages: pics,
      }],
      memo: this.$SubmitOrderMv.memo,
    };
    this.$ConfirmOrderMv.saveConfirmOrder(params).then((data) => {
      if (data.result) {
        Toast.success("支付成功", 3);
        this.$AppStore.clearPageMv(AppStoreKey.SINGLESHOPORDERLISTSTORE);
        this.props.history.push({ pathname: `/${SITE_PATH}/shop/order-list` });
      }
    });
  }

  public setDefaultCheck = (v, index) => {
    const { capitalAccountList, offilinePayList } = this.$ConfirmOrderMv;
    capitalAccountList.forEach((item) => {
      item.isDefault = "N";
    });
    capitalAccountList[index].isDefault = "Y";
    offilinePayList.capitalAccountInfo.bankName = capitalAccountList[index].bankName;
    offilinePayList.capitalAccountInfo.accountCode = capitalAccountList[index].accountCode;
    offilinePayList.capitalAccountInfo.bankAccountName = capitalAccountList[index].bankAccountName;
    offilinePayList.capitalAccountInfo.capitalAccountId = capitalAccountList[index].capitalAccountId;
    this.setState({ isShowMabel: false });
  }

  public showAccountMabel = () => {
    this.setState({
      isShowMabel: true,
    });
  }

  public uploadImage = (file: any) => {
    const formData = new FormData();
    formData.append("file", file.file);

    const xhr = new XMLHttpRequest();

    xhr.open("POST", `${REQUEST_SERVER}/k/integration/scm/payvoucher/upload`, true);
    xhr.setRequestHeader("ssoSessionId", localStorage.getItem("token"));
    xhr.onload = () => {
      if (xhr.status === 200) {
        const url = JSON.parse(xhr.responseText).data.data.pic.url;
        const _file = merge({}, file.file, { url });
        this.addFile(_file);
      }

    };
    xhr.send(formData);
  }

  public addFile = (file) => {
    const { pics } = this.$ConfirmOrderMv;
    const fileList = pics.slice();
    fileList.push(file);
    this.$ConfirmOrderMv.setPics(fileList);
  }

  public onUpload = (files, type, index) => {
    if (type === "add") {
      this.setState({ files }, () => this.uploadImage(files[files.length - 1]));
    } else if (type === "remove") {
      this.$ConfirmOrderMv.setPics(files);
    }
  }

  public render() {
    const { isShowMabel } = this.state;
    const { offilinePayList, capitalAccountList, pics } = this.$ConfirmOrderMv;
    const { docNo, totalQuantity, payableAmount, instalmentName, capitalAccountInfo } = offilinePayList || {};
    const { bankName, bankAccountName, accountCode } = capitalAccountInfo || {};
    return (
      <Wrapper>
        <List>
          <Item>
            <p>
              {bankAccountName}，您的订单提交成功，请尽快支付订单
            </p>
            <p>
              温馨提示：请在下方上传汇款凭证或进入订单上传
            </p>
          </Item>
        </List>
        <img className="border-img" src={require("../../components/assets/ico-colorline.png")} alt=""/>
        <List className="my-list not-mt">
          <Item extra={docNo}>订单编号</Item>
          <Item extra={totalQuantity}>商品数量</Item>
          <Item extra={`¥ ${payableAmount}`} className="text-red">订单金额</Item>
          <Item extra={instalmentName}>款项</Item>
        </List>
        <List className="bank">
          <Item
            arrow="horizontal"
            onClick={this.showAccountMabel}
          >
            <p>
              <span>{bankName}&nbsp;&nbsp;</span>
              <span>{accountCode}</span>
            </p>
            <p>
              <span>开户人&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
              <span>{bankAccountName}</span>
            </p>
            <Brief/>
          </Item>
        </List>
        <PaymentVoucher>
          <p>
            付款凭证
          </p>
          <div>
            <ImagePicker
              files={pics}
              selectable={pics.length < 10}
              onChange={this.onUpload}/>
          </div>
        </PaymentVoucher>
        <ConfirmButton>
          <Button type="primary" onClick={this.onSubmit}>确认</Button>
        </ConfirmButton>
        <Mabel style={{ display: isShowMabel ? "block" : "none" }}>
          {
            capitalAccountList ? capitalAccountList.length > 0 ?
              capitalAccountList.map((capitalAccount, index) => {
                return (
                  <div>
                    {
                      capitalAccount.isDefault === $CartType.ISDEFAULE ?
                        <div>
                          <List className="capitalAccountDefault">
                            <CheckboxItem
                              onChange={(v) => this.setDefaultCheck(v, index)}
                              checked={capitalAccount.isDefault === $CartType.ISDEFAULE ? true : false}
                            >
                              <p>
                                <span>{capitalAccount.bankName}</span>
                                <span>默认</span>
                              </p>
                              <p>
                                {capitalAccount.bankAccountName ? capitalAccount.bankAccountName.length > 6 ? capitalAccount.bankAccountName.slice(0, 6) : capitalAccount.bankAccountName : null} {capitalAccount.accountCode}
                              </p>
                            </CheckboxItem>
                          </List>
                          <img className="border-img" src={require("../../components/assets/ico-colorline.png")}
                               alt=""/>
                        </div>
                        : <List className="capitalAccount" key={index}>
                          <CheckboxItem
                            onChange={(v) => this.setDefaultCheck(v, index)}
                            checked={capitalAccount.isDefault === $CartType.ISDEFAULE ? true : false}
                          >
                            <p>
                              <span>{capitalAccount.bankName}</span>
                            </p>
                            <p>
                              {capitalAccount.bankAccountName ? capitalAccount.bankAccountName.length > 6 ? capitalAccount.bankAccountName.slice(0, 6) : capitalAccount.bankAccountName : null} {capitalAccount.accountCode}
                            </p>
                          </CheckboxItem>
                        </List>
                    }
                  </div>
                );
              })
              : null : null
          }
        </Mabel>
      </Wrapper>
    )
      ;
  }
}

export default ConfirmOrder;

const Wrapper = styled.div`// styled
  & {
    background: #F2F2F2;
    color: #333;
    .am-list-content {
      font-size: 15px;
      color: #333;
    }
    .am-list-content p:first-child {
      color: #333333;
      font-size: 14px;
    }
    .am-list-content p:last-child {
      color: #999999;
      font-size: 12px;
    }
    .border-img {
      width: 100%;
      height: auto;
      margin-top: -22px;
    }
    .my-list {
      margin-top: 10px;
      &.not-mt {
        margin-top: 0px;
      }
    }
    .text-red .am-list-line .am-list-extra {
      color: #FF4242;
    }
    .am-list-item .am-list-line .am-list-content {
      font-size: 14px;
      color: #333;
    }
    .bank {
      margin-top: 10px;
    }
    .bank .am-list-line .am-list-content {
      > p {
        margin-bottom: 0;
      }
      > p span {
        font-size: 14px;
        color: #333;
      }
      > p span:first-child {
        display: inline-block;
        width: 100px;
      }
      > p span:last-child {
        color: #999999;
      }
    }
  }
`;

const Mabel = styled.div`// styled
  & {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 120%;
    background: #F2F2F2;
    z-index: 100;
    .capitalAccountDefault .am-list-content p:first-child span:last-child {
      display: inline-block;
      width: 28px;
      height: 16px;
      background: #ECF6FF;
      color: #307DCD;
      text-align: center;
      font-size: 10px;
      margin-left: 10px;
    }
    .capitalAccount {
      margin-bottom: 20px;
    }
  }
`;

const PaymentVoucher = styled.div`// styled
  & {
    margin-top: 10px;
    width: 100%;
    height: 200px;
    background: #fff;
    > p {
      height: 40px;
      line-height: 40px;
      border-bottom: 1px solid #ddd;
      padding-left: 15px;
    }
    > div {
      padding: 15px;
    }
  }
`;

const ConfirmButton = styled.div`// styled
  & {
    padding: 15px;
  }
`;
