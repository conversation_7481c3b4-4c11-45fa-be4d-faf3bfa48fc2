import { autowired, bean } from "@classes/ioc/ioc";
import { action, observable } from "mobx";
import { $capitalAccountList } from "../../classes/entity/$confirm-order";
import { $File } from "../../classes/entity/$file";
import { $Product } from "../../classes/entity/$product";
import { $ConfirmOrderService } from "../../classes/service/$confirm-order-service";
import { $ComponentService } from '@classes/service/$component-service';

@bean($ConfirmOrderMv)
export class $ConfirmOrderMv {
  @autowired($ConfirmOrderService)
  public $confirmOrderService: $ConfirmOrderService;
  @autowired($ComponentService)
  public $ComponentService: $ComponentService;
  @observable
  public offilinePayList: object;

  @observable
  public capitalAccountList: $capitalAccountList[] = [];

  @observable public pics: $File[] = [];

  @observable public orderPartyId: number;

  @action
  public fetchConfirmOrder(params) {
    return this.$confirmOrderService.queryConfirmOrder(params).then((data) => {
      this.offilinePayList = data;
      this.orderPartyId = data.orderPartyId;
    });
  }

  @action
  public saveConfirmOrder(params) {
    return this.$ComponentService.saveOrderPay(params);
  }

  @action
  public saveReconfirmOrder(params) {
    return this.$confirmOrderService.saveReconfirmOrder(params);
  }

  @action
  public fetchCapitalAccount(params) {
    this.$confirmOrderService.queryCapitalAccount(params).then((data) => {
      const { capitalAccountList } = data;
      this.capitalAccountList = capitalAccountList.map((capitalAccount) => new $Product(capitalAccount));
    });
  }

  @action
  public setPics(pics: any[]) {
    this.pics = pics;
  }

}
