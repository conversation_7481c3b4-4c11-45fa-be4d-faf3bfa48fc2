import * as React from "react";
import { observer } from "mobx-react";
import styled from "styled-components";
import { ReportTable } from "../../components/report-table/report-table";
import { autowired } from "@classes/ioc/ioc";
import { OweGoodsSelectMv } from "./owe-goods-select-mv";
import { Spin } from "antd";
import { Checkbox, SearchBar, Toast } from "antd-mobile";
import { getQueryString } from "../../classes/utils/UrlUtils";
import { SITE_PATH } from "../app";
import { transaction } from "mobx";
import { NoGoods } from "../../components/no-goods/no-goods";

const AgreeItem = Checkbox.AgreeItem;

@observer
class OweGoodsSelect extends React.Component<any, any> {
  private reportTableRef: any;

  @autowired(OweGoodsSelectMv)
  public myMv: OweGoodsSelectMv;

  public constructor(props) {
    super(props);
    this.state = {
      columns: [
        {
          title: "商品",
          width: 214,
          dataIndex: "productSkuName",
          key: "productSkuName",
          fixed: "left",
          className: "productSkuName",
          render: (text, record, index) => this.renderShop(text, record, index),
        },
        {
          title: this.renderTitle("订货数量", "订货金额"),
          dataIndex: "sales",
          key: "sales",
          className: "sales",
          width: 80,
          render: (text, record, index) => this.renderConent(text, record, index),
        },
        {
          title: this.renderTitle("未发货数量", "未发货金额"),
          dataIndex: "unDelivery",
          key: "unDelivery",
          className: "unDelivery",
          width: 80,
          render: (text, record, index) => this.renderConent(text, record, index, "unDelivery"),
        },
        {
          title: this.renderTitle("已配未发数量", "已配未发金额"),
          dataIndex: "distributedUnDelivery",
          key: "distributedUnDelivery",
          className: "distributedUnDelivery",
          width: 90,
          render: (text, record, index) => this.renderConent(text, record, index),
        }
      ],
    };
  }

  public componentDidMount() {
    document.title = "欠货查询";
    this.myMv.clearMyMv();
    const shopInfo = getQueryString("shopInfo") && JSON.parse(getQueryString("shopInfo"));
    this.myMv.shopName = shopInfo && shopInfo.name;
    this.myMv.setShopInfo(shopInfo);
    this.loadData();
    // this.myMv.freezeTitleHeight = $(".report-table-wrap").offset().top;
    // $(document.body).scroll(this.calcPositionFixedFunc);
  }

  public calcPositionFixedFunc = () => {
    const scrollTop = $(document.body).scrollTop();
    const { freezeTitleHeight } = this.myMv;
    const obj = $(".titleTable")[0];
    const paddingTop = parseInt($(document.body).css("paddingTop"), 0);
    if (obj) {
      const listHeaderHeight = $(obj).height() + 1;
      if (scrollTop >= freezeTitleHeight && !paddingTop) {
        $(obj).css({ position: "fixed", zIndex: 1, top: 0, width: "100vw" });
        $(document.body).css({ paddingTop: `${listHeaderHeight}px` });
      } else if (scrollTop < freezeTitleHeight && paddingTop) {
        $(obj).css({ position: "relative", zIndex: 0, top: 0 });
        $(document.body).css({ paddingTop: "0px" });
      }
    }
  }

  public loadData = () => {
    const { finished, isLoading } = this.myMv;
    if (finished || isLoading) {
      return;
    }
    this.ordermallOweGoodsQuery();
  }

  public ordermallOweGoodsQuery = () => {
    const { pageIndex, keyword, pageSize, shopInfo, isChoose ,isChooseDistributed} = this.myMv;
    const params = {
      keyword,
      orgId: shopInfo && shopInfo.oid,
      pageIndex,
      pageSize,
      isShowUnreceivedGoods: isChoose ? "Y" : "N",
      isShowDistributedUnDelivery:isChooseDistributed?"Y":"N"
    };
    // if (!pageIndex) {
    //   this.myMv.showSpin();
    // }
    this.myMv.showSpin();
    this.myMv.isLoading = true;
    this.myMv.ordermallOweGoodsQuery(params).then((data) => {
      this.myMv.hideSpin();
      transaction(() => {
        this.myMv.totalUnDeliveryQuantity = data.totalUnDeliveryQuantity;
        this.myMv.totalUnDeliveryAmount = data.totalUnDeliveryAmount;
        this.myMv.totalWaitReceiveAmount = data.totalWaitReceiveAmount;
        this.myMv.totalWaitReceiveQuantity = data.totalWaitReceiveQuantity;
        this.myMv.totalDistributedUnDeliveryAmount = data.totalDistributedUnDeliveryAmount;
        this.myMv.totalDistributedUnDeliveryQuantity = data.totalDistributedUnDeliveryQuantity;
        this.myMv.itemCount = data.itemCount;
        this.myMv.isLoading = false;
        if (data.list && data.list.length >= 0) {
          this.myMv.list = pageIndex ? this.myMv.list.concat(data.list) : data.list;
          this.myMv.changePage(pageIndex + 1);
          // console.log("数据加载");
          // console.log(this.myMv.list.length);
          // console.log(this.myMv.list);
          // console.log(data.itemCount);
          if (this.myMv.list.length >= data.itemCount) {
            this.myMv.finished = true;
          }
          // const scrollTitleItem = $(".titleTable .ant-table-scroll .ant-table-body")[0];
          // $(scrollTitleItem).scrollLeft(0);
        }
      });
    }).catch((err) => {
      this.myMv.hideSpin();
      this.myMv.isLoading = false;
      Toast.fail(err.response.body.message);
    });
  }

  public onChangeSearch = (val) => {
    console.log(val);
    this.myMv.keyword = val;
  }

  public onSearch = () => {
    this.myMv.pageIndex = 0;
    this.myMv.finished = false;
    this.myMv.list = [];
    this.loadData();
  }

  public changeShop = () => {
    const { shopInfo } = this.myMv;
    // this.props.history.push({ pathname: `/${SITE_PATH}/choose-shop?shopInfo=${JSON.stringify(shopInfo)}` });
    window.location.href = `/${SITE_PATH}/choose-shop?shopInfo=${JSON.stringify(shopInfo)}`;
    // document.getElementsByClassName("orderPage")[0].style.position = "relative";
  }

  public changeShowDistributed=(e)=>{
    transaction(() => {
      this.myMv.isChooseDistributed = e.target.checked;
      this.myMv.pageIndex = 0;
      this.myMv.finished = false;
      this.myMv.list = [];
    });
    this.ordermallOweGoodsQuery();
  }
  public changeShow = (e) => {
    console.log(e.target.checked);
    console.log("Ref", this.reportTableRef);
    transaction(() => {
      this.myMv.isChoose = e.target.checked;
      this.myMv.pageIndex = 0;
      this.myMv.finished = false;
      this.myMv.list = [];
    });
    this.ordermallOweGoodsQuery();
    if (e.target.checked) {
      this.setState({
        columns: [
          {
            title: "商品",
            width: 80,
            dataIndex: "productSkuName",
            key: "productSkuName",
            fixed: "left",
            className: "productSkuName",
            render: (text, record, index) => this.renderShop(text, record, index),
          },
          {
            title: this.renderTitle("订货数量", "订货金额"),
            dataIndex: "sales",
            key: "sales",
            className: "sales",
            width: 80,
            render: (text, record, index) => this.renderConent(text, record, index),
          },
          {
            title: this.renderTitle("未发货数量", "未发货金额"),
            dataIndex: "unDelivery",
            key: "unDelivery",
            className: "unDelivery",
            width: 80,
            render: (text, record, index) => this.renderConent(text, record, index, "unDelivery"),
          },
          {
            title: this.renderTitle("已配未发数量", "已配未发金额"),
            dataIndex: "distributedUnDelivery",
            key: "distributedUnDelivery",
            className: "distributedUnDelivery",
            width: 90,
            render: (text, record, index) => this.renderConent(text, record, index),
          },
          {
            title: this.renderTitle("待收货数量", "待收货金额"),
            dataIndex: "waitReceive",
            key: "waitReceive",
            className: "waitReceive",
            width: 80,
            render: (text, record, index) => this.renderConent(text, record, index),
          },
          {
            title: this.renderTitle("已收货数量", "已收货金额"),
            dataIndex: "received",
            key: "received",
            width: 80,
            render: (text, record, index) => this.renderConent(text, record, index),
          },
        ],
      });
    } else {
      this.setState({
        columns: [
          {
            title: "商品",
            width: 214,
            dataIndex: "productSkuName",
            key: "productSkuName",
            fixed: "left",
            className: "productSkuName",
            render: (text, record, index) => this.renderShop(text, record, index),
          },
          {
            title: this.renderTitle("订货数量", "订货金额"),
            dataIndex: "sales",
            key: "sales",
            className: "sales",
            width: 80,
            render: (text, record, index) => this.renderConent(text, record, index),
          },
          {
            title: this.renderTitle("未发货数量", "未发货金额"),
            dataIndex: "unDelivery",
            key: "unDelivery",
            className: "unDelivery",
            width: 80,
            render: (text, record, index) => this.renderConent(text, record, index, "unDelivery"),
          },
          {
            title: this.renderTitle("已配未发数量", "已配未发金额"),
            dataIndex: "distributedUnDelivery",
            key: "distributedUnDelivery",
            className: "distributedUnDelivery",
            width: 90,
            render: (text, record, index) => this.renderConent(text, record, index),
          }
        ],
      });
    }
  }

  public renderTitle = (title1, title2) => {
    return (
      <div>
        <p>{title1}</p>
        <p>{title2}</p>
      </div>
    );
  }

  public renderShop = (text, record, index) => {
    return (
      <div
        style={{
          wordWrap: "break-word",
          wordBreak: "break-word",
          backgroundColor: (index + 1) % 2 === 0 ? "#F2F2F2" : "#fff",
        }}
        className={"scm-shop-item"}
      >
        {text}
      </div>
    );
  }

  public renderConent = (text, record, index, type?) => {
    const amount = String(parseInt(text && text.amount, 0));
    return (
      <div style={{ color: `${type === "unDelivery" ? "#307DCD" : ""}`, boxSizing: "border-box" }}
           className="scm-inner-td">
        <p>{text && text.quantity}</p>
        <p>{typeof(text && text.amount) === "number" ? `¥${amount.length > 7 ? amount.slice(0, 7) + "..." : Number(text && text.amount).toFixed(2)}` : (text && text.amount)}</p>
      </div>
    );
  }

  public render() {
    const { list, isSpin, finished, isLoading, totalUnDeliveryQuantity, totalUnDeliveryAmount, totalWaitReceiveAmount, totalWaitReceiveQuantity, keyword, shopName, itemCount, isChoose,totalDistributedUnDeliveryQuantity,totalDistributedUnDeliveryAmount } = this.myMv;
    return (
      <OweGoodsSelectWrapper>
        <Spin spinning={isSpin}>
          <ShopInfo>
            <div className="shop-info-content">
              <div className="shop-base-info">
                <div className="title">
                  <span onClick={this.changeShop} className={"choose-shop"}>
                    <i className="scmIconfont scm-icon-shop"/>
                    <span className="title-text">{shopName}</span>
                    <i className="scmIconfont scm-icon-jiantou-shang"/>
                  </span>
                  <AgreeItem data-seed="logId" onChange={this.changeShowDistributed}>
                    已配未发>0 
                  </AgreeItem>
                  <AgreeItem data-seed="logId" onChange={this.changeShow}>
                    显示未收货
                  </AgreeItem>
                  
                </div>
                <div className="info-list-key list-key-active">
                  <div className="left">
                    <span className="key">未发货总数：</span>
                    <span className="value active">{totalUnDeliveryQuantity}</span>
                  </div>
                  <div className="right">
                    <span className="key">未发货总金额：</span>
                    <span className="value active">{Number(totalUnDeliveryAmount).toFixed(2)}</span>
                  </div>
                </div>
                {
                   <div className="info-list-key">
                    <div className="left">
                      <span className="key">已配未发总数：</span>
                      <span className="value">{totalDistributedUnDeliveryQuantity}</span>
                    </div>
                    <div className="right ">
                      <span className="key">已配未发总金额：</span>
                      <span className="value">{Number(totalDistributedUnDeliveryAmount).toFixed(2)}</span>
                    </div>
                  </div>
                }
                {
                  isChoose && <div className="info-list-key">
                    <div className="left">
                      <span className="key">待收货总数：</span>
                      <span className="value">{totalWaitReceiveQuantity}</span>
                    </div>
                    <div className="right">
                      <span className="key">待收货总金额：</span>
                      <span className="value">{Number(totalWaitReceiveAmount).toFixed(2)}</span>
                    </div>
                  </div>
                }
                
              </div>
              <div className="shop-search-content">
                <SearchBar
                  value={keyword}
                  placeholder="输入订单号/订货方案/商品"
                  onSubmit={this.onSearch}
                  onChange={this.onChangeSearch}
                />
              </div>
            </div>
          </ShopInfo>
          <div className="report-table-wrap">
            {list.length > 0 ? <ReportTable
              contentData={[...list]}
              finished={finished}
              itemCount={itemCount}
              ref={(el) => this.reportTableRef = el}
              isLoading={isLoading}
              ordermallOweGoodsQuery={this.ordermallOweGoodsQuery}
              isChoose={isChoose}
              columns={this.state.columns}
            /> : <NoGoods title="暂无数据" height={document.documentElement.clientHeight - 158}/>}
          </div>
        </Spin>
      </OweGoodsSelectWrapper>
    );
  }
}

export default OweGoodsSelect;

const OweGoodsSelectWrapper = styled.div`// styled
  & {
    width: 100%;
    height: auto;
    background-color: rgba(242, 242, 242, 1);
    min-height: 100vh;
    .report-table-wrap {
      width: auto;
      height: auto;
    }
  }
`;
const ShopInfo = styled.div`// styled
  & {
    width: 100%;
    height: auto;
    position: relative;
    overflow: hidden;
    //.back-ground-wrap {
    //  position: absolute;
    //  top: 0;
    //  left: 0;
    //  height: 106px;
    //  width: 100%;
    //  background-color: #5176AC;
    //}
    .shop-info-content {
      //position: absolute;
      //z-index: 1;
      //width: calc(100vw - 24px);
      //margin: 12px 12px 0;
      background-color: #fff;
      //background-image: url("https://order.fwh1988.cn:14501/static-img/scm/img_bg_returned_status.png");
      //-webkit-background-size: 100% 100%;
      //background-size: 100% 100%;
      //background-repeat: no-repeat;
      //background-position: center center;
      //border-radius: 8px;
      //box-shadow: 0px 0px 3px 0px rgba(0, 0, 0, 0.05);
      height: auto;
      .shop-base-info {
        padding: 12px 12px 0px;
        overflow: hidden;
        .title {
          font-size: 14px;
          line-height: 14px;
          color: #2F2F2F;
          margin-bottom: 13px;
          overflow: hidden;
          .choose-shop {
            display: inline-block;
            width: calc(100% - 190px);
          }
          .scm-icon-shop {
            font-size: 14px;
            color: #999999;
            margin-right: 5px;
            float: left;
          }
          .title-text {
            display: inline-block;
            max-width: calc(100% - 45px);;
            height: 100%;
            line-height: 14px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            float: left;
          }
          .scm-icon-jiantou-shang {
            font-size: 12px;
            color: #999999;
            margin-left: 10px;
            float: left;
            transform: rotate(180deg);
            -ms-transform: rotate(180deg); /* Internet Explorer */
            -moz-transform: rotate(180deg); /* Firefox */
            -webkit-transform: rotate(180deg); /* Safari 和 Chrome */
            -o-transform: rotate(180deg); /* Opera */
            &:before {
              transform: rotate(180deg);
            }
          }
          .am-checkbox-agree {
            display: inline-block;
            padding-top: 0;
            padding-bottom: 0;
            top: -2px;
            margin-left:6px;
          }
          .am-checkbox-agree .am-checkbox {
            width: 16px;
            height: 16px;
          }
          .am-checkbox-agree .am-checkbox-agree-label {
            font-size: 13px;
            font-family: SourceHanSansCN-Regular, SourceHanSansCN;
            font-weight: 400;
            color: rgba(117, 117, 117, 1);
            line-height: unset;
            margin-top: 0;
          }
          .am-checkbox-agree .am-checkbox-inner {
            top: 0;
          }
          .am-checkbox-inner {
            width: 15px;
            height: 15px;
          }
          .am-checkbox-agree .am-checkbox-agree-label {
            margin-left: 20px;
          }
          .am-checkbox-inner:after {
            right: 4px;
            height: 8px;
          }
        }
        .info-list-key {
          margin-bottom: 8px;
          font-size: 12px;
          line-height: 13px;
          color: #999999;
          overflow: hidden;
          .left {
            float: left;
            /* width:84px; */
            .key{
              text-align:right;
              display:inline-block;
              width:84px;
            }
          }
          .right {
            float: right;
            width: 165px;
            text-align: left;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            .key{
              display:inline-block;
              width:96px;
              text-align:right;
            }
          }
          .active {
            color: #307DCD;
          }
        }
        .list-key-active {
         .value{ color: #307DCD;}
          /* .key {
            color: #595959;
          } */
        }
      }
      .shop-search-content {
        height: 38px;
        padding: 0 12px 8px;
      }
    }
    .am-search {
      background-color: transparent;
      height: 30px;
      width: 100%;
      display: inline-block;
      padding: 0;
    }
    .am-search-input {
      border-radius: 15px;
      background-color: #F2F2F2;
      height: 100%
    }
    .am-search-input .am-search-synthetic-ph {
      height: 30px;
      line-height: 30px;
      padding: 0 0 0 15px;
      text-align: left;
      width: 100% !important;
    }
    .am-search-input input[type="search"] {
      height: 30px;
    }
    .am-search-cancel {
      display: none;
    }
    .am-search-input .am-search-synthetic-ph-placeholder { /* WebKit, Blink, Edge */
      font-family: PingFangSC-Regular;
      font-size: 14px;
      color: #999999;
    }
    .am-search-value:focus::-webkit-input-placeholder {
      color: transparent !important;
    }
    .am-search-input .am-search-clear-show {
      top: 50%;
      right: 10px;
      transform: translateY(-50%);
    }
  }
`;
