import { autowired, bean } from "@classes/ioc/ioc";
import { action, observable } from "mobx";
import { $OrderReportService } from "../../classes/service/$order-report-service";

@bean(OweGoodsSelectMv)
export class OweGoodsSelectMv {
  @autowired($OrderReportService)
  public $orderReportService: $OrderReportService;

  @observable public isSpin: boolean = false;

  @observable public finished: boolean = false;

  @observable public isLoading: boolean = false;

  @observable public pageIndex: number = 0;

  @observable public pageSize: number = 10;

  @observable public keyword: string = "";

  // @observable public searchContent: string = "";

  @observable public totalUnDeliveryQuantity: number = 0;

  @observable public totalUnDeliveryAmount: number = 0;

  @observable public totalWaitReceiveQuantity: number = 0;

  @observable public totalWaitReceiveAmount: number = 0;

  @observable public totalDistributedUnDeliveryQuantity: number = 0;

  @observable public totalDistributedUnDeliveryAmount: number = 0;

  @observable public shopInfo = { oid: null, name: null };

  @observable public shopName: string = "";

  @observable public itemCount: number = 0;

  @observable public freezeTitleHeight: number = 0;

  @observable public list: any[] = [];

  @observable public isChoose: boolean = false;
  @observable public isChooseDistributed: boolean = false;

  @action
  public showSpin() {
    this.isSpin = true;
  }

  @action
  public hideSpin() {
    this.isSpin = false;
  }

  @action
  public changePage(pageIndex) {
    this.pageIndex = pageIndex;
  }

  @action
  public setShopInfo(item) {
    this.shopInfo = item;
  }

  @action
  public ordermallOweGoodsQuery(params) {
    return this.$orderReportService.ordermallOweGoodsQuery(params);
  }

  @action
  public clearMyMv() {
    this.isSpin = false;
    this.pageIndex = 0;
    this.pageSize = 20;
    this.keyword = "";
    this.shopName = "";
    this.totalUnDeliveryQuantity = 0;
    this.totalUnDeliveryAmount = 0;
    this.totalWaitReceiveQuantity = 0;
    this.totalWaitReceiveAmount = 0;
    this.totalDistributedUnDeliveryQuantity = 0;
    this.totalDistributedUnDeliveryAmount = 0;
    this.itemCount = 0;
    this.freezeTitleHeight = 0;
    this.shopInfo = { oid: null, name: null };
    this.list = [];
  }
}
