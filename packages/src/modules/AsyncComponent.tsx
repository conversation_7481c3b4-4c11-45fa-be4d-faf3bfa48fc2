import React, { Component } from "react";
import { ActivityIndicator } from "antd-mobile";
import { post } from "../helpers/ajax-helpers";
import { $AppService } from "../classes/service/$app-service";
import { autowired } from "@classes/ioc/ioc";

export default function asyncComponent(importComponent, isSkipGetPermission?) {
  class AsyncComponent extends Component {
    @autowired($AppService)
    public $AppService: $AppService;

    constructor(props) {
      super(props);

      this.state = {
        component: null,
      };
    }

    public async componentDidMount() {
      if (!isSkipGetPermission) {
        post("/integration/scm/ordermall/login/permission/query", {}, "routeSkip");
      }
      try {
        if (!isSkipGetPermission) {
          const obj = {
            type: "recordPositionInfo",
            path: "",
            pagePath: window.location.href,
            reason: "",
          };
          this.$AppService.logRecord({ logInfo: obj });
        }
        const { default: component } = await importComponent();
        this.setState({
          component,
        });
      } catch (e) {
        const obj = {
          type: "recordErrorInfo",
          path: "",
          pagePath: window.location.href,
          reason: `codeExecutionError${JSON.stringify(e)}`,
        };
        this.$AppService.logRecord({ logInfo: obj });
      }
    }

    public render() {
      const C = this.state.component;
      return C ?
        <C {...this.props} />
        :
        <ActivityIndicator
          toast={true}
          text="加载中..."
          animating={true}
        />;
    }
  }

  return AsyncComponent;
}
