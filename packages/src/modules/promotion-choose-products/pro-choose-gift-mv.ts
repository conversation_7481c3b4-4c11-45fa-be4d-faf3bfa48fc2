import { autowired, bean } from "@classes/ioc/ioc";
import { toAmountNumber } from "@classes/utils/FormatAmount"
import { Toast } from "antd-mobile";
import { sum } from "lodash";
import { action, computed, observable } from "mobx";
import { $PromotionType } from "../../classes/const/$promotion-type";
import { $PromotionGiftGroup } from "../../classes/entity/$promotion-gift-group";
import { $PromotionGiftList } from "../../classes/entity/$promotion-gift-list";
import { $PromotionMatchService } from "../../classes/service/$promotion-match-service";
import { $PromotionMatchMv } from "../promotion-match/promotion-match-mv";

@bean($proChooseGiftMv)
export class $proChooseGiftMv {

  @autowired($PromotionMatchService)
  public $promotionMatchService: $PromotionMatchService;

  @autowired($PromotionMatchMv)
  public $PromotionMatchMv: $PromotionMatchMv;

  @observable public promotionGiftGroup: $PromotionGiftGroup[] = [];

  @observable public saveGiftList: $PromotionGiftList[] = [];  //   选择的列表

  @observable public saveGiftObj: object;   //保存的数据

  @observable public isSelectGift: boolean = false;

  @observable public promotionInfo: object;

  @observable public times: number;    //参加了几次活动

  @observable public isSpin: boolean = false;

  @observable public selectDonationAmount: number = 0;

  @observable public isDonationAmont: boolean = true;

  @action
  public showSpin() {
    this.isSpin = true;
  }

  @action
  public hideSpin() {
    this.isSpin = false;
  }

  @action
  public fetchGiftData(parmas) {
    return this.$promotionMatchService.queryFree(parmas);
  }

  @action
  public changeIsSelectGift() {
    this.isSelectGift = false;
  }

  @action
  public setPromotionInfo(promotionInfo) {
    this.promotionInfo = promotionInfo;
  }

  @action
  public setTimes(times) {
    this.times = times;
  }

  @action
  public clearSelectDonationAmount() {
    this.selectDonationAmount = 0;
  }

  @action
  public setPromotionGiftGroup(giftGroup: $PromotionGiftGroup[], actionId) {
    this.promotionGiftGroup = giftGroup.map((list) => new $PromotionGiftGroup(list));
    this.promotionGiftGroup.map((g) => {
      g.groupInfo.quantity *= this.times;
      g.groupInfo.amount *= this.times;
      g.groupInfo.setHadMoney(0);
      g.groupInfo.setHadSelect(0);
      g.giftList.map((gift) => {
        gift.setQuantity(0);
        gift.setCheckFlag("N");
      });
    });
    this.promotionGiftGroup.map((group) => {
      group.giftList.map((gift) => {
        this.promotionInfo.actionInfoList.map((info) => {
          //console.log(selectedGift.productSkuId, gift.productSkuId)
          if (actionId === info.actionId) {
            info.adjustItemList.map((selectedGift) => {
              if (selectedGift.index === gift.index && selectedGift.productSkuId === gift.productSkuId) {
                gift.checkFlag = "Y";
                gift.quantity = selectedGift.quantity;
                group.groupInfo.hadSelect += selectedGift.quantity;
              }
            });
          }
        });
      });
    });
    this.promotionGiftGroup.map((group) => {
      if (group.groupInfo.usePriceType && group.groupInfo.usePriceType === $PromotionType.USERETAILPRICE) {
        group.giftList.map((gift) => {
          if (gift.checkFlag === "Y") {
            group.groupInfo.hadMoney += gift.quantity * gift.retailPrice;
            this.selectDonationAmount += gift.quantity * gift.retailPrice;
          }
        });
      } else {
        group.giftList.map((gift) => {
          if (gift.checkFlag === "Y") {
            group.groupInfo.hadMoney += gift.quantity * gift.orderPrice;
            this.selectDonationAmount += gift.quantity * gift.orderPrice;
          }
        });
      }
    });
    //console.log(toJS(this.promotionGiftGroup));
  }

  @action
  public addQuantityByProduct(v?, pindex, index) {
    const { donationAmountType, donationAmount, quantity, optionalType, repeatType, hadMoney, amount, hadSelect, amountType, usePriceType } = this.promotionGiftGroup[pindex].groupInfo;
    // 赠品倍数管控
    let multiple;
    multiple = this.promotionGiftGroup[pindex].giftList[index].multiple;
    if (index > -1) {
      if ((optionalType && optionalType !== "Many") || (repeatType === "No")) {
        if (this.promotionGiftGroup[pindex].giftList[index].quantity >= 1 * this.times) {
          Toast.info(`每件商品只可选择${1 * this.times}件`, 3);
          return;
        }
      }
      if (quantity > 0) {   // 数量控制
        const canNum = quantity - hadSelect;
        if (canNum < multiple) {
          // Toast.info(`最多可选择${this.promotionGiftGroup[pindex].giftList[index].quantity}`, 3);
          /*Toast.info(`最多可选择${quantity}件`, 3);*/
          Toast.info(`剩余可选商品数量为${canNum}个，请合理选购`, 3);
          return;
        }
      } else {
        if (amountType === "InPrice" || amountType === null) {    // 金额控制
          if (usePriceType && usePriceType === $PromotionType.USERETAILPRICE) {
            if (amount) { // 固定金额
              if (amount - hadMoney < this.promotionGiftGroup[pindex].giftList[index].retailPrice * multiple) {
                Toast.info(`所选商品价格超出${(amount - hadMoney) ? Number(amount - hadMoney).toFixed(2) : 0}元，请合理选购`, 3);
                return;
              }
            }
          } else {
            if (amount) {
              if (amount - hadMoney < this.promotionGiftGroup[pindex].giftList[index].orderPrice * multiple) {
                Toast.info(`所选商品价格超出${(amount - hadMoney) ? Number(amount - hadMoney).toFixed(2) : 0}元，请合理选购`, 3);
                return;
              }
            }
          }
        }
      }
      if (donationAmountType === "Limited") { // 当促销活动为赠送且为指定范围产品中赠送金额限制时
        if (this.isDonationAmont) {
          if (usePriceType && usePriceType === $PromotionType.USERETAILPRICE) {
            this.selectDonationAmount += toAmountNumber(this.promotionGiftGroup[pindex].giftList[index].retailPrice * multiple);
          } else {
            this.selectDonationAmount += toAmountNumber(this.promotionGiftGroup[pindex].giftList[index].orderPrice * multiple);
          }
        }
        if ((amountType === "InPrice" || amountType === null) &&
          toAmountNumber(this.selectDonationAmount) > toAmountNumber(donationAmount * this.times)) {
          /*Toast.info(`所选赠送金额最多为${(donationAmount * this.times) ? Number(donationAmount * this.times).toFixed(2) : 0}元，请合理选购`, 3);*/
          // this.isDonationAmont = false;
          if (usePriceType && usePriceType === $PromotionType.USERETAILPRICE) {
            this.selectDonationAmount -= toAmountNumber(this.promotionGiftGroup[pindex].giftList[index].retailPrice * multiple);
          } else {
            this.selectDonationAmount -= toAmountNumber(this.promotionGiftGroup[pindex].giftList[index].orderPrice * multiple);
          }
          Toast.info(`剩余可选金额${toAmountNumber(toAmountNumber(donationAmount * this.times) - toAmountNumber(this.selectDonationAmount))}元，请合理选购`, 3);
          return;
        } else {
          // 当前商品数量
          this.promotionGiftGroup[pindex].giftList[index].quantity = this.promotionGiftGroup[pindex].giftList[index].quantity + multiple;
          // 所选总数量数量
          this.promotionGiftGroup[pindex].groupInfo.hadSelect = this.promotionGiftGroup[pindex].groupInfo.hadSelect + multiple;
          // 所选总金额
          // todo: had money计算有问题 原来用的是 orderPrice
          if (usePriceType && usePriceType === $PromotionType.USERETAILPRICE) {
            this.promotionGiftGroup[pindex].groupInfo.hadMoney +=
              toAmountNumber(this.promotionGiftGroup[pindex].giftList[index].retailPrice * multiple);
          } else {
            this.promotionGiftGroup[pindex].groupInfo.hadMoney +=
              toAmountNumber(this.promotionGiftGroup[pindex].giftList[index].orderPrice * multiple);
          }
        }
      } else {
        // 当前商品数量
        this.promotionGiftGroup[pindex].giftList[index].quantity = this.promotionGiftGroup[pindex].giftList[index].quantity + multiple;
        // 所选总数量数量
        this.promotionGiftGroup[pindex].groupInfo.hadSelect = this.promotionGiftGroup[pindex].groupInfo.hadSelect + multiple;
        // 所选总金额
        // todo: had money计算有问题 原来用的是 orderPrice
        if (usePriceType && usePriceType === $PromotionType.USERETAILPRICE) {
          this.promotionGiftGroup[pindex].groupInfo.hadMoney +=
            toAmountNumber(this.promotionGiftGroup[pindex].giftList[index].retailPrice * multiple);
        } else {
          this.promotionGiftGroup[pindex].groupInfo.hadMoney +=
            toAmountNumber(this.promotionGiftGroup[pindex].giftList[index].orderPrice * multiple);
        }
      }
    }
    if (this.promotionGiftGroup[pindex].giftList[index].quantity >= 1) {
      this.promotionGiftGroup[pindex].giftList[index].checkFlag = "Y";
    } else {
      this.promotionGiftGroup[pindex].giftList[index].checkFlag = "N";
    }
  }

  @action
  public minusQuantityByProduct(pindex, index) {
    const { usePriceType } = this.promotionGiftGroup[pindex].groupInfo;
    let multiple;
    multiple = this.promotionGiftGroup[pindex].giftList[index].multiple;
    if (index > -1) {
      if (this.promotionGiftGroup[pindex].groupInfo.donationAmountType === "Limited") { // 当促销活动为赠送且为指定范围产品中赠送金额限制时
        // console.log(this.isDonationAmont);
        if (this.isDonationAmont === false) {
          // console.log(this.selectDonationAmount);
          if (usePriceType && usePriceType === $PromotionType.USERETAILPRICE) {
            this.selectDonationAmount -= toAmountNumber(this.promotionGiftGroup[pindex].giftList[index].retailPrice * multiple);
          } else {
            this.selectDonationAmount -= toAmountNumber(this.promotionGiftGroup[pindex].giftList[index].orderPrice * multiple);
          }
        }
        if (usePriceType && usePriceType === $PromotionType.USERETAILPRICE) {
          this.selectDonationAmount -= toAmountNumber(this.promotionGiftGroup[pindex].giftList[index].retailPrice * multiple);
        } else {
          this.selectDonationAmount -= toAmountNumber(this.promotionGiftGroup[pindex].giftList[index].orderPrice * multiple);
        }
        this.isDonationAmont = true;
        // console.log(this.selectDonationAmount);
      }
      if (this.promotionGiftGroup[pindex].giftList[index].quantity === 0) {
        return;
      } else {
        // 当前商品数量
        this.promotionGiftGroup[pindex].giftList[index].quantity = this.promotionGiftGroup[pindex].giftList[index].quantity - multiple;
        // 所选总数量数量
        this.promotionGiftGroup[pindex].groupInfo.hadSelect = this.promotionGiftGroup[pindex].groupInfo.hadSelect - multiple;
        if (usePriceType && usePriceType === $PromotionType.USERETAILPRICE) {
          this.promotionGiftGroup[pindex].groupInfo.hadMoney -= toAmountNumber(this.promotionGiftGroup[pindex].giftList[index].retailPrice * multiple);
        } else {
          this.promotionGiftGroup[pindex].groupInfo.hadMoney -= toAmountNumber(this.promotionGiftGroup[pindex].giftList[index].orderPrice * multiple);
        }
      }
    }
    if (this.promotionGiftGroup[pindex].giftList[index].quantity < 1) {
      this.promotionGiftGroup[pindex].giftList[index].checkFlag = "N";
    } else {
      this.promotionGiftGroup[pindex].giftList[index].checkFlag = "Y";
    }
  }

  @action
  public checkGift(pindex, index) {
    const { donationAmountType, donationAmount, quantity, hadSelect, amount, amountType, hadMoney, usePriceType } = this.promotionGiftGroup[pindex].groupInfo;
    let multiple;
    multiple = this.promotionGiftGroup[pindex].giftList[index].multiple;
    if (this.promotionGiftGroup[pindex].giftList[index].isActive === "N" && this.promotionGiftGroup[pindex].giftList[index].checkFlag === "N") {
      // Toast.info("该商品已换完", 3);
      return;
    }
    if (this.promotionGiftGroup[pindex].giftList[index].checkFlag === "Y") {// 取消选中
      if (donationAmountType === "Limited") {
        if (this.isDonationAmont === false) {
          // console.log(this.selectDonationAmount);
          if (usePriceType && usePriceType === $PromotionType.USERETAILPRICE) {
            this.selectDonationAmount -= toAmountNumber(this.promotionGiftGroup[pindex].giftList[index].retailPrice * multiple);
          } else {
            this.selectDonationAmount -= toAmountNumber(this.promotionGiftGroup[pindex].giftList[index].orderPrice * multiple);
          }
        }
        if (usePriceType && usePriceType === $PromotionType.USERETAILPRICE) {
          this.selectDonationAmount -= toAmountNumber(this.promotionGiftGroup[pindex].giftList[index].retailPrice * this.promotionGiftGroup[pindex].giftList[index].quantity);
        } else {
          this.selectDonationAmount -= toAmountNumber(this.promotionGiftGroup[pindex].giftList[index].orderPrice * this.promotionGiftGroup[pindex].giftList[index].quantity);
        }
        // console.log(this.selectDonationAmount);
        this.isDonationAmont = true;
      }
      this.promotionGiftGroup[pindex].giftList[index].checkFlag = "N";
      this.promotionGiftGroup[pindex].groupInfo.hadSelect -= this.promotionGiftGroup[pindex].giftList[index].quantity;
      // todo: had money计算有问题 原来用的是 orderPrice
      if (usePriceType && usePriceType === $PromotionType.USERETAILPRICE) {
        this.promotionGiftGroup[pindex].groupInfo.hadMoney -= toAmountNumber(this.promotionGiftGroup[pindex].giftList[index].retailPrice * this.promotionGiftGroup[pindex].giftList[index].quantity);
      } else {
        this.promotionGiftGroup[pindex].groupInfo.hadMoney -= toAmountNumber(this.promotionGiftGroup[pindex].giftList[index].orderPrice * this.promotionGiftGroup[pindex].giftList[index].quantity);
      }
      this.promotionGiftGroup[pindex].giftList[index].quantity = 0;
    } else {// 选中
      const canNum = quantity - hadSelect;
      if (quantity > 0) {   //数量控制
        if (canNum < 1) {
          // Toast.info(`最多可选择${this.promotionGiftGroup[pindex].giftList[index].quantity}`, 3);
          // Toast.info(`最多可选择${quantity}件`, 3);
          Toast.info(`剩余可选商品数量为${canNum}个，请合理选购`, 3);
          return;
        }
      } else {
        if (amountType === "InPrice" || amountType === null) {    //金额控制
          if (usePriceType && usePriceType === $PromotionType.USERETAILPRICE) {
            if (amount) {
              if (toAmountNumber(amount - hadMoney) < toAmountNumber(this.promotionGiftGroup[pindex].giftList[index].retailPrice * multiple)) {
                Toast.info(`所选商品价格超出${amount ? toAmountNumber(amount) : 0}元，请合理选购`, 3);
                return;
              }
            }
          } else {
            if (amount) {
              if (toAmountNumber(amount - hadMoney) < toAmountNumber(this.promotionGiftGroup[pindex].giftList[index].orderPrice * multiple)) {
                Toast.info(`所选商品价格超出${amount ? toAmountNumber(amount) : 0}元，请合理选购`, 3);
                return;
              }
            }
          }
        }
      }
      if (donationAmountType === "Limited") {
        if (usePriceType && usePriceType === $PromotionType.USERETAILPRICE) {
          if ((amountType === "InPrice" || amountType === null) && toAmountNumber(this.promotionGiftGroup[pindex].giftList[index].retailPrice * multiple) > toAmountNumber(donationAmount * this.times)) {
            /*Toast.info(`所选赠送金额最多为${(donationAmount * this.times) ? Number(donationAmount * this.times).toFixed(2) : 0}元，请合理选购`, 3);*/
            Toast.info(`剩余可选金额${Number(donationAmount * this.times - this.selectDonationAmount).toFixed(2)}元，请合理选购`, 3);
            return;
          }
        } else {
          if ((amountType === "InPrice" || amountType === null) && toAmountNumber(this.promotionGiftGroup[pindex].giftList[index].orderPrice * multiple) > toAmountNumber(donationAmount * this.times)) {
            /*Toast.info(`所选赠送金额最多为${(donationAmount * this.times) ? Number(donationAmount * this.times).toFixed(2) : 0}元，请合理选购`, 3);*/
            Toast.info(`剩余可选金额${Number(donationAmount * this.times - this.selectDonationAmount).toFixed(2)}元，请合理选购`, 3);
            return;
          }
        }
        if (this.isDonationAmont) {
          // console.log(787878);
          if (this.promotionGiftGroup[pindex].giftList[index].quantity > 0) {
            if (usePriceType && usePriceType === $PromotionType.USERETAILPRICE) {
              this.selectDonationAmount += toAmountNumber(this.promotionGiftGroup[pindex].giftList[index].retailPrice * this.promotionGiftGroup[pindex].giftList[index].quantity);
            } else {
              this.selectDonationAmount += toAmountNumber(this.promotionGiftGroup[pindex].giftList[index].orderPrice * this.promotionGiftGroup[pindex].giftList[index].quantity);
            }
          }
          if (this.promotionGiftGroup[pindex].giftList[index].quantity === 0) {
            if (usePriceType && usePriceType === $PromotionType.USERETAILPRICE) {
              this.selectDonationAmount += toAmountNumber(this.promotionGiftGroup[pindex].giftList[index].retailPrice * multiple);
            } else {
              this.selectDonationAmount += toAmountNumber(this.promotionGiftGroup[pindex].giftList[index].orderPrice * multiple);
            }
            if ((amountType === "InPrice" || amountType === null) && toAmountNumber(this.selectDonationAmount) > toAmountNumber(donationAmount * this.times)) {
              /*Toast.info(`所选赠送金额最多为${(donationAmount * this.times) ? Number(donationAmount * this.times).toFixed(2) : 0}元，请合理选购`, 3);*/
              if (usePriceType && usePriceType === $PromotionType.USERETAILPRICE) {
                this.selectDonationAmount -= toAmountNumber(this.promotionGiftGroup[pindex].giftList[index].retailPrice * multiple);
              } else {
                this.selectDonationAmount -= toAmountNumber(this.promotionGiftGroup[pindex].giftList[index].orderPrice * multiple);
              }
              Toast.info(`剩余可选金额${toAmountNumber(donationAmount * this.times - this.selectDonationAmount)}元，请合理选购`, 3);
              return;
            }
          }
          // console.log(this.selectDonationAmount);
          this.promotionGiftGroup[pindex].giftList[index].checkFlag = "Y";
          // 当前商品数量
          this.promotionGiftGroup[pindex].giftList[index].quantity = this.promotionGiftGroup[pindex].giftList[index].quantity + multiple;
          // 所选总数量数量
          this.promotionGiftGroup[pindex].groupInfo.hadSelect = this.promotionGiftGroup[pindex].groupInfo.hadSelect + this.promotionGiftGroup[pindex].giftList[index].quantity;
          // todo: had money计算有问题 原来用的是 orderPrice
          /*this.promotionGiftGroup[pindex].groupInfo.hadMoney += this.promotionGiftGroup[pindex].giftList[index].promotionPrice;*/
          if (usePriceType && usePriceType === $PromotionType.USERETAILPRICE) {
            this.promotionGiftGroup[pindex].groupInfo.hadMoney += toAmountNumber(this.promotionGiftGroup[pindex].giftList[index].retailPrice * multiple);
          } else {
            this.promotionGiftGroup[pindex].groupInfo.hadMoney += toAmountNumber(this.promotionGiftGroup[pindex].giftList[index].orderPrice * multiple);
          }
        }
      } else {
        // console.log(5656565);
        this.promotionGiftGroup[pindex].giftList[index].checkFlag = "Y";
        // 当前商品数量
        this.promotionGiftGroup[pindex].giftList[index].quantity = this.promotionGiftGroup[pindex].giftList[index].quantity + multiple;
        // 所选总数量数量
        this.promotionGiftGroup[pindex].groupInfo.hadSelect = this.promotionGiftGroup[pindex].groupInfo.hadSelect + this.promotionGiftGroup[pindex].giftList[index].quantity;
        // todo: had money计算有问题 原来用的是 orderPrice
        /*this.promotionGiftGroup[pindex].groupInfo.hadMoney += this.promotionGiftGroup[pindex].giftList[index].promotionPrice;*/
        if (usePriceType && usePriceType === $PromotionType.USERETAILPRICE) {
          this.promotionGiftGroup[pindex].groupInfo.hadMoney += toAmountNumber(this.promotionGiftGroup[pindex].giftList[index].retailPrice * multiple);
        } else {
          this.promotionGiftGroup[pindex].groupInfo.hadMoney += toAmountNumber(this.promotionGiftGroup[pindex].giftList[index].orderPrice * multiple);
        }
      }
    }
  }

  @action
  public saveGift() {
    this.saveGiftList = [];
    // console.log(toJS(this.promotionGiftGroup));
    this.promotionGiftGroup.map((g) => {
      const { amount, hadSelect, hadMoney, amountType } = g.groupInfo;
      // console.log(amountType);
      if (amountType === "PriceDiff") {  //如果选择赠品金额可以超出
        // console.log(amountType);
        let promotionPrice = 0;
        if (amount < hadMoney) {
          promotionPrice = (hadMoney - amount) / hadSelect;   // 就重新计算promotionPrice，已选金额减去规定金额，再除以所选总数量
        }
        g.giftList.map((v) => {
          if (v.quantity > 0) {
            v.promotionPrice = promotionPrice;
            this.saveGiftList.push(v);
          }
        });
      } else {
        g.giftList.map((v) => {
          if (v.quantity > 0) {
            this.saveGiftList.push(v);
          }
        });
      }
    });
    this.saveGiftObj = {
      actionId: this.$PromotionMatchMv.actionId,
      promotionId: this.$PromotionMatchMv.promotionId,
      saveGiftList: this.saveGiftList,
    };
    this.isSelectGift = true;
    // console.log(toJS(this.saveGiftObj));
  }

  @action
  public clearGift() {
    this.saveGiftObj = {};
  }

  @computed
  get totalCount() {
    return sum(this.saveGiftList
      .filter((list) => list.checkFlag === $PromotionType.CHECKFLAG)
      .map((item) => {
        return item.quantity;
      }));
  }
}
