import { autowired } from "@classes/ioc/ioc";
import { Spin } from "antd";
import { Button, Modal, Toast } from "antd-mobile";
import { observer } from "mobx-react";
import React from "react";
import { withRouter } from "react-router";
import styled from "styled-components";
import { SITE_PATH } from "../app";
import { $PromotionMatchMv } from "../promotion-match/promotion-match-mv";
import { GiftItem } from "./../../components/table/gift-item";
import { $proChooseGiftMv } from "./pro-choose-gift-mv";
import { $PromotionType } from "../../classes/const/$promotion-type";
import { $AppStore, AppStoreKey } from "../../classes/stores/app-store-mv";
import { toAmountNumber } from "@classes/utils/FormatAmount";

const alert = Modal.alert;

@withRouter
@observer
class ProChooseGift extends React.Component<any, any> {
  @autowired($AppStore)
  public $AppStore: $AppStore;
  @autowired($proChooseGiftMv)
  public $proChooseGiftMv: $proChooseGiftMv;

  @autowired($PromotionMatchMv)
  public $PromotionMatchMv: $PromotionMatchMv;

  constructor(props) {
    super(props);
    this.state = {
      orderSchemeType: "",
      tagColor: "",
      tagName: "",
    };
  }

  public componentDidMount() {
    document.title = "赠品";
    // console.log(this.props.location.state);
    const { promotionId, actionType, promotionInfo, times, actionId } = this.props.location.state;
    this.$proChooseGiftMv.setPromotionInfo(promotionInfo);
    this.$proChooseGiftMv.setTimes(times);
    // this.$proChooseGiftMv.clearSelectDonationAmount();
    const params = {
      actionId,
      promotionId,
    };
    this.$proChooseGiftMv.showSpin();
    this.$proChooseGiftMv.fetchGiftData(params).then((data) => {
      this.$proChooseGiftMv.hideSpin();
      // console.log(data);
      const groupData = data.groupList;
      groupData.map((item, index) => {
        item.giftList.map((item2, index2) => {
          item2.index = `${index}${index2}${item2.productSkuId}`
        })
        if (item.groupInfo.scopeType === "FixedAmount") {
          item.groupInfo.donationAmountType = "Limited"
        }
      })
      this.$proChooseGiftMv.setPromotionGiftGroup(groupData, actionId);
      this.setState({ orderSchemeType: data.orderSchemeType });
    });
    this.filterTagInfo(actionType);
  }

  public saveGiftList = () => {
    const { times } = this.props.location.state;
    const { promotionGiftGroup, selectDonationAmount } = this.$proChooseGiftMv;
    // console.log(promotionGiftGroup[0].groupInfo.donationAmount * times);
    // console.log(selectDonationAmount);
    if (promotionGiftGroup[0].groupInfo.forceGiveType === $PromotionType.FORCEGIFTTYPE) {
      if (promotionGiftGroup[0].groupInfo.amount) { // 赠送固定金额
        if (promotionGiftGroup[0].groupInfo.amount - promotionGiftGroup[0].groupInfo.hadMoney > promotionGiftGroup[0].groupInfo.errorAmount) {
          if (promotionGiftGroup[0].groupInfo.usePriceType && promotionGiftGroup[0].groupInfo.usePriceType === $PromotionType.USERETAILPRICE) {
            Toast.info(`至少要选择零售价${promotionGiftGroup[0].groupInfo.amount - promotionGiftGroup[0].groupInfo.errorAmount}元的商品，请继续选购。`, 5);
            return;
          } else {
            Toast.info(`至少要选择订货价${promotionGiftGroup[0].groupInfo.amount - promotionGiftGroup[0].groupInfo.errorAmount}元的商品，请继续选购。`, 5);
            return;
          }
        }
      } else if (promotionGiftGroup[0].groupInfo.donationAmount) { // 赠送指定范围
        if (promotionGiftGroup[0].groupInfo.donationAmount * times - selectDonationAmount > promotionGiftGroup[0].groupInfo.errorAmount) {
          if (promotionGiftGroup[0].groupInfo.usePriceType && promotionGiftGroup[0].groupInfo.usePriceType === $PromotionType.USERETAILPRICE) {
            Toast.info(`至少要选择零售价${promotionGiftGroup[0].groupInfo.donationAmount * times - promotionGiftGroup[0].groupInfo.errorAmount}元的商品，请继续选购。`, 5);
            return;
          } else {
            Toast.info(`至少要选择订货价${promotionGiftGroup[0].groupInfo.donationAmount * times - promotionGiftGroup[0].groupInfo.errorAmount}元的商品，请继续选购。`, 5);
            return;
          }
        }
      }
    }
    if (promotionGiftGroup[0].groupInfo.amountType === $PromotionType.PRICEDIFF) { // 赠送指定范围指定金额
      const useAmount = promotionGiftGroup[0].groupInfo.donationAmount || promotionGiftGroup[0].groupInfo.amount;
      if (useAmount * times < selectDonationAmount) {
        alert("提示", `您此次赠送金额为${useAmount * times ? Number(useAmount * times).toFixed(2) : 0}元，超出${(selectDonationAmount - (useAmount * times)) ? Number(selectDonationAmount - (useAmount * times)).toFixed(2) : 0}元,需要支付差价,是否继续？`, [
          { text: "取消", onPress: () => console.log('cancel') },
          {
            text: "支付差价带走",
            onPress: () => {
              this.$proChooseGiftMv.saveGift();
              this.$AppStore.clearPageMv(AppStoreKey.PROMOTIONMATCH);
              this.props.history.push({
                pathname: `/${SITE_PATH}/promotion/match`,
                state: {
                  shopCartTotalAmount: this.props.location.state.shopCartTotalAmount,
                  backSource: this.props.location.state ? this.props.location.state.backSource : null,
                  prePageSource: this.props.location.state ? this.props.location.state.prePageSource : null,
                  promotionList: this.props.location.state ? this.props.location.state.promotionList : [],
                  hasPromotion: this.props.location.state ? this.props.location.state.hasPromotion : null,
                },
              });
            },
          },
        ])
        return;
      }
    }
    this.$proChooseGiftMv.saveGift();
    this.$AppStore.clearPageMv(AppStoreKey.PROMOTIONMATCH);
    this.props.history.push({
      pathname: `/${SITE_PATH}/promotion/match`,
      state: {
        shopCartTotalAmount: this.props.location.state.shopCartTotalAmount,
        backSource: this.props.location.state ? this.props.location.state.backSource : null,
        prePageSource: this.props.location.state ? this.props.location.state.prePageSource : null,
        promotionList: this.props.location.state ? this.props.location.state.promotionList : [],
        hasPromotion: this.props.location.state ? this.props.location.state.hasPromotion : null,
      },
    });
  }

  public filterTagInfo(n) {
    switch (n) {
      case "Deduct":
        this.setState({
          tagName: "减",
          tagColor: "#8DBD00",
        });
        break;
      case "Discount":
        this.setState({
          tagName: "折",
          tagColor: "#F7B51C",
        });
        break;
      case "Special":
        this.setState({
          tagName: "特",
          tagColor: "#F25EA9",
        });
        break;
      case "PartFree":
        this.setState({
          tagName: "免",
          tagColor: "#B385F5",
        });
        break;
      case "Gift":
        this.setState({
          tagName: "赠",
          tagColor: "#FF7564",
        });
        break;
      case "MutiDiscount":
        this.setState({
          tagName: "折",
          tagColor: "#F7B51C",
        });
        break;
      case "TradeUp":
        this.setState({
          tagName: "换",
          tagColor: "#FF8627",
        });
        break;
    }
  }

  public render() {
    const { promotionGiftGroup, isSpin, selectDonationAmount } = this.$proChooseGiftMv;
    const { orderSchemeType } = this.state;
    const { name, times } = this.props.location.state ? this.props.location.state : {};
    return (
      <ProChooseProductsWrapper>
        <Spin spinning={isSpin}>
          <div className="header">
            <span className="tag" style={{ background: this.state.tagColor }}>{this.state.tagName}</span>
            <span className="title">{name}</span>
          </div>
          {
            promotionGiftGroup ?
              promotionGiftGroup.map((group, pindex) => {
                return <div
                  className={group.groupInfo.scopeType === $PromotionType.OPTIONALGROUP && promotionGiftGroup.length > 1 ? "optionalGroup" : "group"}
                  key={pindex}>
                  <div
                    className={group.groupInfo.scopeType === $PromotionType.SCOPERANGE || group.groupInfo.scopeType === $PromotionType.OPTIONALGROUP ? "promptGift" : ""}>
                    {/*从下列产品中选{group.groupInfo.quantity}件产品，每个产品最多选
                      {
                        group.groupInfo.optionalType === "Many" || group.groupInfo.repeatType === "Yes" ? group.groupInfo.quantity : 1 * times
                      }
                      次*/}
                    {
                      group.groupInfo.scopeType === $PromotionType.SCOPERANGE || group.groupInfo.scopeType === $PromotionType.OPTIONALGROUP ?
                        group.groupInfo.usePriceType === $PromotionType.USERETAILPRICE ?
                          <div>
                            {
                              group.groupInfo.donationAmountType === $PromotionType.DONATIONAMOUNTTYPE ?
                                <p>
                              <span>
                                <span>赠送零售价总金额(元)</span>
                                <span>{(times * group.groupInfo.donationAmount) ? Number(times * group.groupInfo.donationAmount).toFixed(2) : 0}</span>
                              </span>
                                  <span>
                                 <span>已选择零售价金额(元)</span>
                                 <span>{Number(selectDonationAmount).toFixed(2)}</span>
                              </span>
                                  <span>
                                  <span>剩余可选零售价金额(元)</span>
                                  <span>{Number(toAmountNumber(promotionGiftGroup[0].groupInfo.donationAmount * times) - toAmountNumber(selectDonationAmount)).toFixed(2)}</span>
                              </span>
                                </p> : null
                            }
                            {
                              group.groupInfo.donationQuantityType === $PromotionType.DONATIONQUANTITYTYPE ? null :

                                <p>
                                  <span>
                                    <span>赠送总数量(个)</span>
                                    <span>{group.groupInfo.quantity}</span>
                                  </span>
                                  <span>
                                    <span>已选择数量(个)</span>
                                    <span>{group.groupInfo.hadSelect}</span>
                                  </span>
                                  <span>
                                    <span>剩余可选数量(个)</span>
                                    <span>{group.groupInfo.quantity - group.groupInfo.hadSelect}</span>
                                  </span>
                                </p>
                            }
                          </div> : <div>
                            {
                              group.groupInfo.donationAmountType === "Limited" ?
                                <p>
                              <span>
                                <span>赠送订货价总金额(元)</span>
                                <span>{(times * group.groupInfo.donationAmount) ? Number(times * group.groupInfo.donationAmount).toFixed(2) + "元" : 0}</span>
                                </span>
                                  <span>
                                    <span>已选择订货价金额(元)</span>
                                    <span>{Number(selectDonationAmount).toFixed(2)}</span>
                                    </span>
                                  <span>
                                    <span>剩余可选订货价金额(元)</span>
                                    <span>{Number(toAmountNumber(promotionGiftGroup[0].groupInfo.donationAmount * times) - toAmountNumber(selectDonationAmount)).toFixed(2)}</span>
                                    </span>
                                </p> : null
                            }
                            {
                              group.groupInfo.scopeType === $PromotionType.SCOPERANGE || group.groupInfo.scopeType === $PromotionType.OPTIONALGROUP ?
                                group.groupInfo.donationQuantityType === $PromotionType.DONATIONQUANTITYTYPE ? null :
                                  <p>
                                  <span>
                                    <span>赠送总数量(个)</span>
                                    <span>{group.groupInfo.quantity}</span>
                                    </span>
                                    <span>
                                    <span>已选择数量(个)</span>
                                    <span>{group.groupInfo.hadSelect}</span>
                                    </span>
                                    <span>
                                    <span>剩余可选数量(个)</span>
                                    <span>{group.groupInfo.quantity - group.groupInfo.hadSelect}</span>
                                    </span>
                                  </p> : null
                            }
                          </div> : null
                    }
                  </div>
                  {
                    group.groupInfo.scopeType === $PromotionType.FIXEDAMOUNT ? group.groupInfo.amountType === "InPrice" ?
                    <div className="prompt">
                      {`已选满${group.groupInfo.hadMoney ? Number(group.groupInfo.hadMoney).toFixed(2) : 0}元，还可选${(group.groupInfo.amount - group.groupInfo.hadMoney) ? Number(group.groupInfo.amount - group.groupInfo.hadMoney).toFixed(2) : 0}元`}
                    </div> : <div className="prompt">
                      {`已选满${Number(group.groupInfo.hadMoney).toFixed(2)}元，商品超过${Number(group.groupInfo.amount).toFixed(2)}元需要补差价，目前需补${group.groupInfo.hadMoney - group.groupInfo.amount > 0 ? Number(group.groupInfo.hadMoney - group.groupInfo.amount).toFixed(2) : 0}元`}
                    </div> : null
                  }
                  <div className="giftList">
                    {
                      group.giftList.map((value, index) => {
                        return <GiftItem key={value.productSkuId} data={value} pindex={pindex} index={index} orderSchemeType={orderSchemeType}/>;
                        // return <div>111</div>;
                      })
                    }
                  </div>
                </div>;
              }) : null
          }
        </Spin>
        <ConfirmButton>
          <Button type="primary" onClick={this.saveGiftList}>完成</Button>
        </ConfirmButton>
      </ProChooseProductsWrapper>
    );
  }
}

export default ProChooseGift;

const ProChooseProductsWrapper = styled.div`// styled
  & {
    // padding: 12px 0 0px 0;
    .tag {
      border-radius: 2px;
      color: white;
      font-size: 10px;
      padding: 3px 4px 2px;
      margin: 0 5px 0 16px;
      position: relative;
      top: -2px;
    }
    .group {
      position: relative;
      margin-top: 15px;
    }
    .header {
      padding-top: 12px;
      width: 100%;
      background: #fff;
    }
    .am-checkbox-inner:after {
      top: 2.5px;
      right: 7px;
    }
    .title {
      font-size: 14px;
      color: #307DCD;
    }
    
    .giftList {
      overflow:auto;
      -webkit-overflow-scrolling: touch;
      padding-bottom: 45px;
    }
    .promptGift {
      // position: fixed;
      position: relative;
      margin: -3px 16px 10px 16px;
      padding: 8px 10px;
      background: #fff;
      z-index: 99;
      width: calc(100% - 16px - 16px);
      :after {
        content: "  ";
        position: absolute;
        left: 0;
        top: 0;
        z-index: 1;
        width: 200%;
        height: 200%;
        border: 1px solid #D8D8D8;
        border-radius: 4px;
        -webkit-transform-origin: 0 0;
        transform-origin: 0 0;
        -webkit-transform: scale(.5, .5);
        transform: scale(.5, .5);
      }
      > div {
        > p {
          margin-bottom: 0;
          > span {
            display: inline-block;
            width: 33%;
            text-align: left;
            > span:nth-of-type(1) {
              font-family: "PingFangSC-Regular";
              font-size: 9px;
              color: #333333;
              display: block;
              width: 100%;
              letter-spacing: 0;
            }
            > span:nth-of-type(2) {
              font-family: "PingFangSC-Regular";
              font-size: 12px;
              color: #333333;
              letter-spacing: 0;
            }
          }
        }
        > p:nth-of-type(1) {
          margin-bottom: 6px;
        }
      }
    }
    .prompt {
      background: #F2F2F2;
      line-height: 30px;
      padding: 0 5px;
      margin-bottom: 12px;
      font-size: 12px;
      color: #333333;
      position: sticky;
      top: 0;
      width: 100%;
      z-index: 99;
    }
    .optionalGroup {
      margin: 15px 0;
      position: relative;

      :after {
        content: '';
        position: absolute;
        background-color: #D8D8D8 !important;
        display: block;
        z-index: 1;
        top: auto;
        right: auto;
        bottom: 0;
        left: 0;
        width: 100%;
        height: 1.2px;
        transform-origin: 50% 100%;
        transform: scaleY(0.5);
      }
    }
    .optionalGroup:nth-child(1) {
      margin-top: 50px;
    }
  }
`;

const GiftWrapper = styled.div`// styled
  & {
    height: calc(${document.documentElement.clientHeight}px - 12px - 90px - 62px - 10px);
    overflow-y: auto;
    padding-bottom: 62px;
  }
`;

const ConfirmButton = styled.div`// styled
  & {
    padding: 6px 12px;
    left: 0;
    width: 100%;
    position: fixed;
    bottom: 0px;
    background: #FFFFFF;
    border-top: 1px solid #d8d8d8;
    z-index: 99;
    .am-button {
      height: 42px;
      line-height: 42px;
      font-size: 16px;
    }
    .am-button-primary {
      width: 88px;
      height: 36px;
      line-height: 36px;
      font-size: 14px;
      font-family: "SourceHanSansCN-Normal";
      font-weight: 400;
      color: rgba(255, 255, 255, 1);
      border-radius: 20px;
      float: right;
      background: linear-gradient(135deg, rgba(93, 172, 255, 1) 0%, rgba(48, 125, 205, 1) 100%);
    }
  }
`;
