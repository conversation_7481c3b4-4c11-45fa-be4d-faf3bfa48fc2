import { autowired, bean } from "@classes/ioc/ioc";
import { action, observable } from "mobx";
import { beanMapper } from "../../helpers/bean-helpers";
import { $OrderSchemeList } from "../../classes/entity/orderSchemeList";
import { $OrderPartyService } from "../../classes/service/$order-party-service";

@bean($OrderSchemeSelectionMV)
export class $OrderSchemeSelectionMV {
  @autowired($OrderPartyService)
  public $orderPartyService: $OrderPartyService;
  @observable public activeKey: string = "";
  @observable public scrollHeight: number = 0;
  @observable public isSpin: boolean = false;
  @action
  public showSpin() {
    this.isSpin = true;
  }

  @action
  public hideSpin() {
    this.isSpin = false;
  }
  @action
  public fetchQueryOrderSchemeList(params) {
    return this.$orderPartyService.queryOrderSchemeList(params).then((data) => {
      const { orderSchemeList, orderPartyName, orderPartyId } = data;
      this.orderPartyName = orderPartyName;
      this.orderPartyId = orderPartyId;
      this.orderSchemeList = orderSchemeList.map((orderScheme) => new $OrderSchemeList(orderScheme));
      return data;
    }).then(() => this.hideSpin());
  }
  @action
  public queryOldData(obj) {
    beanMapper(obj, this);
    console.log(this);
  }
  @action
  public clearMVData() {
    this.scrollHeight = 0;
    this.activeKey = "";
  }
}
