import { autowired } from "@classes/ioc/ioc";
import { Spin } from "antd";
import { observer } from "mobx-react";
import React from "react";
import { withRouter } from "react-router";
import styled from "styled-components";
import { $ComponentService } from "../../classes/service/$component-service";
import DateUtils from "../../classes/utils/DateUtils";
import { NoGoods } from "../../components/no-goods/no-goods";
import { SITE_PATH } from "../app";
import { $OrderPartySelectionMV } from "../order-party-selection/$order-party-selection-mv";
import { $CartMv } from "../shop-cart/cart-mv";
import { $AppStore, AppStoreKey } from "../../classes/stores/app-store-mv";
import { AnnouncementModelMv } from "../../components/announcement-model/announcement-model-mv"
import { $EnterType } from "../../classes/const/$enter-Type"



declare let window: any;

@withRouter
@observer
class OrderSchemeSelection extends React.Component<any, any> {
  @autowired($AppStore)
  public $AppStore: $AppStore;

  @autowired($OrderPartySelectionMV)
  public $myMv: $OrderPartySelectionMV;

  @autowired($ComponentService)
  public $componentService: $ComponentService;

  @autowired($CartMv)
  public $CartMv: $CartMv;

  @autowired(AnnouncementModelMv)
  public AnnouncementModelMv: AnnouncementModelMv;


  public constructor(props) {
    super(props);
    this.state = {
      activeKey: "",
    };
  }
  public saveMV = () => {
    const scrollHeight = $(".ant-spin-nested-loading") && $(".ant-spin-nested-loading").position() && $(".ant-spin-nested-loading").position().top;
    const { activeKey } = this.state;
    this.$AppStore.savePageMv(AppStoreKey.ORDERSCHEMELIST, { activeKey, scrollHeight });
  }
  public componentDidMount() {
    document.title = "订货方案列表";
    this.AnnouncementModelMv.removeSessionStorage($EnterType.ENTER_ORDER_SCHEME)
    this.initPage();
  }
  public initPage = () => {
    this.$myMv.showSpin();
    this.loadOrderschemeList();
  }

  public loadOrderschemeList() {
    console.log(this.props, "---------");
    if (this.props.location.state) {
      const params = { orderPartyId: this.props.location.state.orderPartyId };
      this.$myMv.fetchQueryOrderSchemeList(params).then(() => {
        let oldData = this.$AppStore.getPageMv(AppStoreKey.ORDERSCHEMELIST); // 这里订货方案列表也是实时取到最新的保存了滚动高度和key
        if (oldData) {
          oldData = JSON.parse(oldData);
          const { scrollHeight, activeKey } = oldData;
          this.setState({ activeKey })
          document.getElementById("order-scheme-page").scrollTo(0, Math.abs(scrollHeight));
          this.$AppStore.clearPageMv(AppStoreKey.ORDERSCHEMELIST);
        }
        const quotaParams = { orderSchemeIdList: this.$myMv.orderSchemeList.map((item) => item.oid), orderPartyId: this.props.location.state.orderPartyId };
        this.$myMv.fetchRemainQuotaList(quotaParams).then(() => {
          this.$myMv.hideSpin();
        });
        // scm/ordermall/orderscheme/remainQuota/list { "orderSchemeIdList":[],"orderPartyId":12}
      });
    }
    /*if (this.props.location.search) {
      console.log(78787);
      const params = { orderPartyId: this.props.location.search.split("=")[1] };
      console.log(params);
      this.$myMv.fetchAgencyList(params);
      this.$myMv.fetchQueryOrderSchemeList(params);
    }*/
  }

  public goToHome(params) {
    sessionStorage.setItem("editOrderId", null);
    const saveParams = { orderPartyId: Number(this.$myMv.orderPartyId), orderSchemeId: params };
    this.$myMv.saveShopAndScheme(saveParams).then((data) => {
      if (data.result) {
        // const paramsCart = { source: "" };
        // this.$CartMv.fetchCarts(paramsCart);
        // this.props.history.push({ pathname: `/scm/home` });
        this.saveMV();
        setTimeout(() => {
          this.$AppStore.clearPageMv(AppStoreKey.SHOPLIST);
          // window.location.href = `/${SITE_PATH}/shop/list`;
          window.location.href = `/${SITE_PATH}/shop/list?orderSchemeId=${params}&orderPartyId=${this.props.location.state.orderPartyId} `;
        });
      }
    });
  }

  public showRule = (oid) => {
    document.getElementsByClassName("list")[0].style.position = "fixed";
    this.setState({ activeKey: oid });
  }

  public hideRule = () => {
    document.getElementsByClassName("list")[0].style.position = "relative";
    this.setState({ activeKey: "" });
  }

  public renderOrderSchemeList = (orderSchemeList) => {
    const { remainQuotaList } = this.$myMv;
    return (
      orderSchemeList.length > 0 ? orderSchemeList.map((orderScheme, index) => {
        const oid = orderScheme.oid;
        return (
          <SchemeGroup key={index}>
            <Scheme>
              <div>
                <p onClick={() => {
                  this.showRule(oid);
                }}>
                  <div>
                    <img src="https://order.fwh1988.cn:14501/static-img/scm/ico-bg-programme.png" alt=""/>
                    {/*<span>{orderScheme.name ? orderScheme.name.length > 10 ? orderScheme.name.slice(0, 10) + "..." : orderScheme.name : null}</span>*/}
                    <span>{orderScheme.name ? orderScheme.name.length > 10 ? orderScheme.name.slice(0, 20) : orderScheme.name : null}</span>
                  </div>
                  {
                    remainQuotaList && remainQuotaList[String(oid)] != null && <div>剩余可订额：¥{remainQuotaList[String(oid)] > 0 ? remainQuotaList[String(oid)] : 0}</div>
                  }
                  <div>
                    订货截止：{DateUtils.toStringFormat(orderScheme.deadlineTime, "yyyy-MM-dd HH:mm")}
                  </div>
                </p>
                <p>
                  <div onClick={() => {
                    this.goToHome(orderScheme.oid);
                  }}>订购商品
                  </div>
                </p>
              </div>
              <div onClick={() => {
                this.showRule(orderScheme.oid);
              }}>
                <img src="https://order.fwh1988.cn:14501/static-img/scm/icon-programme-pic.png" alt=""/>
              </div>
            </Scheme>
            <SchemeRule style={{ display: this.state.activeKey === orderScheme.oid ? "block" : "none" }}>
              <Rule>
                <p>
                  <span>{orderScheme.name ? orderScheme.name.length > 32 ? orderScheme.name.slice(0, 20) + "..." : orderScheme.name : null}</span>
                  <img src="https://order.fwh1988.cn:14501/static-img/scm/icon-close.png" alt="" onClick={() => {
                    this.hideRule();
                  }}/>
                </p>
                <p>
                  <div>订货方案规则：</div>
                  <div>
                    <span className="name">1.订货时间：</span>
                    <span
                      className="value">{DateUtils.toStringFormat(orderScheme.startTime, "yyyy-MM-dd HH:mm")}至{DateUtils.toStringFormat(orderScheme.endTime, "yyyy-MM-dd HH:mm")}</span>
                  </div>
                  <div>
                    <span className="name">2.方案类型：</span>
                    <span className="value">{orderScheme.orderSchemeType}</span>
                  </div>
                  <div>
                    <span
                      className="name">3.{orderScheme.isMutiOrder ? `允许${orderScheme.setOrderNumer}次下单` : "允许多次下单，不限次数"}</span>
                  </div>
                  <div>
                    <span className="name">4.预计发货时间：</span>
                    <span
                      className="value">{DateUtils.toStringFormat(orderScheme.expectedDeliveryStartTime, "yyyy-MM-dd HH:mm")}至{DateUtils.toStringFormat(orderScheme.expectedDeliveryEndTime, "yyyy-MM-dd HH:mm")}</span>
                  </div>
                  <div>
                    <span className="name">5.付款方式：</span>
                    <span className="value">{orderScheme.paymentMode}</span>
                  </div>
                  <div>
                    <span className="name">6.备注：</span>
                    <span className="value">{orderScheme.memo}</span>
                  </div>
                </p>
              </Rule>
            </SchemeRule>
          </SchemeGroup>
        );
      }) : <NoGoods title="暂无订货方案"/>
    );
  }

  public render() {
    const { orderSchemeList, isSpin, orderPartyName } = this.$myMv;
    return (
      <Page id={"order-scheme-page"}>
        <Spin spinning={isSpin}>
          {
            orderPartyName ?
              <Header>
                <i className="scmIconfont scm-icon-shop"/>
                <span>{orderPartyName}</span>
              </Header> : null
          }
          <SchemeList className="list">
            {
              orderSchemeList ? this.renderOrderSchemeList(orderSchemeList) : null
            }
          </SchemeList>
        </Spin>
      </Page>
    );
  }
}

export default OrderSchemeSelection;

const Page = styled.div`// styled
  & {
    width: 100%;
    height: calc(${document.documentElement.clientHeight}px);
    background-color: #f5f5f5;
    overflow-y: scroll;
    -webkit-overflow-scrolling: touch;
    overflow-scrolling: touch;
  }
`;

const SchemeList = styled.div`// styled
  & {
    width: 100%;
    height: auto;
    background-color: #f5f5f5;
    box-sizing: border-box;
    padding: 15px;
    position: relative;
  }
`;

const Header = styled.div`// styled
  & {
    width: 100%;
    height: 30px;
    line-height: 30px;
    background-color: #fff;
    box-sizing: border-box;
    color: #307DCD;
    font-size: 12px;
    border-bottom: 1px solid #D8D8D8;
    padding: 0 10px;
    > span {
      position: relative;
      top: -2px;
      margin-left: 5px;
    }
  }
`;

const Scheme = styled.div`// styled
  & {
    width: 100%;
    height: 120px;
    background: #fff url("https://order.fwh1988.cn:14501/static-img/scm/icon-bg-wave.png") no-repeat center bottom;
    box-sizing: border-box;
    border-radius: 8px;
    margin-bottom: 15px;
    padding: 8px 15px 10px;
    position: relative;
    > div {
      display: inline-block;
    }
    > div:nth-of-type(1) {
      > p:nth-of-type(1) {
        > div:nth-of-type(1) {
          position: relative;
          padding-left: 26px;
          >img{
            width:20px;
            height:20px;
            position: absolute;
            top: 0;
            left: 0;
          }
          >span{
            display: inline-block;
            font-family: PingFangSC-Regular;
            width: 168px;
            font-size: 14px;
            max-height: 40px;
            line-height: 20px;
            color: #666666;
          }
        }
        > div:nth-of-type(2) {
          font-family: PingFangSC-Regular;
          font-size: 10px;
          color: #999999;
          margin-left: 26px;
          line-height: 14px;
          margin-top: 1px;
        }

        > div:nth-of-type(3) {
          font-family: PingFangSC-Regular;
          font-size: 10px;
          color: #999999;
          margin-left: 26px;
          line-height: 14px;
          margin-top: 1px;
        }
        margin-bottom: 10px;
      }
      > p:nth-of-type(2) {
        width: calc(${document.documentElement.clientWidth - 170}px);
        border-top: 1px solid #D8D8D8;
        > div {
          width: 88px;
          height: 28px;
          line-height: 28px;
          text-align: center;
          border: 1px solid #307DCD;
          border-radius: 4px;
          font-family: PingFangSC-Regular;
          font-size: 12px;
          color: #307DCD;
          margin: 11px 0 0 26px;
        }
      }
    }

    > div:nth-of-type(2) {
      position: absolute;
      top:15px;
      right:15px;
      > img {
        width: 88px;
        height: 88px;
      }
    }
  }
`;

const SchemeGroup = styled.div`// styled
  & {
    width: 100%;
    height: auto;
  }
`;

const SchemeRule = styled.div`// styled
  & {
    width: 100%;
    height: calc(${document.documentElement.clientHeight}px);
    position: fixed;
    top: 0;
    left: 0;
    background: rgba(0, 0, 0, 0.40);
    z-index: 99;
  }
`;

const Rule = styled.div`// styled
  & {
    width: 84%;
    height: auto;
    margin: 110px 30px;
    background: #fff url("https://order.fwh1988.cn:14501/static-img/scm/icon-bg-wave.png") no-repeat center bottom;
    border-radius: 8px;
    padding: 10px 15px;
    > p:nth-of-type(1) {
      position: relative;
      padding-bottom: 8px;
      border-bottom: 1px solid #d8d8d8;
      > span {
        font-family: PingFangSC-Regular;
        font-size: 14px;
        color: #307DCD;
        display: inline-block;
        width: calc(100% - 16px);
      }
      > img {
        width: 20px;
        height: 20px;
        position: absolute;
        top: 0px;
        right: 0px;
      }
    }
    > p:nth-of-type(2) {
      height: 257px;
      overflow-y: auto;
      > div:nth-of-type(1) {
        font-family: PingFangSC-Regular;
        font-size: 12px;
        color: #333333;
      }
      > div {
        margin-bottom: 10px;
        .name {
          font-family: PingFangSC-Regular;
          font-size: 12px;
          color: #666666;
        }
        .value {
          font-family: PingFangSC-Regular;
          font-size: 12px;
          color: #151515;
        }
      }
    }
    > p:nth-of-type(2)::-webkit-scrollbar {
      display: none;
    }
  }
`;
