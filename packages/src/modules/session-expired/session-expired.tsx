import React from "react";
import { Modal } from "antd-mobile";
import { withRouter } from "react-router";
import { observer } from "mobx-react";

declare const require: any;
const wxConfig = require("../../helpers/wxconfig.json");

declare let WeixinJSBridge: any;
declare let window: any;

const domainCustomer = document.domain;
console.log("domainCustomer", domainCustomer);
let wxUrl;
if (domainCustomer.indexOf("uatorder.fwh") > -1) {
  wxUrl = wxConfig.fwhuat.wxUrl;
} else if (domainCustomer.indexOf("order.fwh") > -1) {
  wxUrl = wxConfig.fwh.wxUrl;
} else if (domainCustomer.indexOf("ybl.haoduoke") > -1) {
  wxUrl = wxConfig.ybl.wxUrl;
} else if (domainCustomer.indexOf("zcj") > -1) {
  wxUrl = wxConfig.zcj.wxUrl;
} else if (domainCustomer.indexOf("static-show") > -1) {
  wxUrl = wxConfig.sales.wxUrl;
}

@withRouter
@observer
class SessionExpired extends React.Component<any, any> {
  public componentDidMount(): void {
    document.title = "授权";
  }

  public render() {
    return (
      <Modal
        visible={true}
        transparent
        maskClosable={false}
        title="您的登录信息授权已过期，请重新授权。授权后，请重新进行您的操作。"
        footer={[{
          text: '立即授权', onPress: () => {
            // WeixinJSBridge.call('closeWindow');
            window.location.href = wxUrl;
          }
        }]}
      />
    );
  }
}

export default SessionExpired;
