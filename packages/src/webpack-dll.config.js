const webpack = require('webpack');
const path = require('path');
const pkg = require("./app")
const {BundleAnalyzerPlugin} = require('webpack-bundle-analyzer');

const vendors = [
  'react',
  'react-dom',
  'react-router',
  'antd-mobile',
  'mobx',
  'mobx-react',
];

module.exports = {
  output: {
    path: path.resolve(__dirname, `../../__out__/${pkg.name}`),
    filename: '[name].js',
    library: '[name]',
  },
  entry: {
    "lib": vendors,
  },
  plugins: [
    new webpack.DllPlugin({
      path: path.resolve(__dirname, './manifest.json'),
      name: '[name]',
      context: path.resolve(__dirname),
    }),
    // new BundleAnalyzerPlugin()
  ],
};
