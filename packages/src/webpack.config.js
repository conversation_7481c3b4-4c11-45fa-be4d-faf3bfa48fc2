const {dev} = require('./webpack-base.config')
const path = require('path');
const theme = require('./theme.json')

module.exports = dev({
  dirname: __dirname,
  proxyPrefix: '/',
  port: 4444,
  config: {
    entry: [
      path.join(__dirname, './modules/index.tsx'),
    ],
    module: {
      rules: [
        {
          test: /\.tsx?$/,
          use: [
            {
              loader: 'babel-loader',
              options: {
                "plugins": [
                  '@babel/plugin-proposal-object-rest-spread',
                  'syntax-dynamic-import',
                  ["import", {
                    "libraryName": "antd",
                    "style": true,   // or 'css'
                  }, "ant"
                  ],
                  ["import",
                    {
                      "libraryName": "antd-mobile",
                      "style": true,   // or 'css'
                    },
                    "antd-mobile"
                  ]
                ]
              }
            },
            {
              loader: 'ts-loader',
              options: {transpileOnly: true},
            }],
          include: [
            path.resolve(__dirname, '../src')
          ]
        },
        {
          test: /\.svg$/,
          use: [
            {
              loader: "svg-react-loader"
            }
          ]
        },
        {
          test: /\.less$/,
          loader: `style!css!less`,
          include: path.resolve(__dirname, 'node_modules')
        }
      ]
    },
    resolve: {
      extensions: [".webpack.js", ".web.js", ".js", ".ts", ".tsx"],
      alias: {
        "@classes": path.resolve(__dirname, './classes'),
      }
    },
  },
  theme: theme
});
