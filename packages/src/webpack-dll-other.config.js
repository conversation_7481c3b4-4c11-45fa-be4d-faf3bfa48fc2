const webpack = require('webpack');
const path = require('path');
const pkg = require("./app")

const vendors = [
  'lodash',
  'styled-components',
  'inversify',
  'inversify-binding-decorators',
  'inversify-inject-decorators',
  'gregorian-calendar',
  'gregorian-calendar-format'
];

module.exports = {
  output: {
    path: path.resolve(__dirname, `../../__out__/${pkg.name}`),
    filename: '[name].js',
    library: '[name]',
  },
  entry: {
    "otherLib": vendors,
  },
  plugins: [
    new webpack.DllPlugin({
      path: path.resolve(__dirname, './manifest.other.json'),
      name: '[name]',
      context: path.resolve(__dirname),
    }),
  ],
};
