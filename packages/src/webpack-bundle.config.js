const {bundle} = require("./webpack-base.config");
const path = require('path');
const theme = require("./theme.json");
const pkg = require("./app");
const fs = require("fs");

const appDirectory = fs.realpathSync(process.cwd());
const resolveApp = relativePath => path.resolve(appDirectory, relativePath);

const version = require(resolveApp('package.json')).version;

console.log(version)

module.exports = bundle({
  dirname: __dirname,
  config: {
    entry: [
      path.join(__dirname, './modules/index.tsx'),
    ],
    output: {
      path: path.resolve(__dirname, `../../__out__/${pkg.name}`),
    },
    module: {
      rules: [
        {
          test: /\.tsx?$/,
          use: [
            {
              loader: 'babel-loader',
              options: {
                "plugins": [
                  '@babel/plugin-proposal-object-rest-spread',
                  'syntax-dynamic-import',
                  ["import", {
                    "libraryName": "antd",
                    "style": true,   // or 'css'
                  }, "antd"
                  ],
                  ["import",
                    {
                      "libraryName": "antd-mobile",
                      "style": true,   // or 'css'
                    },
                    "antd-mobile"
                  ]
                ]
              }
            },
            {
              loader: 'ts-loader',
              options: {
                transpileOnly: true,
                compilerOptions: {
                  target: 'es5',
                }
              },
            }],
          include: [
            path.resolve(__dirname, '../src')
          ]
        },
        {
          test: /\.svg$/,
          use: [
            {
              loader: "svg-react-loader"
            }
          ]
        },
        {
          test: /\.less$/,
          loader: `style!css!less`,
          include: path.resolve(__dirname, 'node_modules'),
        }
      ]
    },
    resolve: {
      extensions: [".webpack.js", ".web.js", ".js", ".ts", ".tsx"],
      alias: {
        "@classes": path.resolve(__dirname, './classes'),
      }
    },
  },
  theme: theme,
  beta: true,
  version
});
