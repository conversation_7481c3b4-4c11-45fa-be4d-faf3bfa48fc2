import { bean } from "@classes/ioc/ioc";
import { action } from "mobx";
import { wxpost } from "../../helpers/ajax-helpers";

@bean($ScanningLoginService)
export class $ScanningLoginService {
  @action
  public getScanningPath() {
    return wxpost(`/integration/scm/scanninglogin/getscanningpath`);
  }

  @action
  public getOpenid(params) {
    return wxpost(`/integration/scm/scanninglogin/getopenid`, params);
  }

  @action
  public userFindOne(params) {
    return wxpost(`/integration/basic/user/findone`, params);
  }

  @action
  public densityFreeLogin(params) {
    return wxpost(`/integration/scm/scanninglogin/login`, params);
  }
}
