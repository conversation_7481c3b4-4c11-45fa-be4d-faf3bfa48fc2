import { bean } from "@classes/ioc/ioc";
import { action } from "mobx";
import { post, post2 } from "../../helpers/ajax-helpers";

@bean($ComponentService)
export class $ComponentService {
  @action
  public queryProductCategory() {
    return post(`/integration/scm/productcategory/list`);
  }

  @action
  public searchProduct(params) {
    return post(`/integration/scm/product/query`, params);
  }

  @action
  public changeShopCart(params) {
    return post(`/integration/scm/ordermall/shopcart/edit`, params); // 添加到购物车
  }

  @action
  public createOrder(params) {
    return post(`/integration/scm/salesorder/submit`, params); // 创建销售单
  }

  @action
  public queryPayType() {
    return post(`/integration/scm/payType/list`); // 获取付款方式
  }

  @action
  public queryPaymentAccount() {
    return post(`/integration/scm/financialaccount/list`); // 获取汇款账号列表
  }

  @action
  public createReceiptPayment(params) {
    return post(`/integration/scm/account/addPayment`, params); // 提交确认汇款
  }

  @action
  public queryCurUser() {
    return post(`/integration/scm/ordermall/userinfo/load`); // 获取用户信息
  }
  @action
  public queryShopOverdue(params) {
    return post(`/integration/scm/overdue/query`, params); // 获取门店是否有逾期账户
  }

  @action
  public queryAds() {
    return post(`/integration/scm/adrotator/list`);
  }

  @action
  public queryCustomerEmployee() {  // 获取客户联系人列表
    return post(`/integration/scm/customeremployee/list`);
  }

  @action
  public queryPayTypeList() {  // 获取付款方式列表
    return post(`/integration/scm/payType/list`);
  }

  @action
  public queryPayPageList(params) {  // 获取支付页面数据
    return post(`/integration/scm/ordermall/orderinfo/load`, params);
  }

  @action
  public queryDearlerPayList(params) {  //  获取支付页面经销商数据
    return post(`/integration/scm/ordermall/paypage/load`, params);
  }

  @action
  public queryRole() {  //  查询角色
    return post(`/integration/scm/ordermall/shopcart/paypagemodes/load`);
  }

  @action
  public saveOrder(params) {
    return post(`/integration/scm/ordermall/order/save`, params);
  }

  @action
  public saveOrderPay(params) {
    return post(`/integration/scm/ordermall/paymentItem/save`, params);
  }

  @action
  public queryGoodsList(params) {
    return post(`/integration/scm/ordermall/docorder/product/list`, params);
  }

  @action
  public queryReturnedGoodsList(params) {
    return post(`/integration/scm/ordermall/refundorder/actualitem/list`, params);
  }
  //获取装箱明细列表
  @action
  public queryBinningGoodsList(params) {
    return post(`/integration/scm/ordermall/deliveryorder/boxinginfolist`, params);
  }

  @action
  public queryDeliveryOrderList(params) {
    return post(`/integration/scm/ordermall/salesorder/deliveryorder/load`, params);
  }

  @action
  public confirmGoods(deliveryOrderId) {
    return post(`/integration/scm/ordermall/salesorder/deliveryorder/confirm`, { deliveryOrderId });
  }

  @action
  public queryReturnedSalesList(params) {
    return post(`/integration/scm/ordermall/refundorder/list`, params);
  }

  @action
  public queryReturnedDetail(params) {
    // return post(`/integration/scm/ordermall/my/refundorder/load`, params); // 以前的接口
    return post(`/integration/scm/ordermall/refundorder/detail`, params);
  }
  @action
  public businesssCtroCheck(params) {
    return post(`/integration/scm/businessstatuscontrol/check`, params);
  }

  @action
  public queryAccountInfo(params) {
    return post(`/integration/scm/ordermall/account/load`, params);
  }
  @action
  public queryGiftAccountInfo(params) {
    return post(`/integration/scm/ordermall/pzAccount/load`, params);
  }
  @action
  public fetchDebtAccountInfo(params) {
    return post(`/integration/scm/me/qkzh`, params);
  }

  @action
  public saveAccountId(params) {
    return post(`/integration/scm/ordermall/wechatmessage/orderpartyId/save`, params);
  }

  @action
  public queryAccountflowinfo(params) {
    return post(`/integration/scm/me/qkzh/detail`, params);
  }

  @action
  public wxPay(params, ssoSessionId) {
    return post2(`http://114.55.0.216/k/integration/wx/pay/unifiedOrder`, params, ssoSessionId);
  }

  @action
  public wxToken(params) {  // 获取微信token
    return post2(`http://114.55.0.216/k/integration/login`, params);
  }

  @action
  public queryCouponList(params) {  // 查询优惠券列表
    return post(`/integration/scm/promotion/coupon/list`, params);
  }

  @action
  public queryHome() {  // 查询首页门店信息和轮播图
    return post(`/integration/scm/ordermall/index/top/load`);
  }

  @action
  public querySuitList(params) {  // 查询套装列表
    return post(`/integration/scm/ordermall/index/activitysuit/list`, params);
  }

  @action
  public querySuitDetail(params) {  // 查询套装详情
    return post(`/integration/scm/ordermall/index/activitysuit/load`, params);
  }

  @action
  public queryTodayList(params) {  // 查询今日特惠列表
    return post(`/integration/scm/ordermall/index/preferential/list`, params);
  }

  @action
  public saveSuit(params) {
    return post(`/integration/scm/ordermall/index/activitysuit/save`, params);
  }

  @action
  public getOpenId() {
    return post(`/integration/scm/pay/openid`);
  }

  @action
  public calculateFreight(params) {
    return post(`/integration/scm/ordermall/shippingment/freight/calculatefreight`, params);
  }

  @action
  public queryCreditaccountListLoad(params) {
    return post(`/integration/scm/ordermall/account/creditaccountList/load`, params);
  }

  @action
  public queryCreditDetail(params) {
    return post(`/integration/scm/ordermall/account/creditaccount/load`, params);
  }

  @action
  public accountAutomaticUpdate(params) {
    return post(`/integration/scm/ordermall/account/automatic/update`, params);
  }

  @action
  public queryCreditrepaymentAuditedAmount(params) {
    return post(`/integration/scm/ordermall/creditrepayment/auditedAmount`, params);
  }

  @action
  public queryPaymentInfoList(params) {
    return post(`/integration/scm/ordermall/payment/condition/load`, params);
  }
  @action
  public getLogisticsTypeList(params) {
    return post(`/integration/scm/logisticsType/query`, params);
  }
  @action
  public getLogisticsInfo(params) {
    return post(`/integration/scm/refundOrder/logistics/query`, params);
  }
  @action
  public fetchLogisticsCompanyList(params) {
    return post(`/integration/scm/logisticsCompany/list`, params);
  }
  @action
  public changeLogisticsInfo(params) {
    return post(`/integration/scm/refundOrder/logistics/save`, params);
  }
  @action
  public checkTongLianPaymentStatus(params) {
    return post(`/integration/scm/ordermall/salesorder/orderpayment/list`, params);
  }
  @action
  public checkContinuePay(params) {
    return post(`/integration/scm/ordermall/salesorder/payment/url`, params);
  }
  @action
  public cancelTongLianPay(params) {
    return post(`/integration/scm/ordermall/orderpayment/cancel`, params);
  }

  @action
  public queryNewAccountDetail(params) {
    return post(`/integration/scm/ordermall/accountFlow/relatedDocDetail`, params);
  }

  @action
  public queryFreezeList(params) {
    return post(`/integration/scm/ordermall/account/loadFrozenItem`, params);
  }

  @action
  public queryGiftsLimitList(params) {
    return post(`/integration/scm/ordermall/account/loadOrderQuotaLimitFlow`, params);
  }

  @action
  public shopSchemeTipsLoad() {
    return post(`/integration/scm/ordermall/shopScheme/tipsLoad`); // 查询订货方案总额度（配赠或返利）
  }
}
