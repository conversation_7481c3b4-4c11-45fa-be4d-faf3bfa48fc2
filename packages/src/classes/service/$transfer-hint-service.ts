import { bean } from "@classes/ioc/ioc";
import { action } from "mobx";
import { post } from "../../helpers/ajax-helpers";

@bean($TransferHintService)
export class $TransferHintService {
  @action // 余额付款转账加载接口
  public transferBatchLoad = (params) => {
    return post("/integration/scm/ordermall/paymentTransfer/batchLoad", params);
  }

  @action // 转账记录-查询
  public transferRecordList = (params) => {
    return post("/integration/scm/ordermall/salesorder/payment/transferRecordList", params);
  }

  @action
  public transferSubmit = (params) => {
    return post("/integration/scm/ordermall/salesorder/payment/transfer", params);
  }

  @action
  public manualTransferSubmit = (params) => {
    return post("/integration/scm/ordermall/shopcart/auto/transfer", params);
  }

  @action
  public pzTransferSubmit(params) {
    return post(`/integration/scm/ordermall/shopCart/pz/transfer`, params);
  }
}
