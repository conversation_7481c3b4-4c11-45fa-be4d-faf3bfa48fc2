import { bean } from "@classes/ioc/ioc";
import { action } from "mobx";
import { post } from "../../helpers/ajax-helpers";

@bean($ConfirmOrderService)
export class $ConfirmOrderService {
  @action
  public queryConfirmOrder(params) {
    return post(`/integration/scm/ordermall/offilinepay/load`, params);
  }

  @action
  public saveReconfirmOrder(params) {
    return post(`/integration/scm/ordermall/salesorder/orderpayment/update`, params);
  }

  @action
  public queryCapitalAccount(params) {
    return post(`/integration/scm/ordermall/capitalAccount/list`, params);
  }
}
