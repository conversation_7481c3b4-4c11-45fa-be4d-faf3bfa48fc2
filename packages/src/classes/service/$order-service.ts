import { bean } from "@classes/ioc/ioc";
import { action } from "mobx";
import { post } from "../../helpers/ajax-helpers";

@bean($OrderService)
export class $OrderService {
  @action
  public querySchemeList(params) {
    return post(`/integration/scm/orderscheme/list/selectByLoginUserToken`, params);
  }
  @action
  public queryOrderList(params) {
    const { pageIndex, ...otherParams } = params;
    return post(`/integration/scm/ordermall/salesorder/load`,
      { pageIndex, ...otherParams });
  }

  @action
  public queryOrderDetail(params) {
    return post(`/integration/scm/ordermall/salesorder/detail`, params);
  }

  @action
  public queryLogisticsTracking(params) {
    return post(`/integration/tp/logisticstrack/search`, params);
  }
  @action
  public copyOrder(params) {
    return post(`/integration/scm/ordermall/salesorder/copy`, params);
  }

  @action
  public orderpartyList(params) {
    return post(`/integration/scm/ordermall/new/orderparty/list`, params);
  }

  @action
  public reoveOrder(orderId) {
    return post(`/integration/scm/ordermall/salesorder/cancel`, { orderId });
  }

  @action
  public compileOrder(orderId) {
    return post(`/integration/scm/ordermall/salesorder/edit`, { orderId });
  }

  @action
  public examineOrder(orderId) {
    return post(`/integration/scm/ordermall/salesorder/audit`, { orderId });
  }

  @action
  public queryPaymentInfo(params) {
    return post(`/integration/scm/ordermall/salesorder/payment/load`, params);
  }

  @action
  public queryAllPaymentInfo(params) {
    return post(`/integration/scm/ordermall/salesorder/payment/batchLoad`, params);
  }

  @action
  public queryBatchLoadShops(params) {
    return post(`/integration/scm/ordermall/salesorder/batchLoadShops`, params);
  }

  @action
  public batchSave(params) {
    return post(`/integration/scm/admin/orderpayment/batchSave`, params);
  }
  @action
  public batchFeeDocumentPaymentSave(params) {
    return post(`/integration/scm/admin/feedocumentpayment/batchsave`, params);
  }

  @action
  public queryCapitalAccount(params) {
    return post(`/integration/scm/ordermall/capitalAccount/list`, params);
  }

  // @action
  // public saveOrderInfo(params) {
  //   return post(`/integration/scm/ordermall/paymentItem/save`, params);
  // }

  @action
  public queryQuestionHelp(params) {
    return post(`/integration/scm/ordermall/account/question/help`, params);
  }

  @action
  public batchFilterSalesOrder(params) {
    return post(`/integration/scm/ordermall/salesorder/batchFilter`, params);
  }

  @action
  public checkBatchPaymentMode(params) {
    return post(`/integration/scm/ordermall/salesorder/payment/checkbatchpaymentmode`, params);
  }

  @action
  public salesOrderStatus(params) {
    return post(`/integration/scm/salesorder/status`, params);
  }
  // 费用单相关接口
  @action
  public queryExpenseList(params) {
    const { pageIndex, ...otherParams } = params;
    return post(`/integration/scm/ordermall/feedocument/load`,
      { pageIndex, ...otherParams });
  }
  @action
  public queryExpenseOrderDetail(params) {
    return post(`/integration/scm/ordermall/feedocument/detail`, params);
  }

  @action
  public salesorderPayinfo(params) {
    return post(`/integration/scm/ordermall/salesorder/payinfo`, params);
  }

  @action
  public feedocumentDetail(params) {
    return post(`/integration/scm/ordermall/feedocument/detail`, params);
  }
  @action
  public batchFilterFeedocumentList(params){
    return post(`/integration/scm/ordermall/scmfeedocument/batchFilter`,params)
  }
}
