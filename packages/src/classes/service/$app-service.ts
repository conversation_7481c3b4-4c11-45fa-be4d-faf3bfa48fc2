import { action } from "mobx";
import { post } from "../../helpers/ajax-helpers";
import { bean } from "@classes/ioc/ioc";
import packageJson from "../../../../package.json";
@bean($AppService)
export class $AppService {
  @action
  public logRecord(params) {
    const { logInfo } = params;
    const serverVersion = localStorage.getItem("server_version");
    logInfo.js_version = packageJson.version;
    logInfo.server_version = serverVersion ? serverVersion : "没有获取到服务端版本号";
    logInfo.mobileInfo = window.ENV.mobileInfo;
    // logInfo
    return post(`/integration/scm/frontlog/print`, {logInfo: JSON.stringify(logInfo)});
  }
}
