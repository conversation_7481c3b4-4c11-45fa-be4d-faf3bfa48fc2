import { bean } from "@classes/ioc/ioc";
import { action } from "mobx";
import { post } from "../../helpers/ajax-helpers";

@bean($OrderReportService)
export class $OrderReportService {
  @action
  public orderreportStatistics() {
    return post(`/integration/scm/ordermall/orderreport/statistics`, {});
  }

  @action
  public salesorderItemstatuslist() {
    return post(`/integration/scm/ordermall/salesorder/itemstatuslist`, {});
  }

  @action
  public orderpartyList(params) {
    return post(`/integration/scm/ordermall/new/orderparty/list`, params);
  }

  @action
  public orderreportSearch(params) {
    return post(`/integration/scm/ordermall/orderreport/search`, params);
  }

  @action
  public ordermallOweGoodsQuery(params) {
    return post(`/integration/scm/ordermall/oweGoods/query`, params);
  }
}
