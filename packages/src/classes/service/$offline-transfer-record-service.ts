import { bean } from "@classes/ioc/ioc";
import { action } from "mobx";
import { post } from "../../helpers/ajax-helpers";

@bean($OfflineTransferRecordService)
export class $OfflineTransferRecordService {
  @action
  public getOfflineTransferRecordList(params) {
    return post(`/integration/scm/ordermall/transfer/record/list`, params);
  }
  @action
  public getOfflineTransferRecordDetail(params) {
    return post(`/integration/scm/ordermall/transfer/record/detail`, params);
  }
  @action
  public withdraw(params) {
    return post(`/integration/scm/ordermall/transfer/record/withdraw`, params);
  }

  @action
  public queryOrderDetail(params) {
    return post(`/integration/scm/ordermall/salesorder/detail`, params);
  }
}
