import { bean } from "@classes/ioc/ioc";
import { action } from "mobx";
import { post } from "../../helpers/ajax-helpers";

@bean($ProductService)
export class $ProductService {
  @action
  public queryProductsClasses() {
    return post(`/integration/scm/ordermall/productclassification/load`);
  }

  @action
  public queryProducts(params) {
    return post(`/integration/scm/ordermall/product/load`, params);
  }

  @action
  public queryProductsSalesRanking(params) {
    const { pageIndex, ...otherParams } = params;
    return post(`/integration/scm/salesrankings/list`, {
      pageIndex: pageIndex > 0 ? pageIndex - 1 : pageIndex,
      ...otherParams,
    });
  }

  @action
  public queryCarts() {// 购物车页面专用接口
    return post(`/integration/scm/ordermall/shopcart/load`);
  }

  @action
  public queryShopScheme() { // 购物车查询是否使用优惠价和优惠额度和转入优惠额度
    return post(`/integration/scm/ordermall/shopScheme/load`);
  }

  @action
  public queryShopcartproductnum() {// 除购物车以外页面专用接口
    return post(`/integration/scm/ordermall/shopcartproductnum/load`);
  }

  @action
  public queryProductDetail(params) {   // 获取产品详情
    return post(`/integration/scm/productsku/query`, params);
  }

  @action
  public deleteShopCartById(params) {
    return post(`/integration/scm/ordermall/shopcart/delete`, params);
  }

  @action
  public chooseProductPage(params) {
    return post(`/integration/scm/ordermall/shopcart/productpage/nextpage`, params);
  }

  // 商品详情
  @action
  public fetchCommodityDetails(params) {
    return post(`/integration/scm/ordermall/product/detail/load`, params);
  }

  @action
  public fetchVirtualsuitDetail(params) {
    return post(`/integration/scm/ordermall/index/virtualsuit/load`, params);
  }

  @action
  public fetchMultimediaDesc(params) {
    return post(`/integration/scm/ordermall/product/multimediadesc/load`, params);
  }

  @action
  public fetchOrderSuggestion(params) {
    return post(`/integration/scm/ordermall/product/orderSuggestion`, params);
  }
  @action
  public fetchOtherStoreInventoryList(params) {
    return post(`/integration/scm/ordermall/product/otherStoreInventoryList`, params);
  }

  @action
  public batchCheckOrUnCheck(params) {
    return post(`/integration/scm/ordermall/shopcart/batchCheckOrUnCheck`, params);
  }

  @action
  public shopcartBatchDelete(params) {
    return post(`/integration/scm/ordermall/shopcart/batchDelete`, params);
  }

  @action
  public fetchTransferInfo(params) {
    return post(`/integration/scm/ordermall/shopCartTransfer/batchLoad`, params);
  }

  @action
  public fetchShopcartItemSplit(params) {
    return post(`/integration/scm/ordermall/shopcart/ItemSpilt`, params);
  }

  @action
  public fetchNewTransferInfo(params) {
    return post(`/integration/scm/ordermall/flTransfer/batchLoad`, params);
  }

  @action
  public fetchPzTransferInfo(params) {
    return post(`/integration/scm/ordermall/shopCart/pzTransferLoad`, params);
  }
}
