import { bean } from "@classes/ioc/ioc";
import { action } from "mobx";
import { post } from "../../helpers/ajax-helpers";

@bean($PromotionMatchService)
export class $PromotionMatchService {
  @action
  public queryPromotionMatch(params) {
    return post(`/integration/scm/promotion/engine/match`, params);
  }

  @action
  public countPromotion(params) {
    return post(`/integration/scm/promotion/engine/compute`, params);
  }

  @action
  public queryFree(params) {
    return post(`/integration/scm/promotion/gifts/load`, params);
  }

  @action
  public forceGive(params) {
    return post(`/integration/scm/promotion/choose/check`, params);
  }
}
