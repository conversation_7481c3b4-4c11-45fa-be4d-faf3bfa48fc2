import { bean } from "@classes/ioc/ioc";
import { action } from "mobx";
import { post } from "../../helpers/ajax-helpers";

@bean($ExchangeGoodsService)
export class $ExchangeGoodsService {
  @action
  public queryProductLoad(params) {
    return post(`/integration/scm/ordermall/exchangegoodsrequest/product/load`, params);
  }

  @action
  public queryExchangegoodsrequestDetail(params) {
    return post(`/integration/scm/ordermall/exchangegoodsrequest/load`, params);
  }

  @action
  public exchangegoodsrequestUpdate(params) {
    return post(`/integration/scm/ordermall/exchangegoodsrequest/update`, params);
  }

  @action
  public queryExchangegoodsrequestList(params) {
    return post(`/integration/scm/ordermall/exchangegoodsrequest/list`, params);
  }

  @action
  public queryExchangeGoodsRequestStatusList() {
    return post(`/integration/scm/ordermall/exchangegoodsrequest/status/list`);
  }

  @action
  public saveExchangeGoods(params) {
    return post(`/integration/scm/ordermall/exchangegoodsrequest/edit`, params);
  }
}
