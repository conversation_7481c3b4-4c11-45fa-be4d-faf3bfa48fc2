import { bean } from "@classes/ioc/ioc";
import { action } from "mobx";
import { post } from "../../helpers/ajax-helpers";

@bean($OrderPartyService)
export class $OrderPartyService {
  @action
  public queryOrderParties() {
    return post(`/integration/scm/orderparty/list`);
  }

  @action
  public queryCurrentOrderParty() {
    return post(`/integration/scm/orderparty/load`);
  }

  @action
  public saveOrderClients(params) {
    return post(`/integration/scm/orderparty/addKey`, params);
  }

  @action
  public queryAddress(params) {
    return post(`/integration/scm/orderparty/address/list`, params);
  }

  @action
  public queryBatchLoadShops(params) {
    return post(`/integration/scm/ordermall/salesorder/batchLoadShops`, params);
  }

  @action
  public createPayment(params) {
    return post(`/integration/scm/account/addPayment`, params);
  }

  // new select interface
  @action
  public queryAgencyList() {
    return post("/integration/scm/ordermall/new/orderparty/untreated/load", {});
  }
  @action
  public queryAgencyListIsNew(params) {
    return post("/integration/scm/ordermall/new/markquery", params);
  }

  @action
  public queryOrderPartyList(params) {
    return post("/integration/scm/ordermall/new/orderparty/list", params);
  }

  @action
  public queryOrderSchemeList(params) {
    return post("/integration/scm/ordermall/new/orderscheme/list", params);
  }

  @action
  public saveShopAndScheme(params) {
    return post("/integration/scm/ordermall/new/shopandscheme/save", params);
  }
  @action
  public recordinsert(params) {
    return post("/integration/scm/ordermall/new/recordinsert", params);
  }

  @action
  public queryRemainQuotaList(params) {
    return post("/integration/scm/ordermall/orderscheme/remainQuota/list", params);
  }

}
