import { bean } from "@classes/ioc/ioc";
import { action } from "mobx";
import { post } from "../../helpers/ajax-helpers";

@bean($ShippingAddressService)
export class $ShippingAddressService {
  @action
  public queryShippingAddress() {  // 获取地址列表
    return post(`/integration/scm/ordermall/address/list`);
  }

  @action
  public queryAddressList(params) {  // 获取地址区域选择列表
    return post(`/integration/scm/ordermall/region/tree`, params);
  }

  @action
  public queryAddressById(params) {  // 根据ID查找地址
    return post(`/integration/scm/ordermall/address/load`, params);
  }

  @action
  public queryTagList() {     // 获取tag标签
    return post(`/integration/scm/address/tagtype/list`);
  }

  @action
  public editAddress(data) {   // 编辑地址
    return post(`/integration/scm/ordermall/address/edit`, data);
  }

  @action
  public deleteAddress(data) {   // 删除地址
    return post(`/integration/scm/ordermall/address/remove`, data);

  }

  @action
  public saveAddress(data) {  // 新建地址
    return post(`/integration/scm/address/add`, data);
  }

  @action
  public setDefaultAddress(data) {  // 设置默认地址
    return post(`/integration/scm/ordermall/address/default`, data);
  }

  @action
  public loadAddress(oid) {
    return post(`/integration/scm/address/load`, { oid });
  }
}
