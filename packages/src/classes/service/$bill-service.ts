import { bean } from "@classes/ioc/ioc";
import { action } from "mobx";
import { post, postNew } from "../../helpers/ajax-helpers";

@bean($BillService)
export class $BillService {
  @action
  public queryBillList(params) {
    return postNew(`/custom/scm/ordermall/billOrder/list`, params);
  }
  @action
  public queryBillStatus(params) {
    return postNew(`/custom/scm/ordermall/billOrder/status`, params);
  }

  @action
  public orderpartyList(params) {
    return post(`/integration/scm/ordermall/new/orderparty/list`, params);
  }

  @action queryBillDetail(params) {
    return postNew(`/custom/scm/ordermall/billOrder/detail`,params)
  }
  @action queryBillTurnover(params){
    return postNew(`/custom/scm/ordermall/billOrder/accountFlow`,params)
  }
  @action queryBillFlowSummary(params){
    return postNew(`/custom/scm/ordermall/billOrder/relatedDoc`,params)
  }
  @action confirmBill(params){
    return postNew(`/custom/scm/ordermall/billOrder/confirm`,params)
  }

  @action
  public queryDetailByType(params) {
    return postNew(`/custom/scm/ordermall/billOrder/detailByType`, params);
  }

  @action
  public queryListNew(params) {
    return postNew(`/custom/scm/ordermall/billOrder/flowList`, params);
  }
}
