import { bean } from "@classes/ioc/ioc";
import { action } from "mobx";
import { post } from "../../helpers/ajax-helpers";

@bean($MyInfoService)
export class $MyInfoService {
  @action
  public queryInformation() {  // 获取消息通知
    return post(`/integration/scm/ordermall/items/list`);
  }

  @action
  public loadRepaymentPage() {   // 信用还款页面加载
    return post(`/integration/scm/ordermall/account/creditaccountrepayment/load`);
  }

  @action
  public toRepayment(params) {  // 提交信用还款
    return post(`/integration/scm/ordermall/account/creditaccountrepayment/repayment`, params);
  }

  @action
  public getCreditAccountRepayment(params) {  // 获取还款总金额
    return post(`/integration/scm/ordermall/account/creditaccountrepayment/returnamount`, params);
  }

  @action
  public loadRechargePage(params) {   // 充值页面加载
    return post(`/integration/scm/ordermall/account/storedaccountrecharge/load`, params);
  }

  @action
  public toRecharge(params) {  // 提交充值
    return post(`/integration/scm/ordermall/account/storedaccountrecharge/recharge`, params);
  }

  @action
  public loadTransferPage(params) {   // 转账页面加载
    return post(`/integration/scm/ordermall/account/transfer/load`, params);
  }

  @action
  public toTransfer(params) {  // 提交转账
    return post(`/integration/scm/ordermall/account/accounttransfer`, params);
  }

  @action
  public loadGiftTransferPage(params) {   // 配赠转店页面加载
    return post(`/integration/scm/ordermall/pzAccount/transfer/load`, params);
  }

  @action
  public toGiftTransfer(params) {   // 配赠转店-转账
    return post(`/integration/scm/ordermall/pzAccount/transfer`, params);
  }

  @action
  public queryTransferInAccountList(params) {  // 转账门店列表加载
    return post(`/integration/scm/ordermall/account/transfer/orderparty/load`, params);
  }

  @action
  public queryCouponList(params) {
    return post(`/integration/scm/ordermall/my/coupon/list`, params);
  }

  @action
  public queryCouponDetail(params) {
    return post(`/integration/scm/ordermall/my/coupon/detail`, params);
  }

  // 消息通知
  @action
  public queryMessageCateList() {
    return post(`/integration/scm/ordermall/message/cate/list`);
  }

  @action
  public queryMessageBusinessList(params) {
    return post(`/integration/scm/ordermall/message/list`, params);
  }

  @action
  public queryAnnouncementList(params) {
    return post(`/integration/scm/ordermall/announcement/list`, params);
  }

  @action
  public queryAnnouncementDetail(params) {
    return post(`/integration/scm/ordermall/announcement/detail`, params);
  }

  @action
  public messageRead(params) {
    return post(`/integration/scm/ordermall/message/read`, params);
  }

  @action
  public queryCapitalAccount(params) {
    return post(`/integration/scm/ordermall/capitalAccount/list`, params);
  }

  @action
  public loadRechargeRecord(params) {
    return post(`/integration/scm/ordermall/storedaccountrecharge/record`, params);
  }

  @action
  public loadRepaymentRecord(params) {
    return post(`/integration/scm/ordermall/account/credit/paymentrecords`, params);
  }

  @action
  public loadCreditAmountList(params) {
    return post(`/integration/scm/me/quota`, params);
  }

  @action
  public ordermallMenushowformycenter() {
    return post(`/integration/scm/ordermall/menushowformycenter`);
  }

  @action
  public loadDebtAccountInfo() {
    return post(`/integration/scm/me/credit/load`);
  }

  @action
  public loadCreditAdjust(params) {
    return post(`/integration/scm/me/creditAdjust/load`, params);
  }

  @action
  public loadNoLimitCredit(params) {
    return post(`/integration/scm/me/noLimitPeriodCredit/load`, params);
  }
}
