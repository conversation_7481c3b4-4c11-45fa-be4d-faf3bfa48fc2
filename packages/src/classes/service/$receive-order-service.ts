import { bean } from "@classes/ioc/ioc";
import { action } from "mobx";
import { post, post2 } from "../../helpers/ajax-helpers";

@bean($ReceiveService)
export class $ReceiveService {
  @action
  public queryReceiveOrderList(params) {
    return post(`/integration/scm/deliveryOrder/list`, params);
  }
  @action
  public getReceiveOrderDetail(params) {
    return post(`/integration/scm/deliveryOrder/detail`, params);
  }
  @action
  public confirmReceive(params) {
    return post(`/integration/scm/deliveryOrder/confirm`, params);
  }
  @action
  public getLogisticsInfoList(params) {
    return post(`/integration/scm/logistics/list`, params);
  }
}
