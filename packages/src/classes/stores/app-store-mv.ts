/**
 * 先声明，再使用；
 * 为规范数据存储位置，里面的数据设置与清除必须使用appStore的方法，获取不必
 * */
import { autowired, bean } from "@classes/ioc/ioc";
import { action, observable } from "mobx";
import { SITE_PATH } from "../../modules/app";
import { $ComponentService } from "@classes/service/$component-service";
import { $OrderPartyService } from "@classes/service/$order-party-service";
import { Modal } from "antd-mobile";
const alert = Modal.alert;
export enum AppStoreKey {
  ALLSHOPORDERLISTSTORE = "ALLSHOPORDERLISTSTORE", // 所有门店订单列表页面
  BILLLIST = "BILLLIST",//账单页面
  OLD_BILL_LIST = "OLD_BILL_LIST",
  BILLTURNOVER = "BILLTURNOVER",//账单流水页面
  SINGLESHOPORDERLISTSTORE = "SINGLESHOPORDERLISTSTORE", // 单个门店订单列表页面
  RECEIVEORDERLISTSTORE = "RECEIVEORDERLISTSTORE", // 收货单列表页面
  RETURNEDSALES = "RETURNEDSALES", // 退货单列表页面
  ORDERPARTYSELECTION = "ORDERPARTYSELECTION", // 订货门店列表页面
  ORDERSCHEMELIST = "ORDERSCHEMELIST", // 订货方案列表页面
  ACCOUNTINFO = "ACCOUNTINFO", // 余额，返利账户列表
  ACCOUNTINFOLIST = "ACCOUNTINFOLIST", // 欠款账户列表
  OFFLINETRANSFERRECORDLIST= "OFFLINETRANSFERRECORDLIST", // 线下转账记录列表页面
  SHOPLIST = "SHOPLIST", // 商品分类页
  SEARCHSHOPLIST = "SEARCHSHOPLIST", // 商品搜索页
  HOMEPRODUCTS = "HOMEPRODUCTS", // 我的主页
  MESSAGELIST = "MESSAGELIST", // 消息列表
  MESSAGECOMPANY = "MESSAGECOMPANY", // 公司公告列表
  SHOPCART = "SHOPCART", // 购物车列表
  INITIATINGRETURNS = "INITIATINGRETURNS", // 发起退货
  PROMOTIONMATCH = "PROMOTIONMATCH", // 促销赠品选择页面
  EXCHANGEGOODSLIST = "EXCHANGEGOODSLIST", // 换货社区列表
  COUPONLIST = "COUPONLIST", // 优惠卷列表
  ADDRESSLIST = "ADDRESSLIST", // 收货地址列表
  MYBANKLIST = "MYBANKLIST", // 银行卡列表
  REPAYMENTSTORAGE = "REPAYMENTSTORAGE", // 还款页面
  RECHARGESTORAGE = "RECHARGESTORAGE", // 充值页面
  SINGLEPAYMENTSTORAGE = "SINGLEPAYMENTSTORAGE", // 单笔付款页面
  ALLORDERPAYMENT = "ALLORDERPAYMENT", // 合并付款页面
}
@bean($AppStore)
export class $AppStore {
  @autowired($ComponentService)
  public $componentService: $ComponentService;
  @autowired($OrderPartyService)
  public $orderPartyService: $OrderPartyService;

  @action
  public savePageMv(tag, obj) {
    console.log("savePageMv");
    localStorage.setItem(tag, JSON.stringify(obj));
  }
  @action
  public clearPageMv(tag) {
    if (this.getPageMv(tag)) {
      // console.log("clearPageMv");
      localStorage.removeItem(tag);
    }
  }
  @action
  public getPageMv(tag) {
    // console.log("getPageMv");
    return localStorage.getItem(tag);
  }
  @action
  public queryShopOverdue(shopId, cb, isGoBack?) {
    return this.$componentService.queryShopOverdue({ orderPartyId: shopId ? shopId : null }).then((res) => {
      console.log("hasOverdue", res);
      const { hasOverdue, overdueType, accountId, orderOrgName, amount } = res;
      if (hasOverdue) {
        alert(`${overdueType === "feeDocument" ? "您的门店存在逾期的费用单，请先付款再继续订购商品。" : (orderOrgName + "有" + amount + "元逾期欠款未还，请先还款再继续订购商品。")}`, "", [
          {
            text: "取消", onPress: () => {
              if (isGoBack) {
                history.go(-1);
              }
            },
          },
          {
            text: "立即还款", onPress: () => {
              this.clearPageMv(AppStoreKey.ACCOUNTINFOLIST);
              if (shopId) {
                this.$orderPartyService.saveShopAndScheme({ orderPartyId: shopId }).then((data) => {
                  console.log(data);
                  window.location.href = overdueType === "feeDocument" ? `/${SITE_PATH}/expense-node-list/Payment/null` : `/${SITE_PATH}/account-info-list/${accountId}/XYZH`;
                }).catch((err) => {
                  console.log(err);
                });
              } else {
                window.location.href = overdueType === "feeDocument" ? `/${SITE_PATH}/expense-node-list/Payment/null` : `/${SITE_PATH}/account-info-list/${accountId}/XYZH`;
              }
            },
          },
        ]);
      } else {
        cb && cb();
      }
    });
  }
}
