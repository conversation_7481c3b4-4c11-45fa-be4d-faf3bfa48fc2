import { action, observable } from "mobx";
import { beanMapper } from "../../helpers/bean-helpers";

export class $requestItem {
  @observable public skuId: number;

  @observable public quantity: number;

  @observable public name: string;

  @observable public code: string;

  @observable public retailPrice: number;

  constructor(list: any) {
    beanMapper(list, this);
  }

  @action
  public setQuantity(quantity) {
    this.quantity = quantity;
  }
}
