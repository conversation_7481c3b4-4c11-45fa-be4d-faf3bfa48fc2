import { bean } from "@classes/ioc/ioc";
import { observable } from "mobx";
import { beanMapper } from "../../helpers/bean-helpers";

@bean($OrderDetail)
export class $OrderDetail {
  @observable public contactPerson: string = "";
  @observable public retailPriceViewPermission: string = "";
  @observable public location: string = "";
  @observable public addressId: any;
  @observable public postalCode: string = "";
  @observable public isDefault: string = "";

  constructor(address?) {
    beanMapper(address, this);
  }
}
