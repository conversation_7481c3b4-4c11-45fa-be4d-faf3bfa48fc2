import { bean } from "@classes/ioc/ioc";
import { observable } from "mobx";
import { beanMapper } from "../../helpers/bean-helpers";

@bean($AllOrder)
export class $AllOrder {
  @observable public checked: boolean = false;

  @observable public disabled: boolean = false;

  @observable public orderId: string;

  @observable public orderOrgId: any;

  @observable public orderSchemeId: any;

  @observable public orderSchemeName: string;

  @observable public docNo: string;

  @observable public docStatus: string;

  @observable public orderOrgName: string;

  @observable public totalQuantity: string;

  @observable public docDate: string;

  @observable public paymentInfo: string;

  @observable public totalAmount: string;

  @observable public paymentStatus: string;

  @observable public unAuditedAmount: string;

  @observable public auditedAmount: string;

  @observable public payableAmount: string;

  @observable public originType: string;

  @observable public isShowPaymentBtn: boolean;

  @observable public isShowAuditBtn: boolean;

  @observable public isShowEditBtn: boolean;

  @observable public isShowCancelBtn: boolean;

  @observable public isShowRejectBtn: boolean;

  @observable public isShowSendFinancialBtn: boolean;

  @observable public isShowDeliveryBtn: boolean;

  @observable public paymentStatusCode: boolean;

  @observable public retailPriceTotalAmount: number;

  @observable public memberPriceTotalAmount: number;

  @observable public giftOrderPriceTotalAmount: number;

  @observable public giftRetailPriceTotalAmount: number;

  @observable public priceDiffTotalAmount: number;

  @observable public freightAmount: number;

  @observable public paymentModeInfo: object;

  constructor(order) {
    beanMapper(order, this);
  }
}
