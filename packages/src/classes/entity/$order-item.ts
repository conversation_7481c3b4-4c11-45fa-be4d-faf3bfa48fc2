import { bean } from "@classes/ioc/ioc";
import { observable } from "mobx";
import { beanMapper } from "../../helpers/bean-helpers";

@bean($OrderItem)
export class $OrderItem {
  @observable public orderNo: string;

  @observable public itemStatusName: string;

  @observable public itemStatusCode: string;

  @observable public orgName: string;

  @observable public orderSchemeName: string;

  @observable public skuName: string;

  @observable public skuCode: string;

  @observable public orderNum: number;

  @observable public oweNum: number;

  @observable public onTheWayNum: number;

  constructor(info?) {
    beanMapper(info, this);
  }
}
