import { observable } from "mobx";
import { beanMapper } from "../../helpers/bean-helpers";
import { bean } from "@classes/ioc/ioc";

@bean($MessageBusiness)
export class $MessageBusiness {
  @observable public id: string;

  @observable public tilte: string;

  @observable public subTitle: string;

  @observable public receiveTime: string;

  @observable public isRead: boolean;

  @observable public type: string;

  @observable public orderId: number;

  @observable public accountType: string;

  @observable public accountId: number;

  @observable public orderSchemeName: string;

  @observable public linkTo: string;

  constructor(message: any) {
    beanMapper(message, this);
  }
}
