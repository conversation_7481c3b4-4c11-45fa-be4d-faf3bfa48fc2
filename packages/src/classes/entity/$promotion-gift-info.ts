import { bean } from "@classes/ioc/ioc";
import { action, observable } from "mobx";
import { beanMapper } from "../../helpers/bean-helpers";

@bean($PromotionGiftInfo)
export class $PromotionGiftInfo {
  @observable public quantity: number;

  @observable public repeatType: string;

  @observable public repeatTypeName: string;

  @observable public amount: number;

  @observable public amountType: string;

  @observable public amountTypeName: string;

  @observable public optionalType: string;

  @observable public optionalTypeName: string;

  @observable public hadSelect: number;

  @observable public hadMoney: number;

  @observable public donationAmount: number;

  @observable public donationAmountType: string;

  @observable public usePriceType: string;

  @observable public forceGiveType: string;

  @observable public errorAmount: number;

  @observable public donationQuantityType: string;

  @observable public amountOfFixAmount: number;

  @observable public scopeType: string;

  constructor(entity: any) {
    beanMapper(entity, this);
  }

  @action
  public setHadSelect(hadSelect: number) {
    this.hadSelect = hadSelect;
  }

  @action
  public setHadMoney(hadMoney: number) {
    this.hadMoney = hadMoney;
  }
}
