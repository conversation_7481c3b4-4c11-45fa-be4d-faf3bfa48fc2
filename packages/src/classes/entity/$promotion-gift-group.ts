import { bean } from "@classes/ioc/ioc";
import { observable } from "mobx";
import { $PromotionGiftInfo } from "./$promotion-gift-info";
import { $PromotionGiftList } from "./$promotion-gift-list";

@bean($PromotionGiftGroup)
export class $PromotionGiftGroup {
  @observable public groupInfo: $PromotionGiftInfo = new $PromotionGiftInfo({});
  @observable public giftList: $PromotionGiftList[] = [];

  constructor(entity: any) {
    // beanMapper(entity, this);
    this.groupInfo = new $PromotionGiftInfo(entity.groupInfo);
    this.giftList = entity.giftList.map((gift) => new $PromotionGiftList(gift));
  }
}
