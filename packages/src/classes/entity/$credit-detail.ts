import { observable } from "mobx";
import { bean } from "@classes/ioc/ioc";

@bean($CreditDetail)
export class $CreditDetail {
  @observable public accountId: number;

  @observable public code: string;

  @observable public creditAmount: number;

  @observable public surplusToReturnAmount: number;

  @observable public repaymentTime: string;

  @observable public creditAmountExpiryDateFrom: string;

  @observable public creditAmountExpiryDateTo: string;

  @observable public creditStatus: string;

  @observable public creditStatusDesc: string;

  @observable public accountRuleLimit: string;

  @observable public seqNo: number;

  @observable public isMulti: string;

  @observable public planId: string;
}
