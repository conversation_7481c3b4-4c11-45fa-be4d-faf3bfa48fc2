import { bean } from "@classes/ioc/ioc";
import { observable } from "mobx";
import { beanMapper } from "../../helpers/bean-helpers";

@bean($Bank)
export class $Bank {
  @observable public id: number;

  @observable public bankName: string;

  @observable public bankCardType: string;

  @observable public bankCardTypeName: string;

  @observable public bankCardNo: string;

  @observable public isCheck: boolean = false;

  @observable public bankCardRate: string;

  @observable public bankCardTypeCode: string;

  @observable public bankCardTypeName: string;

  constructor(bank?) {
    beanMapper(bank, this);
  }
}
