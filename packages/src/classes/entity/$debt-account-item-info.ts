import { bean } from "@classes/ioc/ioc";
import { beanMapper } from "../../helpers/bean-helpers";
import { observable } from "mobx";

@bean($DebtAccountItemInfo)
export class $DebtAccountItemInfo {

  @observable public validityType: string; // 效期类型， Y 有效期,常规  N 无效期 新模式

  @observable public availableAmount: number; // 可用金额

  @observable public waitRepayAmount: number; // 待还金额

  @observable public creditAmount: number; // 总额度

  constructor(item: any) {
    beanMapper(item, this);
  }
}