import { bean } from "@classes/ioc/ioc";
import { observable } from "mobx";
import { beanMapper } from "../../helpers/bean-helpers";

@bean($Order)
export class $Order {
  @observable public oid: string;
  @observable public docNo: string;
  @observable public orderDate: string; // 订货时间
  @observable public totalAmount: string; // 总金额
  @observable public totalSku: string; // 共多少种
  @observable public totalSkuCount: string; // 共多少件

  constructor(order) {
    beanMapper(order, this);
  }
}
