import { bean } from "@classes/ioc/ioc";
import { action, observable } from "mobx";
import { beanMapper } from "../../helpers/bean-helpers";

@bean($Product)
export class $Product {
  @observable public productIdentifier: string;

  @observable public productSkuId: string;

  @observable public name: string;

  @observable public productUomId: string;

  @observable public orderPrice: number; // 订货价
  @observable public retailPrice: number; // 零售价
  @observable public itemDesc: string; // 商品简介
  @observable public imageUrl: string; // 商品图片
  @observable public productUomName: string; // 商品单位
  @observable public quantity: number = 0; //
  @observable public stockQuantity: string; // 库存
  @observable public checked: boolean; // 是否勾选
  @observable public multimediaDescription: string; // 富文本
  @observable public isActive: string; // 是否失效
  @observable public unitOfMeasure: string; // 商品单位

  // for ad
  @observable public defaultImageURL: string; // for ad
  @observable public productItemDesc: string; // for ad
  @observable public descriptionTitle: string; // for ad

  @observable public itemType: string = "sp";

  @observable public itemId: string;

  @observable public product: object;

  @observable public activitySuit: object;

  @observable public virtualSuit: object;

  @observable public virtualSuitList: any[] = [];

  @observable public skuType: string;

  @observable public code: string;

  constructor(product: any) {
    beanMapper(product, this);
  }

  @action
  public setQuantity(quantity: number) {
    this.quantity = quantity;
  }

  @action
  public setChecked(checked: boolean) {
    this.checked = checked;
  }
}
