import { observable } from "mobx";
import { beanMapper } from "../../helpers/bean-helpers";

export class $PaymentProduct {
  @observable public oid: number;

  @observable public isActive: string;

  @observable public isCheck: boolean = false;

  @observable public paymentCode: string;

  @observable public paymentName: string;

  @observable public rate: number;

  @observable public isDisabled: boolean = false;

  @observable public priority: number;

  @observable public expectedPaymentAmount: number = 0;

  constructor(mode) {
    beanMapper(mode, this);
  }
}
