import { bean } from "@classes/ioc/ioc";
import { observable } from "mobx";

@bean($OrderParty)
export class $OrderParty {
  @observable public oid: string;
  @observable public name: string;
  @observable public quantity: string;
  @observable public isDefault: boolean;
  @observable public isNew: boolean;

  constructor(orderParty: any) {
    const { oid, name, quantity, isDefault, isNew } = orderParty;
    this.oid = oid;
    this.name = name;
    this.quantity = quantity;
    this.isDefault = isDefault;
    this.isNew = isNew;
  }
}
