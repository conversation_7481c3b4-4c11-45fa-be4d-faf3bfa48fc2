import { bean } from "@classes/ioc/ioc";
import { observable } from "mobx";
import { beanMapper } from "../../helpers/bean-helpers";

@bean($SelectOrderparty)
export class $SelectOrderparty {
  @observable public oid: number;

  @observable public name: string;

  @observable public code: string;

  @observable public checked: boolean = true;

  @observable public showActiveOrderparty: boolean = true;

  constructor(product: any) {
    beanMapper(product, this);
  }
}
