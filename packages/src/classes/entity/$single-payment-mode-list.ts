import { bean } from "@classes/ioc/ioc";
import { observable } from "mobx";
import { beanMapper } from "../../helpers/bean-helpers";

@bean($SinglePaymentModeList)
export class $SinglePaymentModeList {
  @observable public oid: number;

  @observable public code: string;

  @observable public name: string;

  @observable public isActive: string;

  @observable public isTPPymt: string = "Y";

  @observable public accountId: number;

  @observable public availableTotalAmount: number = 0;

  @observable public isCheck: boolean = false;

  @observable public isDisabled: boolean = false;

  @observable public expectedPaymentAmount: number = 0;

  @observable public priority: number;

  @observable public paymentProductList: any[] = [];

  @observable public rate: number = 0;

  @observable public rateList: any[] = [];

  @observable public rateBookList: any[] = [];

  @observable public availableOrderAmount: number = 0;

  @observable public availableFreightAmount: number = 0;
  @observable public freightAmount: number = 0; // 使用使用信用支付的运费金额
  @observable public orderAmount: number = 0; // 使用使用信用支付的订单金额

  @observable public availableTotalAmountForNoLimitPeriod: number; // 无限期信用账户总金额

  @observable public availableOrderAmountForNoLimitPeriod: number; // 无限期信用账户订单金额

  @observable public availableFreightAmountForNoLimitPeriod: number; // 无限期信用账户运费金额

  @observable public hasCredit: boolean; // 是否用常规信用

  @observable public hasNewModeCredit: boolean; // 是否用新模式信用

  constructor(product: any) {
    beanMapper(product, this);
  }
}
