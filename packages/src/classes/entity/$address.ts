import { bean } from "@classes/ioc/ioc";
import { observable } from "mobx";
import { beanMapper } from "../../helpers/bean-helpers";

@bean($Address)
export class $Address {
  @observable public contactPerson: string = "";
  @observable public phoneNumber: string = "";
  @observable public location: string = "";
  @observable public addressId: any;
  @observable public postalCode: string = "";
  @observable public isDefault: string = "";

  constructor(address?) {
    beanMapper(address, this);
  }
}
