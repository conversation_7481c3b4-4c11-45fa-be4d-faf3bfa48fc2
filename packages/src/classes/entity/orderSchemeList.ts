import { bean } from "@classes/ioc/ioc";
import { observable } from "mobx";

@bean($OrderSchemeList)
export class $OrderSchemeList {
  @observable public oid: string;

  @observable public name: string;

  @observable public imgUrl: string;

  @observable public readOnly: boolean;

  @observable public startTime: string;

  @observable public endTime: string;

  @observable public orderSchemeType: string;

  @observable public isMutiOrder: boolean;

  @observable public setOrderNumer: number;

  @observable public expectedDeliveryStartTime: string;

  @observable public expectedDeliveryEndTime: string;

  @observable public paymentMode: string;

  @observable public memo: string;

  @observable public deadlineTime: string;

  constructor(orderSchemeList: any) {
    const { oid, name, imgUrl, readOnly, startTime, endTime, orderSchemeType, isMutiOrder, expectedDeliveryStartTime,
       expectedDeliveryEndTime, paymentMode, memo, setOrderNumer, deadlineTime } = orderSchemeList;
    this.oid = oid;
    this.name = name;
    this.imgUrl = imgUrl;
    this.readOnly = readOnly;
    this.startTime = startTime;
    this.endTime = endTime;
    this.orderSchemeType = orderSchemeType;
    this.isMutiOrder = isMutiOrder;
    this.expectedDeliveryStartTime = expectedDeliveryStartTime;
    this.expectedDeliveryEndTime = expectedDeliveryEndTime;
    this.paymentMode = paymentMode;
    this.memo = memo;
    this.setOrderNumer = setOrderNumer;
    this.deadlineTime = deadlineTime;

  }
}
