import { bean } from "@classes/ioc/ioc";
import { action, observable } from "mobx";
import { beanMapper } from "../../helpers/bean-helpers";

@bean($Commodity)
export class $Commodity {
  @observable public productSkuId: string;

  @observable public imageList: any[];

  @observable public name: string;

  @observable public unitOfMeasure: string;

  @observable public itemDesc: string;

  @observable public orderPrice: number;

  @observable public retailPrice: number;

  @observable public stockStatus: string;

  @observable public briefIntroduction: string;

  @observable public orderSchemeType: string;

  @observable public promotionList: any[];

  @observable public quantity: number;

  @observable public itemType: string = "sp";

  @observable public productList: any[] = [];

  constructor(product: any) {
    beanMapper(product, this);
  }

  @action
  public setQuantity(quantity: number) {
    this.quantity = quantity;
  }
}
