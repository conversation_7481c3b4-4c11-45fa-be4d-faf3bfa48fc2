import { observable } from "mobx";
import { beanMapper } from "../../helpers/bean-helpers";

export class $PaymentInfo {
  @observable public expectedAmount: number;

  @observable public orgCode: string;

  @observable public orgOid: number;

  @observable public isCheck: boolean = false;

  @observable public orgName: string;

  @observable public unPayAmount: number;

  @observable public usableAmount: number;

  @observable public salesOrderIdList: any[] = [];

  constructor(mode) {
    beanMapper(mode, this);
  }
}
