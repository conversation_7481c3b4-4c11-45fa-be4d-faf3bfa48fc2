import { action, observable } from "mobx";
import { beanMapper } from "../../helpers/bean-helpers";

export class $skuList {
  @observable public skuId: number;

  @observable public name: string;

  @observable public code: string;

  @observable public retailPrice: number;

  @observable public quantity: number;

  constructor(sku: any) {
    beanMapper(sku, this);
  }

  @action
  public setQuantity(quantity) {
    this.quantity = quantity;
  }
}
