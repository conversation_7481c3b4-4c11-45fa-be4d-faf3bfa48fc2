import { bean } from "@classes/ioc/ioc";
import { action, observable } from "mobx";

@bean($SearchInfo)
export class $SearchInfo {
  @observable public title: string;

  @observable public statusCode: any[] = [];

  @observable public startTime: string;

  @observable public endTime: string;

  @observable public regionId: any[] = [];

  @observable public pageIndex: number = 0;

  @observable public pageSize: number = 10;
}
