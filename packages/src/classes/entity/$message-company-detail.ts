import { observable } from "mobx";
import { beanMapper } from "../../helpers/bean-helpers";
import { bean } from "@classes/ioc/ioc";

@bean($MessageCompanyDetail)
export class $MessageCompanyDetail {
  @observable public id: number;

  @observable public title: string;

  @observable public annoucementTime: string;

  @observable public content: string;

  constructor(detail: any) {
    beanMapper(detail, this);
  }
}
