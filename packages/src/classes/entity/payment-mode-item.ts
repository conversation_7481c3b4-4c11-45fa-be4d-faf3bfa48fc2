import { observable } from "mobx";
import { beanMapper } from "../../helpers/bean-helpers";

export class $PaymentModeItem {
  @observable public oid: number;

  @observable public name: string;

  @observable public code: string;

  @observable public isTPPymt: string = "Y";

  @observable public paymentProductList: any[] = [];

  @observable public isCheck: boolean = false;

  @observable public rate: number = 0;

  constructor(sku: any) {
    beanMapper(sku, this);
  }
}
