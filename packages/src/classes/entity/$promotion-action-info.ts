import { bean } from "@classes/ioc/ioc";
import { action, observable } from "mobx";
import { beanMapper } from "../../helpers/bean-helpers";

@bean($PromotionActionInfo)
export class $PromotionActionInfo {
  @observable public actionId: string;

  @observable public actionType: string;

  @observable public actionSubType: string;

  @observable public interactFlag: string;

  @observable public usePriceType: string;

  @observable public forceGiveType: string;

  @observable public errorAmount: string;

  @observable public donationAmount: string;

  @observable public amountOfFixAmount: string;

  @observable public amountType: string;

  @observable public quantity: string;

  @observable public appearType: string;

  @observable public adjustItemList: any[] = [];

  @observable public checked: boolean = false;

  @observable public selectLength: number = 2;

  constructor(promotion) {
    beanMapper(promotion, this);
  }

  @action
  public setChecked(checked) {
    this.checked = checked;
  }

}
