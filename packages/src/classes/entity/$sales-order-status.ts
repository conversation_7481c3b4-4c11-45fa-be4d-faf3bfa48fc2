import { bean } from "@classes/ioc/ioc";
import { observable } from "mobx";
import { beanMapper } from "../../helpers/bean-helpers";

@bean($SalesOrderStatus)
export class $SalesOrderStatus {
  @observable public text: string;

  @observable public value: string;

  @observable public checked: boolean = true;

  @observable public showActiveOrderparty: boolean = true;

  constructor(product: any) {
    beanMapper(product, this);
  }
}
