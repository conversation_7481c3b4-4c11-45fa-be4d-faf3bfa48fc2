import { bean } from "@classes/ioc/ioc";
import { observable } from "mobx";
import { beanMapper } from "../../helpers/bean-helpers";
import { $Product } from "./$product";

@bean($Ad)
export class $Ad {
  @observable public adRotatorItemId: number;
  @observable public seqNo: number;
  @observable public imageUrl: string;
  @observable public linkType: string;
  @observable public announcementId: number;
  @observable public productSkuId: number;
  @observable public urlLink: number;

  constructor(ad) {
    beanMapper(ad, this);
  }
}
