import { bean } from "@classes/ioc/ioc";
import { action, observable } from "mobx";
import { beanMapper } from "../../helpers/bean-helpers";
import { $PromotionActionInfo } from "./$promotion-action-info";
import { $PromotionAdjustItem } from "./$promotion-adjust-item";

@bean($Promotion)
export class $Promotion {
  @observable public promotionId: string; // 促销ID
  @observable public name: string; // 促销名称
  @observable public marketingType: string; // 营销类型：促销，优惠券

  @observable public promotionType: string; // 促销类型：整单，单品

  @observable public actionType: string; // 优惠类型：减价，打折等

  @observable public times: number; // 促销参与次数

  @observable public priority: number; // 优先级

  @observable public checkFlag: string; // 促销选中状态 Y／N

  @observable public amount: number; // 预计优惠金额

  @observable public interactFlag: string; // 是否需要与后续交互

  @observable public repelId: string; // 当前促销互斥的促销ID

  @observable public errorMessage: string; // 当前促销不能选中的消息内容

  @observable public adjustItemList: $PromotionAdjustItem[] = []; // 当前订单商品清单调整内容(固定赠品)

  @observable public actionInfoList: $PromotionActionInfo[] = []; // 当前订单商品清单调整内容(固定赠品)

  @observable public usedCouponList: any[] = []; // 活动使用的券信息列表

  @observable public forceGiveType: string;

  @observable public errorAmount: number;

  @observable public donationAmount: number;

  @observable public amountOfFixAmount: number;

  @observable public virtualSuitList: any[] = [];

  constructor(promotion) {
    beanMapper(promotion, this);
  }

  @action
  public setCheckFlag(checked: boolean) {
    if (checked) {
      this.checkFlag = "T";
    } else {
      this.checkFlag = "N";
    }
  }

  @action
  public setAdjustItemList(list: any[]) {
    this.adjustItemList = list;
  }

  @action
  public setUsedCouponList(list: any[]) {
    this.usedCouponList = list;
  }

  @action
  public setNullAdjustItemList() {
    this.adjustItemList = [];
  }

  @action
  public setTimes() {
    this.times = 1;
  }

  @action
  public setSingleCouponTimes(times) {
    this.times = times;
  }

}
