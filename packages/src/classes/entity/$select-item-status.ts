import { bean } from "@classes/ioc/ioc";
import { observable } from "mobx";
import { beanMapper } from "../../helpers/bean-helpers";

@bean($SelectItemStatus)
export class $SelectItemStatus {
  @observable public text: string;

  @observable public value: string;

  @observable public checked: boolean = false;

  @observable public showActiveStatus: boolean = false;

  constructor(product: any) {
    beanMapper(product, this);
  }
}
