import { bean } from "@classes/ioc/ioc";
import { beanMapper } from "../../helpers/bean-helpers";
import { observable } from "mobx";

@bean($CreditItemInfo)
export class $CreditItemInfo {

  @observable public date: string;

  @observable public amount: number;

  @observable public label: string;

  @observable public oid: number;

  @observable public startTime: string;

  @observable public endTime: string;

  @observable public inOutType: "ADD" | "REDUCE";

  @observable public afterAmount: number; // 更新后额度

  @observable public adjustId: string; // 授信调整ID

  @observable public isJobAdjust: string; // 是否是工作流调整

  constructor(item: any) {
    beanMapper(item, this);
  }
}
