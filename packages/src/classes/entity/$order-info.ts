import { bean } from "@classes/ioc/ioc";
import { observable } from "mobx";
import { beanMapper } from "../../helpers/bean-helpers";

@bean($OrderInfo)
export class $OrderInfo {
  @observable public docDate: string;

  @observable public orderId: number;

  @observable public orderPartyId: number;

  @observable public orderSchemeId: number;

  @observable public orderSchemeName: string;

  @observable public totalAmount: number;

  @observable public totalDiscountAmount: number;

  @observable public totalQuantity: number;

  constructor(info?) {
    beanMapper(info, this);
  }
}
