import { bean } from "@classes/ioc/ioc";
import { observable } from "mobx";
import { beanMapper } from "../../helpers/bean-helpers";

@bean($AcountFlowDetail)
export class $AcountFlowDetail {
  @observable public amount: number;
  @observable public serviceCharge: number;
  @observable public repaymentAmount: number;
  @observable public salesOrderId: number;
  @observable public relatedDocId: number;
  @observable public docTypeText: string;
  @observable public docTypeValue: string;
  @observable public tradeTime: string;
  @observable public flowNo: string;
  @observable public relatedDocNo: string;
  @observable public orderScheme: string;
  @observable public orderOrgName: string;
  @observable public deductType: string;
  @observable public memo: string;
  @observable public repaymentPaymentMode: string;
  @observable public rechargeAmount: number;
  @observable public rechargeMode: string;
  @observable public relateDocTypeText: string;
  @observable public relateDocTypeValue: string;
  @observable public createDate: string;
  @observable public rebateAmount: number;
  @observable public refundOrderId: number;
  @observable public feeDocumentId: number;
  @observable public orgId: number;
  @observable public orgName: string;
  @observable public relatedDocType: string;
  @observable public accountFlowCode: string;
  @observable public accountFlowDocTime: string;
  @observable public accountFlowInOutType: string;
  @observable public accountFlowAmount: number;
  @observable public frozenCode: string; // 冻结编码
  @observable public frozenDocTime: string; // 冻结时间
  @observable public frozenAmount: number; // 冻结金额
  @observable public frozenType: string; // 冻结类型
  @observable public inOutOrderCode: string; // 收支单编码
  @observable public inOutType: string; // 收支单类型
  @observable public relatedSaleOrder: string; // 关联订货单
  @observable public relatedStoreOrder: string; // 关联充值单
  @observable public inOutAmount: number; // 收支金额
  @observable public totalAmount: number; // 总金额
  @observable public availableAmount: number; // 可用余额
  @observable public accountFrozenAmount: number; // 冻结余额
  @observable public saleOrderId: number; // 订货单ID
  @observable public inOutOrderType: string; // 支收方式
  @observable public posOrderNo: string; // POS订单单号
  @observable public activityDesc: string; // 活动力度说明
  @observable public deliveryOrder: string; // 发货单
  @observable public deliveryOrderId: string; // 发货单Id
  @observable public refundOrder: string; // 退货单
  @observable public relatedStoredAccountTransfer: string; // 转账单号
  @observable public inOutOrder_memo: string; // 备注
  @observable public relatedList: any;
  @observable public accountType: string; // 账户类型
  @observable public operatorUser: string; // 操作人
  @observable public transferInOrg: string; // 转入机构
  @observable public transferOutOrg: string; // 转出机构
  @observable public itemType: string;
  @observable public accountTypeLabel: string;

  constructor(order) {
    beanMapper(order, this);
  }
}
