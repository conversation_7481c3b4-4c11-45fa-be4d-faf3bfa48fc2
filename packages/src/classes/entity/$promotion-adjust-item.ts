import { bean } from "@classes/ioc/ioc";
import { observable } from "mobx";
import { beanMapper } from "../../helpers/bean-helpers";

@bean($PromotionAdjustItem)
export class $PromotionAdjustItem {
  @observable public productSkuId: string;

  @observable public name: string;

  @observable public itemDesc: string;

  @observable public quantity: string;

  @observable public orderPrice: string;

  @observable public retailPrice: string;

  @observable public promotionPrice: string;

  @observable public stockStatusDesc: string;

  @observable public stockStatus: string;

  @observable public imageUrl: string;

  @observable public isActive: string;

  @observable public unit: string;

  @observable public virtualSuitList: any[] = [];

  @observable public checked: boolean = false;

  @observable public selectLength: number = 2;

  constructor(promotion) {
    beanMapper(promotion, this);
  }

}
