import { autowired, bean } from "@classes/ioc/ioc";
import { $TransferHintService } from "@classes/service/$transfer-hint-service";
import { action, observable, toJS } from "mobx";
import { isEmpty } from "lodash";

@bean($TransferHintMv)
export class $TransferHintMv {
  @autowired($TransferHintService)
  public transferHintService: $TransferHintService;

  @observable public transferList: any[] = [];
  @observable public totalWaitPayAmount: number | string = 0;

  @action
  public loadTransferInfo = (params) => {
    return this.transferHintService.transferBatchLoad(params).then((res: any) => {
      const { transferList, totalWaitPayAmount } = res;
      this.transferList = transferList.map((inOrg: any) => ({
        ...inOrg,
        transferOutList: !isEmpty(inOrg.transferOutList) ? inOrg.transferOutList.map((outOrg: any) => ({
          ...outOrg,
          checked: true,
        })) : [],
      }));
      this.totalWaitPayAmount = totalWaitPayAmount;
    });
  }

  @action
  public submitTransferInfo = (params) => {
    return this.transferHintService.transferSubmit(params);
  }

  @action
  public loadTransferRecordList = (params) => {
    return this.transferHintService.transferRecordList(params);
  }

  @action
  public manualSubmitTransferInfo = (params) => {
    return this.transferHintService.manualTransferSubmit(params);
  }
}
