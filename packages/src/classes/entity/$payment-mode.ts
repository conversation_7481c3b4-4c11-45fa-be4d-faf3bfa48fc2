import { observable } from "mobx";
import { $PaymentInfo } from "./$payment-info";
import { beanMapper } from "../../helpers/bean-helpers";

export class $PaymentMode {
  @observable public oid: number;

  @observable public name: string;

  @observable public isCheck: boolean = false;

  @observable public code: string;

  @observable public allUsableAmount: number;

  @observable public allExpectedAmount: number;

  @observable public payInfo: $PaymentInfo[] = [];

  @observable public showMore: boolean = false;

  @observable public payInfoIndex: number = 0;

  @observable public totalAmount: number = 0;

  constructor(mode) {
    beanMapper(mode, this);
  }
}
