import { bean } from "@classes/ioc/ioc";
import { observable } from "mobx";
import { beanMapper } from "../../helpers/bean-helpers";

@bean(Logistics)
export class Logistics {
  @observable public companyName: string;

  @observable public logisticsDocNo: string;

  @observable public logisticsDocStatusName: string;

  @observable public logisticsDocStatusCode: string;

  @observable public logisticsTravelList: any[] = [];

  @observable public isShow: boolean = false;

  constructor(order) {
    beanMapper(order, this);
  }
}
