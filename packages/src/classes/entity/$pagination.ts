import { bean } from "@classes/ioc/ioc";
import { action, observable } from "mobx";

const DEFAULT_PAGE_SIZE = 10;

@bean($Page)
export class $Page {
  /**
   * 当前页面
   * @pageIndex 1
   */
  @observable public pageIndex: number = 1;
  /**
   * 每页条数
   * @pageSize 10
   */
  @observable public pageSize: number = 10;
  /**
   * 总条数
   * @total 0
   */
  @observable public total: number = 0;

  constructor() {
    // do nothing
  }

  @action
  public setCurrent(current: number) {
    this.pageIndex = current;
  }

  @action
  public setPageSize(pageSize: number = DEFAULT_PAGE_SIZE) {
    this.pageSize = Number(pageSize);
  }

  @action
  public setTotal(total: number) {
    this.total = total;
  }

}
