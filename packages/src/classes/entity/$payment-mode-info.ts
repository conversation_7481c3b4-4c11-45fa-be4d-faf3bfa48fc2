import { bean } from "@classes/ioc/ioc";
import { observable } from "mobx";
import { beanMapper } from "../../helpers/bean-helpers";

@bean($PaymentModeInfo)
export class $PaymentModeInfo {
  @observable public isCheck: boolean;

  @observable public expectedAmount: 0;

  @observable public availableTotalAmount: 0;

  @observable public test: number;

  constructor(order) {
    beanMapper(order, this);
  }
}
