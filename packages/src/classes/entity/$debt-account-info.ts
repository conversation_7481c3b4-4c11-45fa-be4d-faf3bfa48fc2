import { bean } from "@classes/ioc/ioc";
import { beanMapper } from "../../helpers/bean-helpers";
import { observable } from "mobx";
import { $DebtAccountItemInfo } from "./$debt-account-item-info";

@bean($DebtAccountInfo)
export class $DebtAccountInfo {

  @observable public totalAvailableAmount: number; // 总可用

  @observable public totalCreditAmount: number; // 总额度

  @observable public totalWaitRepayAmount: number; // 总待还
  
  @observable public restWaitRepayAmount: number; // 剩余待还

  @observable public accountList: $DebtAccountItemInfo[] = []; // 账户列表

  constructor(info: any) {
    beanMapper(info, this);
  }
}