import { bean } from "@classes/ioc/ioc";
import { action, observable } from "mobx";
import { beanMapper } from "../../helpers/bean-helpers";

@bean($PromotionGiftList)
export class $PromotionGiftList {
  @observable public productSkuId: number;

  @observable public name: string;

  @observable public itemDesc: string;

  @observable public quantity: number;

  @observable public orderPrice: number;

  @observable public retailPrice: number;

  @observable public promotionPrice: number;

  @observable public stockStatus: string;

  @observable public stockStatusDesc: string;

  @observable public imageUrl: string;

  @observable public isActive: string;

  @observable public checkFlag: string;

  @observable public selectDonationQuantity: number = 0;

  @observable public virtualSuitList: any[] = [];

  @observable public multiple: number = 1;

  @observable public index: string;

  constructor(product: any) {
    beanMapper(product, this);
  }

  @action
  public setQuantity(quantity: number) {
    this.quantity = quantity;
  }

  @action
  public setCheckFlag(checkFlag: string) {
    this.checkFlag = checkFlag;
  }
}
