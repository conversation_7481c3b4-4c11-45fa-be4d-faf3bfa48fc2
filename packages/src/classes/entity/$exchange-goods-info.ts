import { observable } from "mobx";
import { $requestItem } from "./$request-item";
import { beanMapper } from "../../helpers/bean-helpers";

export class $exchangeGoodsInfo {
  @observable public id: number;
  @observable public title: string;
  @observable public contactPerson: string;
  @observable public phoneNumber: number;
  @observable public regionId: any[];
  @observable public memo: string;
  @observable public images: any[] = [];
  @observable public requestItemList: $requestItem[] = [];
  @observable public isShowDeleteBtn: boolean;
  @observable public isShowEditBtn: boolean = true;
  @observable public isShowDealBtn: boolean;

  // 详情
  @observable public goodslmages: any[] = [];

  constructor(info: any) {
    beanMapper(info, this);
  }
}
