import { bean } from "@classes/ioc/ioc";
import { observable } from "mobx";
import { beanMapper } from "../../helpers/bean-helpers";

@bean($CreditAccount)
export class $CreditAccount {
  @observable public accountId: number;

  @observable public code: string;

  @observable public creditAmount: number;

  @observable public surplusToReturnAmount: number;

  @observable public repaymentTime: string;

  @observable public creditAmountExpiryDateFrom: string;

  @observable public creditAmountExpiryDateTo: string;

  @observable public creditStatus: string;

  @observable public creditStatusDesc: string;

  @observable public ruleType: string;

  @observable public checkFlag: boolean = true;

  constructor(product: any) {
    beanMapper(product, this);
  }
}
