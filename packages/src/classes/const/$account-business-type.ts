
export enum AccountBusinessType {
  ALL = "",
  SALE = "销售",
  SALE_RETURN = "销售退货",
  GIFT_ORDER = "配赠订货",
  GIFT_ORDER_CANCEL = "配赠订货取消",
  BALANCE_ADJUSTMENT = "其他调整",
  GIFT_TRANSFER = "配赠转账",
  ORDER = "订货",
  RETURN = "退货",
  SALE_REVERSE = "销售红冲",
  SALE_RETURN_REVERSE = "销售退货红冲",
  SALE_CORRECTION = "销售更正",
  SALE_RETURN_CORRECTION = "销售退货更正",
  MARKETING_ACTIVITY = "营销活动",
  MARKETING_ACTIVITY_REVERSE = "营销活动红冲",
  MARKETING_ACTIVITY_CORRECTION = "营销活动更正",
  MARKETING_ACTIVITY_RETURN = "营销活动退货",
  MARKETING_ACTIVITY_RETURN_REVERSE = "营销活动退货红冲",
  MARKETING_ACTIVITY_RETURN_CORRECTION = "营销活动退货更正",
  REBATE_ORDER = "返利订货",
  REBATE_ORDER_CANCEL = "返利订货取消",
  REBATE_TRANSFER = "返利转账"
}

// 可用配赠列表
export const AVAILABLE_GIFT_LIST = [
  { label: "全部", value: AccountBusinessType.ALL },
  { label: "销售", value: AccountBusinessType.SALE },
  { label: "销售退货", value: AccountBusinessType.SALE_RETURN },
  { label: "配赠订货", value: AccountBusinessType.GIFT_ORDER },
  { label: "配赠订货取消", value: AccountBusinessType.GIFT_ORDER_CANCEL },
  { label: "其他调整", value: AccountBusinessType.BALANCE_ADJUSTMENT },
  { label: "配赠转账", value: AccountBusinessType.GIFT_TRANSFER },
  { label: "订货", value: AccountBusinessType.ORDER },
  { label: "退货", value: AccountBusinessType.RETURN },
  { label: "销售红冲", value: AccountBusinessType.SALE_REVERSE },
  { label: "销售退货红冲", value: AccountBusinessType.SALE_RETURN_REVERSE },
  { label: "销售更正", value: AccountBusinessType.SALE_CORRECTION },
  { label: "销售退货更正", value: AccountBusinessType.SALE_RETURN_CORRECTION },
  { label: "营销活动", value: AccountBusinessType.MARKETING_ACTIVITY },
  { label: "营销活动红冲", value: AccountBusinessType.MARKETING_ACTIVITY_REVERSE },
  { label: "营销活动更正", value: AccountBusinessType.MARKETING_ACTIVITY_CORRECTION },
  { label: "营销活动退货", value: AccountBusinessType.MARKETING_ACTIVITY_RETURN },
  { label: "营销活动退货红冲", value: AccountBusinessType.MARKETING_ACTIVITY_RETURN_REVERSE },
  { label: "营销活动退货更正", value: AccountBusinessType.MARKETING_ACTIVITY_RETURN_CORRECTION }
]

// 返利列表
export const REBATE_LIST = [
  { label: "全部", value: AccountBusinessType.ALL },
  { label: "返利订货", value: AccountBusinessType.REBATE_ORDER },
  { label: "返利订货取消", value: AccountBusinessType.REBATE_ORDER_CANCEL },
  { label: "销售", value: AccountBusinessType.SALE },
  { label: "其他调整", value: AccountBusinessType.BALANCE_ADJUSTMENT },
  { label: "返利转账", value: AccountBusinessType.REBATE_TRANSFER }
]

// 总配赠列表
export const TOTAL_GIFT_LIST = [
  { label: "全部", value: AccountBusinessType.ALL },
  { label: "订货", value: AccountBusinessType.ORDER },
  { label: "退货", value: AccountBusinessType.RETURN },
  { label: "配赠订货", value: AccountBusinessType.GIFT_ORDER },
  { label: "配赠订货取消", value: AccountBusinessType.GIFT_ORDER_CANCEL },
  { label: "其他调整", value: AccountBusinessType.BALANCE_ADJUSTMENT },
  { label: "配赠转账", value: AccountBusinessType.GIFT_TRANSFER },
  { label: "营销活动", value: AccountBusinessType.MARKETING_ACTIVITY },
  { label: "营销活动红冲", value: AccountBusinessType.MARKETING_ACTIVITY_REVERSE },
  { label: "营销活动更正", value: AccountBusinessType.MARKETING_ACTIVITY_CORRECTION },
  { label: "营销活动退货", value: AccountBusinessType.MARKETING_ACTIVITY_RETURN },
  { label: "营销活动退货红冲", value: AccountBusinessType.MARKETING_ACTIVITY_RETURN_REVERSE },
  { label: "营销活动退货更正", value: AccountBusinessType.MARKETING_ACTIVITY_RETURN_CORRECTION }
]

// 配置账户业务类型
export enum GiftAccountBusinessType {
  ORDER = "配赠额度-订货",
  GIFT_ORDER_ED = "配赠额度-配赠订货",
  MEMBER_ACTIVITY_ED = "配赠额度-会员活动",
  SYSTEM_ADJUSTMENT_ED = "配赠额度-系统调整",
  GIFT_TRANSFER = "配赠额度-配赠转账",
  GIFT_ORDER = "配赠上限-配赠订货",
  MEMBER_ACTIVITY = "配赠上限-会员活动",
  SYSTEM_ADJUSTMENT = "配赠上限-系统调整",
  SALE = "配赠上限-销售",
  UPPER_LIMIT_TRANSFER = "配赠上限-上限转账",
}

// 额度列表
export const GIFT_LIMIT_LIST = [
  { label: "订货", value: GiftAccountBusinessType.ORDER },
  { label: "配赠订货", value: GiftAccountBusinessType.GIFT_ORDER_ED },
  { label: "会员活动", value: GiftAccountBusinessType.MEMBER_ACTIVITY_ED },
  { label: "配赠转账", value: GiftAccountBusinessType.GIFT_TRANSFER },
  { label: "系统调整", value: GiftAccountBusinessType.SYSTEM_ADJUSTMENT_ED },
];

// 上限列表
export const GIFT_UPPER_LIMIT_LIST = [
  { label: "销售", value: GiftAccountBusinessType.SALE },
  { label: "配赠订货", value: GiftAccountBusinessType.GIFT_ORDER },
  { label: "会员活动", value: GiftAccountBusinessType.MEMBER_ACTIVITY },
  { label: "上限转账", value: GiftAccountBusinessType.UPPER_LIMIT_TRANSFER },
  { label: "系统调整", value: GiftAccountBusinessType.SYSTEM_ADJUSTMENT },
];
