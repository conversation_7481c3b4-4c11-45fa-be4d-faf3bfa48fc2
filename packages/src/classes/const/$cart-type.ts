export enum $CartType {
  ISACTIVE_KEY = "N",
  ISDEFAULE = "Y",
  STOCKSTATUS = "Empty",
  FUTURE_ORDER = "FUTURE_ORDER",
  NEXTPAGE = "SC_PROM",
  ITEMTYPE = "sp",
  ACTIVITSUITTYPE = "tz",
  ERRORCODEDZ = "40120",
  ORG_ORDER_ISORDEMUST_ERROR = "40195",
  ERRORCODEJXS = "40121",
  ERRORCODEXE = "40122",
  NOSCHEME = "40110",
  TIMELIMIT = "40123",
  AMOUNTLIMIT = "40124",
  COUNTLIMIT = "40125",
  GROUPING = "40150",
  NOORDER = "40160",
  NOSEND = "40161",
  BALANCE_ERROR = "40162",
  NO_BALANCE_ACCOUNT = "40163",
  ONEMINORDERLIMIT = "40190",
  NO_USE_ORDER_PRICE = "40270",
  NOT_SUPPORTING_GIFT_ACCOUNT = "40263",
  NO_USE_DEDUCTION = "40263",
  ORDERPRICEVIEWPERMISSION = "Y",
  ORDERPREFERENTIALACTIVITIESPERMISSION = "Y",
  ORDERFREIGHTVIEWPERMISSION = "Y",
  RETAILPRICEVIEWPERMISSION = "Y",
  VIRTUALSUIT = "virtualSuit",
  HASPROMOTION = "40184",
  PAYEND = "PAY_END",
  BALANCEPAYEND = "BALANCE_PAY_END",
  PRODUCT = "product",
  PARTPAIDAUDITING = "PART_PAID_AUDITING",
  PAYAUDITING = "PAY_AUDITING",
  DEPOSITPAYPARTAUDITING = "DEPOSIT_PAY_PART_AUDITING",
  DEPOSITPAYAUDITING = "DEPOSIT_PAY_AUDITING",
  BALANCEPAYPARTAUDITING = "BALANCE_PAY_PART_AUDITING",
  BALANCEPAYAUDITING = "BALANCE_PAY_AUDITING",
  AUDITON = "AUDIT_ON",
  DEPOSITPAYEND = "DEPOSIT_PAY_END",
  VIRTUALSUITDETAIL = "VIRTUALSUITDETAIL",
  COMMODITYDETAILS = "COMMODITYDETAILS",
  ISCREATEFREIGHTFEEDOCUMENT = "Y",
  PAYON = "PAY_ON",
}
