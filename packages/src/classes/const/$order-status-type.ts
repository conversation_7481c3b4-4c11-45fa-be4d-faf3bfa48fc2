 const $OrderStatusType = {
   orderStatusTypeList: [
     {
       name: "客服审核中",
       statusCode: "WAIT_CUSTOMSERVICE_ADUIT",
       style: {color: "#13C2C2"},
     },
     {
       name: "退货中",
       statusCode: "WAIT_SEND_GOODS",
       style: {color: "#FF3030"},
     },
     {
       name: "仓库收货中",
       statusCode: "WAIT_WAREHOUSE_CONFIRM_GOODS",
       style: {color: "#52C41A"},
     },
     {
       name: "财务退款中",
       statusCode: "WAIT_FINANCIAL_CONFIRM_REFUNDFEE",
       style: {color: "#52C41A"},
     },
     {
       name: "已完成",
       statusCode: "REFUNDFEE_FINASH",
       style: {color: "#307DCD"},
     },
     {
       name: "已作废",
       statusCode: "REFUNDFEE_CLOSED",
       style: {color: "#999999"},
     },
   ],
   returnedStatusCode: {
     WAITCUSTOMSERVICEADUIT: "WAIT_CUSTOMSERVICE_ADUIT",
     WAITSENDGOODS: "WAIT_SEND_GOODS",
     WAITWAREHOUSECONFIRMGOODS: "WAIT_WAREHOUSE_CONFIRM_GOODS",
     WAITFINANCIALCONFIRMREFUNDFEE: "WAIT_FINANCIAL_CONFIRM_REFUNDFEE",
     REFUNDFEEFINASH: "REFUNDFEE_FINASH",
     REFUNDFEECLOSED: "REFUNDFEE_CLOSED",
   },
   expenseStatusCode: {
     PAY_ON: "PAY_ON",
     DONE: "Done",
     CLOSED: "Closed",
     DRAFT: "Draft",
   },
 }
 export default $OrderStatusType;
