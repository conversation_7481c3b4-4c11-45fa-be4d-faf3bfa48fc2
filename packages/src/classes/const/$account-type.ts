export enum $AccountType {
  CREDIT = "XYZH",
  BALANCE = "YECZ",
  ADVANCE = "YFKZH",
  FLZH = "FLZH",
  ADD = "ADD",
  REDUCE = "REDUCE",
  DIVIDEND_REBATE_DISCOUNT_LIMIT = "FZFLYHED", // 分账返利优惠额度
  DIVIDEND_REBATE = "FZFL", // 分账返利
  AUTOMATICTYPE = "Yes",
  ISAUTOREPAY = "Yes",
  WEIXINUPLOADCTRL = "No",
  ORDERPAYMENT = "OrderPayment", // 订货付款单
  DEDUCTDOCUMENTS = "DeductDocuments", // 扣款单
  CREDITACCOUNTREPAYMENT = "CreditAccountRepayment",  // 信用账户还款单
  StoredAccountRecharge = "StoredAccountRecharge", // 储值账户充值单
  RefundDocuments = "RefundDocuments", // 退款单
  RebateAccountDocuments = "RebateAccountDocuments", // 返利单
  MYBANKCARD = "MYBANKCARD",
  EXPENSENODE = "EXPENSENODE",
  BILLLIST = "BILLLIST", // 账单列表
  DEDUCTION_LIMIT = "SYFZ", // 返利额度
  RETURN_FREE_DISTRIBUTION = "HFPZ", // 后返赠配
}
export enum $GiftAccountTabType {
  ALL = "ALL",
  PZED = "PZED", // 额度
  HFPZ = "HFPZ", // 上限
}
