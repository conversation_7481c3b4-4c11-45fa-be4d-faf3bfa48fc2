export enum $OfflineTransferRecordOrderType {
  STOREDACCOUNTRECHARGE = "StoredAccountRecharge", // 充值单
  ORDERPAYMENT = "OrderPayment", // 付款单
  CREDITACCOUNTREPAYMENT = "CreditAccountRepayment", // 还款单
}
export enum $OfflineTransferRecordOrderStatus {
  DRAFT = "N", // 草稿
  DEALWITH =  "S", // 待审核
  CONFIRMEDRECEIPT = "A", // 已确认收款
  PAYFORFAILURE = "E", // 支付失败
  CANCELLATION = "C", // 已作废
  WITHDRAW = "D", // 已撤回
  INPAYMENT = "InPayment",//付款中
}
export enum $OrderStatus {
  DRAFT_ORDER = "N", // 草稿
  PAY_ON = "PAY_ON", // 付款中
  JXS_AUDIT =  "JXS_AUDIT", // 经销商审核中
  KF_REJECTED =  "KF_REJECTED", // 客服驳回
  JXS_PAY = "JXS_PAY", // 经销商支付中
  PHASE_PAY = "PHASE_PAY", // 阶段支付中
  CWYFK_AUDIT = "CWYFK_AUDIT", // 财务预付款审核中
  CWWK_AUDIT = "CWWK_AUDIT", // 财务尾款审核中
  CWQK_AUDIT = "CWQK_AUDIT", // 财务全款审核中
  WAIT_DELIVER = "WAIT_DELIVER", // 待发货
  DELIVER_ON = "DELIVER_ON", // 待收货
  DELIVER_END = "DELIVER_END", // 发货完成
  PD_CLOSED = "PD_CLOSED", // 部分发货已关闭
  SOM_CLOSED = "SOM_CLOSED", // 订单合并已关闭
  CANCEL = "CANCEL", // 已取消
}
const { STOREDACCOUNTRECHARGE, ORDERPAYMENT, CREDITACCOUNTREPAYMENT } = $OfflineTransferRecordOrderType
const { DRAFT, DEALWITH, CONFIRMEDRECEIPT, PAYFORFAILURE, CANCELLATION, WITHDRAW,INPAYMENT } = $OfflineTransferRecordOrderStatus
const { DRAFT_ORDER, PAY_ON, JXS_AUDIT, KF_REJECTED, JXS_PAY, PHASE_PAY, CWYFK_AUDIT, CWWK_AUDIT, CWQK_AUDIT, WAIT_DELIVER, DELIVER_ON, DELIVER_END, PD_CLOSED, SOM_CLOSED, CANCEL } = $OrderStatus;
export const offlineTransferRecordOrderType = [
  {
    key: STOREDACCOUNTRECHARGE,
    text: "充值单",
  },
  {
    key: ORDERPAYMENT,
    text: "付款单",
  },
  {
    key: CREDITACCOUNTREPAYMENT,
    text: "还款单",
  },
];
export const offlineTransferRecordOrderStatus = [
  {
    color: "#999999",
    key: DRAFT,
    text: "草稿",
  },
  {
    color: "#FF3030",
    key: DEALWITH,
    text: "待审核",
  },{
    color: "#FF3030",
    key: INPAYMENT,
    text: "付款中",
  },
  {
    color: "#52C41A",
    key: CONFIRMEDRECEIPT,
    text: "已确认收款",
  },
  {
    color: "#999999",
    key: PAYFORFAILURE,
    text: "支付失败",
  },
  {
    color: "#999999",
    key: CANCELLATION,
    text: "已作废",
  },
  {
    color: "#999999",
    key: WITHDRAW,
    text: "已撤回",
  },
];
export const orderStatus = [
  {
    color: "#FF3030",
    key: DRAFT_ORDER,
    text: "草稿",
  },
  {
    color: "#FF3030",
    key: PAY_ON,
    text: "付款中",
  },
  {
    color: "#FF3030",
    key: JXS_AUDIT,
    text: "经销商审核中",
  },
  {
    color: "#FF3030",
    key: KF_REJECTED,
    text: "客服驳回",
  },
  {
    color: "#FF3030",
    key: JXS_PAY,
    text: "经销商支付中",
  },
  {
    color: "#FF3030",
    key: PHASE_PAY,
    text: "阶段支付中",
  },
  {
    color: "#FF3030",
    key: CWYFK_AUDIT,
    text: "财务预付款审核中",
  },
  {
    color: "#FF3030",
    key: CWWK_AUDIT,
    text: "财务尾款审核中",
  },
  {
    color: "#FF3030",
    key: CWQK_AUDIT,
    text: "财务全款审核中",
  },
  {
    color: "#FF3030",
    key: WAIT_DELIVER,
    text: "待发货",
  },
  {
    color: "#FF3030",
    key: DELIVER_ON,
    text: "待收货",
  },
  {
    color: "#FF3030",
    key: DELIVER_END,
    text: "发货完成",
  },
  {
    color: "#FF3030",
    key: PD_CLOSED,
    text: "部分发货已关闭",
  },
  {
    color: "#FF3030",
    key: SOM_CLOSED,
    text: "订单合并已关闭",
  },
  {
    color: "#FF3030",
    key: CANCEL,
    text: "已取消",
  },
];
