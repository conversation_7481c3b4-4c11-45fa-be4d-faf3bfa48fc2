import GregorianCalendar from "gregorian-calendar";
import DateTimeFormat from "gregorian-calendar-format";
import zhCN from "gregorian-calendar/lib/locale/zh_CN";
// import moment from "moment";

export const disabledDate = (current) => {
  return current && current.getTime() < Date.now();
};

// 加法：

export const dcmAdd = (arg1, arg2) => {

  let r1 = 0;
  let r2 = 0;
  let m = 0;
  try {
    r1 = arg1.toString().split(".")[1].length;
  } catch (e) {
    r1 = 0;
  }
  try {
    r2 = arg2.toString().split(".")[1].length;
  } catch (e) {
    r2 = 0;
  }
  m = Math.pow(10, Math.max(r1, r2));
  return (accMul(arg1, m) + accMul(arg2, m)) / m;
}

// 减法：

export const dcmReduce = (arg1, arg2) => {

  let r1 = 0;
  let r2 = 0;
  let m = 0;
  try {
    r1 = arg1.toString().split(".")[1].length;
  } catch (e) {
    r1 = 0;
  }
  try {
    r2 = arg2.toString().split(".")[1].length;
  } catch (e) {
    r2 = 0;
  }
  m = Math.pow(10, Math.max(r1, r2));
  return (accMul(arg1, m) - accMul(arg2, m)) / m;
}

// 除法：
export const accDiv = (arg1, arg2) => {
  let t1 = 0;
  let t2 = 0;
  let r1 = 0;
  let r2 = 0;
  try {
    t1 = arg1.toString().split(".")[1].length;
  } catch (e) {
  }
  try {
    t2 = arg2.toString().split(".")[1].length;
  } catch (e) {
  }
  r1 = Number(arg1.toString().replace(".", ""))
  r2 = Number(arg2.toString().replace(".", ""))
  return (r1 / r2) * Math.pow(10, t2 - t1);

}

// 乘法
export const accMul = (arg1, arg2) => {

  let m = 0;
  const s1 = arg1.toString();
  const s2 = arg2.toString();
  try {
    m += s1.split(".")[1].length;
  } catch (e) {
  }
  try {
    m += s2.split(".")[1].length;
  } catch (e) {
  }
  return Number(s1.replace(".", "")) * Number(s2.replace(".", "")) / Math.pow(10, m);
}

export const toFixedOptimizing = (current, fractionDigits = 100) => {
  // let a = "1";
  // for (let i = 0; i ++ ; i >= fractionDigits) {
  //   a = a + "0";
  //   console.log("a", a);
  // }
  // const b = Math.round(current * Number(a)) / Number(a);
  const newCurrent = accMul(current, fractionDigits);
  const b = Math.round(newCurrent) / fractionDigits;
  return b;
};
export default class DateUtils {
  public static MD = "MM-dd";

  public static YMD = "yyyy-MM-dd";

  public static YMDHM = "yyyy-MM-dd HH:mm";

  public static toStringFormat(value, format?: string = this.YMDHM, noText? = "无"): string {
    if (value) {
      let date;
      if (typeof value === "string") {
        return value;
      }

      let datedValue;

      if (typeof value === "number") {
        datedValue = new Date(value);
      }
      if (_.isDate(value)) {
        datedValue = value;
      }

      const df = new DateTimeFormat(format);
      date = new GregorianCalendar(zhCN);
      date.setTime(+datedValue);
      return df.format(date);
    }

    return noText;
  }

  // public static momentFormat(data, format): string {
  //  return  moment(data).format(format);
  // }
}
