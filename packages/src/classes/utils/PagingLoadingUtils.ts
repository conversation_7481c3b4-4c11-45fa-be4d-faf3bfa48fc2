
let startx = 0;
let starty = 0;

export default class PagingLoadingUtils {
  constructor(props) {
    console.log(props);
  }
  public touchStart = (e) => {
    startx = e.touches[0].pageX;
    starty = e.touches[0].pageY;
  }

  public touchEnd = (e) => {
    let endx, endy;
    endx = e.changedTouches[0].pageX;
    endy = e.changedTouches[0].pageY;
    const direction = this.getDirection(startx, starty, endx, endy);
    switch (direction) {
      case 0:
        console.log("未滑动！");
        break;
      case 1:
        console.log("向上！");
        // this.loadData();
        break;
      case 2:
        console.log("向下！");
        break;
      case 3:
        console.log("向左！");
        break;
      case 4:
        console.log("向右！");
        break;
      default:
    }
  }

  public getAngle(angx, angy) {
    return Math.atan2(angy, angx) * 180 / Math.PI;
  }

  public getDirection(startx, starty, endx, endy) {
    const angx = endx - startx;
    const angy = endy - starty;
    let result = 0;

    // 如果滑动距离太短
    if (Math.abs(angx) < 2 && Math.abs(angy) < 2) {
      return result;
    }
    const angle = this.getAngle(angx, angy);
    if (angle >= -135 && angle <= -45) {
      result = 1;
    } else if (angle > 45 && angle < 135) {
      result = 2;
    } else if ((angle >= 135 && angle <= 180) || (angle >= -180 && angle < -135)) {
      result = 3;
    } else if (angle >= -45 && angle <= 45) {
      result = 4;
    }
    return result;
  }
 }
