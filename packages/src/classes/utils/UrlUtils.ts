export const getUrlParam = (paramname) => {

  const reg = new RegExp("(^|&)" + paramname + "=([^&]*)(&|$)");

  // 查询匹配 substr(1)删除? match()匹配
  const s = window.location.search.substr(1).match(reg);

  if (s != null) {

    return unescape(s[2]); // unescape() 函数可对通过 escape() 编码的字符串进行解码。

  }
  return null;
};

export const getQueryString = (key) => {
  const reg = new RegExp("(^|&)" + key + "=([^&]*)(&|$)");
  const result = window.location.search.substr(1).match(reg);
  return result ? decodeURIComponent(result[2]) : null;
};
