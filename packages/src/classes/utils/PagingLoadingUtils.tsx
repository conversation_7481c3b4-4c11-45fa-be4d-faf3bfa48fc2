import React, { Component } from "react";
import { ActivityIndicator } from "antd-mobile";

export default function asyncComponent(importComponent) {
  class AsyncComponent extends Component {
    constructor(props) {
      super(props);

      this.state = {
        component: null,
        isHaveScrollEnd: false,
        isScrollEnd: false,
      };
    }

    public async componentDidMount() {
      const { default: component } = await importComponent();
      const { prototype } = component.WrappedComponent
      if (prototype.paginationLoading) {
        this.setState({isHaveScrollEnd: prototype.paginationLoading.isPaginationLoadingData, component});
      } else {
        this.setState({
          component,
        });
      }
    }
    public touchStart = (e) => {
      this.setState({
        startx: e.touches[0].pageX,
        starty: e.touches[0].pageY,
      });
    }

    public touchEnd = (e) => {
      let endx, endy;
      endx = e.changedTouches[0].pageX;
      endy = e.changedTouches[0].pageY;
      const direction = this.getDirection(this.state.startx, this.state.starty, endx, endy);
      switch (direction) {
        case 0:
          console.log("未滑动！");
          break;
        case 1:
          console.log("向上！");
          this.isScrollToEnd();
          break;
        case 2:
          console.log("向下！");
          break;
        case 3:
          console.log("向左！");
          break;
        case 4:
          console.log("向右！");
          break;
        default:
      }
    }
    public getAngle(angx, angy) {
      return Math.atan2(angy, angx) * 180 / Math.PI;
    }
    public isScrollToEnd() {
      document.getElementsByClassName("scroll-ability")[0].style.position = "relative"
      const dataHeight = document.getElementsByClassName("scroll-ability")[0].clientHeight;
      const scrollHeight = document.body.scrollTop || document.documentElement.scrollTop;
      const screenHeight = document.documentElement.clientHeight;
      const h = 10;
      const { isScrollEnd } = this.state;
      console.log(isScrollEnd, dataHeight, scrollHeight, dataHeight - scrollHeight - h, screenHeight)
      if (dataHeight - scrollHeight - h < screenHeight) {
        if (!isScrollEnd) {
          this.setState({isScrollEnd: true}, () => {
            this.setState({isScrollEnd: false});
          });
          console.log("进来了", true);
        }
      } else {
        if (isScrollEnd) {
          this.setState({isScrollEnd: false});
          console.log("进来了", false);
        }
        console.log("还未到底");
      }
    }

    public getDirection(startx, starty, endx, endy) {
      const angx = endx - startx;
      const angy = endy - starty;
      let result = 0;

      // 如果滑动距离太短
      if (Math.abs(angx) < 2 && Math.abs(angy) < 2) {
        return result;
      }
      const angle = this.getAngle(angx, angy);
      if (angle >= -135 && angle <= -45) {
        result = 1;
      } else if (angle > 45 && angle < 135) {
        result = 2;
      } else if ((angle >= 135 && angle <= 180) || (angle >= -180 && angle < -135)) {
        result = 3;
      } else if (angle >= -45 && angle <= 45) {
        result = 4;
      }
      return result;
    }
    public render() {
      const C = this.state.component;
      console.log(this.props);
      const { isScrollEnd, isHaveScrollEnd } = this.state;
      console.log(isScrollEnd);
      return C ?
        isHaveScrollEnd ? <div className="scroll-ability" onTouchStart={this.touchStart} onTouchEnd={this.touchEnd}>
          <C {...this.props} isScrollEnd={isScrollEnd} />
      <div className="scroll-ability-loading-icon" />
        </div> :  <C {...this.props} />
    :
      <ActivityIndicator
        toast={true}
      text="加载中..."
      animating={true}
      />;
    }
  }

  return AsyncComponent;
}