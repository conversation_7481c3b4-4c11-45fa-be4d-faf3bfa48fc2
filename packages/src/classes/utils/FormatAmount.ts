export const formatAmount = (amount) => {
  // 金额转换 分->元 保留2位小数 并每隔3位用逗号分开 1,234.56
  const str = (Number(amount)).toFixed(2) + "";
  const intSum = str.substring(0, str.indexOf(".")).replace(/\B(?=(?:\d{3})+$)/g, ","); // 取到整数部分
  const dot = str.substring(str.length, str.indexOf(".")); // 取到小数部分搜索
  return intSum + dot;
};

export const toAmountNumber = (num: any): number => {
  let res: any = num;
  if (typeof res === "string") {
    res = Number(res);
  }
  return Number(res.toFixed(2));
};

export const autoFormatNumber = (num: any): number => {
  let res: any = num;
  if (typeof res === "string") {
    res = Number(res);
  }
  return parseFloat(res.toFixed(2));
};
