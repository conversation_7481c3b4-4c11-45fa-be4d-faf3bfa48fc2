const merge = require('webpack-merge');
const path = require('path');
const webpack = require('webpack');
const BrowserSyncPlugin = require('browser-sync-webpack-plugin');
const {BundleAnalyzerPlugin} = require('webpack-bundle-analyzer');
const UglifyJSPlugin = require('uglifyjs-webpack-plugin')

const PROXY = process.env.PROXY;

const EXTERNALS = {
  "react": "React",
  "react-dom": "ReactDOM",
  "react-router": "ReactRouter",
  // "mobx": "mobx",
  // "mobx-react": "mobxReact",
  "lodash": {
    commonjs: 'lodash',
    amd: 'lodash',
    root: '_'
  },
};

const base = function ({theme = {}}) {
  return {
    entry: [],

    output: {},

    resolve: {
      extensions: ['.webpack.js', '.web.js', '.js', '.ts', '.tsx']
    },

    plugins: [],

    module: {
      rules: [
        {
          test: /\.css$/,
          use: [
            'style-loader',
            'css-loader',
          ]
        },
        {
          test: /\.less$/,
          use: [
            'style-loader',
            'css-loader',
            {
              loader: 'less-loader',
              options: {
                sourceMap: true,
                modifyVars: theme,
                javascriptEnabled: true
              }
            }
          ]
        },
        // Needed for the css-loader when [bootstrap-webpack](https://github.com/bline/bootstrap-webpack)
        // loads bootstrap's css.
        {
          test: /\.woff(\?v=\d+\.\d+\.\d+)?$/,
          use: [
            {
              loader: 'url-loader',
              options: {
                limit: 10000,
                minetype: 'application/font-woff'
              }
            }
          ]
        },
        {
          test: /\.woff2(\?v=\d+\.\d+\.\d+)?$/,
          use: [
            {
              loader: 'url-loader',
              options: {
                limit: 10000,
                minetype: 'application/font-woff'
              }
            }
          ]
        },
        {
          test: /\.ttf(\?v=\d+\.\d+\.\d+)?$/,
          use: [
            {
              loader: 'url-loader',
              options: {
                limit: 10000,
                minetype: 'application/octet-stream'
              }
            }
          ]
        },
        {
          test: /\.eot(\?v=\d+\.\d+\.\d+)?$/,
          use: ['file-loader']
        },
        {
          test: /\.(png|jpg|jpeg|webp)$/i,
          use: [
            {
              loader: 'url-loader',
              options: {
                limit: 10000,
              }
            }
          ],
          exclude: /node_modules/
        }
      ]
    },
  }
};

const dev = function ({dirname, config, theme, port = 4000, proxyPrefix = "/api"}) {

  return merge(base({theme}), {
    entry: [
      `webpack-dev-server/client?http://0.0.0.0:${port}`,
      "webpack/hot/only-dev-server",
    ],
    output: {
      path: path.resolve(__dirname, `../../__out__/scm`),
      filename: '[name].js',
      publicPath: '/',
      chunkFilename: '[name].[hash].chunk.js'
    },
    devtool: 'source-map',
    module: {
      rules: []
    },
    devServer: {
      host: '0.0.0.0',
      hot: true,
      historyApiFallback: true,
      port: port,
      contentBase: path.resolve(dirname, '.'),
      stats: {
        colors: true,
        timings: true,
        chunks: false   // less verbose, disable chunk information output
      },
      proxy: {
        [proxyPrefix]: {
          target: PROXY,
          secure: true,
          changeOrigin: true,
          bypass: function (req) {
            if (req.headers.accept && req.headers.accept.indexOf("html") !== -1) {
              console.log("Bypass the proxy - " + req.headers.accept);
              return "/index.local.html";
            }
          }
        },
        ['/assets']: {
          target: PROXY,
          secure: false,
          bypass: function (req) {
            if (req.headers.accept && req.headers.accept.indexOf("html") !== -1) {
              console.log("Bypass the proxy - " + req.headers.accept);
              return "/index.local.html";
            }
          }
        }
      },
    },
    plugins: [
      new webpack.NamedModulesPlugin(),
      new BrowserSyncPlugin({
        host: '0.0.0.0',
        port: port + 10,
        proxy: `http://0.0.0.0:${port}`
      }, {
        reload: false
      }),
      // new webpack.DllReferencePlugin({
      //   context: path.resolve(__dirname),
      //   manifest: require(path.resolve(__dirname, './manifest.json'))
      // }),
      // new webpack.DllReferencePlugin({
      //   context: path.resolve(dirname),
      //   manifest: require(path.resolve(dirname, './manifest.other.json'))
      // }),
    ],
    // externals: EXTERNALS,
  }, config);
};

const bundle = function ({name = 'main', dirname, config, theme, beta, version}) {
  return merge(base({theme}), {
    entry: [],
    output: {
      filename: beta ? `${version}/${name}.js` : '[name].[chunkhash:8].js',
      publicPath: '/scm/',
      chunkFilename: `${version}/[name].chunk.js`
    },
    plugins: [
      new UglifyJSPlugin(),
      new webpack.DefinePlugin({
        'process.env': {
          NODE_ENV: JSON.stringify(process.env.NODE_ENV),
        },
      }),
      // new webpack.DllReferencePlugin({
      //   context: path.resolve(__dirname),
      //   manifest: require(path.resolve(__dirname, './manifest.json'))
      // }),
      // new webpack.DllReferencePlugin({
      //   context: path.resolve(dirname),
      //   manifest: require(path.resolve(dirname, './manifest.other.json'))
      // }),
      // new BundleAnalyzerPlugin(),
    ],
    module: {
      rules: []
    },
    optimization: {
      splitChunks: {
        chunks: "all",
        minSize: 30000, // 模块的最小体积
        minChunks: 1, // 模块的最小被引用次数
        maxAsyncRequests: 5, // 按需加载的最大并行请求数
        maxInitialRequests: 3, // 一个入口最大并行请求数
        automaticNameDelimiter: '~', // 文件名的连接符
        name: true,
        cacheGroups: {
          react: {
            test: /node_modules\/react/,
            name: 'react',
            chunks: "all",
            priority: 50,
            enforce: true
          },
          mobile: {
            test: /node_modules\/antd-mobile\//,
            name: 'mobile',
            chunks: "all",
            priority: 40,
            enforce: true
          },
          antd: {
            test: /node_modules\/antd\//,
            name: 'antd',
            chunks: "all",
            priority: 30,
            enforce: true
          },
          common: {
            test: /node_modules\//,
            name: 'common',
            chunks: "all",
            priority: 20,
            enforce: true
          }
        }
      }
    },
    // externals : EXTERNALS,
  }, config);
};

module.exports = {
  dev, bundle
};
