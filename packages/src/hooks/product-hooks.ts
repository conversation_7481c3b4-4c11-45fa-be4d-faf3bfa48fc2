/**
 * 根据商品标志返回对应的分类数据
 * @param isHomeProduct 是否为家居产品
 * @param homeData 家居数据
 * @param storeData 店护数据
 * @returns 返回对应的分类数据
 */
export const useProductCateByData = (isHomeProduct: boolean, homeData, storeData) => {
  return isHomeProduct ? homeData : storeData;
};

export const useProductIndexColor = (index: string) => {
  return "正常" === index ? "#437DF0" : "#FF3030";
};

export const useProductIndexValueColor = (value: string, index: string | null) => {
  if (value === null) {
    return "#666";
  }
  return "正常" === index ? "#437DF0" : "#FF3030";
};

export const useProductIndexValue = (value: string) => {
  if (value === null) {
    return "-";
  }
  return value;
};
