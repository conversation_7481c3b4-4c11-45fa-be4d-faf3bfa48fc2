import { Modal } from "antd-mobile";
import { ModalProps } from "antd-mobile/lib/modal/Modal";
import * as React from "react";
import ReactDOM from "react-dom";
import "./index.less";

interface IModalProps extends ModalProps {
  children: React.ReactNode;
}

export const useShowModal = (props: Omit<IModalProps, "visible">) => {
  const div = document.createElement("div");
  document.body.appendChild(div);

  const close = () => {
    ReactDOM.unmountComponentAtNode(div);
    if (div && div.parentNode) {
      div.parentNode.removeChild(div);
    }
  };

  const rc = React.cloneElement(props.children as any, {
    onClose: close,
  });

  ReactDOM.render(<Modal
    popup={true}
    visible={true}
    animationType={"slide-up"}
    wrapClassName={"pop-modal-wrap"}
    onClose={() => close()}
    {...props}
  >
    {rc}
  </Modal>, div);
};
