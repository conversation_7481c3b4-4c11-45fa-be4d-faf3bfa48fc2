<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="utf-8">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <meta name="viewport"
        content="width=device-width,height=device-height,user-scalable=no,initial-scale=1, minimum-scale=1, maximum-scale=1">
  <title>订货商城</title>
  <link rel="icon" href="https://order.fwh1988.cn:14501/static-img/scm/favicon.png" type="image/x-icon">
  <link rel="stylesheet" type="text/css" charset="UTF-8"
        href="https://order.fwh1988.cn:14501/scm/slick.min.css"/>
  <link rel="stylesheet" type="text/css"
        href="https://order.fwh1988.cn:14501/scm/slick-theme.min.css"/>
  <script src="https://order.fwh1988.cn:14501/scm/ga-util.js"></script>
  <!--<script src="http://pv.sohu.com/cityjson?ie=utf-8"></script>-->
  <script>
    var _hmt = _hmt || [];
    (function () {
      var hm = document.createElement("script");
      hm.src = "https://hm.baidu.com/hm.js?${statisticsCode}";
      var s = document.getElementsByTagName("script")[0];
      s.parentNode.insertBefore(hm, s);
    })();
  </script>

  <!-- GrowingIO Analytics code version 2.1 -->
  <!-- Copyright 2015-2018 GrowingIO, Inc. More info available at http://www.growingio.com -->

  <script type='text/javascript'>
    !function(e,t,n,g,i){e[i]=e[i]||function(){(e[i].q=e[i].q||[]).push(arguments)},n=t.createElement("script"),tag=t.getElementsByTagName("script")[0],n.async=1,n.src=('https:'==document.location.protocol?'https://':'http://')+g,tag.parentNode.insertBefore(n,tag)}(window,document,"script","assets.growingio.com/2.1/gio.js","gio");
    gio('init','8d1889a82770ce4f', {});

    //custom page code begin here

    //custom page code end here

    gio('send');

  </script>

  <!-- End GrowingIO Analytics code version: 2.1 -->

</head>
<body>
<script>
  window.ENV = {
    reactMountPoint: 'react-root',
    sitePath: '/scm',
    server: "${server}",
    userIp: "************"
    /*userIp: returnCitySN["cip"]*/

  }
</script>
<div id="react-root"></div>
<script src="https://order.fwh1988.cn:14501/scm/reflect-metadata.js"></script>
<script type="text/javascript" src="https://order.fwh1988.cn:14501/scm/jquery-3-2-1.min.js"></script>
<script src="https://res.wx.qq.com/open/js/jweixin-1.4.0.js"></script>
<!--<script type="text/javascript" src="/scm/lib.js?v=2.1.16"></script>-->
<!--<script type="text/javascript" src="/scm/otherLib.js"></script>-->
<script src="https://order.fwh1988.cn:14501/scm/viewer-1-3-6.js"></script>
<link rel="stylesheet" type="text/css"
      href="https://order.fwh1988.cn:14501/scm/viewer-1-3-6.css"/>
<!--<script type="text/javascript" src="/scm/otherLib.js?v=2.1.16"></script>-->
<script type="text/javascript" src="/scm/${timestamp}/react.chunk.js"></script>
<script type="text/javascript" src="/scm/${timestamp}/common.chunk.js"></script>
<script type="text/javascript" src="/scm/${timestamp}/mobile.chunk.js"></script>
<script type="text/javascript" src="/scm/${timestamp}/antd.chunk.js"></script>
<script type="text/javascript" src="/scm/${timestamp}/main.js"></script>
<script src="https://order.fwh1988.cn:14501/scm/mobile-detect.min.js"></script>
<script>
	// 1，苹果手机型号匹配列表
	(function () {
		var canvas, gl, glRenderer, models,
			devices = [
				['a7', '640x1136', ['iPhone 5', 'iPhone 5s']],
				['a7', '640x1136', ['iPhone 5', 'iPhone 5s']],
				['a7', '1536x2048', ['iPad Air', 'iPad Mini 2', 'iPad Mini 3']],
				['a8', '640x1136', ['iPod touch (6th gen)']],
				['a8', '750x1334', ['iPhone 6']],
				['a8', '1242x2208', ['iPhone 6 Plus']],
				['a8', '1536x2048', ['iPad Air 2', 'iPad Mini 4']],
				['a9', '640x1136', ['iPhone SE']],
				['a9', '750x1334', ['iPhone 6s']],
				['a9', '1242x2208', ['iPhone 6s Plus']],
				['a9x', '1536x2048', ['iPad Pro (1st gen 9.7-inch)']],
				['a9x', '2048x2732', ['iPad Pro (1st gen 12.9-inch)']],
				['a10', '750x1334', ['iPhone 7']],
				['a10', '1242x2208', ['iPhone 7 Plus']],
				['a10x', '1668x2224', ['iPad Pro (2th gen 10.5-inch)']],
				['a10x', '2048x2732', ['iPad Pro (2th gen 12.9-inch)']],
				['a11', '750x1334', ['iPhone 8']],
				['a11', '1242x2208', ['iPhone 8 Plus']],
				['a11', '1125x2436', ['iPhone X']],
				['a12', '828x1792', ['iPhone Xr']],
				['a12', '1125x2436', ['iPhone Xs']],
				['a12', '1242x2688', ['iPhone Xs Max']],
				['a12x', '1668x2388', ['iPad Pro (3rd gen 11-inch)']],
				['a12x', '2048x2732', ['iPad Pro (3rd gen 12.9-inch)']],
				['', '640x1136', ['iPhone 5', 'iPhone 5s']],// 从这里开始不实用第一个元素
				['', '640x1136', ['iPhone 5', 'iPhone 5s']],
				['', '1536x2048', ['iPad Air', 'iPad Mini 2', 'iPad Mini 3']],
				['', '640x1136', ['iPod touch (6th gen)']],
				['', '750x1334', ['iPhone 6']],
				['', '1242x2208', ['iPhone 6 Plus']],
				['', '1536x2048', ['iPad Air 2', 'iPad Mini 4']],
				['', '640x1136', ['iPhone SE']],
				['', '750x1334', ['iPhone 6s']],
				['', '1242x2208', ['iPhone 6s Plus']],
				['', '1536x2048', ['iPad Pro (1st gen 9.7-inch)']],
				['', '2048x2732', ['iPad Pro (1st gen 12.9-inch)']],
				['', '750x1334', ['iPhone 7']],
				['', '1242x2208', ['iPhone 7 Plus']],
				['', '1668x2224', ['iPad Pro (2th gen 10.5-inch)']],
				['', '2048x2732', ['iPad Pro (2th gen 12.9-inch)']],
				['', '750x1334', ['iPhone 8']],
				['', '1242x2208', ['iPhone 8 Plus']],
				['', '1125x2436', ['iPhone X']],
				['', '828x1792', ['iPhone Xr']],
				['', '1125x2436', ['iPhone Xs']],
				['', '1242x2688', ['iPhone Xs Max']],
				['', '1668x2388', ['iPad Pro (3rd gen 11-inch)']],
				['', '2048x2732', ['iPad Pro (3rd gen 12.9-inch)']]
			];

		function getCanvas() {
			if (canvas == null) {
				canvas = document.createElement('canvas');
			}
			return canvas;
		}

		function getGl() {
			if (gl == null) {
				gl = getCanvas().getContext('experimental-webgl');
			}

			return gl;
		}

		function getResolution() {
			var ratio = window.devicePixelRatio || 1;
			return (Math.min(screen.width, screen.height) * ratio)
				+ 'x' + (Math.max(screen.width, screen.height) * ratio);
		}

		function getGlRenderer() {
			if (glRenderer == null) {
				debugInfo = getGl().getExtension('WEBGL_debug_renderer_info');
				glRenderer = debugInfo == null ? 'unknown' : getGl().getParameter(debugInfo.UNMASKED_RENDERER_WEBGL);
			}

			return glRenderer;
		}

		function getModels() {
			if (models == null) {
				var gpu = getGlRenderer(),
					matches = gpu.match(/^apple\s*([_a-z0-9-]?)\s*gpu$/i),
					res = getResolution();
				models = [`unknown,gpu=${gpu},Resolution=${res}`];
				if (matches) {
					for (var i = 0; i < devices.length; i++) {
						var device = devices[i];
						if (matches[1].toLowerCase() == device[0]
							&& res == device[1]) {
							models = matches[1].toLowerCase() ? device[2] : [`${device[2]},gpu=${gpu},Resolution=${res}`];
							break;
						}
					}
				}
			}
			return models;
		}

		if (window.MobileDevice == undefined) {
			window.MobileDevice = {};
		}

		window.MobileDevice.getGlRenderer = getGlRenderer;
		window.MobileDevice.getModels = getModels;
		window.MobileDevice.getResolution = getResolution;

		window.MobileDevice.is = function (match) {
			var currentModels = getModels();
			match = match.toLowerCase().replace(/\s+$/, '') + ' ';

			for (var i = 0; i < currentModels.length; i++) {
				var model = currentModels[i].toLowerCase() + ' ';

				if (0 === model.indexOf(math)) {
					return true;
				}
			}

			return false;
		};
	})();
	// 2，获取用户手机信息
	//获取userAgent信息
	var user_agent = navigator.userAgent;
	console.log("navigator", navigator);
	//初始化mobile-detect
	var md = new MobileDetect(user_agent);
	var os = md.os();//获取系统
	var newMobile = "";
	if (os == "iOS") {
		//ios系统的处理
		os = md.os() + md.version("iPhone");
		ios = md.mobile();
		//再通过iphone-device.js获取具体的苹果手机型号
		newMobile = MobileDevice.getModels().join(' or ');
		if (newMobile == 'unknown') {
			newMobile = '';
		}
	} else if (os == "AndroidOS") {
		//Android系统的处理
		os = md.os() + md.version("Android");
		var sss = user_agent.split(";");
		//判断UA里边有没有Build信息，通过这个拿到安卓的具体机型
		// var i = sss.contains();
		var i = contains("Build/", sss);
		if (i > -1) {
			newMobile = sss[i].substring(0, sss[i].indexOf("Build/"));
		}
	}

	//判断数组中是否包含某字符串（安卓机型获取用到）
	function contains(needle, arr = []) {
		let index = -1;
		if (arr.length > 0) {
			arr.some((item, i) => {
				if (item.indexOf(needle) > -1) {
					index = i;
				}
				;
			});
		}
		return index
	}

	// Array.prototype.contains = function (needle) {
	// 	for (i in this) {
	// 		if (this[i].indexOf(needle) > 0){
	// 			return i;
	//         }
	// 	}
	// 	return -1;
	// }
	//写入数据库
	const mobileInfo = `手机型号=${newMobile},系统型号=${os},user_agent=${user_agent}`;
	window.ENV.mobileInfo = mobileInfo;
</script>
</body>
</html>
