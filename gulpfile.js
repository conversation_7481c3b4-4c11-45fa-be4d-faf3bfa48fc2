let gulp = require('gulp');
let concat = require('gulp-concat');
let ftp = require('vinyl-ftp');
let sftp = require('gulp-sftp');
let util = require('gulp-util');
let rename = require('gulp-rename');
let replace = require('gulp-replace');
let fs = require('fs');
const path = require('path');
const pkg = require("./packages/src/app");
const envConfig = require("./env");

const appDirectory = fs.realpathSync(process.cwd());
const resolveApp = relativePath => path.resolve(appDirectory, relativePath);

const version = require(resolveApp('package.json')).version;

// gulp.task("ftp:saas:beta", function () {
// 	let conn = ftp.create({
// 		host: '***************',
// 		user: 'cdn',
// 		password: 'BingkunCdn',
// 		log: util.log
// 	});
//
// 	return gulp
// 		.src([`./__out__/${pkg.name}/**/*`], {base: `__out__/${pkg.name}`, buffer: false})
// 		.pipe(conn.dest(pkg.name))
// });

gulp.task('jsConcat1', function () {

  const arr = [];
  arr.push("./node_modules/react/umd/react.production.min.js");
  arr.push("./node_modules/react-dom/umd/react-dom.production.min.js");
  arr.push("./node_modules/react-router/umd/react-router.min.js");
  // arr.push("./node_modules/mobx/lib/mobx.min.js");
  // arr.push("./node_modules/mobx-react/index.min.js");
  arr.push("./node_modules/lodash/lodash.min.js");

  gulp.src(arr)    //要合并的所有js文件

    .pipe(concat('reactMobx.js'))//合并后的文件名

    .pipe(gulp.dest("__out__/jsConcat"));  //合并后存放的目录，如果没有目录会自动创建

});

gulp.task('jsConcat2', function () {

  const arr = [];
  arr.push("./node_modules/antd-mobile/dist/antd-mobile.min.js");
  gulp.src(arr)    //要合并的所有js文件

    .pipe(concat('antdMobile.js'))//合并后的文件名

    .pipe(gulp.dest("__out__/jsConcat"));  //合并后存放的目录，如果没有目录会自动创建

});

// create remote index.html to __out__
function registerHtmlTask(env) {
  gulp.task(`html:${env}`, function () {
    return gulp
      .src('./packages/src/remote.html')
      .pipe(replace("${server}", envConfig[env].server))
      .pipe(replace("${statisticsCode}", envConfig[env].statisticsCode))
      .pipe(replace("${timestamp}", version))
      .pipe(rename('index.html'))
      .pipe(gulp.dest(`__out__/${pkg.name}`));
  });
}

function registerJsTask(env) {
  const ftpServer = envConfig[env].ftpServer;
  let conn = sftp({
    host: ftpServer.ip,
    user: ftpServer.user,
    password: ftpServer.password,
    port: ftpServer.port,
    remotePath: ftpServer.path,
    log: util.log,
    timeout: 30000,
  });
  gulp.task(`js:${env}`, function () {
    return gulp
      .src([`./__out__/${pkg.name}/**/*`], {base: `__out__/${pkg.name}`, buffer: false})
      .pipe(conn);
  });
}

registerHtmlTask("dev");
registerHtmlTask("uat");
registerHtmlTask("ppe");
registerHtmlTask("pressure");
registerHtmlTask("prod");
registerHtmlTask("zcjprod");
registerHtmlTask("sales");
registerHtmlTask("yblprod");
registerHtmlTask("hmsprod");
registerHtmlTask("updev");
registerHtmlTask("upuat");

registerJsTask("dev");
registerJsTask("uat");
registerJsTask("ppe");
registerJsTask("pressure");
registerJsTask("prod");
registerJsTask("zcjprod");
registerJsTask("sales");
registerJsTask("yblprod");
registerJsTask("hmsprod");
registerJsTask("updev");
registerJsTask("upuat");

